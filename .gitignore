# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
node_modules
.pnp
.pnp.js

# testing
coverage

# next.js
.next/
out/
next-env.d.ts

# nitro
.nitro/
.output/

# mobile
.expo/
expo-env.d.ts
apps/kabadiwala-mobile/.gitignore
apps/kabadiwala-mobile/ios
apps/kabadiwala-mobile/android

# production
build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env
.env*.local

# vercel
.vercel

# typescript
dist/
.cache

# turbo
.turbo

# pwa files
public/sw*
public/swe-worker*

.tool-versions

sw.js
