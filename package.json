{"name": "create-t3-turbo", "private": true, "scripts": {"build": "turbo run build", "clean": "git clean -xdf node_modules", "clean:workspaces": "turbo run clean", "db:push": "turbo -F @acme/db push", "db:studio": "turbo -F @acme/db studio", "dev": "turbo watch dev --continue", "dev:next": "turbo watch dev -F @acme/nextjs...", "format": "turbo run format --continue -- --cache --cache-location .cache/.prettiercache", "format:fix": "turbo run format --continue -- --write --cache --cache-location .cache/.prettiercache", "postinstall": "pnpm lint:ws", "lint": "turbo run lint --continue -- --cache --cache-location .cache/.eslintcache", "lint:fix": "turbo run lint --continue -- --fix --cache --cache-location .cache/.eslintcache", "lint:ws": "pnpm dlx sherif@latest", "typecheck": "turbo run typecheck"}, "prettier": "@acme/prettier-config", "devDependencies": {"@acme/eslint-config": "workspace:*", "@acme/prettier-config": "workspace:*", "@acme/tsconfig": "workspace:*", "@turbo/gen": "^2.5.1", "prettier": "catalog:", "turbo": "^2.5.1", "typescript": "catalog:"}, "packageManager": "pnpm@10.6.3", "engines": {"node": ">=22.14.0", "pnpm": ">=9.6.0"}}