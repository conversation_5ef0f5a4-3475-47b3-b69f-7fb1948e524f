import { useRouter } from "next/navigation";
import { LogOut, User2 } from "lucide-react";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@acme/ui/components/ui/dropdown-menu";
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@acme/ui/components/ui/sidebar";

import { authClient, useSession } from "~/server/auth/client";

const SidebarFooterContent = () => {
  const router = useRouter();
  const { open } = useSidebar();

  const { data: session } = useSession();

  const handleLogout = async () => {
    await authClient.signOut();
    router.push("/auth/login");
  };

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger className="w-full">
            <SidebarMenuButton className="w-full rounded-md bg-background/50 px-3 py-6 shadow-sm hover:bg-accent/50">
              <User2 className="mr-2 h-5 w-5 shrink-0 text-primary" />
              {open ? (
                <div className="flex flex-1 flex-col items-start text-sm">
                  <p className="line-clamp-1 font-medium">
                    {session?.user.name}
                  </p>
                  <p className="max-w-[160px] truncate text-xs text-muted-foreground">
                    {session?.user.email}
                  </p>
                </div>
              ) : (
                <span className="sr-only">{session?.user.name}</span>
              )}
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent side="right" align="start" className="w-60">
            {/* <DropdownMenuItem
              className="flex items-center gap-2 py-2"
              onClick={(e) => {e.stopPropagation(); e.preventDefault();} }
            >
              <Settings className="h-4 w-4" />
            </DropdownMenuItem> */}

            <DropdownMenuItem
              onClick={handleLogout}
              className="flex cursor-pointer items-center gap-2 py-2 text-destructive hover:text-destructive"
            >
              <LogOut className="h-4 w-4" />
              <span>Sign out</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
};

export default SidebarFooterContent;
