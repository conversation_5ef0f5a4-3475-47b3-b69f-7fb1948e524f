"use client";

import type { ReactNode } from "react";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet<PERSON>itle,
} from "@acme/ui/components/ui/sheet";
import { cn } from "@acme/ui/lib/utils";

/**
 * A reusable sheet component that can be used for modals, forms, or other content.
 * This component is designed to work with the `useSheet` hook for state management.
 *
 * @param {Object} props - The props for the GenericSheet component.
 * @param {boolean} props.isOpen - Controls whether the sheet is open or closed.
 * @param {Function} props.onClose - Callback function to close the sheet.
 * @param {string} props.title - The title displayed at the top of the sheet.
 * @param {ReactNode} props.children - The content to be displayed inside the sheet.
 * @param {string} [props.className] - Optional additional class names for styling.
 */
export const GenericSheet = ({
  isOpen,
  onClose,
  title,
  children,
  className,
}: {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: ReactNode;
  className?: string;
}) => {
  return (
    <Sheet open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <SheetContent
        className={cn(
          "min-w-full space-y-4 overflow-y-auto rounded-l-2xl md:min-w-[500px] lg:min-w-[600px] 2xl:min-w-[800px]",
          className,
        )}
      >
        <SheetHeader>
          <SheetTitle>{title}</SheetTitle>
        </SheetHeader>
        {children}
      </SheetContent>
    </Sheet>
  );
};
