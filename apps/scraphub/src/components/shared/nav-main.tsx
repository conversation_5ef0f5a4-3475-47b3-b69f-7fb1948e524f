"use client";

import type { LucideIcon } from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { ChevronRight } from "lucide-react";

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@acme/ui/components/ui/collapsible";
import {
  SidebarGroup,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@acme/ui/components/ui/sidebar";
import { cn } from "@acme/ui/lib/utils";

export function NavMain({
  items,
}: {
  items: {
    title: string;
    url: string;
    icon?: LucideIcon;
    isActive?: boolean;
    items?: {
      title: string;
      url: string;
    }[];
  }[];
}) {
  const pathName = usePathname();

  return (
    <SidebarGroup className="px-2">
      <SidebarMenu className="space-y-1">
        {items.map((item) =>
          item.items?.length === 0 ? (
            <SidebarMenuItem key={item.title}>
              <SidebarMenuButton asChild tooltip={item.title}>
                <Link
                  href={item.url}
                  className={cn(
                    "group relative flex items-center gap-3 rounded-lg px-3 py-2.5 text-sm font-medium transition-all duration-200 hover:bg-teal-50 hover:text-teal-800",
                    pathName === item.url
                      ? "border-l-4 border-teal-600 bg-teal-50 font-bold text-teal-800 shadow-sm"
                      : "text-black-600 hover:translate-x-1",
                  )}
                >
                  {item.icon && (
                    <item.icon
                      className={cn(
                        "size-4 transition-colors duration-200",
                        pathName === item.url
                          ? "text-teal-600"
                          : "text-black-500 group-hover:text-teal-600",
                      )}
                    />
                  )}
                  <span className="truncate">{item.title}</span>
                  {pathName === item.url && (
                    <div className="absolute right-2 size-2 rounded-full bg-teal-600" />
                  )}
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          ) : (
            <Collapsible
              key={item.title}
              asChild
              defaultOpen={item.isActive}
              className="group/collapsible"
            >
              <SidebarMenuItem>
                <CollapsibleTrigger asChild>
                  <SidebarMenuButton
                    tooltip={item.title}
                    className={cn(
                      "group flex w-full items-center gap-3 rounded-lg px-3 py-2.5 text-sm font-medium transition-all duration-200 hover:bg-teal-50 hover:text-teal-800",
                      "text-black-600 hover:translate-x-1",
                    )}
                  >
                    {item.icon && (
                      <item.icon className="size-4 text-black-500 transition-colors duration-200 group-hover:text-teal-600" />
                    )}
                    <span className="truncate font-semibold">{item.title}</span>
                    <ChevronRight className="ml-auto size-4 text-black-400 transition-all duration-200 group-hover:text-teal-600 group-data-[state=open]/collapsible:rotate-90" />
                  </SidebarMenuButton>
                </CollapsibleTrigger>
                <CollapsibleContent className="animate-accordion-down">
                  <SidebarMenuSub className="ml-6 mt-1 space-y-1 border-l-2 border-black-150 pl-4">
                    {item.items?.map((subItem) => (
                      <SidebarMenuSubItem key={subItem.title}>
                        <SidebarMenuSubButton asChild>
                          <Link
                            href={subItem.url}
                            className={cn(
                              "group relative block rounded-md px-3 py-2 text-sm transition-all duration-200 hover:bg-yellow-50 hover:text-yellow-800",
                              pathName === subItem.url
                                ? "border-l-3 border-yellow-600 bg-yellow-50 font-bold text-yellow-800 shadow-sm"
                                : "text-black-500 hover:translate-x-1",
                            )}
                          >
                            <span className="truncate">{subItem.title}</span>
                            {pathName === subItem.url && (
                              <div className="absolute right-2 top-1/2 size-1.5 -translate-y-1/2 rounded-full bg-yellow-600" />
                            )}
                          </Link>
                        </SidebarMenuSubButton>
                      </SidebarMenuSubItem>
                    ))}
                  </SidebarMenuSub>
                </CollapsibleContent>
              </SidebarMenuItem>
            </Collapsible>
          ),
        )}
      </SidebarMenu>
    </SidebarGroup>
  );
}
