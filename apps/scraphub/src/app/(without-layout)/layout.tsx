import "@acme/ui/globals.css";

import type { Metadata } from "next";
import { Poppins } from "next/font/google";

// import localFont from "next/font/local";

import { Toaster } from "@acme/ui/components/ui/sonner";

import { TRPCReactProvider } from "~/trpc/react";
import { HydrateClient } from "~/trpc/server";

export const metadata: Metadata = {
  title: "Scraphub",
  description: "Scraphub",
  openGraph: {
    title: "Scraphub",
    description: "Scraphub",
  },
  icons: [{ rel: "icon", url: "/favicon.png" }],
};

const poppins = Poppins({
  subsets: ["latin"],
  variable: "--font-poppins",
  display: "swap",
  weight: ["100", "200", "300", "400", "500", "600", "700", "800", "900"],
});

export default function RootLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  return (
    <html
      lang="en"
      className={`${poppins.className} min-h-screen text-foreground antialiased`}
    >
      <body>
        <TRPCReactProvider>
          <HydrateClient>{children}</HydrateClient>
          <Toaster richColors />
        </TRPCReactProvider>
      </body>
    </html>
  );
}
