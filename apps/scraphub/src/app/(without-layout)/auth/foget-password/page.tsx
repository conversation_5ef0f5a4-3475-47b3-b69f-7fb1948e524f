"use client";

import { useState } from "react";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";

import { <PERSON><PERSON> } from "@acme/ui/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@acme/ui/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@acme/ui/components/ui/form";
import { Input } from "@acme/ui/components/ui/input";
import { emailSchema } from "@acme/validators";

import { authClient } from "~/server/auth/client";

const ResetPasswordSchema = z.object({
  email: emailSchema,
});

export default function ForgetPasswordPage() {
  const [submitted, setSubmitted] = useState(false);

  const form = useForm<z.infer<typeof ResetPasswordSchema>>({
    resolver: zodResolver(ResetPasswordSchema),
    defaultValues: {
      email: "",
    },
  });

  const onSubmit = async (values: z.infer<typeof ResetPasswordSchema>) => {
    try {
      await authClient.forgetPassword(
        {
          email: values.email,
          redirectTo: `/auth/reset-password`,
        },
        {
          onSuccess: (val) => {
            console.log("sucess vsl ", val);
          },
        },
      );
      setSubmitted(true);
      toast.success("Reset password link sent to your email");
    } catch (error) {
      console.error(error);
      toast.error("Failed to send reset link. Please try again.");
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle className="text-xl">Reset Your Password</CardTitle>
        </CardHeader>
        <CardContent>
          {submitted ? (
            <div className="text-center">
              <p className="mb-4">
                We've sent a password reset link to your email address.
              </p>
              <p>
                Please check your inbox and follow the instructions to reset
                your password.
              </p>
            </div>
          ) : (
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-6"
              >
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input placeholder="<EMAIL>" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <Button type="submit" className="w-full">
                  Send Reset Link
                </Button>
              </form>
            </Form>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
