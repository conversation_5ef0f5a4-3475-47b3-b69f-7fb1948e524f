import Image from "next/image";
import Link from "next/link";

import LoginForm from "./login-form";

const LoginPage = () => {
  return (
    <div className="flex min-h-svh flex-col items-center justify-center gap-6 bg-muted p-6 md:p-10">
      <div className="flex w-full max-w-sm flex-col gap-6">
        <Link
          href="/"
          className="flex w-full items-center justify-center gap-2 self-center text-lg font-medium"
        >
          <Image
            src="/static/logo/scraplo.svg"
            alt="scraplo"
            height={500}
            width={500}
            className="size-10"
          />
          Scraplo Scraphub
        </Link>
        <LoginForm />
      </div>
    </div>
  );
};

export default LoginPage;
