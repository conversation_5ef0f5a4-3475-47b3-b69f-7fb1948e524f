import { Separator } from "@acme/ui/components/ui/separator";

import { HydrateClient, prefetch, trpc } from "~/trpc/server";
import OrdersDataTable from "../orders-data-table";

const AllOrdersPage = () => {
  void prefetch(
    trpc.order.getAllOrders.queryOptions({
      status: "ALL",
    }),
  );

  return (
    <HydrateClient>
      <main className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex flex-col gap-3">
            <h1 className="text-3xl font-bold tracking-tight">
              All Orders Management
            </h1>
            <p className="text-muted-foreground">
              Below orders are filtered based on kabadiwalas assigned to this
              scraphub from the admin panel. Opening any of the order detail
              will assign it to you.
            </p>
          </div>
        </div>
        <Separator className="my-6" />
        <OrdersDataTable statusFilter="ALL" />
      </main>
    </HydrateClient>
  );
};

export default AllOrdersPage;
