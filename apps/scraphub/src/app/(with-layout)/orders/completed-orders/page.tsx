import { Separator } from "@acme/ui/components/ui/separator";

import { HydrateClient, prefetch, trpc } from "~/trpc/server";
import OrdersDataTable from "../orders-data-table";

const CompletedOrders = () => {
  void prefetch(
    trpc.order.getAllOrders.queryOptions({
      status: "COMPLETED",
    }),
  );

  return (
    <HydrateClient>
      <main className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex flex-col gap-3">
            <h1 className="text-3xl font-bold tracking-tight">
              Completed Orders Management
            </h1>
            <p className="text-muted-foreground">
              Below orders in table are completed by you. You can view the
              details of each order.
            </p>
          </div>
        </div>
        <Separator className="my-6" />
        <OrdersDataTable statusFilter="COMPLETED" />
      </main>
    </HydrateClient>
  );
};

export default CompletedOrders;
