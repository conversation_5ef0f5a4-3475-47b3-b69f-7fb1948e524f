import { Separator } from "@acme/ui/components/ui/separator";

import { HydrateClient, prefetch, trpc } from "~/trpc/server";
import OrdersDataTable from "../orders-data-table";

const PendingOrdersPage = () => {
  void prefetch(
    trpc.order.getAllOrders.queryOptions({
      status: "PENDING",
    }),
  );

  return (
    <HydrateClient>
      <main className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex flex-col gap-3">
            <h1 className="text-3xl font-bold tracking-tight">
              Pending Orders Management
            </h1>
            <p className="text-muted-foreground">
              Below orders in table are assigned to you but not yet completed.
              You can view the details of each order.
            </p>
          </div>
        </div>
        <Separator className="my-6" />
        <OrdersDataTable statusFilter="PENDING" />
      </main>
    </HydrateClient>
  );
};

export default PendingOrdersPage;
