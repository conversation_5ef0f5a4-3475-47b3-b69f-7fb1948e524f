"use client";

import type {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
} from "@tanstack/react-table";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { useMutation, useSuspenseQuery } from "@tanstack/react-query";
import {
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import {
  Calendar,
  ChevronDown,
  ExternalLink,
  MapPin,
  User,
} from "lucide-react";

import { Badge } from "@acme/ui/components/ui/badge";
import { Button } from "@acme/ui/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@acme/ui/components/ui/dropdown-menu";
import { Input } from "@acme/ui/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@acme/ui/components/ui/table";

import AlertWrapper from "~/components/shared/alert-wrapper";
import SortableColumnHeader from "~/components/shared/sortable-column-header";
import { useTRPC } from "~/trpc/react";

interface OrdersDataTableProps {
  statusFilter: "ALL" | "PENDING" | "COMPLETED";
}

const OrdersDataTable = ({ statusFilter }: OrdersDataTableProps) => {
  const trpc = useTRPC();
  const router = useRouter();
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState({});

  const { data: orders = [], isLoading } = useSuspenseQuery(
    trpc.order.getAllOrders.queryOptions({
      status: statusFilter,
    }),
  );

  const { mutate: assignOrder } = useMutation(
    trpc.order.assignOrderToScraphubEmployee.mutationOptions(),
  );

  const handleAssignOrder = (orderId: string) => {
    assignOrder(
      { orderId },
      {
        onSuccess: () => {
          router.push(`/orders/${orderId}`);
        },
        onError: (error) => {
          console.error("Failed to assign order:", error);
        },
      },
    );
  };

  const getStatusVariant = (status: string) => {
    switch (status) {
      case "PENDING":
        return "outline";
      case "COMPLETED":
        return "default";
      default:
        return "secondary";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "PENDING":
        return "text-yellow-700 bg-yellow-100 border-yellow-300";
      case "COMPLETED":
        return "text-teal-700 bg-teal-100 border-teal-300";
      default:
        return "text-black-700 bg-black-100 border-black-300";
    }
  };

  const columns: ColumnDef<(typeof orders)[number]>[] = [
    {
      accessorKey: "id",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Order ID" />
      ),
      cell: ({ row }) => (
        <div className="rounded-md border border-teal-200 bg-teal-50 px-2 py-1 font-mono text-sm font-semibold text-teal-700">
          #{row.getValue("id")}
        </div>
      ),
    },
    {
      accessorKey: "sellerName",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Seller Name" />
      ),
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <User className="size-4 text-black-500" />
          <span className="font-medium text-black-800">
            {row.original.seller.fullName}
          </span>
        </div>
      ),
    },
    {
      accessorKey: "kabadiwalaName",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Kabadiwala Name" />
      ),
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <User className="size-4 text-black-500" />
          {row.original.kabadiwala?.name ? (
            <span className="font-medium text-black-800">
              {row.original.kabadiwala.name}
            </span>
          ) : (
            <Badge variant="outline" className="bg-black-50 text-black-500">
              Not Assigned
            </Badge>
          )}
        </div>
      ),
    },
    {
      accessorKey: "status",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Status" />
      ),
      cell: ({ row }) => {
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        const status = row.original.status!;

        return (
          <Badge
            variant={getStatusVariant(status)}
            className={`${getStatusColor(status)} font-medium`}
          >
            {status}
          </Badge>
        );
      },
    },
    {
      accessorKey: "totalAmount",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Total Amount" />
      ),
      cell: ({ row }) => {
        const amount = parseFloat(row.original.totalAmount ?? String(0));
        const formatted = new Intl.NumberFormat("en-IN", {
          style: "currency",
          currency: "INR",
        }).format(amount);
        return (
          <div className="rounded-lg border border-teal-200 bg-teal-50 px-3 py-1 text-center text-lg font-bold text-teal-700">
            {formatted}
          </div>
        );
      },
    },
    {
      accessorKey: "addressDisplay",
      header: "Address",
      cell: ({ row }) => (
        <div className="flex max-w-xs items-start gap-2">
          <MapPin className="mt-0.5 size-4 flex-shrink-0 text-black-500" />
          <span className="line-clamp-2 text-sm text-black-600">
            {row.original.address.display}
          </span>
        </div>
      ),
    },
    {
      accessorKey: "createdAt",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Created At" />
      ),
      cell: ({ row }) => {
        const date = new Date(row.getValue("createdAt"));
        return (
          <div className="flex items-center gap-2">
            <Calendar className="size-4 text-black-500" />
            <span className="text-sm text-black-600">
              {date.toLocaleDateString()}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const openAlert = row.original.scraphubEmployeeId ? false : true;

        return openAlert ? (
          <AlertWrapper
            trigger={
              <Button
                variant="outline"
                size="sm"
                className="hover:border-teal-300 hover:bg-teal-50 hover:text-teal-700"
              >
                <ExternalLink className="size-4" />
                <span className="ml-2">View</span>
              </Button>
            }
            title="Are you sure you want to view this order?"
            description="Opening this order we will assign it to you."
            confirmText="Yes, open order"
            onConfirm={() => handleAssignOrder(row.original.id)}
            cancelText="No, cancel"
          />
        ) : (
          <Button
            variant="outline"
            size="sm"
            onClick={() => router.push(`/orders/${row.original.id}`)}
            className="hover:border-teal-300 hover:bg-teal-50 hover:text-teal-700"
          >
            <ExternalLink className="size-4" />
            <span className="ml-2">View</span>
          </Button>
        );
      },
    },
  ];

  const table = useReactTable({
    data: orders,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  return (
    <div className="w-full space-y-4">
      <div className="flex items-center justify-between rounded-lg border border-black-150 bg-white px-4 py-4">
        <div className="flex w-full items-center gap-4">
          <Input
            placeholder="Search by Order ID..."
            value={(table.getColumn("id")?.getFilterValue() ?? "") as string}
            onChange={(event) =>
              table.getColumn("id")?.setFilterValue(event.target.value)
            }
            className="max-w-sm border-black-200 focus:border-teal-300 focus:ring-teal-100"
            disabled={isLoading}
          />
          <Badge variant="outline" className="w-fit bg-black-50 text-black-600">
            {table.getFilteredRowModel().rows.length} orders
          </Badge>
        </div>
        <div className="flex gap-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                disabled={isLoading}
                className="border-black-200 hover:bg-black-50"
              >
                Columns <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              align="end"
              className="border-black-200 bg-white"
            >
              {table
                .getAllColumns()
                .filter((column) => column.getCanHide())
                .map((column) => {
                  return (
                    <DropdownMenuCheckboxItem
                      key={column.id}
                      className="capitalize hover:bg-teal-50"
                      checked={column.getIsVisible()}
                      onCheckedChange={(value) =>
                        column.toggleVisibility(!!value)
                      }
                    >
                      {column.id}
                    </DropdownMenuCheckboxItem>
                  );
                })}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      <div className="overflow-hidden rounded-lg border border-black-150 bg-white shadow-sm">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow
                key={headerGroup.id}
                className="border-b border-black-150 bg-black-50"
              >
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead
                      key={header.id}
                      className="py-4 font-semibold text-black-700"
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext(),
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody className="relative">
            {table.getRowModel().rows.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                  className="border-b border-black-100 transition-colors duration-200 hover:bg-black-50"
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id} className="py-4">
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 py-8 text-center text-black-500"
                >
                  <div className="flex flex-col items-center gap-2">
                    <div className="text-black-400">No orders found</div>
                    <div className="text-sm text-black-400">
                      Try adjusting your search criteria
                    </div>
                  </div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-between rounded-lg border border-black-150 bg-white px-4 py-4">
        <div className="flex-1 text-sm text-black-600">
          <Badge variant="outline" className="bg-black-50 text-black-600">
            {table.getFilteredSelectedRowModel().rows.length} of{" "}
            {table.getFilteredRowModel().rows.length} row(s) selected
          </Badge>
        </div>
        <div className="space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
            className="border-black-200 hover:bg-black-50"
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
            className="border-black-200 hover:bg-black-50"
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  );
};

export default OrdersDataTable;
