"use client";

import { useEffect, useState } from "react";
import Image from "next/image";
import {
  useMutation,
  useQueryClient,
  useSuspenseQuery,
} from "@tanstack/react-query";
import {
  Clock,
  CreditCard,
  DollarSign,
  Edit2,
  Info,
  Loader2,
  Mail,
  Package,
  Phone,
  User,
  X,
} from "lucide-react";
import { toast } from "sonner";

import { Badge } from "@acme/ui/components/ui/badge";
import { Button } from "@acme/ui/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@acme/ui/components/ui/card";
import { Separator } from "@acme/ui/components/ui/separator";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@acme/ui/components/ui/tooltip";

import { useTRPC } from "~/trpc/react";

interface OrderDetailsProps {
  orderId: string;
}

const getStatusColor = (status: string) => {
  switch (status) {
    case "CART":
      return "bg-black-200 text-black-700";
    case "ACTIVE":
      return "bg-yellow-200 text-yellow-800";
    case "PENDING":
      return "bg-teal-200 text-teal-800";
    case "COMPLETED":
      return "bg-green-100 text-green-800";
    case "CANCELLED":
      return "bg-red-100 text-red-800";
    default:
      return "bg-black-100 text-black-600";
  }
};

const getPaymentStatusColor = (status: string) => {
  switch (status) {
    case "PENDING":
      return "bg-yellow-200 text-yellow-800";
    case "COMPLETED":
      return "bg-green-100 text-green-800";
    case "FAILED":
      return "bg-red-100 text-red-800";
    default:
      return "bg-black-100 text-black-600";
  }
};

const OrderDetails = ({ orderId }: OrderDetailsProps) => {
  const trpc = useTRPC();
  const { data: orderDetails } = useSuspenseQuery(
    trpc.order.getOrderById.queryOptions({
      id: orderId,
    }),
  );

  // Add state for tracking input values and mismatches
  const [itemQuantities, setItemQuantities] = useState<Record<string, number>>(
    {},
  );
  const [itemMismatches, setItemMismatches] = useState<Record<string, boolean>>(
    {},
  );
  const [editingItems, setEditingItems] = useState<Record<string, boolean>>({});

  // Initialize quantities when order details load
  useEffect(() => {
    const initialQuantities: Record<string, number> = {};
    const initialMismatches: Record<string, boolean> = {};
    orderDetails.items.forEach((item) => {
      initialQuantities[item.id] = Number(
        item.quantityAtScraphub ?? item.quantity,
      );
      initialMismatches[item.id] = false; // Reset mismatches when data loads
    });
    setItemQuantities(initialQuantities);
    setItemMismatches(initialMismatches);
  }, [orderDetails.items]);

  const queryClient = useQueryClient();
  const { mutate: verifyOrderItem, isPending: isVerifyingOrderItem } =
    useMutation(
      trpc.order.verifyOrderItemAtScraphub.mutationOptions({
        onSuccess: async (opts) => {
          await queryClient.invalidateQueries(
            trpc.order.getOrderById.queryOptions({
              id: orderId,
            }),
          );
          // Reset mismatch state for all items after successful update
          setItemMismatches({});
          toast.success(opts.message);
        },
      }),
    );

  const { mutate: confirmPayment, isPending: isPendingPayment } = useMutation(
    trpc.order.confirmPayment.mutationOptions({
      onSuccess: async (opts) => {
        await queryClient.invalidateQueries(
          trpc.order.getOrderById.queryOptions({ id: orderId }),
        );
        toast.success(opts.message);
      },
      onError: (opts) => toast.error(opts.message),
    }),
  );

  const formatCurrency = (amount: string | null) => {
    if (!amount) return "₹0.00";
    return `₹${parseFloat(amount).toFixed(2)}`;
  };

  const formatDate = (date: Date | null) => {
    if (!date) return "Not set";
    return new Intl.DateTimeFormat("en-IN", {
      dateStyle: "medium",
      timeStyle: "short",
    }).format(new Date(date));
  };

  const handleQuantityChange = (
    itemId: string,
    value: number,
    originalQuantity: number | null,
  ) => {
    setItemQuantities((prev) => ({ ...prev, [itemId]: value }));
    setItemMismatches((prev) => ({
      ...prev,
      [itemId]: value !== Number(originalQuantity ?? 0),
    }));
  };

  const handleEditItem = (itemId: string) => {
    setEditingItems((prev) => ({ ...prev, [itemId]: true }));
  };

  const handleCancelEdit = (
    itemId: string,
    originalQuantity: string | null,
  ) => {
    setEditingItems((prev) => ({ ...prev, [itemId]: false }));
    setItemQuantities((prev) => ({
      ...prev,
      [itemId]: Number(originalQuantity ?? 0),
    }));
    setItemMismatches((prev) => ({ ...prev, [itemId]: false }));
  };

  const handleVerifyItem = (orderItemId: string) => {
    if (isVerifyingOrderItem) return;

    const quantity = itemQuantities[orderItemId] ?? 0;
    verifyOrderItem({
      orderId: orderDetails.id,
      orderItemId,
      quantityAtScraphub: quantity,
    });
    setEditingItems((prev) => ({ ...prev, [orderItemId]: false }));
  };

  const handleConfirmPayment = () => {
    if (isPendingPayment || !orderDetails.kabadiwalaId) return;

    confirmPayment({
      orderId: orderDetails.id,
      kabadiwalaId: orderDetails.kabadiwalaId,
    });
  };

  return (
    <div className="min-h-screen bg-black-0 p-4 md:p-6 lg:p-8">
      <div className="mx-auto max-w-7xl space-y-6">
        {/* Header */}
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div className="space-y-2">
            <h1 className="font-jakarta text-3xl font-bold text-black-900">
              Order Details
            </h1>
            <p className="font-inter text-black-500">
              Order ID: {orderDetails.id}
            </p>
          </div>
          <div className="flex flex-col gap-2 md:flex-row">
            <Badge className={getStatusColor(orderDetails.status ?? "")}>
              {orderDetails.status}
            </Badge>
            <Badge
              className={getPaymentStatusColor(
                orderDetails.paymentStatus ?? "",
              )}
            >
              Payment: {orderDetails.paymentStatus}
            </Badge>
          </div>
        </div>

        <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
          {/* Left Column - Main Details */}
          <div className="space-y-6 lg:col-span-2">
            {/* Customer Information */}
            <Card className="border-black-150 shadow-sm">
              <CardHeader className="border-b border-black-150 bg-teal-50">
                <CardTitle className="flex items-center gap-2 font-jakarta text-lg">
                  <User className="h-5 w-5 text-teal-700" />
                  Customer Information
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="flex flex-row flex-col items-start gap-4">
                  <div className="flex-shrink-0">
                    {orderDetails.seller.image ? (
                      <div className="shrink-0">
                        <div className="relative h-16 w-16 overflow-hidden rounded-full">
                          <Image
                            src={orderDetails.seller.image}
                            alt={orderDetails.seller.fullName}
                            fill
                            className="rounded-full border-2 border-teal-200 object-cover"
                          />
                        </div>
                      </div>
                    ) : (
                      <div className="flex h-16 w-16 items-center justify-center rounded-full bg-teal-100 text-teal-700">
                        {orderDetails.seller.fullName.charAt(0)}
                      </div>
                    )}
                  </div>
                  <div className="flex-1 space-y-2">
                    <h3 className="font-jakarta text-lg font-semibold text-black-900">
                      {orderDetails.seller.fullName}
                    </h3>
                    <div className="grid grid-cols-1 gap-2 md:grid-cols-2">
                      <div className="flex items-center gap-2 text-sm text-black-600">
                        <Mail className="h-4 w-4" />
                        {orderDetails.seller.email}
                        {orderDetails.seller.emailVerified && (
                          <Badge variant="outline" className="text-xs">
                            Verified
                          </Badge>
                        )}
                      </div>
                      <div className="flex items-center gap-2 text-sm text-black-600">
                        <Phone className="h-4 w-4" />
                        {orderDetails.seller.phoneNumber ?? "Not provided"}
                        {orderDetails.seller.phoneNumberVerified && (
                          <Badge variant="outline" className="text-xs">
                            Verified
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Kabadiwala Information */}
            {orderDetails.kabadiwala && (
              <Card className="border-black-150 shadow-sm">
                <CardHeader className="border-b border-black-150 bg-yellow-50">
                  <CardTitle className="flex items-center gap-2 font-jakarta text-lg">
                    <Package className="h-5 w-5 text-yellow-700" />
                    Assigned Kabadiwala
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-6">
                  <div className="flex flex-col gap-4 md:flex-row md:items-start">
                    <div className="flex-shrink-0">
                      {orderDetails.kabadiwala.image ? (
                        <Image
                          src={orderDetails.kabadiwala.image}
                          alt={orderDetails.kabadiwala.name}
                          width={64}
                          height={64}
                          className="rounded-full border-2 border-yellow-200"
                        />
                      ) : (
                        <div className="flex h-16 w-16 items-center justify-center rounded-full bg-yellow-100 text-yellow-700">
                          {orderDetails.kabadiwala.name.charAt(0)}
                        </div>
                      )}
                    </div>
                    <div className="flex-1 space-y-2">
                      <div className="flex items-center gap-2">
                        <h3 className="font-jakarta text-lg font-semibold text-black-900">
                          {orderDetails.kabadiwala.name}
                        </h3>
                        <Badge
                          variant={
                            orderDetails.kabadiwala.isOnDuty
                              ? "default"
                              : "secondary"
                          }
                        >
                          {orderDetails.kabadiwala.isOnDuty
                            ? "On Duty"
                            : "Off Duty"}
                        </Badge>
                      </div>
                      <div className="grid grid-cols-1 gap-2 md:grid-cols-2">
                        <div className="flex items-center gap-2 text-sm text-black-600">
                          <Mail className="h-4 w-4" />
                          {orderDetails.kabadiwala.email}
                        </div>
                        <div className="flex items-center gap-2 text-sm text-black-600">
                          <Phone className="h-4 w-4" />
                          {orderDetails.kabadiwala.phoneNumber ??
                            "Not provided"}
                        </div>
                        <div className="flex items-center gap-2 text-sm text-black-600">
                          <DollarSign className="h-4 w-4" />
                          Wallet:{" "}
                          {formatCurrency(
                            orderDetails.kabadiwala.walletBalance,
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Order Items */}
            <Card className="border-black-150 shadow-sm">
              <CardHeader className="border-b border-black-150 bg-yellow-50">
                <CardTitle className="flex items-center gap-2 font-jakarta text-lg">
                  <Package className="h-5 w-5 text-yellow-700" />
                  Order Items
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="space-y-4">
                  {orderDetails.items.map((item, index) => (
                    <div key={item.id}>
                      <div className="grid grid-cols-3 items-center gap-4">
                        {/* Left Column - Item Info */}
                        <div className="flex items-center gap-3">
                          <Image
                            src={item.category.image}
                            alt={item.category.name}
                            width={48}
                            height={48}
                            className="rounded-lg border border-black-150"
                          />
                          <div>
                            <h4 className="font-jakarta font-medium text-black-900">
                              {item.category.name}
                              {item.quantityAtScraphub ? (
                                <span className="ml-2 rounded border border-green-700 px-1 py-0.5 text-xs text-green-500">
                                  {" "}
                                  verified
                                </span>
                              ) : (
                                <span className="ml-2 rounded border border-red-700 px-1 py-0.5 text-xs text-red-500">
                                  {" "}
                                  unverified
                                </span>
                              )}
                            </h4>
                            <p className="text-sm text-black-600">
                              {formatCurrency(item.category.rate)}{" "}
                              {item.category.rateType
                                ?.toLowerCase()
                                .replaceAll("_", " ")}
                            </p>
                          </div>
                        </div>

                        {/* Center Column - Quantity */}
                        <div className="text-center">
                          <p className="font-jakarta font-semibold text-black-900">
                            Quantity: {item.quantity}
                            {item.category.rateType
                              ?.toLowerCase()
                              .replace("per_", " ")}
                          </p>
                          <p className="text-sm text-black-600">
                            Total:{" "}
                            {formatCurrency(
                              (
                                parseFloat(item.category.rate) *
                                parseFloat(item.quantity)
                              ).toString(),
                            )}
                          </p>
                        </div>

                        {/* Right Column - Input and Save */}
                        {orderDetails.scraphubPaymentAt ? null : (
                          <div className="flex items-center justify-end gap-2">
                            {!editingItems[item.id] ? (
                              // Display mode
                              <>
                                <span className="w-20 text-center text-sm font-medium">
                                  {itemQuantities[item.id] ?? 0}
                                </span>
                                <button
                                  onClick={() => handleEditItem(item.id)}
                                  className="rounded p-1 text-black-600 hover:bg-black-100 hover:text-black-800"
                                  title="Edit quantity"
                                >
                                  <Edit2 className="h-4 w-4" />
                                </button>
                              </>
                            ) : (
                              // Edit mode
                              <>
                                <input
                                  type="number"
                                  value={itemQuantities[item.id] ?? 0}
                                  onChange={(e) => {
                                    const value =
                                      parseFloat(e.target.value) || 0;
                                    handleQuantityChange(
                                      item.id,
                                      value,
                                      parseFloat(
                                        item.quantityAtScraphub ?? "0",
                                      ),
                                    );
                                  }}
                                  className="w-20 rounded border border-black-200 px-2 py-1 text-center text-sm"
                                  min="0"
                                  autoFocus
                                />
                                <button
                                  onClick={() => handleVerifyItem(item.id)}
                                  className="rounded bg-teal-600 px-3 py-1 text-sm text-white hover:bg-teal-700 disabled:opacity-50"
                                  disabled={isVerifyingOrderItem}
                                >
                                  Save
                                </button>
                                <button
                                  onClick={() =>
                                    handleCancelEdit(
                                      item.id,
                                      item.quantityAtScraphub,
                                    )
                                  }
                                  className="rounded bg-black-200 p-1 text-black-600 hover:bg-black-300"
                                  title="Cancel edit"
                                >
                                  <X className="h-4 w-4" />
                                </button>
                              </>
                            )}
                            {itemMismatches[item.id] && (
                              <Tooltip>
                                <TooltipTrigger>
                                  <div className="text-sm text-red-500">
                                    <Info className="size-3 text-red-500" />
                                  </div>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>
                                    Quantity mismatch with last saved quantity{" "}
                                    {item.quantityAtScraphub}
                                  </p>
                                </TooltipContent>
                              </Tooltip>
                            )}
                          </div>
                        )}
                      </div>
                      {index < orderDetails.items.length - 1 && (
                        <Separator className="mt-4" />
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Right Column - Financial & Status */}
          <div className="space-y-6">
            {/* Order Summary */}
            <Card className="border-black-150 shadow-sm">
              <CardHeader className="border-b border-black-150 bg-teal-50">
                <CardTitle className="flex items-center gap-2 font-jakarta text-lg">
                  <DollarSign className="h-5 w-5 text-teal-700" />
                  Order Summary
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3 p-6">
                <div className="flex justify-between">
                  <span className="text-black-600">Total Amount</span>
                  <span className="font-jakarta font-semibold">
                    {formatCurrency(orderDetails.totalAmount)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-black-600">Handling Charge</span>
                  <span>{formatCurrency(orderDetails.handlingCharge)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-black-600">Security Fee</span>
                  <span>{formatCurrency(orderDetails.securityFeeAmount)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-black-600">GST</span>
                  <span>{formatCurrency(orderDetails.gstAmount)}</span>
                </div>
                <Separator />
                <div className="flex justify-between font-jakarta text-sm font-bold">
                  <span>Value of items received at Scraphub</span>
                  <span className="text-teal-700">
                    {formatCurrency(
                      String(orderDetails.valueOfItemsReceivedAtScraphub),
                    )}
                  </span>
                </div>
                <Separator />
                <div className="flex justify-between font-jakarta text-sm">
                  <span>Fixed Earning</span>
                  <span className="text-teal-700">
                    {formatCurrency(
                      String(orderDetails.fixedKabadiwalaEarning),
                    )}
                  </span>
                </div>
                <div className="flex justify-between font-jakarta text-sm">
                  <span>Total Compensation</span>
                  <span className="text-teal-700">
                    {formatCurrency(
                      String(orderDetails.kabadiwalaTotalCompensation),
                    )}
                  </span>
                </div>
                <div className="flex justify-between font-jakarta text-sm">
                  <span>Security Fees</span>
                  <span className="text-teal-700">
                    {formatCurrency(String(orderDetails.securityFeeAmount))}
                  </span>
                </div>
                <div className="flex justify-between font-jakarta text-sm font-bold">
                  <span>Amount payable to kabadiwala</span>
                  <span className="text-teal-700">
                    {formatCurrency(
                      String(orderDetails.amountToBePaidToKabadiwala),
                    )}
                  </span>
                </div>

                {orderDetails.scraphubPaymentAt ? (
                  <div className="flex items-center justify-center gap-2 rounded-lg border border-green-200 bg-green-50 p-4">
                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-green-100">
                      <CreditCard className="h-4 w-4 text-green-600" />
                    </div>
                    <div className="text-center">
                      <p className="font-jakarta font-medium text-green-800">
                        Payment Confirmed
                      </p>
                      <p className="text-sm text-green-600">
                        {formatDate(orderDetails.scraphubPaymentAt)}
                      </p>
                    </div>
                  </div>
                ) : (
                  <Button
                    className="w-full lg:text-base"
                    onClick={handleConfirmPayment}
                    disabled={isPendingPayment}
                  >
                    {isPendingPayment ? (
                      <Loader2 className="mr-2 animate-spin" />
                    ) : (
                      <CreditCard className="mr-2" />
                    )}
                    Confirm Payment
                  </Button>
                )}
              </CardContent>
            </Card>

            {/* Timeline */}
            <Card className="border-black-150 shadow-sm">
              <CardHeader className="border-b border-black-150 bg-yellow-50">
                <CardTitle className="flex items-center gap-2 font-jakarta text-lg">
                  <Clock className="h-5 w-5 text-yellow-700" />
                  Timeline
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 p-6">
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <div className="h-3 w-3 rounded-full bg-teal-500"></div>
                    <div>
                      <p className="font-jakarta font-medium text-black-900">
                        Order Created
                      </p>
                      <p className="text-sm text-black-600">
                        {formatDate(orderDetails.createdAt)}
                      </p>
                    </div>
                  </div>
                  {orderDetails.updatedAt && (
                    <div className="flex items-center gap-3">
                      <div className="h-3 w-3 rounded-full bg-yellow-500"></div>
                      <div>
                        <p className="font-jakarta font-medium text-black-900">
                          Last Updated
                        </p>
                        <p className="text-sm text-black-600">
                          {formatDate(orderDetails.updatedAt)}
                        </p>
                      </div>
                    </div>
                  )}
                  {orderDetails.completedAt && (
                    <div className="flex items-center gap-3">
                      <div className="h-3 w-3 rounded-full bg-green-500"></div>
                      <div>
                        <p className="font-jakarta font-medium text-black-900">
                          Completed
                        </p>
                        <p className="text-sm text-black-600">
                          {formatDate(orderDetails.completedAt)}
                        </p>
                      </div>
                    </div>
                  )}
                  {orderDetails.paymentCompletedAt && (
                    <div className="flex items-center gap-3">
                      <div className="h-3 w-3 rounded-full bg-blue-500"></div>
                      <div>
                        <p className="font-jakarta font-medium text-black-900">
                          Payment Completed
                        </p>
                        <p className="text-sm text-black-600">
                          {formatDate(orderDetails.paymentCompletedAt)}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderDetails;
