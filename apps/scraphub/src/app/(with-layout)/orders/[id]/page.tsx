import { HydrateClient, prefetch, trpc } from "~/trpc/server";
import OrderDetails from "./order-details";

interface OrderDetailsPageProps {
  params: Promise<{ id: string }>;
}

const OrderDetailsPage = async ({ params }: OrderDetailsPageProps) => {
  const { id } = await params;

  void prefetch(trpc.order.getOrderById.queryOptions({ id }));

  return (
    <HydrateClient>
      <OrderDetails orderId={id} />
    </HydrateClient>
  );
};

export default OrderDetailsPage;
