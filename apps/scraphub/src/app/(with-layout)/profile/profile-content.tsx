"use client";

import { useState } from "react";
import {
  useMutation,
  useQueryClient,
  useSuspenseQuery,
} from "@tanstack/react-query";
import {
  Building2,
  Edit3,
  ExternalLink,
  MapPin,
  Phone,
  Save,
  User,
  X,
} from "lucide-react";
import { toast } from "sonner";

import { useTRPC } from "~/trpc/react";

const ProfileContent = () => {
  const trpc = useTRPC();
  const queryClient = useQueryClient();
  const [isEditing, setIsEditing] = useState(false);
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");

  const { data: profile, isError } = useSuspenseQuery(
    trpc.profile.get.queryOptions(),
  );

  const updateMutation = useMutation(
    trpc.profile.update.mutationOptions({
      onSuccess: async () => {
        await queryClient.invalidateQueries(trpc.profile.get.queryOptions());
        setIsEditing(false);
        setFirstName("");
        setLastName("");
      },
      onError: (error) => {
        toast.error(error.message);
      },
    }),
  );

  if (isError) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-teal-50 to-yellow-50">
        <div className="mx-4 w-full max-w-md rounded-2xl bg-white p-8 shadow-xl">
          <div className="text-center">
            <div className="mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-destructive/10">
              <X className="h-8 w-8 text-destructive" />
            </div>
            <h3 className="mb-2 font-jakarta text-lg font-semibold text-black-800">
              Error Loading Profile
            </h3>
            <p className="text-black-500">
              We couldn't load your profile information. Please try again.
            </p>
          </div>
        </div>
      </div>
    );
  }

  const handleEdit = () => {
    setFirstName(profile.firstName);
    setLastName(profile.lastName);
    setIsEditing(true);
  };

  const handleSave = () => {
    updateMutation.mutate({ firstName, lastName });
  };

  const handleCancel = () => {
    setIsEditing(false);
    setFirstName("");
    setLastName("");
  };

  const handleOpenMap = () => {
    if (profile.scraphub.address.coordinates) {
      const { latitude, longitude } = profile.scraphub.address.coordinates;
      const googleMapsUrl = `https://www.google.com/maps?q=${latitude},${longitude}`;
      window.open(googleMapsUrl, "_blank");
    } else {
      toast.error("Location coordinates not available");
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-teal-50 via-yellow-0 to-yellow-50 px-4 py-8">
      <div className="xl:max-w-8xl mx-auto max-w-7xl">
        {/* Header */}
        <div className="mb-8 text-center">
          <h1 className="mb-2 font-jakarta text-3xl font-bold text-black-800 md:text-4xl lg:text-5xl">
            Profile
          </h1>
          <p className="font-inter text-black-500 md:text-lg">
            Manage your account information and preferences
          </p>
        </div>

        {/* Main Profile Card */}
        <div className="overflow-hidden rounded-3xl bg-white shadow-xl">
          {/* Header Section */}
          <div className="relative overflow-hidden bg-gradient-to-r from-teal-600 to-teal-700 px-6 py-8 text-white md:px-8 md:py-12 lg:px-12 lg:py-16">
            <div className="absolute right-0 top-0 h-32 w-32 -translate-y-16 translate-x-16 rounded-full bg-teal-500/20 md:h-64 md:w-64 md:-translate-y-32 md:translate-x-32"></div>
            <div className="absolute bottom-0 left-0 h-24 w-24 -translate-x-12 translate-y-12 rounded-full bg-yellow-400/20 md:h-48 md:w-48 md:-translate-x-24 md:translate-y-24"></div>

            <div className="relative z-10 flex flex-col items-center space-y-6 md:flex-row md:items-center md:justify-between md:space-y-0">
              <div className="flex flex-col items-center space-y-4 md:flex-row md:items-center md:space-x-6 md:space-y-0">
                {/* Avatar */}
                <div className="flex h-20 w-20 items-center justify-center rounded-2xl bg-white/20 backdrop-blur-sm md:h-24 md:w-24 lg:h-28 lg:w-28">
                  <User className="h-10 w-10 text-white md:h-12 md:w-12 lg:h-14 lg:w-14" />
                </div>

                {/* Name Section */}
                <div className="text-center md:text-left">
                  {isEditing ? (
                    <div className="space-y-3">
                      <input
                        type="text"
                        value={firstName}
                        onChange={(e) => setFirstName(e.target.value)}
                        className="w-full rounded-xl border border-white/30 bg-white/20 px-4 py-2 font-jakarta text-xl font-semibold text-white placeholder-white/70 backdrop-blur-sm md:w-48 md:text-2xl"
                        placeholder="First Name"
                      />
                      <input
                        type="text"
                        value={lastName}
                        onChange={(e) => setLastName(e.target.value)}
                        className="w-full rounded-xl border border-white/30 bg-white/20 px-4 py-2 font-jakarta text-xl font-semibold text-white placeholder-white/70 backdrop-blur-sm md:w-48 md:text-2xl"
                        placeholder="Last Name"
                      />
                    </div>
                  ) : (
                    <>
                      <h2 className="mb-1 font-jakarta text-2xl font-bold md:text-3xl lg:text-4xl">
                        {profile.firstName} {profile.lastName}
                      </h2>
                      <p className="font-inter text-teal-100 md:text-lg">
                        {profile.email}
                      </p>
                    </>
                  )}
                </div>
              </div>

              {/* Edit Button */}
              <div className="flex items-center space-x-2">
                {isEditing ? (
                  <>
                    <button
                      onClick={handleSave}
                      disabled={updateMutation.isPending}
                      className="rounded-xl bg-yellow-500 p-3 text-black-800 transition-colors duration-200 hover:bg-yellow-600 disabled:opacity-50 lg:p-4"
                    >
                      <Save className="h-5 w-5 lg:h-6 lg:w-6" />
                    </button>
                    <button
                      onClick={handleCancel}
                      className="rounded-xl bg-white/20 p-3 text-white transition-colors duration-200 hover:bg-white/30 lg:p-4"
                    >
                      <X className="h-5 w-5 lg:h-6 lg:w-6" />
                    </button>
                  </>
                ) : (
                  <button
                    onClick={handleEdit}
                    className="rounded-xl bg-white/20 p-3 text-white backdrop-blur-sm transition-colors duration-200 hover:bg-white/30 lg:p-4"
                  >
                    <Edit3 className="h-5 w-5 lg:h-6 lg:w-6" />
                  </button>
                )}
              </div>
            </div>
          </div>

          {/* Content Section */}
          <div className="p-6 md:p-8 lg:p-12">
            <div className="grid gap-6 md:gap-8 lg:grid-cols-2 xl:gap-12">
              {/* Organization Info */}
              <div className="space-y-6">
                <h3 className="border-b border-black-100 pb-2 font-jakarta text-lg font-semibold text-black-800 md:text-xl lg:text-2xl">
                  Organization
                </h3>

                <div className="space-y-4 lg:space-y-6">
                  <div className="flex items-start space-x-4 rounded-2xl bg-teal-50 p-4 lg:p-6">
                    <div className="flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-xl bg-teal-600 lg:h-12 lg:w-12">
                      <Building2 className="h-5 w-5 text-white lg:h-6 lg:w-6" />
                    </div>
                    <div>
                      <p className="mb-1 font-jakarta font-medium text-black-700 lg:text-lg">
                        Company Name
                      </p>
                      <p className="font-inter text-black-600 lg:text-lg">
                        {profile.scraphub.name}
                      </p>
                    </div>
                  </div>

                  {profile.scraphub.phoneNumber && (
                    <div className="flex items-start space-x-4 rounded-2xl bg-yellow-50 p-4 lg:p-6">
                      <div className="flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-xl bg-yellow-500 lg:h-12 lg:w-12">
                        <Phone className="h-5 w-5 text-black-800 lg:h-6 lg:w-6" />
                      </div>
                      <div>
                        <p className="mb-1 font-jakarta font-medium text-black-700 lg:text-lg">
                          Phone Number
                        </p>
                        <p className="font-inter text-black-600 lg:text-lg">
                          {profile.scraphub.phoneNumber}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Address Info */}
              <div className="space-y-6">
                <h3 className="border-b border-black-100 pb-2 font-jakarta text-lg font-semibold text-black-800 md:text-xl lg:text-2xl">
                  Location
                </h3>

                <div className="rounded-2xl bg-gradient-to-br from-teal-50 to-yellow-50 p-6 lg:p-8">
                  <div className="flex items-start space-x-4">
                    <div className="mt-1 flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-xl bg-teal-600 lg:h-12 lg:w-12">
                      <MapPin className="h-5 w-5 text-white lg:h-6 lg:w-6" />
                    </div>
                    <div className="flex-1 space-y-3 lg:space-y-4">
                      <div className="flex flex-col space-y-3 md:flex-row md:items-center md:justify-between md:space-y-0">
                        <p className="font-jakarta font-medium text-black-700 lg:text-lg">
                          Address
                        </p>
                        {profile.scraphub.address.coordinates && (
                          <button
                            onClick={handleOpenMap}
                            className="flex items-center space-x-2 rounded-lg bg-teal-600 px-3 py-1.5 text-sm font-medium text-white transition-colors hover:bg-teal-700 md:px-4 md:py-2 lg:text-base"
                          >
                            <ExternalLink className="h-4 w-4 lg:h-5 lg:w-5" />
                            <span>View on Map</span>
                          </button>
                        )}
                      </div>
                      <div className="space-y-2 font-inter text-black-600 lg:space-y-3">
                        <p className="lg:text-lg">
                          {profile.scraphub.address.display}
                        </p>
                        {profile.scraphub.address.street && (
                          <p className="text-sm lg:text-base">
                            {profile.scraphub.address.street}
                          </p>
                        )}
                        <p className="text-sm lg:text-base">
                          {[
                            profile.scraphub.address.city,
                            profile.scraphub.address.state,
                            profile.scraphub.address.postalCode,
                          ]
                            .filter(Boolean)
                            .join(", ")}
                        </p>
                        {profile.scraphub.address.landmark && (
                          <p className="text-sm font-medium text-teal-700 lg:text-base">
                            Near: {profile.scraphub.address.landmark}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Account Status */}
            <div className="mt-6 border-t border-black-100 pt-6 md:mt-8 md:pt-8 lg:mt-12 lg:pt-12">
              <div className="flex flex-col space-y-4 md:flex-row md:items-center md:justify-between md:space-y-0">
                <div>
                  <h4 className="font-jakarta font-medium text-black-700 lg:text-lg">
                    Email Verification
                  </h4>
                  <p className="font-inter text-sm text-black-500 lg:text-base">
                    Your email verification status
                  </p>
                </div>
                <div className="flex items-center space-x-2 lg:space-x-3">
                  <div
                    className={`h-3 w-3 rounded-full lg:h-4 lg:w-4 ${
                      profile.emailVerified ? "bg-teal-500" : "bg-yellow-500"
                    }`}
                  ></div>
                  <span
                    className={`text-sm font-medium lg:text-base ${
                      profile.emailVerified
                        ? "text-teal-600"
                        : "text-yellow-600"
                    }`}
                  >
                    {profile.emailVerified ? "Verified" : "Pending"}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfileContent;
