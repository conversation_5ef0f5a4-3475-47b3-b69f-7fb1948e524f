import "@acme/ui/globals.css";

import type { Metadata, Viewport } from "next";
import { Poppin<PERSON> } from "next/font/google";

import SidebarProviderWrapper from "~/components/shared/sidebar-provider";
import { TRPCReactProvider } from "~/trpc/react";
import { HydrateClient } from "~/trpc/server";

export const metadata: Metadata = {
  title: "Scraphub",
  description: "Scraphub",
  openGraph: {
    title: "Scraphub",
    description: "Scraphub",
  },
  icons: [{ rel: "icon", url: "/favicon.png" }],
};

export const viewport: Viewport = {
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "white" },
    { media: "(prefers-color-scheme: dark)", color: "black" },
  ],
};

const poppins = Poppins({
  subsets: ["latin"],
  variable: "--font-poppins",
  display: "swap",
  weight: ["100", "200", "300", "400", "500", "600", "700", "800", "900"],
});

export default function RootLayout(props: { children: React.ReactNode }) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${poppins.className} min-h-screen text-foreground antialiased`}
      >
        <TRPCReactProvider>
          <SidebarProviderWrapper>
            <HydrateClient>{props.children}</HydrateClient>
          </SidebarProviderWrapper>
        </TRPCReactProvider>
      </body>
    </html>
  );
}
