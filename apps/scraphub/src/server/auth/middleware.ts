import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { createAuthClient } from "better-auth/client";

import { env } from "~/env";

export const client = createAuthClient({
  baseURL: env.NEXT_PUBLIC_SCRAPHUB_EMPLOYEE_BETTER_AUTH_URL,
});

const PRIVATE_ROUTES = ["/"];

export async function authMiddleware(request: NextRequest) {
  /**
   * This is an example of how you can use the client to get the session
   * from the request headers.
   *
   * You can then use this session to make decisions about the request
   */

  const { data: session } = await client.getSession({
    fetchOptions: {
      headers: request.headers,
    },
  });

  const isPrivateRoute = PRIVATE_ROUTES.includes(request.nextUrl.pathname);

  if (isPrivateRoute && !session) {
    return NextResponse.redirect(new URL("/auth/login", request.url));
  }

  return NextResponse.next();
}
