import { UTApi } from "uploadthing/server";

import { env } from "~/env";

export const utapi = new UTApi({
  token: env.UPLOADTHING_TOKEN,
});

export function generatePassword(length = 10): string {
  const lowercase = "abcdefghijklmnopqrstuvwxyz";
  const uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
  const numbers = "0123456789";
  const special = "@$!%*?&";
  const allChars = lowercase + uppercase + numbers + special;

  const getRandom = (chars: string) =>
    chars[Math.floor(Math.random() * chars.length)];

  // Guarantee one of each required type
  const pwd = [
    getRandom(lowercase),
    getRandom(uppercase),
    getRandom(numbers),
    getRandom(special),
  ];

  // Fill the rest
  for (let i = pwd.length; i < length; i++) {
    pwd.push(getRandom(allChars));
  }

  // Shuffle
  for (let i = pwd.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [pwd[i], pwd[j]] = [pwd[j], pwd[i]];
  }

  return pwd.join("");
}
