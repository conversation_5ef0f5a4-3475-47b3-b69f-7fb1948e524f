"use client";

import { useQueryState } from "nuqs";

/**
 * A custom hook to manage the state of a sheet (or modal) using URL query parameters.
 *
 * @param {string} queryParam - The query parameter key used to control the sheet's state (e.g., "bankId").
 * @returns {Object} - An object containing the sheet's state and control functions.
 * @property {boolean} isOpen - Indicates whether the sheet is open (true if the query parameter exists).
 * @property {string | null} paramValue - The value of the query parameter (null if the parameter doesn't exist).
 * @property {Function} openSheet - Opens the sheet and optionally sets a value for the query parameter.
 * @property {Function} closeSheet - Closes the sheet and removes the query parameter from the URL.
 */
export const useSheet = (queryParam: string) => {
  const [paramValue, setParamValue] = useQueryState(queryParam, {
    clearOnDefault: true,
  });
  const isOpen = paramValue !== null;
  const sanitizedParamValue =
    paramValue && paramValue.length > 0 ? paramValue : null;
  const openSheet = (value?: string | null) => {
    void setParamValue(value ?? "");
  };
  const closeSheet = () => {
    void setParamValue(null);
  };
  return { isOpen, paramValue: sanitizedParamValue, openSheet, closeSheet };
};
