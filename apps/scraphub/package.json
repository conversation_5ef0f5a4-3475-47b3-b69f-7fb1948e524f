{"name": "@acme/scraphub", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "pnpm with-env next build", "clean": "git clean -xdf .cache .next .turbo node_modules", "dev": "pnpm with-env next dev -p 3003 --turbopack", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "start": "pnpm with-env next start -p 3003", "typecheck": "tsc --noEmit", "with-env": "dotenv -e ../../.env --"}, "prettier": "@acme/prettier-config", "dependencies": {"@acme/db": "workspace:*", "@acme/mail": "workspace:*", "@acme/razorpay-sdk": "workspace:*", "@acme/ui": "workspace:*", "@acme/validators": "workspace:*", "@t3-oss/env-nextjs": "^0.13.0", "@tanstack/react-query": "catalog:", "@tanstack/react-table": "^8.21.3", "@trpc/client": "catalog:", "@trpc/server": "catalog:", "@uploadthing/react": "^7.3.0", "@vis.gl/react-google-maps": "^1.5.2", "bcryptjs": "3.0.2", "better-auth": "^1.2.10", "next": "^15.2.3", "nuqs": "^2.4.3", "react": "catalog:react19", "react-dom": "catalog:react19", "react-onesignal": "^3.2.2", "superjson": "2.2.2", "uploadthing": "^7.6.0", "zod": "catalog:"}, "devDependencies": {"@acme/eslint-config": "workspace:*", "@acme/prettier-config": "workspace:*", "@acme/tailwind-config": "workspace:*", "@acme/tsconfig": "workspace:*", "@types/node": "^22.14.1", "@types/react": "catalog:react19", "@types/react-dom": "catalog:react19", "dotenv-cli": "^8.0.0", "eslint": "catalog:", "jiti": "^1.21.7", "prettier": "catalog:", "tailwindcss": "catalog:", "typescript": "catalog:"}}