import { create } from "zustand";

import type { UserAddress } from "~/app/lib/types";

interface UserLocationState {
  userLocation: UserAddress | null;
  setUserLocation: (location: UserAddress | null) => void;
}

interface UserConfirmedLocationState {
  confirmedLocation: UserAddress | null;
  setConfirmedLocation: (location: UserAddress | null) => void;
}

export const useUserLocationStore = create<UserLocationState>()((set) => ({
  userLocation: null,
  setUserLocation: (location) => set({ userLocation: location }),
}));

export const useUserConfirmedLocationStore =
  create<UserConfirmedLocationState>()((set) => ({
    confirmedLocation: null,
    setConfirmedLocation: (location) => set({ confirmedLocation: location }),
  }));
