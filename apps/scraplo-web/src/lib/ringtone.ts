export class RingtonePlayer {
  private audioContext: AudioContext | null = null;
  private oscillator: OscillatorNode | null = null;
  private gainNode: GainNode | null = null;
  private isPlaying = false;

  constructor() {
    if (typeof window !== "undefined") {
      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
      this.audioContext = new (window.AudioContext ||
        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-explicit-any
        (window as any).webkitAudioContext)();
    }
  }

  play() {
    if (!this.audioContext || this.isPlaying) return;

    try {
      // Create oscillator for the ringtone
      this.oscillator = this.audioContext.createOscillator();
      this.gainNode = this.audioContext.createGain();

      // Connect nodes
      this.oscillator.connect(this.gainNode);
      this.gainNode.connect(this.audioContext.destination);

      // Set up the ringtone pattern (two-tone pattern)
      this.oscillator.type = "sine";
      this.oscillator.frequency.setValueAtTime(
        800,
        this.audioContext.currentTime,
      );
      this.oscillator.frequency.setValueAtTime(
        600,
        this.audioContext.currentTime + 0.2,
      );
      this.oscillator.frequency.setValueAtTime(
        800,
        this.audioContext.currentTime + 0.4,
      );
      this.oscillator.frequency.setValueAtTime(
        600,
        this.audioContext.currentTime + 0.6,
      );

      // Set volume
      this.gainNode.gain.setValueAtTime(0.3, this.audioContext.currentTime);

      // Start playing
      this.oscillator.start();
      this.isPlaying = true;

      // Create a repeating pattern
      this.createRepeatingPattern();
    } catch (error) {
      console.error("Failed to play ringtone:", error);
    }
  }

  private createRepeatingPattern() {
    if (!this.oscillator || !this.audioContext) return;

    const pattern = () => {
      if (!this.isPlaying || !this.oscillator || !this.audioContext) return;

      const now = this.audioContext.currentTime;

      // Create two-tone pattern
      this.oscillator.frequency.setValueAtTime(800, now);
      this.oscillator.frequency.setValueAtTime(600, now + 0.2);
      this.oscillator.frequency.setValueAtTime(800, now + 0.4);
      this.oscillator.frequency.setValueAtTime(600, now + 0.6);

      // Schedule next pattern
      setTimeout(pattern, 800);
    };

    setTimeout(pattern, 800);
  }

  stop() {
    if (!this.isPlaying) return;

    this.isPlaying = false;

    if (this.oscillator) {
      this.oscillator.stop();
      this.oscillator = null;
    }

    if (this.gainNode) {
      this.gainNode.disconnect();
      this.gainNode = null;
    }
  }

  destroy() {
    this.stop();
    if (this.audioContext) {
      this.audioContext.close().catch((err) => {
        console.error("Failed to close audio context:", err);
      });
      this.audioContext = null;
    }
  }
}

// Create a singleton instance
let ringtonePlayer: RingtonePlayer | null = null;

export const getRingtonePlayer = () => {
  ringtonePlayer ??= new RingtonePlayer();
  return ringtonePlayer;
};

export const playRingtone = () => {
  const player = getRingtonePlayer();
  player.play();
};

export const stopRingtone = () => {
  if (ringtonePlayer) {
    ringtonePlayer.stop();
  }
};
