import type { BetterAuthOptions } from "better-auth";
import { betterAuth } from "better-auth";
import { drizzleAdapter } from "better-auth/adapters/drizzle";
import { customSession, phoneNumber } from "better-auth/plugins";

import { eq } from "@acme/db";
import { db } from "@acme/db/client";
import * as schema from "@acme/db/schema";
import { sendSms } from "@acme/msg91";
import { tryCatch } from "@acme/validators/utils";

import {
  OTP_EXPIRATION_TIME_IN_SECONDS,
  OTP_LENGTH,
} from "~/app/lib/constants";
import { env } from "../../env";
import { streamClient } from "../api/utils";

export const config = {
  database: drizzleAdapter(db, {
    provider: "pg",
    schema: schema,
  }),
  user: {
    modelName: "seller",
    fields: {
      name: "fullName",
    },
  },
  session: {
    modelName: "sellerSession",
    fields: {
      userId: "sellerId",
    },
  },
  account: {
    modelName: "sellerAccount",
    fields: {
      userId: "sellerId",
    },
  },
  verification: {
    modelName: "sellerVerification",
  },
  secret: env.SELLER_BETTER_AUTH_SECRET,
  baseURL: env.NEXT_PUBLIC_SELLER_BETTER_AUTH_URL,
  plugins: [
    customSession(async ({ user, session }) => {
      const { data, err } = await tryCatch(
        db
          .select({
            phoneNumber: schema.seller.phoneNumber,
            isBlocked: schema.seller.isBlocked,
          })
          .from(schema.seller)
          .where(eq(schema.seller.id, user.id)),
      );

      const userData = data?.[0];

      if (err || !userData) {
        return Promise.reject(new Error("Failed to login user"));
      }

      if (userData.isBlocked) {
        return Promise.reject(
          new Error(
            "Your account is blocked, Please contact support to unblock your account.",
          ),
        );
      }

      const authToken = streamClient.generateUserToken({
        user_id: user.id,
        validity_in_seconds: 60 * 60 * 24, // 24 hours
      });

      return {
        session,
        user: {
          ...user,
          phoneNumber: userData.phoneNumber,
          streamToken: authToken,
        },
      };
    }),
    phoneNumber({
      sendOTP: async ({ phoneNumber, code }) => {
        const { err: sendOtpError } = await tryCatch(
          sendSms({
            receiversPhoneNumber: phoneNumber,
            otp: code,
          }),
        );

        if (sendOtpError) {
          return Promise.reject(sendOtpError);
        }

        return Promise.resolve();
      },

      signUpOnVerification: {
        // Actually the better-auth doesn't work without an email address so we need to provide a dummy email address
        // for the user. This is a workaround until we can use the email and password auth

        // NOTE: This is recommended by better-auth docs itself

        getTempEmail: (phoneNumber) => {
          return `${phoneNumber}@scraplo-dummy.com`;
        },
        getTempName: (phoneNumber) => {
          return phoneNumber;
        },
      },
      expiresIn: OTP_EXPIRATION_TIME_IN_SECONDS,
      otpLength: OTP_LENGTH,
    }),
  ],
  trustedOrigins: ["localhost"],
} satisfies BetterAuthOptions;

export const auth = betterAuth(config);
export type Session = typeof auth.$Infer.Session;
