import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { createAuthClient } from "better-auth/client";

import { env } from "~/env";

export const client = createAuthClient({
  baseURL: env.NEXT_PUBLIC_SELLER_BETTER_AUTH_URL,
});

const PRIVATE_ROUTES = [
  "/",
  "/auth/otp-verification/success",
  "/onboarding/STEP_1",
  "/onboarding/STEP_2",
  "/onboarding/success",
  "/account",
  "/cart",
  "/profile",
];

export async function authMiddleware(request: NextRequest) {
  /**
   * This is an example of how you can use the client to get the session
   * from the request headers.
   *
   * You can then use this session to make decisions about the request
   */

  const { data: session } = await client.getSession({
    fetchOptions: {
      headers: request.headers,
    },
  });

  const isPrivateRoute = PRIVATE_ROUTES.includes(request.nextUrl.pathname);

  if (isPrivateRoute && !session) {
    return NextResponse.redirect(new URL("/how-it-works", request.url));
  }

  return NextResponse.next();
}
