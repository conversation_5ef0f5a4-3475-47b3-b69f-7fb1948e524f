import { addressRouter } from "./router/address";
import { authRouter } from "./router/auth";
import { callRouter } from "./router/call";
import { categoryRouter } from "./router/category";
import { configRouter } from "./router/config";
import { conversationRouter } from "./router/conversation";
import { faqRouter } from "./router/faq";
import { issueRouter } from "./router/issue";
import { notificationRouter } from "./router/notification";
import { onboardingRouter } from "./router/onboarding";
import { orderRouter } from "./router/order";
import { paymentRouter } from "./router/payment";
import { userRouter } from "./router/user";
import { utilsRouter } from "./router/utils";
import { createTRPCRouter } from "./trpc";

export const appRouter = createTRPCRouter({
  auth: authRouter,
  user: userRouter,
  onboarding: onboardingRouter,
  category: categoryRouter,
  address: addressRouter,
  order: orderRouter,
  faq: faqRouter,
  payment: paymentRouter,
  conversation: conversationRouter,
  notification: notificationRouter,
  config: configRouter,
  streamCall: callRouter,
  issue: issueRouter,
  utils: utilsRouter,
});

// export type definition of API
export type AppRouter = typeof appRouter;
