import { StreamClient } from "@stream-io/node-sdk";
import <PERSON><PERSON>pay from "razorpay";

import { getOneSignalClient } from "@acme/onesignal";
import { OneSignalUserType } from "@acme/onesignal/src/types";
import { Razorpay as RazorpayClient } from "@acme/razorpay-sdk";

import { env } from "~/env";

export const razorpay = new Razorpay({
  key_id: env.RAZORPAY_KEY_ID,
  key_secret: env.RAZORPAY_SECRET_KEY,
});

export const razorpayClientPackage = new RazorpayClient({
  keyId: env.RAZORPAY_KEY_ID,
  keySecret: env.RAZORPAY_SECRET_KEY,
});

export const streamClient = new StreamClient(
  env.GETSTREAM_API_KEY,
  env.GETSTREAM_API_SECRET,
  {
    timeout: 10000, // 10 seconds timeout for all requests
  },
);

export const onesignalKabadiwalaClient = getOneSignalClient(
  OneSignalUserType.KABADIWALA,
);

export const onesignalSellerClient = getOneSignalClient(
  OneSignalUserType.SELLER,
);
export const onesignalAdminClient = getOneSignalClient(OneSignalUserType.ADMIN);
