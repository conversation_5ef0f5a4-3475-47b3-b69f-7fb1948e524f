import type { TRPCRouterRecord } from "@trpc/server";

import { and, eq, or } from "@acme/db";
import { order } from "@acme/db/schema";
import { tryCatch } from "@acme/validators/utils";

import { protectedProcedure } from "../trpc";

export const notificationRouter = {
  getOrderNotificationCount: protectedProcedure.query(async ({ ctx }) => {
    const { data: orderData, err: orderError } = await tryCatch(
      ctx.db.query.order.findMany({
        where: and(
          or(eq(order.status, "PENDING"), eq(order.status, "ACTIVE")),
          eq(order.sellerId, ctx.session.user.id),
        ),
        columns: {
          id: true,
        },
      }),
    );

    if (orderError) {
      return {
        warning: "Failed to fetch active order notification count",
        error: orderError.message,
      };
    }

    return {
      count: orderData.length,
    };
  }),

  getCartNotificationCount: protectedProcedure.query(async ({ ctx }) => {
    const { data: cartData, err: cartError } = await tryCatch(
      ctx.db.query.order.findFirst({
        where: and(
          eq(order.status, "CART"),
          eq(order.sellerId, ctx.session.user.id),
        ),
        columns: {
          id: true,
        },
        with: {
          items: {
            columns: {
              id: true,
            },
          },
        },
      }),
    );

    const count = cartData?.items.length;

    if (cartError) {
      return {
        warning: "Failed to fetch cart notification count",
        error: cartError.message,
      };
    }

    return {
      count: count ?? 0,
    };
  }),
} satisfies TRPCRouterRecord;
