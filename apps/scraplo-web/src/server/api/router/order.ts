import type { TRP<PERSON>outerRecord } from "@trpc/server";
import { TRPCError } from "@trpc/server";
import { z } from "zod";

import { generateOTP, getSystemConfig } from "@acme/core";
import { and, desc, eq, lt, not } from "@acme/db";
import {
  address,
  kabadiwala,
  notification,
  order,
  orderApprovalStatusEnum,
  orderItem,
  reviews,
  seller,
  vehicle,
} from "@acme/db/schema";
import { AddCartItemSchema, OrderFilterTabsSchema } from "@acme/validators";
import { sellerToKabadiwalaNotificationMessages } from "@acme/validators/notification";
import { tryCatch } from "@acme/validators/utils";

import type { DLAdvanceApiResult } from "~/app/lib/types";
import { protectedProcedure } from "../trpc";
import {
  onesignalKabadiwalaClient,
  onesignalSellerClient,
  razorpayClientPackage,
} from "../utils";

export const orderRouter = {
  getOrderById: protectedProcedure
    .input(z.object({ orderId: z.string() }))
    .query(async ({ ctx, input }) => {
      const { data: orderData, err: orderErr } = await tryCatch(
        ctx.db.query.order.findFirst({
          where: and(
            eq(order.id, input.orderId),
            eq(order.sellerId, ctx.session.user.id),
          ),
          with: {
            address: true,
            seller: {
              columns: {
                fullName: true,
              },
              with: {
                addresses: {
                  columns: {
                    localAddress: true,
                  },
                },
              },
            },
            items: {
              with: {
                category: {
                  columns: {
                    name: true,
                    rate: true,
                    rateType: true,
                    image: true,
                  },
                  with: {
                    parent: {
                      columns: {
                        name: true,
                      },
                    },
                  },
                },
              },
            },
            kabadiwala: {
              columns: {
                id: true,
                name: true,
                phoneNumber: true,
                liveLocationCoordinate: true,
                image: true,
                dlVerificationResponse: true,
              },

              with: {
                vehicles: {
                  where: eq(vehicle.isActive, true),
                  columns: {
                    id: true,
                    vehicleNumber: true,
                    vehicleType: true,
                  },
                  limit: 1,
                },
              },
            },
          },
        }),
      );

      if (orderErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch order",
        });
      }

      if (!orderData) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Order not found",
        });
      }
      const modifiedOrderData = {
        ...orderData,
        kabadiwala: {
          ...orderData.kabadiwala,
          dlVerificationResponse: orderData.kabadiwala?.dlVerificationResponse
            ? {
                user_full_name: (
                  orderData.kabadiwala
                    .dlVerificationResponse as DLAdvanceApiResult
                ).user_full_name,
                user_image: (
                  orderData.kabadiwala
                    .dlVerificationResponse as DLAdvanceApiResult
                ).user_image,
                dl_number: (
                  orderData.kabadiwala
                    .dlVerificationResponse as DLAdvanceApiResult
                ).dl_number,
              }
            : null,
        },
      };

      return {
        ...modifiedOrderData,

        amountAddedToWallet: 2000,
        amountPaidToCustomer: 1520.7,
        taxes: 20.5,
        serviceCharge: 50,
        earnings: 1400.2,
      };
    }),

  getOrderAmountBreakdown: protectedProcedure
    .input(z.object({ orderId: z.string() }))
    .query(async ({ ctx, input }) => {
      // Fetch order and items
      const { data: orderData, err: orderErr } = await tryCatch(
        ctx.db.query.order.findFirst({
          where: eq(order.id, input.orderId),
          with: {
            items: { with: { category: true } },
          },
        }),
      );

      if (orderErr || !orderData) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Order not found",
        });
      }
      // Calculate totalBeforeCharges
      const totalBeforeCharges = orderData.items.reduce((acc, item) => {
        return acc + Number(item.category.rate) * Number(item.quantity);
      }, 0);

      const minOrderValue = await getSystemConfig({
        key: "MINIMUM_ORDER_VALUE",
        defaultValue: "500",
      });
      const pickupCharge = await getSystemConfig({
        key: "PICKUP_CHARGE",
        defaultValue: "60",
      });
      const gstPercent = await getSystemConfig({
        key: "GST_PERCENTAGE",
        defaultValue: "18",
      });
      const isBelowMinOrder =
        Number(totalBeforeCharges) < Number(minOrderValue);
      // let gstAmount = 0;
      let totalCharges = 0;
      let payoutToCustomer = totalBeforeCharges;
      if (isBelowMinOrder) {
        // gstAmount =
        //   Math.round(
        //     ((Number(pickupCharge) * Number(gstPercent)) / 100) * 100,
        //   ) / 100;
        totalCharges = Number(pickupCharge); //+ Number(gstAmount);
        payoutToCustomer =
          Math.round((totalBeforeCharges - totalCharges) * 100) / 100;
      }

      return {
        totalBeforeCharges,
        minOrderValue,
        pickupCharge: isBelowMinOrder ? pickupCharge : 0,
        gstPercent: isBelowMinOrder ? gstPercent : 0,
        gstAmount: 0,
        totalCharges: isBelowMinOrder ? totalCharges : 0,
        payoutToCustomer,
        isBelowMinOrder,
      };
    }),

  getKabadiwalaReviewByOrderId: protectedProcedure
    .input(z.object({ orderId: z.string() }))
    .query(async ({ ctx, input }) => {
      const { data: kabadiwalaReview, err: reviewErr } = await tryCatch(
        ctx.db.query.reviews.findFirst({
          where: and(
            eq(reviews.orderId, input.orderId),
            eq(reviews.reviewFor, "KABADIWALA"),
            eq(reviews.sellerId, ctx.session.user.id),
          ),
        }),
      );

      if (reviewErr || !kabadiwalaReview) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch review",
        });
      }

      return kabadiwalaReview;
    }),

  getSellerReviewByKabadiwalaByOrderId: protectedProcedure
    .input(z.object({ orderId: z.string() }))
    .query(async ({ ctx, input }) => {
      const { data: sellerReview, err: reviewErr } = await tryCatch(
        ctx.db.query.reviews.findFirst({
          where: and(
            eq(reviews.orderId, input.orderId),
            eq(reviews.reviewFor, "SELLER"),
            eq(reviews.sellerId, ctx.session.user.id),
          ),
        }),
      );

      if (reviewErr || !sellerReview) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch review",
        });
      }

      return sellerReview;
    }),

  getAllCartItemsMap: protectedProcedure.query(async ({ ctx }) => {
    const { data: existingCart, err: existingCartErr } = await tryCatch(
      ctx.db.query.order.findFirst({
        where: and(
          eq(order.sellerId, ctx.session.user.id),
          eq(order.status, "CART"),
        ),
        with: {
          items: {
            with: {
              category: true,
            },
          },
        },
      }),
    );

    if (existingCartErr || !existingCart) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch cart",
      });
    }

    const cartItemsMap: Record<string, (typeof existingCart.items)[number]> =
      {};
    existingCart.items.forEach((item) => {
      cartItemsMap[item.categoryId] = item;
    });

    return cartItemsMap;
  }),

  getCartItemByCategoryId: protectedProcedure
    .input(z.object({ categoryId: z.string() }))
    .query(async ({ ctx, input }) => {
      const { data: existingCart, err: existingCartErr } = await tryCatch(
        ctx.db.query.order.findFirst({
          where: and(
            eq(order.sellerId, ctx.session.user.id),
            eq(order.status, "CART"),
          ),
          with: {
            items: {
              where: eq(orderItem.categoryId, input.categoryId),
            },
          },
        }),
      );

      if (existingCartErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch cart",
        });
      }

      const cartItem = existingCart?.items[0];

      if (!cartItem) {
        return null;
      }

      return cartItem;
    }),

  getCurrentCartStatistics: protectedProcedure.query(async ({ ctx }) => {
    // First find or create a cart for the user
    const { data: existingCart, err: cartErr } = await tryCatch(
      ctx.db
        .select()
        .from(order)
        .where(
          and(
            eq(order.sellerId, ctx.session.user.id),
            eq(order.status, "CART"),
          ),
        )
        .orderBy(desc(order.createdAt))
        .limit(1),
    );

    if (cartErr) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch cart",
      });
    }

    if (!existingCart.length || !existingCart[0]) {
      return {
        message: "Cart is empty",
        cart: null,
        numberOfItems: 0,
        estimatedPrice: 0,
      };
    }

    const currentCart = existingCart[0];
    // Get cart items with category details
    const { data: cartItems, err: itemsErr } = await tryCatch(
      ctx.db.query.orderItem.findMany({
        where: eq(orderItem.orderId, currentCart.id),
        with: {
          category: true,
        },
        orderBy: desc(orderItem.createdAt),
      }),
    );

    if (itemsErr) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch cart items",
      });
    }

    let estimatedPrice = 0;

    cartItems.forEach((item) => {
      const cartItemPrice = Number(item.category.rate) * Number(item.quantity);
      estimatedPrice += cartItemPrice;
    });

    return {
      estimatedPrice: estimatedPrice,
      numberOfItems: cartItems.length,
    };
  }),

  getCurrentCart: protectedProcedure.query(async ({ ctx }) => {
    // First find or create a cart for the user
    const { data: existingCart, err: cartErr } = await tryCatch(
      ctx.db.query.order.findFirst({
        where: and(
          eq(order.sellerId, ctx.session.user.id),
          eq(order.status, "CART"),
        ),
        orderBy: desc(order.createdAt),
      }),
    );

    if (cartErr) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch cart",
      });
    }

    if (!existingCart) {
      return {
        message: "Cart is empty",
        cart: null,
        items: [],
        estimatedPrice: 0,
        payoutMethod: null,
        breakdown: null,
      };
    }

    const currentCart = existingCart;

    // Get cart items with category details
    const { data: cartItems, err: itemsErr } = await tryCatch(
      ctx.db.query.orderItem.findMany({
        where: eq(orderItem.orderId, currentCart.id),
        with: {
          category: true,
        },
        orderBy: desc(orderItem.createdAt),
      }),
    );

    if (itemsErr) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch cart items",
      });
    }

    let estimatedPrice = 0;
    cartItems.forEach((item) => {
      const cartItemPrice = Number(item.category.rate) * Number(item.quantity);
      estimatedPrice += cartItemPrice;
    });

    const minOrderValue =
      (await getSystemConfig({ key: "MINIMUM_ORDER_VALUE" })) ?? 300;
    const pickupCharge =
      (await getSystemConfig({ key: "PICKUP_CHARGE" })) ?? 50;
    const gstPercent = (await getSystemConfig({ key: "GST_PERCENTAGE" })) ?? 18;
    const handlingCharge =
      (await getSystemConfig({ key: "HANDLING_CHARGE" })) ?? 0;

    const isBelowMinOrder = Number(estimatedPrice) < Number(minOrderValue);
    const gstAmount =
      Math.round(((Number(pickupCharge) * Number(gstPercent)) / 100) * 100) /
      100;
    let totalCharges = 0;
    let payoutToCustomer = estimatedPrice;
    if (isBelowMinOrder) {
      totalCharges =
        Number(pickupCharge) + Number(gstAmount) + Number(handlingCharge);
      payoutToCustomer =
        Math.round((Number(estimatedPrice) - totalCharges) * 100) / 100;
    }

    const breakdown = {
      totalBeforeCharges: estimatedPrice,
      minOrderValue,
      pickupCharge,
      gstPercent,
      gstAmount,
      totalCharges: isBelowMinOrder ? totalCharges : 0,
      payoutToCustomer,
      isBelowMinOrder,
      handlingCharge,
    };

    // Fetch payout method details from Razorpay if a fund account is selected
    let payoutMethod = null;
    if (currentCart.payoutMethodId) {
      const { data: fundAccountDetails, err: fundAccountErr } = await tryCatch(
        razorpayClientPackage.getFundAccount(currentCart.payoutMethodId),
      );

      if (!fundAccountErr && fundAccountDetails.data) {
        const fundAccount = fundAccountDetails.data;

        // Transform Razorpay fund account to our format
        if (fundAccount.account_type === "vpa") {
          payoutMethod = {
            id: fundAccount.id,
            type: "UPI" as const,
            upiId: fundAccount.vpa?.address ?? null,
            bankName: null,
            accountNumber: null,
            isDefault: true, // The selected one is considered default for the order
          };
        } else if (fundAccount.account_type === "bank_account") {
          payoutMethod = {
            id: fundAccount.id,
            type: "BANK_ACCOUNT" as const,
            upiId: null,
            bankName: fundAccount.bank_account?.name ?? null,
            accountNumber: fundAccount.bank_account?.account_number ?? null,
            isDefault: true, // The selected one is considered default for the order
          };
        }
      }
    }

    return {
      message: "Cart fetched successfully",
      cart: currentCart,
      items: cartItems,
      estimatedPrice: estimatedPrice,
      payoutMethod: payoutMethod,
      breakdown,
    };
  }),

  getAllOrders: protectedProcedure
    .input(
      OrderFilterTabsSchema.extend({
        limit: z.number().min(1).max(100).default(10),
        cursor: z.string().optional().nullable(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const filter = input.tab === "CART" ? "ALL" : input.tab;

      const whereConditions = [
        eq(order.sellerId, ctx.session.user.id),
        ...(filter === "ALL"
          ? [not(eq(order.status, "CART"))]
          : [eq(order.status, filter)]),
      ];

      if (input.cursor) {
        // Use <= instead of < to ensure we don't skip items
        whereConditions.push(
          lt(
            order.createdAt,
            (
              await ctx.db.query.order.findFirst({
                where: eq(order.id, input.cursor),
                columns: { createdAt: true },
              })
            )?.createdAt ?? new Date(),
          ),
        );
      }

      const { data: orders, err: ordersErr } = await tryCatch(
        ctx.db.query.order.findMany({
          where: and(...whereConditions),
          orderBy: desc(order.createdAt),
          limit: input.limit + 1,
          with: {
            address: true,
            items: {
              with: {
                category: true,
              },
            },
            kabadiwala: {
              columns: {
                id: true,
                liveLocationCoordinate: true,
              },
            },
          },
        }),
      );

      if (ordersErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch orders",
        });
      }

      const hasNextPage = orders.length > input.limit;
      const finalOrders = hasNextPage ? orders.slice(0, -1) : orders;
      const nextCursor = hasNextPage
        ? finalOrders[finalOrders.length - 1]?.id
        : null;

      return {
        orders: finalOrders,
        nextCursor,
        hasNextPage,
      };
    }),

  getEstimatedOrderTotalAmount: protectedProcedure
    .input(z.object({ orderId: z.string().nonempty() }))
    .query(async ({ ctx, input }) => {
      const { data: orderDetails, err: fetchOrderError } = await tryCatch(
        ctx.db.query.order.findFirst({
          where: eq(order.id, input.orderId),
          with: {
            items: {
              with: {
                category: true,
              },
            },
          },
        }),
      );

      if (fetchOrderError || !orderDetails) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch order details",
        });
      }

      const totalAmount = orderDetails.items.reduce((acc, item) => {
        const rate = item.category.rate;
        const quantity = parseFloat(item.quantity) || 0;
        return acc + Number(rate) * quantity;
      }, 0);

      return totalAmount;
    }),

  // Add an item to cart
  handleAddUpdateItemToCart: protectedProcedure
    .input(AddCartItemSchema)
    .mutation(async ({ ctx, input }) => {
      // fetch default address for the user
      const { data: addressData, err: addressErr } = await tryCatch(
        ctx.db
          .select({ id: address.id })
          .from(address)
          .where(eq(address.sellerId, ctx.session.user.id))
          .limit(1),
      );

      const addressId = addressData?.[0]?.id;

      if (addressErr || !addressId) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch address",
        });
      }

      // Find or create a cart for the user
      const { data: existingCart, err: cartErr } = await tryCatch(
        ctx.db
          .select()
          .from(order)
          .where(
            and(
              eq(order.sellerId, ctx.session.user.id),
              eq(order.status, "CART"),
            ),
          )
          .limit(1),
      );

      if (cartErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to check existing cart",
        });
      }

      let cartId: string | undefined; // ie: orderId

      // Create a new cart if it doesn't exist
      if (!existingCart.length) {
        const { data: newCart, err: createCartErr } = await tryCatch(
          ctx.db
            .insert(order)
            .values({
              sellerId: ctx.session.user.id,
              status: "CART",
              addressId: addressId,
            })
            .returning(),
        );

        if (createCartErr || !newCart.length) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: "Failed to create cart",
          });
        }

        cartId = newCart[0]?.id;
      } else {
        cartId = existingCart[0]?.id;

        // This condition is just to satisfy TypeScript, it should never be null
        if (!cartId) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: "There is some issue with the cart",
          });
        }

        // Check if item already exists in cart
        const { data: existingItems, err: existingItemErr } = await tryCatch(
          ctx.db
            .select()
            .from(orderItem)
            .where(
              and(
                eq(orderItem.orderId, cartId),
                eq(orderItem.categoryId, input.categoryId),
              ),
            )
            .limit(1),
        );

        if (existingItemErr) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: "Failed to check existing items",
          });
        }

        // If item already exists, update quantity instead of adding new item
        if (existingItems.length > 0) {
          const existingItem = existingItems[0];

          // This condition is just to satisfy TypeScript, it should never be null
          if (!existingItem) {
            throw new TRPCError({
              code: "INTERNAL_SERVER_ERROR",
              message: "There is some issue with the cart item",
            });
          }

          const { err: updateErr } = await tryCatch(
            ctx.db
              .update(orderItem)
              .set({
                quantity: String(input.quantity),
              })
              .where(eq(orderItem.id, existingItem.id)),
          );

          if (updateErr) {
            console.error("[updateErr]: ", updateErr);
            throw new TRPCError({
              code: "INTERNAL_SERVER_ERROR",
              message: "Failed to update cart item",
            });
          }

          return {
            message: "Item quantity updated in cart",
          };
        }
      }

      // If cartId is still undefined, throw an error
      // This condition is just to satisfy TypeScript, it should never be null
      if (!cartId) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "There is some issue with the cart",
        });
      }

      // Add the new item to cart
      const { err: addItemErr } = await tryCatch(
        ctx.db.insert(orderItem).values({
          orderId: cartId,
          categoryId: input.categoryId,
          quantity: String(input.quantity),
          rate: "0",
        }),
      );

      if (addItemErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to add item to cart",
        });
      }

      // Notify kabadiwala if assigned
      const { data: orderData } = await tryCatch(
        ctx.db.query.order.findFirst({
          where: eq(order.id, cartId),
          with: { kabadiwala: true },
        }),
      );
      if (orderData?.kabadiwalaId) {
        // Fetch category name
        const { data: categoryData } = await tryCatch(
          ctx.db.query.category.findFirst({
            where: eq(orderItem.categoryId, input.categoryId),
            columns: {
              compensationKabadiwalaRate: false,
              compensationRecyclerRate: false,
            },
          }),
        );
        const message =
          sellerToKabadiwalaNotificationMessages.itemAddedToOrderBySeller({
            orderId: cartId,
            categoryName: categoryData?.name ?? "an item",
          });
        await tryCatch(
          ctx.db.insert(notification).values({
            title: message.title,
            content: message.description,
            kabadiwalaId: orderData.kabadiwalaId,
          }),
        );
        await tryCatch(
          onesignalKabadiwalaClient.sendNotification({
            title: message.title,
            message: message.description,
            externalIds: [orderData.kabadiwalaId],
          }),
        );
      }
      return {
        message: "Item added to cart successfully",
      };
    }),

  placeQuickPickupOrder: protectedProcedure
    .input(z.object({ addressId: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const { data: sellerData, err: sellerError } = await tryCatch(
        ctx.db.query.seller.findFirst({
          where: eq(seller.id, ctx.session.user.id),
          columns: {
            phoneNumber: true,
          },
        }),
      );

      if (sellerError || !sellerData?.phoneNumber) {
        console.error("Seller phone number not found", sellerError);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch seller information",
        });
      }

      const pickupOtp = generateOTP(6);

      const { data: newOrderRes, err: quickPickupError } = await tryCatch(
        ctx.db
          .insert(order)
          .values({
            sellerId: ctx.session.user.id,
            status: "PENDING",
            pickupOtp: pickupOtp,
            addressId: input.addressId,
          })
          .returning(),
      );

      if (quickPickupError) {
        console.log("[quickPickupError]: ", quickPickupError);

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to place quick pickup order",
        });
      }

      const newOrder = newOrderRes[0];

      if (newOrder) {
        const message =
          sellerToKabadiwalaNotificationMessages.quickPickupOrderPlaced({
            orderId: newOrder.id,
          });
        await tryCatch(
          ctx.db.insert(notification).values({
            title: message.title,
            content: message.description,
            kabadiwalaId: newOrder.kabadiwalaId,
          }),
        );
        await tryCatch(
          onesignalSellerClient.sendNotification({
            title: message.title,
            message: message.description,
            externalIds: [ctx.session.user.id],
          }),
        );
      }

      return {
        message:
          "Quick pickup order placed successfully, and pickup otp sent to your phone",
      };
    }),

  placeNewOrder: protectedProcedure
    .input(z.object({ addressId: z.string().nonempty() }))
    .mutation(async ({ ctx, input }) => {
      const { data: sellerData, err: sellerError } = await tryCatch(
        ctx.db.query.seller.findFirst({
          where: eq(seller.id, ctx.session.user.id),
          columns: {
            phoneNumber: true,
          },
        }),
      );

      if (sellerError || !sellerData?.phoneNumber) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch seller information",
        });
      }

      const { data: existingCart, err: cartErr } = await tryCatch(
        ctx.db.query.order.findFirst({
          where: and(
            eq(order.sellerId, ctx.session.user.id),
            eq(order.status, "CART"),
          ),
          with: {
            items: {
              with: {
                category: true,
              },
            },
          },
        }),
      );

      if (cartErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch cart",
        });
      }

      if (!existingCart || existingCart.items.length === 0) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Cart is empty",
        });
      }

      // check weither address is present or not
      const { data: existingAddress, err: addressErr } = await tryCatch(
        ctx.db.query.address.findFirst({
          where: eq(address.id, input.addressId),
        }),
      );

      if (addressErr || !existingAddress) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Address not found",
        });
      }

      let totalPrice = 0;
      existingCart.items.map((item) => {
        totalPrice += Number(item.category.rate) * Number(item.quantity);
      });

      const pickupOtp = generateOTP(6);

      const { err: updateErr } = await tryCatch(
        ctx.db
          .update(order)
          .set({
            status: "PENDING",
            totalAmount: String(totalPrice),
            pickupOtp: pickupOtp,
            addressId: existingAddress.id,
          })
          .where(eq(order.id, existingCart.id)),
      );

      if (updateErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to place order",
        });
      }

      //   await tryCatch(
      //     sendSms({
      //       receiversPhoneNumber: sellerData.phoneNumber,
      //       otp: pickupOtp,
      //     }),
      //   );

      // notify the user about the order placement
      const message = sellerToKabadiwalaNotificationMessages.orderPlaced({
        orderId: existingCart.id,
      });

      await tryCatch(
        ctx.db.insert(notification).values({
          title: message.title,
          content: message.description,
          sellerId: ctx.session.user.id,
        }),
      );

      const { err: onesignalErr } = await tryCatch(
        onesignalSellerClient.sendNotification({
          title: message.title,
          message: message.description,
          externalIds: [ctx.session.user.id],
        }),
      );

      if (onesignalErr) {
        console.error("[onesignal]: ", onesignalErr);
      }

      return {
        message: "Order placed successfully, and pickup otp sent to your phone",
      };
    }),

  createKabadiwalaReview: protectedProcedure
    .input(
      z.object({
        orderId: z.string(),
        rating: z.number().min(1).max(5),
        review: z.string().max(500).optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // 1. Fetch order and check eligibility
      const { data: orderData, err: orderErr } = await tryCatch(
        ctx.db.query.order.findFirst({
          where: eq(order.id, input.orderId),
          with: {
            reviews: {
              where: and(
                eq(reviews.reviewFor, "KABADIWALA"),
                eq(reviews.sellerId, ctx.session.user.id),
              ),
            },
          },
        }),
      );

      if (orderErr || !orderData) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Order not found",
        });
      }

      if (!orderData.kabadiwalaId) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "This order is not assigned to any agent.",
        });
      }

      if (orderData.sellerId !== ctx.session.user.id) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You dont have permission to review this order",
        });
      }

      if (orderData.status !== "COMPLETED") {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "You can only review completed orders",
        });
      }

      const kabadiwalaReview = orderData.reviews.at(0);

      if (kabadiwalaReview) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "You have already reviewed this order",
        });
      }

      // 2. Insert review
      const { err: reviewErr } = await tryCatch(
        ctx.db.insert(reviews).values({
          kabadiwalaId: orderData.kabadiwalaId,
          sellerId: ctx.session.user.id,
          orderId: input.orderId,
          rating: String(input.rating),
          review: input.review,
          reviewFor: "KABADIWALA",
        }),
      );

      if (reviewErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create review",
        });
      }

      const { data: allReviews } = await tryCatch(
        ctx.db.query.reviews.findMany({
          where: eq(reviews.kabadiwalaId, orderData.kabadiwalaId),
          columns: { rating: true },
        }),
      );

      const ratings = allReviews?.map((r) => Number(r.rating)) ?? [];

      const avg =
        ratings.length > 0
          ? ratings.reduce((a, b) => a + b, 0) / ratings.length
          : input.rating;

      await tryCatch(
        ctx.db
          .update(kabadiwala)
          .set({ averageRating: String(avg) })
          .where(eq(kabadiwala.id, orderData.kabadiwalaId)),
      );

      return { message: "Review submitted successfully" };
    }),

  removeFromCart: protectedProcedure
    .input(z.object({ orderItemId: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // Find the cart item
      const { data: items, err: itemErr } = await tryCatch(
        ctx.db.query.orderItem.findMany({
          where: eq(orderItem.id, input.orderItemId),
          with: {
            order: true,
          },
        }),
      );

      if (itemErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch cart item",
        });
      }

      if (!items.length) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Cart item not found",
        });
      }

      const item = items[0];

      // Verify the cart belongs to the user
      if (item?.order.sellerId !== ctx.session.user.id) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You don't have permission to update this cart",
        });
      }

      // Remove the item
      const { err: deleteErr } = await tryCatch(
        ctx.db.delete(orderItem).where(eq(orderItem.id, input.orderItemId)),
      );

      if (deleteErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to remove item from cart",
        });
      }

      return {
        message: "Item removed from cart successfully",
      };
    }),

  updateOrderApproveStatus: protectedProcedure
    .input(
      z.object({
        orderId: z.string(),
        status: z.enum(orderApprovalStatusEnum.enumValues),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { data: orderData, err: fetchOrderError } = await tryCatch(
        ctx.db.query.order.findFirst({
          where: eq(order.id, input.orderId),
        }),
      );

      if (fetchOrderError) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch order details",
        });
      }

      if (!orderData) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Order not found",
        });
      }

      if (!orderData.kabadiwalaId) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "This order is not assigned to any agent.",
        });
      }

      if (orderData.status !== "ACTIVE") {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "This action is only be possible on active order",
        });
      }

      if (orderData.sellerId !== ctx.session.user.id) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You dont have permission for this action",
        });
      }

      const { err: updateErr } = await tryCatch(
        ctx.db
          .update(order)
          .set({
            orderApprovalStatus: input.status,
          })
          .where(eq(order.id, input.orderId)),
      );

      if (updateErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update order status",
        });
      }

      const message =
        sellerToKabadiwalaNotificationMessages.updateOrderApproveStatus({
          orderId: input.orderId,
          status: input.status,
        });

      await tryCatch(
        ctx.db.insert(notification).values({
          title: message.title,
          content: message.description,
          kabadiwalaId: orderData.kabadiwalaId,
        }),
      );

      const { err: onesignalError } = await tryCatch(
        onesignalKabadiwalaClient.sendNotification({
          title: message.title,
          message: message.description,
          externalIds: [orderData.kabadiwalaId],
        }),
      );

      if (onesignalError) {
        console.error("[onesignal]: ", onesignalError);
      }

      return {
        message: "Order status updated successfully",
      };
    }),

  selectPayoutMethod: protectedProcedure
    .input(z.object({ paymentMethodId: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // Get the current cart or create one
      const { data: existingCart, err: cartErr } = await tryCatch(
        ctx.db
          .select()
          .from(order)
          .where(
            and(
              eq(order.sellerId, ctx.session.user.id),
              eq(order.status, "CART"),
            ),
          )
          .orderBy(desc(order.createdAt))
          .limit(1),
      );

      if (cartErr) {
        console.error("Failed to fetch cart", cartErr);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update cart.",
        });
      }

      if (!existingCart.length || !existingCart[0]) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "No active cart found.",
        });
      }

      // Get user data to verify the fund account belongs to them
      const { data: user, err: userErr } = await tryCatch(
        ctx.db.query.seller.findFirst({
          where: eq(seller.id, ctx.session.user.id),
          columns: {
            razorpayContactId: true,
          },
        }),
      );

      if (userErr || !user?.razorpayContactId) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "User payment setup not found.",
        });
      } // Verify the fund account belongs to the user by checking with Razorpay
      const { data: fundAccountDetails, err: fundAccountErr } = await tryCatch(
        razorpayClientPackage.getFundAccount(input.paymentMethodId),
      );

      if (fundAccountErr || fundAccountDetails.error) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Fund account not found.",
        });
      }

      if (fundAccountDetails.data?.contact_id !== user.razorpayContactId) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "This fund account does not belong to you.",
        });
      }

      // Update the cart with the selected payout method (fund account ID)
      const { err: updateErr } = await tryCatch(
        ctx.db
          .update(order)
          .set({ payoutMethodId: input.paymentMethodId })
          .where(eq(order.id, existingCart[0].id)),
      );

      if (updateErr) {
        console.error("Failed to update payout method", updateErr);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to select payout method.",
        });
      }

      return {
        message: "Payout method selected successfully.",
      };
    }),
} satisfies TRPCRouterRecord;
