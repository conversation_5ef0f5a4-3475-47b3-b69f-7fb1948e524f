import type { TRPCRouterRecord } from "@trpc/server";
import { TRPCError } from "@trpc/server";

import { eq } from "@acme/db";
import { address, OnBoardingEnum, seller } from "@acme/db/schema";
import {
  OnboardingStepOneSchema,
  OnboardingStepTwoSchema,
} from "@acme/validators";
import { tryCatch } from "@acme/validators/utils";

import { protectedProcedure } from "../trpc";

export const onboardingRouter = {
  step1: protectedProcedure
    .input(OnboardingStepOneSchema)
    .mutation(async ({ ctx, input }) => {
      // check if user email is already registered
      if (input.email) {
        const { err, data } = await tryCatch(
          ctx.db
            .select({ id: seller.id })
            .from(seller)
            .where(eq(seller.email, input.email)),
        );

        if (err) {
          console.error("Failed to check email existence", err);
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: "Failed to check email existence.",
          });
        }

        if (data[0]) {
          throw new TRPCError({
            code: "CONFLICT",
            message: "<PERSON><PERSON> is already registered.",
          });
        }
      }

      const { err } = await tryCatch(
        ctx.db
          .update(seller)
          .set({
            fullName: input.fullName,
            email: input.email,
            onBoardingStep: OnBoardingEnum.enumValues[1],
          })
          .where(eq(seller.id, ctx.session.user.id)),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update user information.",
        });
      }

      return {
        message: "Onboarding step 1 completed.",
        nextStep: OnBoardingEnum.enumValues[1].toLowerCase(),
      };
    }),

  step2: protectedProcedure
    .input(OnboardingStepTwoSchema)
    .mutation(async ({ ctx, input }) => {
      // create a new default address
      const { data, err: createErr } = await tryCatch(
        ctx.db
          .insert(address)
          .values({
            ...input,
            sellerId: ctx.session.user.id,
            isDefault: true,
          })
          .returning(),
      );

      const addressId = data?.[0]?.id;

      if (createErr || !addressId) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create address.",
        });
      }

      // Update onboarding step
      const { err: updateErr } = await tryCatch(
        ctx.db
          .update(seller)
          .set({
            onBoardingStep: OnBoardingEnum.enumValues[2],
            onBoardingCompleted: true,
          })
          .where(eq(seller.id, ctx.session.user.id)),
      );

      if (updateErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update onboarding step.",
        });
      }

      return {
        message: "Onboarding step 2 completed.",
        // nextStep: OnBoardingEnum.enumValues[2].toLowerCase(),
      };
    }),

  step3: protectedProcedure.mutation(async ({ ctx }) => {
    const { err } = await tryCatch(
      ctx.db
        .update(seller)
        .set({
          onBoardingStep: OnBoardingEnum.enumValues[2],
          onBoardingCompleted: true,
        })
        .where(eq(seller.id, ctx.session.user.id)),
    );

    if (err) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to verify payment.",
      });
    }

    return {
      message: "Payment verified successfully.",
    };
  }),

  getOnboardingStep: protectedProcedure.query(async ({ ctx }) => {
    const { err, data } = await tryCatch(
      ctx.db
        .select({
          onBoardingStep: seller.onBoardingStep,
          onBoardingCompleted: seller.onBoardingCompleted,
        })
        .from(seller)
        .where(eq(seller.id, ctx.session.user.id)),
    );

    if (err) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch user information.",
      });
    }

    return {
      onBoardingStep: data.at(0)?.onBoardingStep,
      onBoardingCompleted: data.at(0)?.onBoardingCompleted,
    };
  }),
} satisfies TRPCRouterRecord;
