import type { TRPCRouterRecord } from "@trpc/server";
import { TRPCError } from "@trpc/server";
import { z } from "zod";

import { and, desc, eq } from "@acme/db";
import {
  address,
  deleteAccountRequest,
  seller,
  sellerVerification,
} from "@acme/db/schema";
import {
  DeleteAccountSchema,
  phoneNumberSchema,
  ProfileUpdateSchema,
} from "@acme/validators";
import { tryCatch } from "@acme/validators/utils";

import { protectedProcedure, publicProcedure } from "../trpc";

export const userRouter = {
  getDefaultLocation: protectedProcedure.query(async ({ ctx }) => {
    const { data, err } = await tryCatch(
      ctx.db
        .select()
        .from(address)
        .where(
          and(
            eq(address.sellerId, ctx.session.user.id),
            eq(address.isDefault, true),
          ),
        )
        .limit(1),
    );

    // This is confirmed that there is always a default location becoz user cannot skip onboarding steps and must provide a default address.
    const defaultLocation = data?.at(0);

    if (err || !defaultLocation) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch default location.",
      });
    }

    return defaultLocation;
  }),

  getLatestOtpExpirationTime: publicProcedure
    .input(z.object({ phoneNumber: phoneNumberSchema }))
    .query(async ({ ctx, input }) => {
      const { data, err } = await tryCatch(
        ctx.db
          .select({
            expiresAt: sellerVerification.expiresAt,
          })
          .from(sellerVerification)
          .where(eq(sellerVerification.identifier, input.phoneNumber))
          .limit(1)
          .orderBy(desc(sellerVerification.expiresAt)),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch OTP expiration time.",
        });
      }

      const otpExpiresAt = data.at(0)?.expiresAt;

      if (!otpExpiresAt) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "No OTP found for this phone number.",
        });
      }

      const currentDate = new Date().toISOString();
      const otpTimerExpired = currentDate > otpExpiresAt.toISOString();

      return {
        expiresAt: otpExpiresAt,
        otpExpired: otpTimerExpired,
      };
    }),

  getProfile: protectedProcedure.query(async ({ ctx }) => {
    const { data: profileData, err } = await tryCatch(
      ctx.db.query.seller.findFirst({
        where: eq(seller.id, ctx.session.user.id),
        columns: {
          id: true,
          fullName: true,
          email: true,
          phoneNumber: true,
          image: true,
          emailVerified: true,
          phoneNumberVerified: true,
          createdAt: true,
          updatedAt: true,
          averageRating: true,
        },
      }),
    );

    if (err) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch user profile.",
      });
    }

    if (!profileData) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "User profile not found.",
      });
    }

    return profileData;
  }),

  isAlreadyBankAccountLinked: protectedProcedure.query(async ({ ctx }) => {
    const { data: user, err: userFetchErr } = await tryCatch(
      ctx.db.query.seller.findFirst({
        where: eq(seller.id, ctx.session.user.id),
        columns: {
          razorpayFundAccountId: true,
        },
      }),
    );

    if (userFetchErr) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to check if bank account is linked.",
      });
    }

    return {
      isLinked: !!user?.razorpayFundAccountId,
    };
  }),

  updateProfile: protectedProcedure
    .input(ProfileUpdateSchema)
    .mutation(async ({ ctx, input }) => {
      const { err } = await tryCatch(
        ctx.db
          .update(seller)
          .set({
            fullName: input.fullName,
            email: input.email,
            phoneNumber: input.phoneNumber,
          })
          .where(eq(seller.id, ctx.session.user.id)),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update user profile.",
        });
      }

      return {
        message: "Profile details updated.",
      };
    }),

  isDeleteAccountRequestPending: protectedProcedure.query(async ({ ctx }) => {
    const { data, err } = await tryCatch(
      ctx.db.query.deleteAccountRequest.findFirst({
        where: eq(deleteAccountRequest.userId, ctx.session.user.id),
        columns: {
          id: true,
        },
      }),
    );

    if (err) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to check delete account request status.",
      });
    }

    return !!data;
  }),

  deleteAccountRequest: protectedProcedure
    .input(DeleteAccountSchema)
    .mutation(async ({ ctx, input }) => {
      if (!input.confirmPermanent) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message:
            "You must confirm that you understand this action is permanent.",
        });
      }

      const { err } = await tryCatch(
        ctx.db.insert(deleteAccountRequest).values({
          userId: ctx.session.user.id,
          reason: input.reason,
        }),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to request account deletion.",
        });
      }

      return {
        message: "Account deletion requested successfully.",
      };
    }),

  revokeDeleteAccountRequest: protectedProcedure.mutation(async ({ ctx }) => {
    const { err } = await tryCatch(
      ctx.db
        .delete(deleteAccountRequest)
        .where(eq(deleteAccountRequest.userId, ctx.session.user.id)),
    );

    if (err) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to revoke account deletion request.",
      });
    }

    return {
      message: "Account deletion request revoked successfully.",
    };
  }),
} satisfies TRPCRouterRecord;
