import type { TRPCRouterRecord } from "@trpc/server";
import { z } from "zod";

import { getSystemConfig } from "@acme/core";
import { systemConfigurationEnum } from "@acme/db/schema";

import { publicProcedure } from "../trpc";

export const configRouter = {
  getSystemConfig: publicProcedure
    .input(z.object({ key: z.enum(systemConfigurationEnum.enumValues) }))
    .query(async ({ input }) => {
      const config = await getSystemConfig({ key: input.key });

      return config;
    }),
} satisfies TRPCRouterRecord;
