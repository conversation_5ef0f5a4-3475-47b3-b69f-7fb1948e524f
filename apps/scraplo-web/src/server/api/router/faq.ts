import type { TRPCRouterRecord } from "@trpc/server";
import { TRPCError } from "@trpc/server";
import { z } from "zod";

import { and, asc, desc, eq, isNull } from "@acme/db";
import { question } from "@acme/db/schema";
import { tryCatch } from "@acme/validators/utils";

import { publicProcedure } from "../trpc";

export const faqRouter = {
  getAllQuestions: publicProcedure.query(async ({ ctx }) => {
    const { data, err } = await tryCatch(
      ctx.db.query.question.findMany({
        orderBy: [asc(question.order), desc(question.createdAt)],
        with: {
          answers: {
            limit: 1,
          },
        },
      }),
    );

    if (err) {
      console.error("Failed to fetch questions:", err);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch questions",
      });
    }

    return data;
  }),

  getTopLevelQuestions: publicProcedure.query(async ({ ctx }) => {
    const { data, err } = await tryCatch(
      ctx.db.query.question.findMany({
        where: and(
          isNull(question.parentId),
          eq(question.questionFor, "SELLER"),
        ),
        orderBy: [asc(question.order), desc(question.createdAt)],
        with: {
          answers: {
            limit: 1,
          },
        },
      }),
    );

    if (err) {
      console.error("Failed to fetch top-level questions:", err);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch top-level questions",
      });
    }

    return data;
  }),

  getQuestionById: publicProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const { data, err } = await tryCatch(
        ctx.db.query.question.findFirst({
          where: eq(question.id, input.id),
          with: {
            answers: {
              with: {
                question: true,
              },
            },
          },
          orderBy: asc(question.order),
        }),
      );

      if (err || !data) {
        console.error("Failed to fetch question:", err);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch question",
        });
      }

      return data;
    }),

  getQuestionsByParentId: publicProcedure
    .input(z.object({ parentId: z.string() }))
    .query(async ({ ctx, input }) => {
      const { data, err } = await tryCatch(
        ctx.db.query.question.findMany({
          where: eq(question.parentId, input.parentId),
          orderBy: [asc(question.order), desc(question.createdAt)],
          with: {
            answers: true,
          },
        }),
      );

      if (err) {
        console.error("Failed to fetch questions by parent ID:", err);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch questions by parent ID",
        });
      }

      return data;
    }),
} satisfies TRPCRouterRecord;
