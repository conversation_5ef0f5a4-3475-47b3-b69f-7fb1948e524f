import type { TRPCRouterRecord } from "@trpc/server";
import { TRPCError } from "@trpc/server";
import { z } from "zod";

import { eq } from "@acme/db";
import { order } from "@acme/db/schema";
import { tryCatch } from "@acme/validators/utils";

import { protectedProcedure } from "../trpc";
import { streamClient } from "../utils";

export const callRouter = {
  createCall: protectedProcedure
    .input(
      z.object({
        orderId: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const uniqueCallId = `${input.orderId}-${Date.now()}`.slice(0, 64);
      const call = streamClient.video.call("default", uniqueCallId);

      const { data: orderData, err: orderErr } = await tryCatch(
        ctx.db.query.order.findFirst({
          where: eq(order.id, input.orderId),
          columns: {
            sellerId: true,
            kabad<PERSON>walaId: true,
          },
        }),
      );

      if (orderErr || !orderData?.kabadiwalaId) {
        console.error("Order fetch error:", orderErr);
        throw new TRPCError({
          code: "NOT_FOUND",
          message: `Order with ID ${input.orderId} not found.`,
        });
      }

      const { err: usersStreamAccCreationErr } = await tryCatch(
        streamClient.upsertUsers([
          {
            id: orderData.sellerId,
          },
          {
            id: orderData.kabadiwalaId,
          },
        ]),
      );

      if (usersStreamAccCreationErr) {
        console.error("Users upsert error:", usersStreamAccCreationErr);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: `Failed to create the call.`,
        });
      }

      const { err: callErr } = await tryCatch(
        call.getOrCreate({
          data: {
            created_by: {
              id: orderData.sellerId,
              name: "Caller",
            },
            video: false,
            members: [
              { user_id: orderData.sellerId },
              { user_id: orderData.kabadiwalaId },
            ],
            settings_override: {
              video: {
                enabled: false,
                target_resolution: {
                  height: 360,
                  width: 640,
                  bitrate: 1000,
                },
                camera_default_on: false,
                access_request_enabled: false,
              },
              audio: {
                mic_default_on: true,
                default_device: "speaker",
              },
              thumbnails: {
                enabled: true,
              },
              limits: {
                max_participants: 2,
                max_duration_seconds: 3600,
              },
              ring: {
                incoming_call_timeout_ms: 30000,
                missed_call_timeout_ms: 60000,
                auto_cancel_timeout_ms: 60000,
              },
              session: {
                inactivity_timeout_seconds: 300,
              },
              screensharing: {
                enabled: false,
                access_request_enabled: false,
              },
            },
          },
        }),
      );

      if (callErr) {
        console.error("Call creation error:", callErr);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: `Failed to create the call.`,
        });
      }

      return {
        callId: call.id,
      };
    }),
} satisfies TRPCRouterRecord;
