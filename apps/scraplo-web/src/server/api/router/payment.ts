import type { TRPCRouterRecord } from "@trpc/server";
import { TRPCError } from "@trpc/server";
import { z } from "zod";

import { eq } from "@acme/db";
import { seller } from "@acme/db/schema";
import { OnboardingStepThreeSchema } from "@acme/validators";
import { tryCatch } from "@acme/validators/utils";

import { env } from "~/env";
import { protectedProcedure } from "../trpc";
import { razorpayClientPackage } from "../utils";

export const paymentRouter = {
  endToEndFundAccountFlow: protectedProcedure
    .input(OnboardingStepThreeSchema)
    .mutation(async ({ ctx, input }) => {
      const { data: userData, err: userErr } = await tryCatch(
        ctx.db.select().from(seller).where(eq(seller.id, ctx.session.user.id)),
      );

      const user = userData?.[0];

      if (userErr || !user) {
        console.error("Failed to fetch user data", userErr);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch user data.",
        });
      }

      const verifyFundAccount = env.ENABLE_RAZORPAY_FUND_ACCOUNT_VERIFICATION;

      if (verifyFundAccount === "yes") {
        if (
          user.razorpayContactId &&
          user.razorpayFundAccountId &&
          user.isPaymentVerified === false
        ) {
          // now, i need to validate the fund account by performing a 1 rupee payout
          const { data: validateData, err: validateErr } = await tryCatch(
            razorpayClientPackage.validateFundAccount({
              account_number: env.RAZORPAYX_ACCOUNT_NUMBER, // this will be our razorpayx account number
              fund_account: {
                id: user.razorpayFundAccountId,
              },
              amount: 100, // 1 rupee for validation
            }),
          );

          if (validateErr || validateData.error) {
            console.error(
              "Failed to validate Razorpay fund account",
              validateErr,
            );
            throw new TRPCError({
              code: "INTERNAL_SERVER_ERROR",
              message: "Failed to validate Razorpay fund account.",
            });
          }

          const validationId = validateData.data?.id;

          if (!validationId) {
            console.error(
              "Razorpay fund account validation did not return a valid ID",
            );
            throw new TRPCError({
              code: "INTERNAL_SERVER_ERROR",
              message:
                "Razorpay fund account validation did not return a valid ID.",
            });
          }

          const { err: userInfoUpdateErr } = await tryCatch(
            ctx.db
              .update(seller)
              .set({
                isPaymentVerified: true,
                razorpayFundValidationResponseId: validationId,
              })
              .where(eq(seller.id, ctx.session.user.id)),
          );

          if (userInfoUpdateErr) {
            console.error(
              "Failed to update user with Razorpay fund validation response ID",
              userInfoUpdateErr,
            );
            throw new TRPCError({
              code: "INTERNAL_SERVER_ERROR",
              message:
                "Failed to verify your payment details. Please try again later.",
            });
          }

          return {
            success: true,
          };
        }
      }

      if (
        user.razorpayContactId &&
        user.razorpayFundAccountId &&
        user.isPaymentVerified
      ) {
        console.log("Razorpay contact and fund account already exist.");
        return {
          success: true,
        };
      }

      const { data: razorpayData, err: razorpayErr } = await tryCatch(
        razorpayClientPackage.createContact({
          name: user.fullName,
          email: user.emailVerified ? user.email : undefined,
          contact: user.phoneNumber ?? undefined,
          type: "customer",
          reference_id: "ref_seller_" + user.phoneNumber,
          notes: {
            scraplo_seller_id: ctx.session.user.id,
            scraplo_seller_phone_number: user.phoneNumber,
          },
        }),
      );

      if (razorpayErr || razorpayData.error) {
        console.error("Failed to create Razorpay contact", razorpayErr);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create Razorpay contact.",
        });
      }

      const contactId = razorpayData.data?.id;

      if (!contactId) {
        console.error("Razorpay contact creation did not return a valid ID");
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Razorpay contact creation did not return a valid ID.",
        });
      }

      const { err: userContactUpdateErr } = await tryCatch(
        ctx.db
          .update(seller)
          .set({
            razorpayContactId: contactId,
          })
          .where(eq(seller.id, ctx.session.user.id)),
      );

      if (userContactUpdateErr) {
        console.error(
          "Failed to update user with Razorpay contact ID",
          userContactUpdateErr,
        );
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update user with Razorpay contact ID.",
        });
      }

      // At this point we have confirmed the contact was created successfully
      // now, we need to create a fund account for the user in Razorpay

      let accountDetail = {};
      if (input.upiId) {
        accountDetail = {
          vpa: {
            address: input.upiId,
          },
        };
      } else {
        accountDetail = {
          bank_account: {
            account_number: input.accountNumber,
            ifsc: input.ifscCode,
            name: input.bankName,
          },
        };
      }

      const { data: fundAccountData, error: fundAccountError } =
        await razorpayClientPackage.createFundAccount({
          contact_id: contactId,
          account_type: input.upiId ? "vpa" : "bank_account",
          ...accountDetail,
        });

      if (fundAccountError) {
        console.error(
          "Failed to create Razorpay fund account",
          fundAccountError,
        );
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create Razorpay fund account.",
        });
      }

      const fundAccountId = fundAccountData?.id;

      if (!fundAccountId) {
        console.error(
          "Razorpay fund account creation did not return a valid ID",
        );
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Razorpay fund account creation did not return a valid ID.",
        });
      }

      const { err: userFundAccountUpdateErr } = await tryCatch(
        ctx.db
          .update(seller)
          .set({
            razorpayFundAccountId: fundAccountId,
          })
          .where(eq(seller.id, ctx.session.user.id)),
      );

      if (userFundAccountUpdateErr) {
        console.error(
          "Failed to update user with Razorpay fund account ID",
          userFundAccountUpdateErr,
        );
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update user with Razorpay fund account ID.",
        });
      }

      if (verifyFundAccount === "yes") {
        // now, i need to validate the fund account by performing a 1 rupee payout
        const { data: validateData, err: validateErr } = await tryCatch(
          razorpayClientPackage.validateFundAccount({
            account_number: env.RAZORPAYX_ACCOUNT_NUMBER, // this will be our razorpayx account number
            fund_account: {
              id: fundAccountId,
            },
            amount: 100, // 1 rupee for validation
          }),
        );

        if (validateErr || validateData.error) {
          console.error(
            "Failed to validate Razorpay fund account",
            validateErr,
          );
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: "Failed to validate Razorpay fund account.",
          });
        }

        const validationId = validateData.data?.id;

        if (!validationId) {
          console.error(
            "Razorpay fund account validation did not return a valid ID",
          );
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message:
              "Razorpay fund account validation did not return a valid ID.",
          });
        }

        const { err: userInfoUpdateErr } = await tryCatch(
          ctx.db
            .update(seller)
            .set({
              isPaymentVerified: true,
              razorpayFundValidationResponseId: validationId,
            })
            .where(eq(seller.id, ctx.session.user.id)),
        );

        if (userInfoUpdateErr) {
          console.error(
            "Failed to update user with Razorpay fund validation response ID",
            userInfoUpdateErr,
          );
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message:
              "Failed to verify your payment details. Please try again later.",
          });
        }
      }

      return {
        success: true,
      };
    }),

  getAllFundAccounts: protectedProcedure.query(async ({ ctx }) => {
    const { data: user, err: userFetchErr } = await tryCatch(
      ctx.db.query.seller.findFirst({
        where: eq(seller.id, ctx.session.user.id),
        columns: {
          razorpayContactId: true,
          razorpayFundAccountId: true,
        },
      }),
    );

    if (userFetchErr) {
      console.error("Failed to fetch user data", userFetchErr);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch user data.",
      });
    }

    // If user doesn't have a Razorpay contact yet, return empty list
    if (!user?.razorpayContactId) {
      return {
        count: 0,
        fundAccounts: [],
        defaultFundAccountId: null,
      };
    }

    const { data: fundAccounts, err: fundAccountFetchErr } = await tryCatch(
      razorpayClientPackage.listFundAccounts(user.razorpayContactId),
    );

    if (fundAccounts?.error || fundAccountFetchErr) {
      console.error(
        "Failed to fetch Razorpay fund accounts",
        fundAccountFetchErr,
      );
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch Razorpay fund accounts.",
      });
    }

    const filteredFundAccounts = fundAccounts.data?.items.filter(
      (account) => account.active,
    );

    return {
      count: fundAccounts.data?.count ?? 0,
      fundAccounts: filteredFundAccounts ?? [],
      defaultFundAccountId: user.razorpayFundAccountId,
    };
  }),

  linkNewPaymentMethod: protectedProcedure
    .input(OnboardingStepThreeSchema)
    .mutation(async ({ ctx, input }) => {
      const { data: user, err: userFetchErr } = await tryCatch(
        ctx.db.query.seller.findFirst({
          where: eq(seller.id, ctx.session.user.id),
          columns: {
            razorpayContactId: true,
            fullName: true,
            email: true,
            emailVerified: true,
            phoneNumber: true,
          },
        }),
      );

      if (userFetchErr || !user) {
        console.error("Failed to fetch user data", userFetchErr);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch user data.",
        });
      }

      let contactId = user.razorpayContactId;

      // If user doesn't have a Razorpay contact, create one first
      if (!contactId) {
        const { data: razorpayData, err: razorpayErr } = await tryCatch(
          razorpayClientPackage.createContact({
            name: user.fullName,
            email: user.emailVerified ? user.email : undefined,
            contact: user.phoneNumber ?? undefined,
            type: "customer",
            reference_id: "ref_seller_" + user.phoneNumber,
            notes: {
              scraplo_seller_id: ctx.session.user.id,
              scraplo_seller_phone_number: user.phoneNumber,
            },
          }),
        );

        if (razorpayErr || razorpayData.error) {
          console.error("Failed to create Razorpay contact", razorpayErr);
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: "Failed to create Razorpay contact.",
          });
        }

        contactId = razorpayData.data?.id ?? null;

        if (!contactId) {
          console.error("Razorpay contact creation did not return a valid ID");
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: "Razorpay contact creation did not return a valid ID.",
          });
        }

        // Save the contact ID to the database
        const { err: userContactUpdateErr } = await tryCatch(
          ctx.db
            .update(seller)
            .set({
              razorpayContactId: contactId,
            })
            .where(eq(seller.id, ctx.session.user.id)),
        );

        if (userContactUpdateErr) {
          console.error(
            "Failed to update user with Razorpay contact ID",
            userContactUpdateErr,
          );
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: "Failed to update user with Razorpay contact ID.",
          });
        }
      }

      let accountDetail = {};
      if (input.upiId) {
        accountDetail = {
          vpa: {
            address: input.upiId,
          },
        };
      } else {
        accountDetail = {
          bank_account: {
            account_number: input.accountNumber,
            ifsc: input.ifscCode,
            name: input.bankName,
          },
        };
      }

      const { data: fundAccountData, error: fundAccountError } =
        await razorpayClientPackage.createFundAccount({
          contact_id: contactId,
          account_type: input.upiId ? "vpa" : "bank_account",
          ...accountDetail,
        });

      if (fundAccountError) {
        console.error(
          "Failed to create Razorpay fund account",
          fundAccountError,
        );
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create Razorpay fund account.",
        });
      }

      const fundAccountId = fundAccountData?.id;

      if (!fundAccountId) {
        console.error(
          "Razorpay fund account creation did not return a valid ID",
        );
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Razorpay fund account creation did not return a valid ID.",
        });
      }

      const { err: userFundAccountUpdateErr } = await tryCatch(
        ctx.db
          .update(seller)
          .set({
            razorpayFundAccountId: fundAccountId,
          })
          .where(eq(seller.id, ctx.session.user.id)),
      );

      if (userFundAccountUpdateErr) {
        console.error(
          "Failed to update user with Razorpay fund account ID",
          userFundAccountUpdateErr,
        );
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update user with Razorpay fund account ID.",
        });
      }

      if (env.ENABLE_RAZORPAY_FUND_ACCOUNT_VERIFICATION === "yes") {
        // now, i need to validate the fund account by performing a 1 rupee payout
        const { data: validateData, err: validateErr } = await tryCatch(
          razorpayClientPackage.validateFundAccount({
            account_number: env.RAZORPAYX_ACCOUNT_NUMBER, // this will be our razorpayx account number
            fund_account: {
              id: fundAccountId,
            },
            amount: 100, // 1 rupee for validation
          }),
        );

        if (validateErr || validateData.error) {
          console.error(
            "Failed to validate Razorpay fund account",
            validateErr,
          );
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: "Failed to validate Razorpay fund account.",
          });
        }

        const validationId = validateData.data?.id;

        if (!validationId) {
          console.error(
            "Razorpay fund account validation did not return a valid ID",
          );
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message:
              "Razorpay fund account validation did not return a valid ID.",
          });
        }

        const { err: userInfoUpdateErr } = await tryCatch(
          ctx.db
            .update(seller)
            .set({
              isPaymentVerified: true,
              razorpayFundValidationResponseId: validationId,
            })
            .where(eq(seller.id, ctx.session.user.id)),
        );

        if (userInfoUpdateErr) {
          console.error(
            "Failed to update user with Razorpay fund validation response ID",
            userInfoUpdateErr,
          );
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message:
              "Failed to verify your payment details. Please try again later.",
          });
        }
      }

      // Deactivate all other payment methods
      //   const { data, err } = await tryCatch(
      //     razorpayClientPackage.listFundAccounts(user.razorpayContactId),
      //   );

      //   if (err || data.error) {
      //     console.error("Failed to fetch Razorpay fund accounts", err);
      //     throw new TRPCError({
      //       code: "INTERNAL_SERVER_ERROR",
      //       message: "Failed to fetch Razorpay fund accounts.",
      //     });
      //   }

      //   if (!data.data?.items) {
      //     return {
      //       message: "Payment method linked successfully.",
      //     };
      //   }

      //   for (const fundAccount of data.data.items) {
      //     if (fundAccount.id !== fundAccountId) {
      //       await tryCatch(
      //         razorpayClientPackage.deactivateFundAccount(fundAccount.id),
      //       );
      //     }
      //   }

      return {
        message: "Payment method linked successfully. Set as default.",
      };
    }),

  updateDefaultPaymentMethod: protectedProcedure
    .input(
      z.object({
        fundAccountId: z
          .string()
          .nonempty({ message: "Fund account ID is required" }),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { data: user, err: userFetchErr } = await tryCatch(
        ctx.db.query.seller.findFirst({
          where: eq(seller.id, ctx.session.user.id),
          columns: {
            razorpayContactId: true,
            razorpayFundAccountId: true,
          },
        }),
      );

      if (userFetchErr || !user?.razorpayContactId) {
        console.error("Failed to fetch user data", userFetchErr);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch user data.",
        });
      }

      // i cannot trust user here. so i am gonna verify the fund account by fetching fund account details from Razorpay using the provided fund account and the returned value is exists under the razorpay contact id of the user
      // maybe this check is overkill here, but i think it's better to be safe than sorry

      const { data: fundAccountDetails, err: fundAccountDetailsErr } =
        await tryCatch(
          razorpayClientPackage.getFundAccount(input.fundAccountId),
        );

      if (fundAccountDetails?.error || fundAccountDetailsErr) {
        console.error(
          "Failed to fetch fund account details",
          fundAccountDetailsErr,
        );
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch fund account details.",
        });
      }

      if (fundAccountDetails.data?.contact_id !== user.razorpayContactId) {
        console.error("Fund account does not belong to the user");
        throw new TRPCError({
          code: "FORBIDDEN",
          message:
            "This fund account does not belong to you. Unauthorized action.",
        });
      }

      // if the payment method is in inactive state, we need to activate it
      const { data: fundAccountActivationData, err: fundAccountActivationErr } =
        await tryCatch(
          razorpayClientPackage.reactivateFundAccount(
            fundAccountDetails.data.id,
          ),
        );

      if (fundAccountActivationErr || fundAccountActivationData.error) {
        console.error(
          "Failed to reactivate Razorpay fund account",
          fundAccountActivationErr,
        );
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to reactivate Razorpay fund account.",
        });
      }

      // deactivate the previous default fund account if it exists
      //   if (user.razorpayFundAccountId) {
      //     const {
      //       data: fundAccountDeactivationData,
      //       err: fundAccountDeactivationErr,
      //     } = await tryCatch(
      //       razorpayClientPackage.deactivateFundAccount(
      //         user.razorpayFundAccountId,
      //       ),
      //     );

      //     if (fundAccountDeactivationErr || fundAccountDeactivationData.error) {
      //       console.error(
      //         "Failed to deactivate Razorpay fund account",
      //         fundAccountDeactivationErr,
      //       );
      //       throw new TRPCError({
      //         code: "INTERNAL_SERVER_ERROR",
      //         message: "Failed to deactivate Razorpay fund account.",
      //       });
      //     }
      //   }

      const { err: userInfoUpdateErr } = await tryCatch(
        ctx.db
          .update(seller)
          .set({
            razorpayFundAccountId: fundAccountDetails.data.id,
          })
          .where(eq(seller.id, ctx.session.user.id)),
      );

      if (userInfoUpdateErr) {
        console.error(
          "Failed to update user with Razorpay fund account ID",
          userInfoUpdateErr,
        );
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update user with Razorpay fund account ID.",
        });
      }

      return {
        message: "Default payment method updated successfully.",
      };
    }),

  deletePaymentMethod: protectedProcedure
    .input(
      z.object({
        fundAccountId: z
          .string()
          .nonempty({ message: "Fund account ID is required" }),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { data: user, err: userFetchErr } = await tryCatch(
        ctx.db.query.seller.findFirst({
          where: eq(seller.id, ctx.session.user.id),
          columns: {
            razorpayContactId: true,
            razorpayFundAccountId: true,
          },
        }),
      );

      if (userFetchErr || !user?.razorpayContactId) {
        console.error("Failed to fetch user data", userFetchErr);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch user data.",
        });
      }

      // check for the fund account id provided by the user is already default or not
      if (user.razorpayFundAccountId === input.fundAccountId) {
        console.error("Provided fund account ID is the default one");
        throw new TRPCError({
          code: "FORBIDDEN",
          message:
            "You cannot delete the default payment method. Please set another payment method as default before deleting this one.",
        });
      }

      // i cannot trust user here. so i am gonna verify the fund account by fetching fund account details from Razorpay using the provided fund account and the returned value is exists under the razorpay contact id of the user
      const { data: fundAccountDetails, err: fundAccountDetailsErr } =
        await tryCatch(
          razorpayClientPackage.getFundAccount(input.fundAccountId),
        );

      if (fundAccountDetails?.error || fundAccountDetailsErr) {
        console.error(
          "Failed to fetch fund account details",
          fundAccountDetailsErr,
        );
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch fund account details.",
        });
      }

      if (fundAccountDetails.data?.contact_id !== user.razorpayContactId) {
        console.error("Fund account does not belong to the user");
        throw new TRPCError({
          code: "FORBIDDEN",
          message:
            "This fund account does not belong to you. Unauthorized action.",
        });
      }

      // now i can proceed to deactivate the fund account

      const {
        data: fundAccountDeactivationData,
        err: fundAccountDeactivationErr,
      } = await tryCatch(
        razorpayClientPackage.deactivateFundAccount(fundAccountDetails.data.id),
      );

      if (fundAccountDeactivationErr || fundAccountDeactivationData.error) {
        console.error(
          "Failed to deactivate Razorpay fund account",
          fundAccountDeactivationErr,
        );
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to deactivate Razorpay fund account.",
        });
      }

      return {
        message: "Payment method deleted successfully.",
      };
    }),
} satisfies TRPCRouterRecord;
