import type { TRP<PERSON>outerRecord } from "@trpc/server";
import { TRPCError } from "@trpc/server";
import { z } from "zod";

import { and, desc, eq } from "@acme/db";
import { address } from "@acme/db/schema";
import { AddressSchema } from "@acme/validators";
import { tryCatch } from "@acme/validators/utils";

import { protectedProcedure } from "../trpc";

export const addressRouter = {
  getAddressById: protectedProcedure
    .input(z.object({ addressId: z.string() }))
    .query(async ({ ctx, input }) => {
      const { data, err } = await tryCatch(
        ctx.db
          .select()
          .from(address)
          .where(
            and(
              eq(address.sellerId, ctx.session.user.id),
              eq(address.id, input.addressId),
            ),
          ),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch address",
        });
      }

      if (!data[0]) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Address not found",
        });
      }

      return {
        message: "Address fetched successfully",
        address: data[0],
      };
    }),

  getAllSavedAddresses: protectedProcedure.query(async ({ ctx }) => {
    const { data, err } = await tryCatch(
      ctx.db
        .select()
        .from(address)
        .where(eq(address.sellerId, ctx.session.user.id))
        .orderBy(desc(address.isDefault)),
    );

    if (err) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch addresses",
      });
    }

    return {
      message: "Addresses fetched successfully",
      addresses: data,
    };
  }),

  addNewAddress: protectedProcedure
    .input(AddressSchema)
    .mutation(async ({ ctx, input }) => {
      const { err } = await tryCatch(
        ctx.db.insert(address).values({
          ...input,
          sellerId: ctx.session.user.id,
          name: input.addressType === "OTHER" ? input.name : input.addressType,
        }),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to add address",
        });
      }

      return {
        message: "Address added successfully",
      };
    }),

  updateAddress: protectedProcedure
    .input(
      AddressSchema.extend({
        addressId: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { data, err } = await tryCatch(
        ctx.db
          .select()
          .from(address)
          .where(
            and(
              eq(address.sellerId, ctx.session.user.id),
              eq(address.id, input.addressId),
            ),
          ),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch address",
        });
      }

      const addressToUpdate = data[0];

      if (!addressToUpdate) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Address not found",
        });
      }

      const { err: updateErr } = await tryCatch(
        ctx.db
          .update(address)
          .set({
            ...input,
            name:
              input.addressType === "OTHER" ? input.name : input.addressType,
          })
          .where(eq(address.id, addressToUpdate.id)),
      );

      if (updateErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update address",
        });
      }

      return {
        message: "Address updated successfully",
      };
    }),

  updateDefaultAddress: protectedProcedure
    .input(z.object({ addressId: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const { data, err: fetchErr } = await tryCatch(
        ctx.db
          .select()
          .from(address)
          .where(
            and(
              eq(address.sellerId, ctx.session.user.id),
              eq(address.isDefault, true),
            ),
          )
          .limit(1),
      );

      if (fetchErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update default address",
        });
      }

      const alreadyDefaultAddress = data[0]?.id;

      if (alreadyDefaultAddress) {
        const { err } = await tryCatch(
          ctx.db
            .update(address)
            .set({ isDefault: false })
            .where(eq(address.id, alreadyDefaultAddress)),
        );

        if (err) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: "Failed to update default address",
          });
        }
      }

      const { err } = await tryCatch(
        ctx.db
          .update(address)
          .set({ isDefault: true })
          .where(eq(address.id, input.addressId)),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update default address",
        });
      }

      return {
        message: "Default address updated",
      };
    }),

  deleteAddress: protectedProcedure
    .input(z.object({ addressId: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const { data, err: fetchErr } = await tryCatch(
        ctx.db
          .select()
          .from(address)
          .where(
            and(
              eq(address.sellerId, ctx.session.user.id),
              eq(address.id, input.addressId),
            ),
          ),
      );

      if (fetchErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to delete address",
        });
      }

      const addressToDelete = data[0];

      if (!addressToDelete) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Address not found",
        });
      }

      if (addressToDelete.isDefault) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Cannot delete default address",
        });
      }

      const { err } = await tryCatch(
        ctx.db.delete(address).where(and(eq(address.id, addressToDelete.id))),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to delete address",
        });
      }

      return {
        message: "Address deleted successfully",
      };
    }),
} satisfies TRPCRouterRecord;
