import { TRPCError } from "@trpc/server";
import { and, eq } from "drizzle-orm";
import { z } from "zod";

import { issue, kabadiwala, order } from "@acme/db/schema";
import { tryCatch } from "@acme/validators/utils";

import { protectedProcedure } from "../trpc";

export const CreateIssueSchema = z.object({
  orderId: z.string().min(1),
  description: z.string().min(1),
  imageUrls: z.array(z.string().url()).max(4),
});

export const issueRouter = {
  getSellerIssues: protectedProcedure.query(async ({ ctx }) => {
    const { data, err } = await tryCatch(
      ctx.db.query.issue.findMany({
        where: eq(issue.customerId, ctx.session.user.id),
        orderBy: (i, { desc }) => [desc(i.createdAt)],
      }),
    );
    if (err)
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: err.message,
      });
    return data;
  }),

  getSellerIssueById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const { data: found, err } = await tryCatch(
        ctx.db.query.issue.findFirst({
          where: and(
            eq(issue.id, input.id),
            eq(issue.customerId, ctx.session.user.id),
          ),
        }),
      );
      if (err)
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: err.message,
        });
      if (!found)
        throw new TRPCError({ code: "NOT_FOUND", message: "Issue not found" });
      return found;
    }),
  createIssue: protectedProcedure
    .input(CreateIssueSchema)
    .mutation(async ({ ctx, input }) => {
      const { data: existing, err: existingErr } = await tryCatch(
        ctx.db.query.issue.findFirst({
          where: and(
            eq(issue.orderId, input.orderId),
            eq(issue.customerId, ctx.session.user.id),
            eq(issue.status, "OPEN"),
          ),
        }),
      );
      if (existingErr)
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: existingErr.message,
        });
      if (existing) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "You already have an open issue for this order.",
        });
      }
      const { data: orderData, err: orderErr } = await tryCatch(
        ctx.db.query.order.findFirst({
          where: eq(order.id, input.orderId),
        }),
      );
      if (orderErr)
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: orderErr.message,
        });
      if (!orderData?.kabadiwalaId) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Order not found or not assigned to a kabadiwala.",
        });
      }
      // Fetch current issueCount for kabadiwala
      const { data: kabadiwalaData, err: kabadiwalaErr } = await tryCatch(
        ctx.db.query.kabadiwala.findFirst({
          where: eq(kabadiwala.id, orderData.kabadiwalaId),
          columns: { issueCount: true },
        }),
      );
      if (kabadiwalaErr)
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: kabadiwalaErr.message,
        });
      const prevIssueCount = Number(kabadiwalaData?.issueCount ?? "0");
      const newIssueCount = (prevIssueCount + 1).toString();
      const { err: updateErr } = await tryCatch(
        ctx.db
          .update(kabadiwala)
          .set({
            issueCount: newIssueCount,
          })
          .where(eq(kabadiwala.id, orderData.kabadiwalaId)),
      );
      if (updateErr)
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: updateErr.message,
        });
      const { data: created, err: createErr } = await tryCatch(
        ctx.db
          .insert(issue)
          .values({
            orderId: input.orderId,
            customerId: ctx.session.user.id,
            kabadiwalaId: orderData.kabadiwalaId,
            description: input.description,
            imageUrls: input.imageUrls,
          })
          .returning(),
      );
      if (createErr)
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: createErr.message,
        });
      return created[0];
    }),
};
