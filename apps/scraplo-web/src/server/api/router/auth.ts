import type { TRP<PERSON>outerRecord } from "@trpc/server";
import { TRPCError } from "@trpc/server";

import { desc, eq } from "@acme/db";
import { seller, sellerVerification } from "@acme/db/schema";
import { sendSms } from "@acme/msg91";
import { env as msg91Env } from "@acme/msg91/env";
import { SignUpSchema } from "@acme/validators";
import { KabadiwalaSignupSchema } from "@acme/validators/kabadiwala";
import { tryCatch } from "@acme/validators/utils";

import {
  OTP_EXPIRATION_TIME_IN_SECONDS,
  OTP_LENGTH,
} from "~/app/lib/constants";
import { protectedProcedure, publicProcedure } from "../trpc";

// Helper function to generate OTP
function generateOTP(length: number = OTP_LENGTH): string {
  const digits = "0123456789";
  let otp = "";
  for (let i = 0; i < length; i++) {
    otp += digits[Math.floor(Math.random() * 10)];
  }
  return otp;
}

export const authRouter = {
  protectedHello: protectedProcedure.query(() => {
    console.log("[AUTH_DEBUG] protectedHello called");
    return {
      message: "Hello from protected route",
    };
  }),
  requestOtp: publicProcedure
    .input(SignUpSchema)
    .mutation(async ({ ctx, input }) => {
      console.log(
        "[AUTH_DEBUG] requestOtp called with phoneNumber:",
        input.phoneNumber,
      );

      const { data: sellerRecord, err: sellerErr } = await tryCatch(
        ctx.db.query.seller.findFirst({
          where: eq(seller.phoneNumber, input.phoneNumber),
          columns: {
            isBlocked: true,
          },
        }),
      );

      if (sellerErr || !sellerRecord) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Seller not found",
        });
      }

      if (sellerRecord.isBlocked) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message:
            "Your account is blocked, Please contact support to unblock your account.",
        });
      }

      // Check if there's an existing verification record
      console.log("[AUTH_DEBUG] Checking for existing OTP verification record");
      const { data: existingVerification } = await tryCatch(
        ctx.db
          .select()
          .from(sellerVerification)
          .where(eq(sellerVerification.identifier, input.phoneNumber))
          .orderBy(desc(sellerVerification.createdAt))
          .limit(1),
      );
      const existingRecord = existingVerification?.[0];
      console.log(
        "[AUTH_DEBUG] Existing verification record found:",
        !!existingRecord,
      );

      // Check daily OTP attempt limit before proceeding
      if (existingRecord) {
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        const recordDate = existingRecord.createdAt
          ? new Date(existingRecord.createdAt)
          : new Date();
        recordDate.setHours(0, 0, 0, 0);
        const isToday = recordDate.getTime() === today.getTime();
        const currentAttemptCount = isToday
          ? parseInt(existingRecord.dailyOtpAttemptsCount || "0", 10) || 0
          : 0;

        // Get daily limit from environment
        const dailyLimit = msg91Env.DAILY_OTP_LIMIT;

        if (currentAttemptCount >= dailyLimit) {
          console.log(
            "[AUTH_DEBUG] Daily OTP limit exceeded:",
            currentAttemptCount,
            ">=",
            dailyLimit,
          );
          throw new TRPCError({
            code: "TOO_MANY_REQUESTS",
            message: `Daily OTP limit of ${dailyLimit} attempts exceeded. Please try again tomorrow.`,
          });
        }
      }

      // If there's an existing OTP that hasn't expired, reuse it without sending new SMS
      if (existingRecord?.expiresAt && existingRecord.expiresAt > new Date()) {
        console.log(
          "[AUTH_DEBUG] Using existing valid OTP, not sending new SMS",
        );
        return {
          success: true,
          message: "OTP already sent.",
          subMessage:
            "Please check your phone for the OTP. Use the previously sent OTP.",
        };
      }

      // Generate new OTP
      const newOtp =
        input.phoneNumber === "9354591238" ? "123456" : generateOTP(OTP_LENGTH);
      const now = new Date();
      const expiresAt = new Date(
        now.getTime() + OTP_EXPIRATION_TIME_IN_SECONDS * 1000,
      );

      console.log("[AUTH_DEBUG] Generated new OTP:", newOtp);

      // Calculate new attempt count
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const recordDate = existingRecord?.createdAt
        ? new Date(existingRecord.createdAt)
        : new Date();
      recordDate.setHours(0, 0, 0, 0);
      const isToday =
        existingRecord && recordDate.getTime() === today.getTime();
      const currentAttemptCount = isToday
        ? parseInt(existingRecord.dailyOtpAttemptsCount, 10) || 0
        : 0;

      try {
        // Update or insert verification record
        if (existingRecord) {
          console.log("[AUTH_DEBUG] Updating existing verification record");
          const { err: updateErr } = await tryCatch(
            ctx.db
              .update(sellerVerification)
              .set({
                value: `${newOtp}:0`, // Store OTP with retry count (otp:retryCount)
                expiresAt,
                updatedAt: now,
                dailyOtpAttemptsCount: (currentAttemptCount + 1).toString(),
              })
              .where(eq(sellerVerification.id, existingRecord.id)),
          );

          if (updateErr) {
            console.error(
              "[AUTH_DEBUG] Error updating verification record:",
              updateErr.message,
            );
            throw new TRPCError({
              code: "INTERNAL_SERVER_ERROR",
              message: "Failed to update OTP record",
            });
          }
        } else {
          console.log("[AUTH_DEBUG] Creating new verification record");
          const { err: insertErr } = await tryCatch(
            ctx.db.insert(sellerVerification).values({
              identifier: input.phoneNumber,
              value: `${newOtp}:0`, // Store OTP with retry count
              expiresAt,
              createdAt: now,
              updatedAt: now,
              dailyOtpAttemptsCount: "1",
            }),
          );

          if (insertErr) {
            console.error(
              "[AUTH_DEBUG] Error creating verification record:",
              insertErr.message,
            );
            throw new TRPCError({
              code: "INTERNAL_SERVER_ERROR",
              message: "Failed to create OTP record",
            });
          }
        }

        // Send SMS (skip for test number)
        if (input.phoneNumber !== "9354591238") {
          console.log("[AUTH_DEBUG] Sending SMS");
          const { data: smsData, err: smsErr } = await tryCatch(
            sendSms({ receiversPhoneNumber: input.phoneNumber, otp: newOtp }),
          );

          if (smsErr || !smsData) {
            console.error("[AUTH_DEBUG] Error sending SMS:", smsErr?.message);
            throw new TRPCError({
              code: "BAD_REQUEST",
              message: "Failed to send OTP via SMS",
            });
          }
        }

        console.log(
          `[AUTH_DEBUG] OTP sent successfully: ${input.phoneNumber}: ${newOtp}`,
        );
        return {
          success: true,
          message: "OTP sent successfully.",
          subMessage: "Please check your phone for the OTP.",
        };
      } catch (error) {
        console.error("[AUTH_DEBUG] Error in requestOtp:", error);
        throw error;
      }
    }),
  resendOtp: publicProcedure
    .input(KabadiwalaSignupSchema.pick({ phoneNumber: true }))
    .mutation(async ({ ctx, input }) => {
      console.log(
        "[AUTH_DEBUG] resendOtp called with phoneNumber:",
        input.phoneNumber,
      );

      const { phoneNumber } = input;

      console.log("[AUTH_DEBUG] Fetching existing verification record");
      const { data, err } = await tryCatch(
        ctx.db
          .select()
          .from(sellerVerification)
          .where(eq(sellerVerification.identifier, phoneNumber))
          .orderBy(desc(sellerVerification.createdAt))
          .limit(1),
      );

      const record = data?.[0];
      console.log("[AUTH_DEBUG] Verification record found:", !!record);

      if (err || !record) {
        console.error(
          "[AUTH_DEBUG] No OTP record found or database error:",
          err?.message,
        );
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "No OTP record found for this phone number.",
        });
      }

      // Parse the current retry count from the value field (format: "otp:retryCount")
      const [currentOtp, retryCountStr] = record.value.split(":");
      const currentRetryCount = parseInt(retryCountStr ?? "0", 10);
      const maxRetries = 3; // Set max retry limit

      console.log(
        "[AUTH_DEBUG] Current retry count:",
        currentRetryCount,
        "Max retries:",
        maxRetries,
      );

      // Check if max retries exceeded
      if (currentRetryCount >= maxRetries) {
        console.log("[AUTH_DEBUG] Max retry count exceeded");
        throw new TRPCError({
          code: "TOO_MANY_REQUESTS",
          message: "OTP retry count maxed out",
        });
      }

      // Check if OTP is still valid
      const now = new Date();
      const isOtpValid = record.expiresAt > now;
      if (isOtpValid) {
        console.log("[AUTH_DEBUG] OTP is still valid, resending same OTP");

        // Send SMS with the current OTP (skip for test number)
        if (phoneNumber !== "9354591238") {
          const { data: smsData, err: smsErr } = await tryCatch(
            sendSms({ receiversPhoneNumber: phoneNumber, otp: currentOtp }),
          );

          if (smsErr || !smsData) {
            console.error(
              "[AUTH_DEBUG] Error resending OTP via SMS:",
              smsErr?.message,
            );
            throw new TRPCError({
              code: "BAD_REQUEST",
              message: "Failed to resend OTP via SMS",
            });
          }
        }

        // Increment retry count and update database
        const newRetryCount = currentRetryCount + 1;
        const { err: updateErr } = await tryCatch(
          ctx.db
            .update(sellerVerification)
            .set({
              value: `${currentOtp}:${newRetryCount}`, // Update retry count
              updatedAt: now,
              // Keep the same expiry time since OTP is still valid
            })
            .where(eq(sellerVerification.id, record.id)),
        );

        if (updateErr) {
          console.error(
            "[AUTH_DEBUG] Error updating retry count:",
            updateErr.message,
          );
        } else {
          console.log(
            "[AUTH_DEBUG] Retry count updated successfully to:",
            newRetryCount,
          );
        }

        console.log(
          `[AUTH_DEBUG] OTP resent successfully: ${phoneNumber}: ${currentOtp}`,
        );
        return {
          success: true,
          message: "OTP resent successfully.",
          subMessage: "Please check your SMS.",
        };
      } else {
        console.log("[AUTH_DEBUG] OTP has expired, need to request new OTP");
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "OTP has expired. Please request a new OTP.",
        });
      }
    }),
} satisfies TRPCRouterRecord;
