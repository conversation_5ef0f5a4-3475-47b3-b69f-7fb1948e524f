import type { TRPCRouterRecord } from "@trpc/server";
import { TRPCError } from "@trpc/server";
import { z } from "zod";

import {
  checkOrderAccessPermission,
  checkOrderActiveStatus,
  checkSendMessagePermission,
  checkSupportConversationOwnership,
} from "@acme/core";
import { and, eq, isNull } from "@acme/db";
import {
  conversation,
  customerSupportConversation,
  customerSupportMessage,
  message,
  order,
} from "@acme/db/schema";
import { tryCatch } from "@acme/validators/utils";

import { protectedProcedure } from "../trpc";
import { onesignalAdminClient } from "../utils";

export const conversationRouter = {
  getConversationByOrderId: protectedProcedure
    .input(
      z.object({
        orderId: z.string().nonempty(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const { data: orderWithConversation, err: conversationErr } =
        await tryCatch(
          ctx.db.query.order.findFirst({
            where: eq(order.id, input.orderId),
            columns: {
              id: true,
              status: true,
              sellerId: true,
              kabadiwalaId: true,
            },
            with: {
              conversation: {
                with: {
                  messages: true,
                },
              },
              kabadiwala: {
                columns: {
                  id: true,
                  name: true,
                  image: true,
                },
              },
            },
          }),
        );

      if (conversationErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch conversation",
        });
      }

      if (!orderWithConversation?.kabadiwalaId) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Conversation not found",
        });
      }

      // check for appropriate permissions
      checkOrderAccessPermission(
        {
          kabadiwalaId: orderWithConversation.kabadiwalaId,
          sellerId: orderWithConversation.sellerId,
        },
        ctx.session.user.id,
      );

      // check for order must be in active state
      checkOrderActiveStatus(orderWithConversation.status);

      return {
        messages: orderWithConversation.conversation,
        kabadiwala: orderWithConversation.kabadiwala,
      };
    }),

  getSupportConversation: protectedProcedure
    .input(z.object({ orderId: z.string().optional() }))
    .query(async ({ ctx, input }) => {
      console.log("[getSupportConversation] called", {
        userId: ctx.session.user.id,
        orderId: input.orderId,
      });
      // Find latest conversation for this customer and orderId (or null for help/support)
      const whereClause = input.orderId
        ? and(
            eq(customerSupportConversation.customerId, ctx.session.user.id),
            eq(customerSupportConversation.orderId, input.orderId),
          )
        : and(
            eq(customerSupportConversation.customerId, ctx.session.user.id),
            isNull(customerSupportConversation.orderId),
          );

      const { data: convos, err: _err } = await tryCatch(
        ctx.db.query.customerSupportConversation.findMany({
          where: whereClause,
          with: {
            messages: true,
          },
          orderBy: (c, { desc }) => [desc(c.createdAt)],
        }),
      );

      // Backend safeguard: If orderId is provided, do not return any conversation with orderId null
      if (input.orderId && convos && convos.length > 0) {
        const filtered = convos.filter((c) => c.orderId === input.orderId);
        if (filtered.length === 0) {
          console.log(
            "[getSupportConversation] no valid order conversation found",
          );
          return null;
        }
        convos.length = 0;
        convos.push(...filtered);
      }

      const convo = convos && convos.length > 0 ? convos[0] : null;
      if (convo && convo.isOpen === false && convo.isRated === true) {
        console.log(
          "[getSupportConversation] returning null convo (closed & rated)",
        );
        return null;
      }
      if (convos) {
        console.log(
          "[getSupportConversation] found convos",
          convos.map((c) => ({
            id: c.id,
            isOpen: c.isOpen,
            isRated: c.isRated,
          })),
        );
      } else {
        console.log("[getSupportConversation] found convos: null");
      }
      // Do NOT auto-create a new conversation here
      if (convo) {
        console.log("[getSupportConversation] returning convo", {
          id: convo.id,
          isOpen: convo.isOpen,
          isRated: convo.isRated,
        });
      } else {
        console.log("[getSupportConversation] returning null convo");
      }
      const { data: supportAdmin } = await tryCatch(
        ctx.db.query.admin.findFirst({
          columns: { id: true, name: true, image: true },
        }),
      );
      return convo ? { ...convo, admin: supportAdmin } : null;
    }),

  sendNewMessage: protectedProcedure
    .input(
      z.object({
        orderId: z.string().nonempty(),
        content: z.string().trim().nonempty(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { data: orderWithConversation, err: orderErr } = await tryCatch(
        ctx.db.query.order.findFirst({
          where: eq(order.id, input.orderId),
          columns: {
            id: true,
            status: true,
            sellerId: true,
            kabadiwalaId: true,
          },
          with: {
            conversation: {
              with: {
                messages: true,
              },
            },
          },
        }),
      );

      if (orderErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch order",
        });
      }
      if (!orderWithConversation) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Order not found",
        });
      }

      // check for appropriate permissions
      checkSendMessagePermission(
        {
          kabadiwalaId: orderWithConversation.kabadiwalaId,
          sellerId: orderWithConversation.sellerId,
        },
        ctx.session.user.id,
      );

      // check for order must be in active state
      checkOrderActiveStatus(orderWithConversation.status);

      // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
      if (!orderWithConversation.conversation) {
        // If conversation does not exist, create it
        const { data: newConversation, err: createConvoErr } = await tryCatch(
          ctx.db
            .insert(conversation)
            .values({
              orderId: orderWithConversation.id,
            })
            .returning(),
        );
        // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
        if (createConvoErr || !newConversation?.[0]) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: "Failed to create conversation",
          });
        }
        orderWithConversation.conversation = {
          ...newConversation[0],
          messages: [],
        };
      }

      const { err: newMessageErr } = await tryCatch(
        ctx.db.insert(message).values({
          senderId: ctx.session.user.id,
          content: input.content,
          conversationId: orderWithConversation.conversation.id,
          isRead: false,
        }),
      );

      if (newMessageErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to send message",
        });
      }

      return {
        success: true,
      };
    }),

  sendSupportMessage: protectedProcedure
    .input(
      z.object({
        orderId: z.string().optional(),
        content: z.string().trim().nonempty(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // Find open conversation for this customer and orderId (or null for help/support)
      const whereClause = input.orderId
        ? and(
            eq(customerSupportConversation.customerId, ctx.session.user.id),
            eq(customerSupportConversation.orderId, input.orderId),
            eq(customerSupportConversation.isOpen, true),
          )
        : and(
            eq(customerSupportConversation.customerId, ctx.session.user.id),
            isNull(customerSupportConversation.orderId),
            eq(customerSupportConversation.isOpen, true),
          );
      let { data: convo } = await tryCatch(
        ctx.db.query.customerSupportConversation.findFirst({
          where: whereClause,
        }),
      );
      let isNewConversation = false;
      if (!convo) {
        // Create new conversation only when sending a message
        const [created] = await ctx.db
          .insert(customerSupportConversation)
          .values({
            customerId: ctx.session.user.id,
            orderId: input.orderId ?? null,
            isOpen: true,
            createdAt: new Date(),
            updatedAt: new Date(),
          })
          .returning();
        convo = created ? { ...created } : null;
        isNewConversation = true;
      }
      if (!convo?.isOpen)
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "This conversation is closed.",
        });
      // Insert message
      await ctx.db.insert(customerSupportMessage).values({
        conversationId: convo.id,
        senderId: ctx.session.user.id,
        senderType: "CUSTOMER",
        content: input.content,
        createdAt: new Date(),
        isRead: false,
      });
      // Fetch all admin IDs
      const { data: admins, err: adminErr } = await tryCatch(
        ctx.db.query.admin.findFirst({ columns: { id: true } }),
      );
      console.log("admin ids are ", JSON.stringify(admins, null, 2));

      if (!adminErr && admins?.id) {
        const { data, err } = await tryCatch(
          onesignalAdminClient.sendNotification({
            title: isNewConversation
              ? "New Support Ticket"
              : "New Support Message",
            message: isNewConversation
              ? "A new support ticket has been opened."
              : input.content,
            externalIds: [admins.id],
          }),
        );
        console.log(JSON.stringify(data));
        console.log(JSON.stringify(err));
      }
      return { success: true };
    }),

  rateSupportConversation: protectedProcedure
    .input(
      z.object({
        conversationId: z.string().nonempty(),
        rating: z.number().min(1).max(5).nullable(),
        reviewText: z.string().max(500).optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { data: convo, err } = await tryCatch(
        ctx.db.query.customerSupportConversation.findFirst({
          where: eq(customerSupportConversation.id, input.conversationId),
        }),
      );
      if (err || !convo) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Conversation not found",
        });
      }
      if (convo.isOpen) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Conversation must be closed before rating.",
        });
      }
      if (convo.isRated) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Conversation already rated.",
        });
      }
      // Update rating, reviewText, and isRated
      await ctx.db
        .update(customerSupportConversation)
        .set({
          rating: input.rating?.toString() ?? null,
          reviewText: input.reviewText ?? null,
          isRated: true,
        })
        .where(eq(customerSupportConversation.id, input.conversationId));
      return { success: true };
    }),

  // Allow customer to close their own support conversation
  closeSupportConversation: protectedProcedure
    .input(z.object({ conversationId: z.string().nonempty() }))
    .mutation(async ({ ctx, input }) => {
      const { data: convo, err } = await tryCatch(
        ctx.db.query.customerSupportConversation.findFirst({
          where: eq(customerSupportConversation.id, input.conversationId),
        }),
      );
      if (err || !convo) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Conversation not found",
        });
      }

      checkSupportConversationOwnership(convo, ctx.session.user.id);

      if (!convo.isOpen) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Conversation is already closed.",
        });
      }
      await ctx.db
        .update(customerSupportConversation)
        .set({
          isOpen: false,
          closedAt: new Date(),
          closedBy: ctx.session.user.id,
        })
        .where(eq(customerSupportConversation.id, convo.id));
      return { success: true };
    }),
} satisfies TRPCRouterRecord;
