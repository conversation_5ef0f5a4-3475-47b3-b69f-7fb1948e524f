"use client";

import { StreamCall, useCalls } from "@stream-io/video-react-sdk";

import { IncomingCallControls } from "./incoming-call";

const IncomingCalls = () => {
  const calls = useCalls();
  if (!calls.length) return null;
  return (
    <>
      {calls.map((call) => (
        <StreamCall call={call} key={call.id}>
          <IncomingCallControls />
        </StreamCall>
      ))}
    </>
  );
};

export default IncomingCalls;
