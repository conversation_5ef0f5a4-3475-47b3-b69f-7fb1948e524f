import {
  CallingState,
  StreamCall,
  useCall,
  useCalls,
  useCallStateHooks,
} from "@stream-io/video-react-sdk";

import { CustomRingingCall } from "./custom-ringing-call";

export const Video = () => {
  const calls = useCalls();
  return (
    <>
      {calls.map((call) => (
        <StreamCall call={call} key={call.cid}>
          <CallPanel />
        </StreamCall>
      ))}
    </>
  );
};

// custom component that renders ringing as well as active call UI
const CallPanel = () => {
  const call = useCall();
  const { useCallCallingState } = useCallStateHooks();
  // eslint-disable-next-line react-hooks/react-compiler
  const callingState = useCallCallingState();

  if (!call) return null;

  if ([CallingState.RINGING, CallingState.JOINING].includes(callingState)) {
    return <CustomRingingCall />;
  }

  return null;
};
