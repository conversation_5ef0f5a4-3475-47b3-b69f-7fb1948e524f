# Stream Video Call UI Components

This directory contains the call UI components for the Scraplo application using Stream Video SDK.

## Components

### CallUI (`call-ui.tsx`)

The main call interface component that handles:

- **Incoming calls**: Shows accept/reject buttons with caller information
- **Outgoing calls**: Shows calling status with recipient information
- **Active calls**: Displays participants in a grid layout with call controls
- **Ringing functionality**: Plays ringtone for incoming calls

### IncomingCallNotification (`incoming-call-notification.tsx`)

A floating notification that appears when receiving an incoming call:

- Shows caller information
- Provides accept/reject buttons
- Plays ringtone
- Can be dismissed

### CallManager (`call-manager.tsx`)

Manages all call states globally:

- Handles multiple calls
- Routes to appropriate UI components
- Manages call lifecycle

### CallMembers (`call-members.tsx`)

Displays participant avatars and names when video is disabled.

## Features

### Ringing Functionality

- **Web Audio API**: Uses `RingtonePlayer` class to generate ringtone sounds
- **Two-tone pattern**: Alternating 800Hz and 600Hz frequencies
- **Automatic cleanup**: Stops ringtone when call is answered/rejected

### Call States

1. **RINGING**: Call is being initiated or received
2. **JOINING**: Call is being connected
3. **JOINED**: Call is active with participants

### UI States

- **Incoming Call**: Green theme with accept/reject buttons
- **Outgoing Call**: Blue theme with cancel button
- **Active Call**: Dark theme with participant grid and controls

## Usage

### Starting a Call

```typescript
import useCallState from "~/hooks/use-call-state";

const { createCall } = useCallState();

// Start a call with an order ID
await createCall(orderId);
```

### Receiving a Call

The `CallManager` component automatically handles incoming calls and shows the appropriate UI.

### Call Controls

- **Microphone**: Toggle audio on/off
- **Camera**: Toggle video on/off
- **End Call**: Leave the call

## Styling

The components use Tailwind CSS classes and follow the application's design system:

- **Colors**: Green for incoming, blue for outgoing, red for actions
- **Animations**: Pulse and bounce effects for ringing states
- **Responsive**: Grid layout adapts to screen size
- **Accessibility**: Proper ARIA labels and keyboard navigation

## Dependencies

- `@stream-io/video-react-sdk`: Stream Video SDK
- `lucide-react`: Icons
- `@acme/ui`: UI components
- `~/lib/ringtone`: Ringtone utility

## File Structure

```
getstream-calling/
├── call-ui.tsx              # Main call interface
├── incoming-call-notification.tsx  # Floating notification
├── call-manager.tsx         # Global call management
├── call-members.tsx         # Participant display
├── call-controls.tsx        # Call control buttons
├── custom-ringing-call.tsx  # Ringing state UI
├── call-calling-state-label.tsx  # Status labels
└── README.md               # This file
```

## Integration

The call UI is integrated into the application through:

1. **Layout**: `CallManager` is added to the main layout
2. **Providers**: `StreamVideoProvider` provides the Stream client
3. **Routing**: Call pages use `/calls/[id]` route
4. **Hooks**: `useCallState` manages call operations

## Browser Support

- **Web Audio API**: Required for ringtone functionality
- **Modern browsers**: Chrome, Firefox, Safari, Edge
- **Mobile browsers**: iOS Safari, Chrome Mobile

## Troubleshooting

### Ringtone not playing

- Check browser permissions for audio
- Ensure Web Audio API is supported
- Verify no other audio is playing

### Call not connecting

- Check Stream Video client initialization
- Verify user authentication
- Check network connectivity

### UI not showing

- Ensure `CallManager` is in the layout
- Check Stream Video provider setup
- Verify call state management
