"use client";

import { useEffect, useState } from "react";
import {
  CallingState,
  useCall,
  useCallStateHooks,
} from "@stream-io/video-react-sdk";
import { Phone, PhoneOff, X } from "lucide-react";
import { toast } from "sonner";

import { But<PERSON> } from "@acme/ui/components/ui/button";

import { playRingtone, stopRingtone } from "~/lib/ringtone";

export const IncomingCallNotification = () => {
  const call = useCall();
  const { useCallCallingState, useCallCreatedBy } = useCallStateHooks();

  // eslint-disable-next-line react-hooks/react-compiler
  const callingState = useCallCallingState();
  // eslint-disable-next-line react-hooks/react-compiler
  const creator = useCallCreatedBy();

  const [isVisible, setIsVisible] = useState(false);

  // Show notification for incoming calls
  useEffect(() => {
    if (callingState === CallingState.RINGING && !call?.isCreatedByMe) {
      setIsVisible(true);
      playRingtone();
    } else {
      setIsVisible(false);
      stopRingtone();
    }

    return () => {
      stopRingtone();
    };
  }, [callingState, call?.isCreatedByMe]);

  if (!isVisible || !call) return null;

  const handleAccept = () => {
    call.join().catch((err) => {
      console.error("Failed to join call:", err);
      toast.error("Failed to join call");
    });
    setIsVisible(false);
  };

  const handleReject = () => {
    call.leave({ reject: true, reason: "decline" }).catch((err) => {
      console.error("Failed to leave call:", err);
      toast.error("Failed to leave call");
    });
    setIsVisible(false);
  };

  const handleDismiss = () => {
    setIsVisible(false);
  };

  return (
    <div className="ring-black/5 fixed right-4 top-4 z-50 w-80 rounded-lg bg-white p-4 shadow-2xl ring-1">
      <div className="flex items-start gap-3">
        {/* Caller Avatar */}
        <div className="flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
          <Phone className="h-6 w-6 text-green-600" />
        </div>

        {/* Call Info */}
        <div className="min-w-0 flex-1">
          <h3 className="font-semibold text-gray-900">Incoming Call</h3>
          <p className="truncate text-sm text-gray-600">
            {creator?.name ?? "Unknown"}
          </p>
          <p className="text-xs text-gray-500">Tap to answer</p>
        </div>

        {/* Dismiss Button */}
        <Button
          variant="ghost"
          size="icon"
          onClick={handleDismiss}
          className="h-6 w-6 rounded-full"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>

      {/* Action Buttons */}
      <div className="mt-4 flex gap-2">
        <Button
          onClick={handleAccept}
          className="flex-1 bg-green-600 hover:bg-green-700"
        >
          <Phone className="mr-2 h-4 w-4" />
          Answer
        </Button>

        <Button
          onClick={handleReject}
          variant="outline"
          className="flex-1 border-red-200 text-red-600 hover:bg-red-50"
        >
          <PhoneOff className="mr-2 h-4 w-4" />
          Decline
        </Button>
      </div>

      {/* Ringing Animation */}
      <div className="pointer-events-none absolute inset-0 animate-pulse rounded-lg bg-green-500/10" />
    </div>
  );
};

export default IncomingCallNotification;
