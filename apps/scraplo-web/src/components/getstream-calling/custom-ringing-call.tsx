import type { UserResponse } from "@stream-io/video-react-sdk";
import { useEffect } from "react";
import {
  useCall,
  useCallStateHooks,
  VideoPreview,
} from "@stream-io/video-react-sdk";

import { CallCallingStateLabel } from "./call-calling-state-label";
import { CallControls } from "./call-controls";
import { CallMembers } from "./call-members";

interface RingingCallProps {
  showMemberCount?: number;
}

export const CustomRingingCall = ({
  showMemberCount = 2,
}: RingingCallProps) => {
  const call = useCall();
  const { useCallMembers, useCallCreatedBy, useCameraState } =
    useCallStateHooks();
  // eslint-disable-next-line react-hooks/react-compiler
  const members = useCallMembers();
  // eslint-disable-next-line react-hooks/react-compiler
  const creator = useCallCreatedBy();
  // eslint-disable-next-line react-hooks/react-compiler
  const { camera, isMute: isCameraMute } = useCameraState();
  useEffect(() => {
    // enable the camera by default for all ring calls
    camera.enable().catch((e) => {
      console.log("Error", e);
    });
  }, [camera]);

  if (!call) return null;

  const caller = creator;
  // show the caller if this is an incoming call or show all the users I am calling to
  let membersToShow: UserResponse[] = [];
  if (call.isCreatedByMe) {
    membersToShow = members.slice(0, showMemberCount).map(({ user }) => user);
  } else if (caller) {
    membersToShow = [caller];
  }

  return (
    <div>
      {isCameraMute ? (
        <CallMembers members={membersToShow} />
      ) : (
        <VideoPreview />
      )}
      <CallCallingStateLabel />
      <CallControls />
    </div>
  );
};
