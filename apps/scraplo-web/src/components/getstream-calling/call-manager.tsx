"use client";

import type { Call } from "@stream-io/video-react-sdk";
import { useEffect, useState } from "react";
import {
  StreamCall,
  useCalls,
  useStreamVideoClient,
} from "@stream-io/video-react-sdk";

import Call<PERSON> from "./call-ui";
import IncomingCallNotification from "./incoming-call-notification";

export const CallManager = () => {
  const client = useStreamVideoClient();
  const calls = useCalls();
  const [activeCall, setActiveCall] = useState<Call | null>(null);

  useEffect(() => {
    if (calls.length > 0) {
      setActiveCall(calls[0] ?? null);
    } else {
      setActiveCall(null);
    }
  }, [calls]);

  if (!client || !activeCall) {
    return <IncomingCallNotification />;
  }

  return (
    <>
      <StreamCall call={activeCall}>
        <CallUI />
      </StreamCall>
      <IncomingCallNotification />
    </>
  );
};

export default CallManager;
