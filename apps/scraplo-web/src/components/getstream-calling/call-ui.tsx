"use client";

import { useEffect, useState } from "react";
import {
  CallingState,
  ParticipantView,
  useCall,
  useCallStateHooks,
} from "@stream-io/video-react-sdk";
import { Mic, MicOff, Phone, PhoneOff } from "lucide-react";

import { But<PERSON> } from "@acme/ui/components/ui/button";
import { cn } from "@acme/ui/lib/utils";

import { playRingtone, stopRingtone } from "~/lib/ringtone";
import { CallMembers } from "./call-members";

export const CallUI = () => {
  const call = useCall();
  const {
    useCallCallingState,
    useParticipants,
    useCallMembers,
    useCallCreatedBy,
    // useCameraState,
    useMicrophoneState,
  } = useCallStateHooks();

  // eslint-disable-next-line react-hooks/react-compiler
  const callingState = useCallCallingState();
  // eslint-disable-next-line react-hooks/react-compiler
  const participants = useParticipants();
  // eslint-disable-next-line react-hooks/react-compiler
  const members = useCallMembers();
  // eslint-disable-next-line react-hooks/react-compiler
  const creator = useCallCreatedBy();

  // const { camera, isMute: isCameraMute } = useCameraState();
  // eslint-disable-next-line react-hooks/react-compiler
  const { microphone, isMute: isMicMute } = useMicrophoneState();

  const [isRinging, setIsRinging] = useState(false);

  // Handle ringing sound
  useEffect(() => {
    if (callingState === CallingState.RINGING && !call?.isCreatedByMe) {
      setIsRinging(true);
      playRingtone();
    } else {
      setIsRinging(false);
      stopRingtone();
    }

    return () => {
      stopRingtone();
    };
  }, [callingState, call?.isCreatedByMe]);

  // Enable camera by default for outgoing calls
  useEffect(() => {
    if (call?.isCreatedByMe && callingState === CallingState.RINGING) {
      // camera.enable().catch(console.error);
    }
  }, [call?.isCreatedByMe, callingState]);

  if (!call) return null;

  const isOutgoingCall = call.isCreatedByMe;
  const isIncomingCall = !call.isCreatedByMe;
  const isJoining = callingState === CallingState.JOINING;
  const isRingingState = callingState === CallingState.RINGING;
  const isJoined = callingState === CallingState.JOINED;

  // Show ringing UI for incoming/outgoing calls
  if (isRingingState || isJoining) {
    return (
      <div className="bg-black/80 fixed inset-0 z-50 flex items-center justify-center">
        <div className="relative w-full max-w-md rounded-2xl bg-white p-6 shadow-2xl">
          {/* Caller/Recipient Info */}
          <div className="mb-6 text-center">
            <div className="mb-4 flex justify-center">
              <div
                className={cn(
                  "flex h-20 w-20 items-center justify-center rounded-full",
                  isRinging && "animate-pulse",
                  isOutgoingCall ? "bg-blue-100" : "bg-green-100",
                )}
              >
                <Phone
                  className={cn(
                    "h-8 w-8",
                    isOutgoingCall ? "text-blue-600" : "text-green-600",
                    isRinging && "animate-bounce",
                  )}
                />
              </div>
            </div>

            <h2 className="mb-2 text-xl font-semibold text-gray-900">
              {isOutgoingCall ? "Calling..." : "Incoming Call"}
            </h2>

            <p className="text-sm text-gray-600">
              {isOutgoingCall
                ? members
                    .slice(0, 3)
                    .map((m) => m.user.name)
                    .join(", ")
                : (creator?.name ?? "Unknown")}
            </p>
          </div>

          {/* Video Preview or Avatar */}
          <div className="mb-6 flex justify-center">
            {/* {isCameraMute ? ( */}
            <CallMembers
              members={
                isOutgoingCall
                  ? members
                      .filter((m) => m.user.id !== creator?.id)
                      .map((m) => m.user)
                  : creator
                    ? [creator]
                    : []
              }
            />
            {/* ) : (
              <div className="h-32 w-32 overflow-hidden rounded-lg">
                <VideoPreview />
              </div> */}
            {/* )} */}
          </div>

          {/* Call Controls */}
          <div className="flex justify-center gap-4">
            {/* Audio/Video Toggle for outgoing calls */}
            {isOutgoingCall && (
              <>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => microphone.toggle()}
                  className={cn(
                    "h-12 w-12 rounded-full",
                    isMicMute && "bg-red-100 text-red-600",
                  )}
                >
                  {isMicMute ? (
                    <MicOff className="h-5 w-5" />
                  ) : (
                    <Mic className="h-5 w-5" />
                  )}
                </Button>

                {/* <Button
                  variant="outline"
                  size="icon"
                  onClick={() => camera.toggle()}
                  className={cn(
                    "h-12 w-12 rounded-full",
                    isCameraMute && "bg-red-100 text-red-600",
                  )}
                >
                  {isCameraMute ? (
                    <VideoOff className="h-5 w-5" />
                  ) : (
                    <Video className="h-5 w-5" />
                  )}
                </Button> */}
              </>
            )}

            {/* Accept/Reject for incoming calls */}
            {isIncomingCall && (
              <>
                <Button
                  onClick={() => call.join()}
                  disabled={isJoining}
                  className="h-12 w-12 rounded-full bg-green-600 hover:bg-green-700"
                >
                  <Phone className="h-5 w-5 text-white" />
                </Button>

                <Button
                  onClick={() =>
                    call.leave({ reject: true, reason: "decline" })
                  }
                  disabled={isJoining}
                  className="h-12 w-12 rounded-full bg-red-600 hover:bg-red-700"
                >
                  <PhoneOff className="h-5 w-5 text-white" />
                </Button>
              </>
            )}

            {/* Cancel for outgoing calls */}
            {isOutgoingCall && (
              <Button
                onClick={() => call.leave({ reject: true, reason: "cancel" })}
                disabled={isJoining}
                className="h-12 w-12 rounded-full bg-red-600 hover:bg-red-700"
              >
                <PhoneOff className="h-5 w-5 text-white" />
              </Button>
            )}
          </div>

          {/* Status */}
          <div className="mt-4 text-center">
            <p className="text-sm text-gray-500">
              {isJoining ? "Connecting..." : "Ringing..."}
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Show active call UI
  if (isJoined) {
    return (
      <div className="fixed inset-0 z-50 bg-gray-900">
        {/* Participants Grid */}
        <div className="grid h-full grid-cols-1 gap-4 p-4 md:grid-cols-2 lg:grid-cols-3">
          {participants.map((participant) => (
            <div
              key={participant.sessionId}
              className="relative overflow-hidden rounded-lg bg-gray-800"
            >
              <ParticipantView
                participant={participant}
                className="h-full w-full"
              />
              <div className="bg-black/50 absolute bottom-2 left-2 rounded px-2 py-1 text-xs text-white">
                {participant.name || participant.userId}
              </div>
            </div>
          ))}
        </div>

        {/* Call Controls */}
        <div className="absolute bottom-8 left-1/2 flex -translate-x-1/2 gap-4">
          <Button
            variant="outline"
            size="icon"
            onClick={() => microphone.toggle()}
            className={cn(
              "h-12 w-12 rounded-full bg-white/10 text-white hover:bg-white/20",
              isMicMute && "bg-red-600 hover:bg-red-700",
            )}
          >
            {isMicMute ? (
              <MicOff className="h-5 w-5" />
            ) : (
              <Mic className="h-5 w-5" />
            )}
          </Button>

          {/* <Button
            variant="outline"
            size="icon"
            onClick={() => camera.toggle()}
            className={cn(
              "h-12 w-12 rounded-full bg-white/10 text-white hover:bg-white/20",
              isCameraMute && "bg-red-600 hover:bg-red-700",
            )}
          >
            {isCameraMute ? (
              <VideoOff className="h-5 w-5" />
            ) : (
              <Video className="h-5 w-5" />
            )}
          </Button> */}

          <Button
            onClick={() => call.leave().catch(console.error)}
            className="h-12 w-12 rounded-full bg-red-600 hover:bg-red-700"
          >
            <PhoneOff className="h-5 w-5 text-white" />
          </Button>
        </div>

        {/* Call Info */}
        <div className="bg-black/50 absolute left-1/2 top-4 -translate-x-1/2 rounded px-4 py-2 text-white">
          <p className="text-sm">
            {participants.length} participant
            {participants.length !== 1 ? "s" : ""} in call
          </p>
        </div>
      </div>
    );
  }

  return null;
};

export default CallUI;
