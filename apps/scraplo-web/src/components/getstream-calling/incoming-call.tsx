"use client";

import {
  AcceptCallButton,
  CallingState,
  CancelCallButton,
  ParticipantView,
  StreamCall,
  useCall,
  useCallStateHooks,
} from "@stream-io/video-react-sdk";

import { Button } from "@acme/ui/components/ui/button";

export const IncomingCallControls = () => {
  const call = useCall();
  const { useCallCallingState } = useCallStateHooks();
  // eslint-disable-next-line react-hooks/react-compiler
  const callingState = useCallCallingState();
  console.log("[IncomingCallControls] call:", call);
  console.log("[IncomingCallControls] callingState:", callingState);
  // Only show controls if this user is NOT the call creator
  if (!call || call.isCreatedByMe) return null;
  const isRinging = callingState === CallingState.RINGING;

  if (isRinging) {
    return (
      <div>
        <AcceptCallButton
          onClick={() => {
            console.log("[IncomingCallControls] Accept clicked");
            void call.join();
          }}
        />
        <CancelCallButton
          onClick={() => {
            console.log("[IncomingCallControls] Reject clicked");
            void call.leave({ reject: true, reason: "decline" });
          }}
        />
      </div>
    );
  }
  return null;
};

export const CallStatus = () => {
  const call = useCall();
  const { useCallCallingState } = useCallStateHooks();
  // eslint-disable-next-line react-hooks/react-compiler
  const callingState = useCallCallingState();
  console.log("[CallStatus] call:", call);
  console.log("[CallStatus] callingState:", callingState);
  // Show persistent modal for RINGING or JOINED
  if (!call) return null;
  if (
    callingState === CallingState.RINGING ||
    callingState === CallingState.JOINED
  ) {
    return (
      <div>
        <div className="text-base font-semibold">
          {callingState === CallingState.RINGING ? "Calling..." : "In Call"}
        </div>
        <Button
          variant="destructive"
          onClick={() => {
            console.log("[CallStatus] End/Cancel clicked");
            void call.leave({ reject: true, reason: "cancel" });
          }}
          className="w-full"
        >
          {callingState === CallingState.RINGING ? "Cancel Call" : "End Call"}
        </Button>
      </div>
    );
  }
  return null;
};

export const AudioCallUI = () => {
  const { useParticipants } = useCallStateHooks();
  // eslint-disable-next-line react-hooks/react-compiler
  const participants = useParticipants();
  console.log("[AudioCallUI] participants:", participants);
  return (
    <div style={{ display: "flex", flexDirection: "column", gap: "10px" }}>
      {participants.map((p) => (
        <ParticipantView participant={p} key={p.sessionId} trackType="none" />
      ))}
    </div>
  );
};

export const IncomingCalls = () => {
  const call = useCall();

  return (
    <div>
      <StreamCall call={call}>
        <IncomingCallControls />
      </StreamCall>
    </div>
  );
};
