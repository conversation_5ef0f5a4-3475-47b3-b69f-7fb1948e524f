import Image from "next/image";

import type { OrderDetail } from "~/app/lib/types";

// Get the type of a single item in the items array
type ItemType = Pick<OrderDetail["items"][number], "category" | "quantity">;

const ItemPriceCard = ({ item }: { item: ItemType }) => {
  const { category, quantity } = item;
  const isPerKg = category.rateType === "PER_KG";
  const rateUnit = isPerKg ? "kg" : "item";
  const quantityLabel = isPerKg ? `${quantity}kg` : `${quantity}x`;
  const totalAmount = (parseFloat(category.rate) * parseInt(quantity)).toFixed(
    0,
  );

  return (
    <div className="flex flex-row justify-between rounded-[12px] border border-black-50 px-3 py-[14px] md:px-4 md:py-5 lg:px-5 lg:py-6">
      <div className="flex flex-row gap-2 md:gap-3 lg:gap-4">
        <div>
          <Image
            src={category.image}
            alt={category.name}
            width={100}
            height={100}
            className="size-[46px] rounded-lg md:size-[60px] lg:size-[70px]"
            unoptimized
          />
        </div>

        <div className="flex flex-col justify-center gap-2">
          <h5 className="text-sm font-semibold text-black-700 md:text-base lg:text-lg">
            {category.name}
          </h5>
          <div className="flex flex-row gap-2">
            <p className="rounded-lg bg-yellow-50 px-2 py-1 text-[10px] font-medium leading-3 text-black-700 md:text-xs lg:text-sm">
              {quantityLabel}
            </p>

            <p className="rounded-lg bg-[#EBFFEE] px-2 py-1 text-[10px] font-medium leading-3 text-black-700 md:text-xs lg:text-sm">
              Price : ₹{category.rate}/{rateUnit}
            </p>
          </div>
        </div>
      </div>
      <div className="flex items-center rounded-lg border border-teal-950 px-[14px] py-[6px] text-center text-sm font-medium text-teal-900 md:text-base lg:text-lg">
        ₹{totalAmount}
      </div>
    </div>
  );
};

export default ItemPriceCard;
