"use client";

import type { z } from "zod";
import { Suspense, useEffect, useState } from "react";
import Image from "next/image";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { ChevronDown, ChevronUp, Loader2, Plus, X } from "lucide-react";
import { useQueryState } from "nuqs";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

// import { useIsMobile } from "~/hooks/use-is-mobile";
import { cn } from "@acme/ui/cn";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@acme/ui/components/ui/accordion";
import { Button } from "@acme/ui/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@acme/ui/components/ui/form";
import { Input } from "@acme/ui/components/ui/input";
import { RadioGroup, RadioGroupItem } from "@acme/ui/components/ui/radio-group";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@acme/ui/components/ui/select";
import { Sheet, SheetContent } from "@acme/ui/components/ui/sheet";
import { useIsMobile } from "@acme/ui/hooks/use-mobile";
import { OnboardingStepThreeSchema } from "@acme/validators";

import { PAYOUT_METHOD_SHEET_PARAM_NAME } from "~/app/lib/param-names";
import { useTRPC } from "~/trpc/react";

const PayoutMethodSheet = () => {
  const trpc = useTRPC();
  const isMobile = useIsMobile();
  const queryClient = useQueryClient();

  const [fullScreen, setFullScreen] = useState(false);
  const [showAddForm, setShowAddForm] = useState(false);
  const [selectedPayoutMethodId, setSelectedPayoutMethodId] = useState<
    string | null
  >(null);

  const [isOpen, setIsOpen] = useQueryState(PAYOUT_METHOD_SHEET_PARAM_NAME, {
    clearOnDefault: true,
  });

  const form = useForm<z.infer<typeof OnboardingStepThreeSchema>>({
    resolver: zodResolver(OnboardingStepThreeSchema),
    defaultValues: {
      bankName: "",
      upiId: "",
      ifscCode: "",
      accountNumber: "",
    },
  });

  // Fetch existing fund accounts
  const { data: fundAccountsData, isPending: isLoadingMethods } = useQuery(
    trpc.payment.getAllFundAccounts.queryOptions(undefined, {
      staleTime: 0,
      refetchOnMount: true,
    }),
  );

  // Add payment method mutation
  const { mutate: addPaymentMethod, isPending: isAddingPaymentMethod } =
    useMutation(
      trpc.payment.linkNewPaymentMethod.mutationOptions({
        onSuccess: async (opts) => {
          await Promise.allSettled([
            queryClient.invalidateQueries(
              trpc.payment.getAllFundAccounts.queryFilter(),
            ),
          ]);

          toast.success(opts.message);
          setShowAddForm(false);

          // Auto-select the newly added payment method by refreshing the list
          await queryClient.invalidateQueries(
            trpc.payment.getAllFundAccounts.queryFilter(),
          );

          form.reset();
        },
        onError: (opts) => {
          toast.error(opts.message);
        },
      }),
    );

  //   // Set default payment method mutation
  //   const { mutate: setDefaultPaymentMethod } = useMutation(
  //     trpc.payment.updateDefaultPaymentMethod.mutationOptions({
  //       onSuccess: async (opts) => {
  //         await Promise.allSettled([
  //           queryClient.invalidateQueries(
  //             trpc.payment.getAllFundAccounts.queryFilter(),
  //           ),
  //         ]);

  //         toast.success(opts.message);
  //       },
  //       onError: (opts) => {
  //         toast.error(opts.message);
  //       },
  //     }),
  //   );

  // Select payment method for current order
  const { mutate: selectPaymentMethodForOrder, isPending: isSelectingMethod } =
    useMutation(
      trpc.order.selectPayoutMethod.mutationOptions({
        onSuccess: async (opts) => {
          await Promise.allSettled([
            queryClient.invalidateQueries(
              trpc.order.getCurrentCart.queryFilter(),
            ),
          ]);

          toast.success(opts.message);

          // Close the sheet and let user proceed with location selection
          void setIsOpen(null);
        },
        onError: (opts) => {
          toast.error(opts.message);
        },
      }),
    );

  useEffect(() => {
    // Auto-select the default payment method
    if (fundAccountsData && fundAccountsData.fundAccounts.length > 0) {
      const defaultMethod = fundAccountsData.fundAccounts.find(
        (account) => account.id === fundAccountsData.defaultFundAccountId,
      );
      if (defaultMethod && !selectedPayoutMethodId) {
        setSelectedPayoutMethodId(defaultMethod.id);
      }
    }
  }, [fundAccountsData, selectedPayoutMethodId]);

  const handleSheetClose = async () => {
    if (!isAddingPaymentMethod && !isSelectingMethod) {
      await setIsOpen(null);
    }
  };

  const onSubmit = (data: z.infer<typeof OnboardingStepThreeSchema>) => {
    addPaymentMethod(data);
  };

  const handleSelectPaymentMethod = () => {
    if (!selectedPayoutMethodId) {
      toast.error("Please select a payout method");
      return;
    }

    // Only select it for the current order (do not update global default)
    selectPaymentMethodForOrder({ paymentMethodId: selectedPayoutMethodId });
  };

  // Loading state
  if (isLoadingMethods) {
    return (
      <Sheet
        open={!!isOpen}
        onOpenChange={(val) => setIsOpen(val ? "yes" : null)}
      >
        <SheetContent
          side={isMobile ? "bottom" : "right"}
          className="flex min-h-[480px] items-center justify-center rounded-t-3xl lg:min-w-[600px] xl:min-w-[862px] xl:text-2xl"
          hideCloseButton
        >
          <Loader2 className="h-6 w-6 animate-spin" />
        </SheetContent>
      </Sheet>
    );
  }

  return (
    <Sheet
      open={!!isOpen}
      onOpenChange={async (open) => {
        if (!open && !isAddingPaymentMethod && !isSelectingMethod) {
          await handleSheetClose();
        }
      }}
    >
      <SheetContent
        side={isMobile ? "bottom" : "right"}
        className={cn(
          "flex min-h-[480px] flex-col gap-6 rounded-t-3xl p-0 transition-all duration-500 md:min-h-[580px] md:gap-9 lg:min-w-[600px] xl:min-h-full xl:min-w-[862px] xl:rounded-l-3xl",
          fullScreen && "min-h-full",
        )}
        hideCloseButton
      >
        {/* Header */}
        <div className="flex items-center justify-between border-b-[1.2px] border-b-black-50 px-6 pb-3 pt-6 md:px-14 md:pb-5 md:pt-8">
          <div className="flex items-center gap-3">
            <Button
              className="rounded-xl bg-yellow-450 p-2"
              onClick={() => setFullScreen((prev) => !prev)}
            >
              {fullScreen ? (
                <ChevronDown className="size-5 md:size-6" />
              ) : (
                <ChevronUp className="size-5 md:size-6" />
              )}
            </Button>

            <div>
              <h2 className="font-bold leading-7 text-black-900 md:text-lg md:leading-[30px]">
                Select Payout Method
              </h2>
              <p className="text-sm leading-5 text-black-500 md:text-base md:leading-6">
                Choose how you want to receive payment for this order
              </p>
            </div>
          </div>

          <Button
            className="rounded-full bg-black-50 p-1.5"
            onClick={handleSheetClose}
            disabled={isAddingPaymentMethod || isSelectingMethod}
          >
            <X className="size-5 md:size-[25px]" />
          </Button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto px-6 md:px-14">
          {!showAddForm && (
            <>
              {/* Existing Fund Accounts */}
              {fundAccountsData && fundAccountsData.fundAccounts.length > 0 && (
                <div className="mb-6">
                  <h3 className="mb-4 text-lg font-semibold text-black-900">
                    Saved Payment Methods
                  </h3>

                  <RadioGroup
                    value={selectedPayoutMethodId ?? ""}
                    onValueChange={setSelectedPayoutMethodId}
                    className="space-y-3"
                  >
                    {fundAccountsData.fundAccounts.map((account) => (
                      <div
                        key={account.id}
                        className="flex items-center justify-between rounded-lg border-[1.2px] border-black-150 p-4"
                      >
                        <div className="flex items-center space-x-4">
                          <RadioGroupItem value={account.id} />
                          <div className="flex items-center gap-3">
                            <Image
                              src={
                                account.account_type === "vpa"
                                  ? "/static/icons/link-upi.svg"
                                  : "/static/icons/net-banking.svg"
                              }
                              alt={account.account_type}
                              height={24}
                              width={24}
                              className="size-6 object-cover"
                              unoptimized
                            />
                            <div>
                              <p className="font-medium text-black-900">
                                {account.account_type === "vpa"
                                  ? account.vpa?.address
                                  : `${account.bank_account?.name} - ****${account.bank_account?.account_number.slice(-4)}`}
                              </p>
                              {account.id ===
                                fundAccountsData.defaultFundAccountId && (
                                <p className="text-sm text-green-600">
                                  Default
                                </p>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </RadioGroup>
                </div>
              )}

              {/* Empty State for No Fund Accounts */}
              {fundAccountsData &&
                fundAccountsData.fundAccounts.length === 0 && (
                  <div className="mb-6 flex flex-col items-center justify-center py-8">
                    <Image
                      src="/static/icons/net-banking.svg"
                      alt="no-payment-methods"
                      height={48}
                      width={48}
                      className="mb-4 opacity-50"
                      unoptimized
                    />
                    <h3 className="mb-2 text-lg font-medium text-black-900">
                      No Payment Methods
                    </h3>
                    <p className="text-center text-sm text-black-500">
                      Add a payment method to receive payouts for your orders
                    </p>
                  </div>
                )}

              {/* Add New Payment Method Button */}
              <Button
                variant="outline"
                className="w-full gap-2 border-dashed"
                onClick={() => setShowAddForm(true)}
              >
                <Plus className="size-4" />
                Add New Payment Method
              </Button>
            </>
          )}

          {/* Add Payment Method Form */}
          {showAddForm && (
            <div>
              <div className="mb-4 flex items-center justify-between">
                <h3 className="text-lg font-semibold text-black-900">
                  Add New Payment Method
                </h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setShowAddForm(false);
                    form.reset();
                  }}
                >
                  Cancel
                </Button>
              </div>

              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(onSubmit, (errors) => {
                    console.log("Form validation errors:", errors);

                    // Check for specific field errors first
                    if (errors.ifscCode) {
                      toast.error(
                        errors.ifscCode.message ?? "Invalid IFSC Code format",
                      );
                      return;
                    }
                    if (errors.accountNumber) {
                      toast.error(
                        errors.accountNumber.message ??
                          "Invalid account number format",
                      );
                      return;
                    }
                    if (errors.upiId) {
                      toast.error(
                        errors.upiId.message ?? "Invalid UPI ID format",
                      );
                      return;
                    }

                    // Fall back to general error
                    toast.error(
                      "Please provide either a valid UPI ID or complete banking details (Bank name, IFSC code, and account number).",
                    );
                  })}
                  className="space-y-6"
                >
                  <Accordion
                    type="single"
                    defaultValue="link-upi"
                    collapsible
                    className="flex flex-col gap-4"
                  >
                    <AccordionItem value="link-upi">
                      <AccordionTrigger>
                        <span className="flex items-center gap-4 text-lg font-medium -tracking-[0.09px] text-black-800">
                          <Image
                            src="/static/icons/link-upi.svg"
                            alt="link-upi"
                            height={100}
                            width={100}
                            className="size-4 object-cover xl:size-6"
                            unoptimized
                          />
                          Link UPI
                        </span>
                      </AccordionTrigger>
                      <AccordionContent className="flex flex-col items-start gap-4">
                        <div className="flex w-full flex-col gap-4 rounded-lg border-[1.2px] border-black-150 px-4 py-3">
                          <div className="flex w-full items-center gap-4">
                            <span className="text-sm font-medium text-black-800">
                              Add UPI ID
                            </span>
                          </div>

                          <div className="flex items-center gap-2">
                            <FormField
                              control={form.control}
                              name="upiId"
                              render={({ field }) => (
                                <FormItem className="flex-1">
                                  <FormControl>
                                    <Input
                                      placeholder="UPI ID"
                                      {...field}
                                      onChange={(e) => {
                                        field.onChange(e);
                                        // Clear banking fields when UPI is entered
                                        if (e.target.value.trim()) {
                                          form.setValue("bankName", "");
                                          form.setValue("ifscCode", "");
                                          form.setValue("accountNumber", "");
                                          // Clear validation errors
                                          form.clearErrors("bankName");
                                          form.clearErrors("ifscCode");
                                          form.clearErrors("accountNumber");
                                        }
                                      }}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <Button
                              type="submit"
                              disabled={isAddingPaymentMethod}
                            >
                              <span className="px-2.5">
                                {isAddingPaymentMethod ? "Adding..." : "Verify"}
                              </span>
                            </Button>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>

                    <AccordionItem value="net-banking">
                      <AccordionTrigger>
                        <span className="flex items-center gap-4 text-lg font-medium -tracking-[0.09px] text-black-800">
                          <Image
                            src="/static/icons/net-banking.svg"
                            alt="net-banking"
                            height={100}
                            width={100}
                            className="size-4 object-cover xl:size-6"
                            unoptimized
                          />
                          Net Banking
                        </span>
                      </AccordionTrigger>
                      <AccordionContent>
                        <div className="flex flex-col gap-5">
                          <div className="flex flex-col gap-4">
                            <FormField
                              control={form.control}
                              name="bankName"
                              render={({ field }) => (
                                <FormItem>
                                  <FormControl>
                                    <Select
                                      value={field.value ?? undefined}
                                      onValueChange={(value) => {
                                        field.onChange(value);
                                        if (value) {
                                          // Clear UPI field when bank is selected
                                          form.setValue("upiId", "");
                                          // Clear validation errors
                                          form.clearErrors("upiId");
                                        }
                                      }}
                                    >
                                      <SelectTrigger>
                                        <SelectValue placeholder="Select a bank" />
                                      </SelectTrigger>
                                      <SelectContent>
                                        <SelectGroup>
                                          <SelectItem value="State Bank of India">
                                            State Bank of India
                                          </SelectItem>
                                          <SelectItem value="HDFC Bank">
                                            HDFC Bank
                                          </SelectItem>
                                          <SelectItem value="ICICI Bank">
                                            ICICI Bank
                                          </SelectItem>
                                          <SelectItem value="Axis Bank">
                                            Axis Bank
                                          </SelectItem>
                                          <SelectItem value="Punjab National Bank">
                                            Punjab National Bank
                                          </SelectItem>
                                        </SelectGroup>
                                      </SelectContent>
                                    </Select>
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={form.control}
                              name="accountNumber"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>Account Number</FormLabel>
                                  <FormControl>
                                    <Input
                                      placeholder="Account Number"
                                      {...field}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />

                            <FormField
                              control={form.control}
                              name="ifscCode"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel>IFSC Code</FormLabel>
                                  <FormControl>
                                    <Input placeholder="IFSC Code" {...field} />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>

                          <Button
                            type="submit"
                            disabled={isAddingPaymentMethod}
                          >
                            {isAddingPaymentMethod
                              ? "Adding..."
                              : "Add Payment Method"}
                          </Button>
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  </Accordion>
                </form>
              </Form>
            </div>
          )}
        </div>

        {/* Footer - Confirm Selection Button */}
        {!showAddForm &&
          fundAccountsData &&
          fundAccountsData.fundAccounts.length > 0 && (
            <div className="border-t border-black-50 px-6 py-4 md:px-14">
              <Button
                className="w-full"
                onClick={handleSelectPaymentMethod}
                disabled={!selectedPayoutMethodId || isSelectingMethod}
              >
                {isSelectingMethod ? "Processing..." : "Confirm"}
              </Button>
            </div>
          )}
      </SheetContent>
    </Sheet>
  );
};

const PayoutMethodSheetWrapper = () => {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <PayoutMethodSheet />
    </Suspense>
  );
};

export default PayoutMethodSheetWrapper;
