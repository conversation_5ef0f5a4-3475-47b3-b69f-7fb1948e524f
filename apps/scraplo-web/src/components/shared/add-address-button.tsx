"use client";

import { useRouter } from "next/navigation";

import { Button } from "@acme/ui/components/ui/button";

import { useUserConfirmedLocationStore } from "~/stores/user-store";

const AddAddressButton = () => {
  const router = useRouter();
  const { setConfirmedLocation } = useUserConfirmedLocationStore();

  const handleAddAddress = () => {
    setConfirmedLocation(null);
    router.push("/add-address");
  };

  return (
    <div className="fixed bottom-0 w-full px-6 pb-10 pt-5 backdrop-blur-[15px] xl:px-[120px]">
      <Button className="w-full" onClick={handleAddAddress}>
        Add Address
      </Button>
    </div>
  );
};

export default AddAddressButton;
