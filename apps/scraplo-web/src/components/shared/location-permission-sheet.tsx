"use client";

import { Suspense, useEffect, useState } from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useSuspenseQuery } from "@tanstack/react-query";
import { Plus, Search } from "lucide-react";
import { useQueryState } from "nuqs";

import { Button } from "@acme/ui/components/ui/button";
import { Separator } from "@acme/ui/components/ui/separator";
import { Sheet, SheetContent } from "@acme/ui/components/ui/sheet";
import { cn } from "@acme/ui/lib/utils";

import type { UserAddress } from "~/app/lib/types";
import { LOCATION_PERMISSION_SHEET_PARAM_NAME } from "~/app/lib/param-names";
import useLocation from "~/hooks/use-location";
import { useUserLocationStore } from "~/stores/user-store";
import { useTRPC } from "~/trpc/react";

const LocationPermissionSheet = () => {
  const router = useRouter();
  const { requestLocation, error } = useLocation();
  const { userLocation, setUserLocation } = useUserLocationStore();
  const [isLocationPermissionSheetOpen, setIsLocationPermissionSheetOpen] =
    useQueryState(LOCATION_PERMISSION_SHEET_PARAM_NAME, {
      clearOnDefault: true,
    });

  // State to track if location permission is granted
  // This state is used to determine whether to show the sheet or not
  // This state helps to prevent sheet-flash on the screen for milliseconds
  const [isLocationPermissionGranted, setIsLocationPermissionGranted] =
    useState(true);

  const trpc = useTRPC();
  const { data: savedAddresses, isPending } = useSuspenseQuery(
    trpc.address.getAllSavedAddresses.queryOptions(),
  );

  useEffect(() => {
    if (typeof navigator !== "undefined") {
      navigator.permissions
        .query({ name: "geolocation" })
        .then((permissionStatus) => {
          if (permissionStatus.state === "granted") {
            console.log(
              "Location permission already granted. Attempting to fetch location.",
            );
            void requestLocation();
          } else if (permissionStatus.state === "prompt") {
            setIsLocationPermissionGranted(false);
            console.log(
              "Location permission not yet granted (prompt). Sheet will open.",
            );
          } else {
            setIsLocationPermissionGranted(false);
            console.log("Location permission denied. Sheet will open.");
          }
        })
        .catch((error) => {
          setIsLocationPermissionGranted(false);
          console.error("Error checking location permission:", error);
        });
    } else {
      console.log(
        "Browser does not support Permissions API. Sheet behavior relies on userLocation.",
      );
    }
  }, [requestLocation, setUserLocation, isPending]);

  const isSheetOpen = isLocationPermissionSheetOpen
    ? true
    : isLocationPermissionGranted
      ? false
      : !userLocation;

  const handleLocationPermissionRequest = async () => {
    await setIsLocationPermissionSheetOpen(null);
    await requestLocation();
  };

  const handleUserLocation = async (location: UserAddress) => {
    await setIsLocationPermissionSheetOpen(null);
    setUserLocation(location);
  };

  const handleAddAddress = () => {
    setUserLocation(null);
    router.push("/add-address");
  };

  const handleEnterLocationManually = () => {
    setUserLocation(null);
    router.push("/add-address/search-location");
  };

  return (
    <Sheet open={isSheetOpen}>
      <SheetContent
        side="bottom"
        className="min-h-[80%] overflow-hidden rounded-t-3xl bg-yellow-00 p-0 shadow-none"
        hideCloseButton
      >
        {/* location indicator with button */}
        <div className="flex items-start justify-between bg-yellow-950 px-6 py-5 md:px-14 xl:px-[120px]">
          <div className="flex flex-col gap-1">
            <span className="flex items-center gap-1 font-jakarta font-bold leading-[26px] -tracking-[0.16px] text-black-50 xl:text-lg">
              <Image
                src="/static/icons/location-2.svg"
                alt="location"
                height={200}
                width={200}
                className="size-6"
                unoptimized
              />
              Location Permission is
              <span className="text-yellow-400">
                {error || !userLocation ? " Off" : " On"}
              </span>
            </span>
            <span className="text-[11px] leading-[18px] -tracking-[0.11px] text-black-150 md:text-sm xl:text-base">
              Giving location permission will ensure accurate address and hassle
              free delivery
            </span>
          </div>

          <Button
            className="rounded-xl bg-yellow-400 px-4 py-2 text-black-900"
            onClick={handleLocationPermissionRequest} // This button calls the same function
          >
            {userLocation ? "Use current" : "Grant"}
          </Button>
        </div>

        <div className="flex flex-col gap-6 p-6 md:px-14 xl:px-[120px]">
          {/* enter address manually */}
          <div className="flex flex-col gap-4">
            <span className="font-semibold leading-6 -tracking-[0.16px] text-black-800 xl:text-lg">
              Select Pickup Address
            </span>

            <button
              onClick={handleEnterLocationManually}
              className="flex w-full items-center gap-2 rounded-lg border-[1.2px] border-black-100 bg-black-0 px-4 py-[14px] text-[13px] font-medium leading-5 text-black-700 shadow-sm transition-colors placeholder:text-black-400 focus-visible:bg-white focus-visible:outline-none focus-visible:ring-[1.2px] focus-visible:ring-teal-750 focus-visible:placeholder:text-transparent disabled:cursor-not-allowed disabled:opacity-50"
            >
              <Search className="size-5 text-teal-800" />
              <input
                placeholder="Enter Location Manually"
                className="w-full bg-transparent focus:outline-none focus:ring-0"
              />
            </button>
          </div>

          <Separator />

          <button
            onClick={handleAddAddress}
            className="flex w-full items-center gap-1 font-jakarta font-semibold tracking-[0.14px] text-teal-750 xl:text-xl"
          >
            <Plus className="size-4 xl:size-6" />
            Add Address
          </button>

          <Separator />

          {/* saved pickup address */}
          <div className="flex flex-col gap-3">
            <p className="font-jakarta text-sm font-semibold leading-5 -tracking-[0.14px] text-black-600 xl:text-base">
              {savedAddresses.addresses.length} Saved Pickup Address
            </p>
            <div className="flex flex-col gap-4">
              {savedAddresses.addresses.length > 0 &&
                savedAddresses.addresses.map((location) => (
                  <DisplayLocationInfo
                    key={location.id}
                    title={location.name}
                    address={location.display}
                    isActive={
                      userLocation?.coordinates === location.coordinates
                    }
                    addressType={location.addressType}
                    // @ts-expect-error: addressType is optional fix later
                    onClick={() => handleUserLocation(location)}
                  />
                ))}
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
};

const MainLocationPermissionSheet = () => {
  return (
    <Suspense>
      <LocationPermissionSheet />
    </Suspense>
  );
};

export default MainLocationPermissionSheet;

interface DisplayLocationInfoProps {
  title: string;
  address: string;
  addressType?: "HOME" | "WORK" | "OTHER" | null;
  isActive?: boolean;
  onClick?: () => void;
}

const DisplayLocationInfo = ({
  title,
  address,
  addressType,
  isActive,
  onClick,
}: DisplayLocationInfoProps) => {
  const iconPath =
    addressType === "HOME"
      ? "/static/icons/home-2.svg"
      : addressType === "WORK"
        ? "/static/icons/work.svg"
        : "/static/icons/location-3.svg";

  return (
    <button
      aria-label="button"
      onClick={onClick}
      className={cn(
        "flex items-center gap-2.5 rounded-xl bg-black-0 px-4 py-3 xl:px-6 xl:py-4",
        isActive && "bg-yellow-100",
      )}
    >
      <div className="relative aspect-square size-6 xl:size-8">
        <Image
          src={iconPath}
          alt={title}
          fill
          className="object-cover"
          unoptimized
        />
      </div>

      <div className="flex flex-col gap-0.5">
        <p className="text-start font-jakarta text-[15px] font-bold leading-6 -tracking-[0.15px] text-black-800 xl:text-lg">
          {title}
        </p>
        <p className="text-start text-xs leading-[18px] text-black-800 xl:text-base">
          {address}
        </p>
      </div>
    </button>
  );
};
