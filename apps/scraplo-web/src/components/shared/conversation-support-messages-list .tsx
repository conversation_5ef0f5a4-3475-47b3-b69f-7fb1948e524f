"use client";

import { useEffect, useRef } from "react";
import Image from "next/image";

import type { SupportMessage } from "~/app/lib/types";
import { useSession } from "~/server/auth/client";

interface ConversationSupportMessagesListProps {
  messages: SupportMessage[];
}

const ConversationSupportMessagesList = ({
  messages = [],
}: ConversationSupportMessagesListProps) => {
  const { data: session } = useSession();
  const userId = session?.user.id;
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({
      behavior: "smooth",
      block: "end",
    });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  return (
    <div className="flex flex-col gap-3 py-4">
      {messages.length === 0 ? (
        <div className="flex h-full flex-col items-center justify-center py-10 text-center text-gray-500">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="mb-2 h-12 w-12 text-gray-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={1.5}
              d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"
            />
          </svg>
          <p className="text-lg font-medium">No messages yet</p>
          <p className="mt-1 text-sm">
            Start the conversation by sending a message
          </p>
        </div>
      ) : (
        <>
          {messages.map((msg) => {
            const isCurrentUser = msg.senderId === userId;

            return (
              <div
                key={msg.id}
                className={`flex w-full items-end gap-4 ${
                  isCurrentUser ? "justify-end" : "justify-start"
                }`}
              >
                {!isCurrentUser && (
                  <div className="h-8 w-8 overflow-hidden rounded-full">
                    <Image
                      src="/static/images/default-user.png"
                      alt="Avatar"
                      width={32}
                      height={32}
                      className="h-full w-full object-cover"
                      unoptimized
                    />
                  </div>
                )}
                <div
                  className={`max-w-[75%] rounded-2xl px-4 py-3 ${
                    isCurrentUser
                      ? "bg-teal-600 text-black-800"
                      : "bg-yellow-100 text-black-800"
                  }`}
                >
                  <div className="break-words leading-4">{msg.content}</div>
                </div>
                {isCurrentUser && (
                  <div className="h-8 w-8 overflow-hidden rounded-full">
                    <Image
                      src={
                        session?.user.image ?? "/static/images/default-user.png"
                      }
                      alt="Your avatar"
                      width={32}
                      height={32}
                      className="h-full w-full object-cover"
                      unoptimized
                    />
                  </div>
                )}
              </div>
            );
          })}
          {/* Invisible element to scroll to */}
          <div ref={messagesEndRef} />
        </>
      )}
    </div>
  );
};

export default ConversationSupportMessagesList;
