"use client";

import { useEffect } from "react";
import OneSignal from "react-onesignal";

import { env } from "~/env";
import { useSession } from "~/server/auth/client";

const Onesignal = () => {
  const { data: session } = useSession();

  useEffect(() => {
    const initializeOneSignal = async () => {
      if (typeof window === "undefined") return;
      if (!session) return;

      try {
        await OneSignal.init({
          appId: env.NEXT_PUBLIC_ONESIGNAL_CUSTOMER_APP_ID,
          safari_web_id: env.NEXT_PUBLIC_ONESIGNAL_CUSTOMER_APP_SAFARI_WEB_ID,
          allowLocalhostAsSecureOrigin: true,
        });

        if (session.user.id) {
          await OneSignal.login(session.user.id);
          OneSignal.User.addEmail(session.user.email);
          if (session.user.phoneNumber) {
            OneSignal.User.addSms(session.user.phoneNumber);
          }
        } else {
          await OneSignal.logout();
        }
      } catch (error) {
        console.error("OneSignal Error:", error);
      }
    };

    void initializeOneSignal();
  }, [session]);

  return null;
};

export default Onesignal;
