"use client";

import { useMemo } from "react";
import { useQuery } from "@tanstack/react-query";

import type { systemConfigurationEnum } from "@acme/db/schema";
import { Editor } from "@acme/ui/editor";

import { useTRPC } from "~/trpc/react";

export interface SystemConfigProps {
  type: (typeof systemConfigurationEnum.enumValues)[number];
  className?: string;
}

export function SystemConfig(props: SystemConfigProps) {
  const { type, className } = props;
  const trpc = useTRPC();
  const { data, isLoading, error } = useQuery(
    trpc.config.getSystemConfig.queryOptions({ key: type }),
  );

  const content = useMemo(() => {
    if (isLoading) return <span className={className}>Loading...</span>;
    if (error) return <span className={className}>Error loading config</span>;
    if (!data) return null;
    if (type === "PRIVACY_POLICY" || type === "TERMS_AND_CONDITIONS") {
      return <Editor editable={false} editorSerializedState={data} />;
    }
    return <span className={className}>{data}</span>;
  }, [isLoading, error, data, type, className]);

  return content;
}
