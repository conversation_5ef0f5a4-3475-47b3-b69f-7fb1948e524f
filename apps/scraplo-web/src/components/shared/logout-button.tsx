"use client";

import { useState } from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@acme/ui/components/ui/alert-dialog";

import { authClient } from "~/server/auth/client";

const LogoutButton = () => {
  const router = useRouter();
  const [showLogoutConfirmation, setShowLogoutConfirmation] = useState(false);

  const handleLogout = async () => {
    await authClient.signOut();
    router.push("/");
  };

  return (
    <>
      <button
        onClick={() => setShowLogoutConfirmation(true)}
        className="flex flex-1 items-center gap-3 rounded-lg bg-black-0 px-4 py-[10px] md:py-3 xl:px-8 xl:py-4"
      >
        <div className="flex flex-1 items-center gap-3">
          <div className="relative aspect-square size-[18px] md:size-5 xl:size-8">
            <Image
              src="/static/icons/logout.svg"
              alt="Log Out"
              fill
              className="object-cover"
              unoptimized
            />
          </div>
          <span className="font-inter text-[13px] font-medium leading-[18px] tracking-[0.26px] text-black-700 md:text-[15px] md:leading-5 md:tracking-[0.3px] xl:text-lg xl:leading-7 xl:tracking-[0.36px]">
            Log Out
          </span>
        </div>
      </button>

      <AlertDialog
        open={showLogoutConfirmation}
        onOpenChange={setShowLogoutConfirmation}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Confirm Logout</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to log out of your account?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel className="md:px-[50px]">
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleLogout}
              className="bg-red-600 text-white md:px-[50px]"
            >
              Log Out
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default LogoutButton;
