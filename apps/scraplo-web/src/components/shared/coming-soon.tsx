"use client";

import { useRouter } from "next/navigation";
import { ChevronLeft } from "lucide-react";

import { Button } from "@acme/ui/components/ui/button";
import { cn } from "@acme/ui/lib/utils";

interface ComingSoonProps {
  title?: string;
  description?: string;
  className?: string;
}

const ComingSoon = ({
  title = "Coming Soon!",
  description = "We're working hard to bring you this feature. Stay tuned for updates!",
  className,
}: ComingSoonProps) => {
  const router = useRouter();

  const handleBack = () => {
    router.back();
  };

  return (
    <div
      className={cn(
        "flex min-h-[80vh] w-full flex-col items-center justify-center px-6",
        className,
      )}
    >
      <div className="flex max-w-md flex-col items-center text-center">
        <h1 className="mb-4 font-jakarta text-4xl font-extrabold leading-tight text-teal-800">
          {title}
        </h1>

        <p className="mb-10 text-base font-medium leading-relaxed text-black-600">
          {description}
        </p>

        <div className="mb-10 flex h-2 w-64 overflow-hidden rounded-full bg-black-50">
          <div className="w-1/3 animate-[pulse_2s_ease-in-out_infinite] rounded-full bg-yellow-500"></div>
        </div>

        <Button variant="outline" size="icon" onClick={handleBack}>
          <ChevronLeft />
        </Button>
      </div>
    </div>
  );
};

export default ComingSoon;
