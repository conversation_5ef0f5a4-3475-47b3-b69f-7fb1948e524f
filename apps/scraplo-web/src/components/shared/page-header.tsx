"use client";

import React from "react";
import { useRouter } from "next/navigation";
import { ArrowLeft, X } from "lucide-react";

import { Button } from "@acme/ui/components/ui/button";
import { cn } from "@acme/ui/lib/utils";

interface PageHeaderProps {
  title?: string | React.ReactNode;
  subTitle?: string | React.ReactNode;
  action?: React.ReactNode;

  titleClassName?: string;
  subTitleClassName?: string;
}

const PageHeader = ({
  title,
  subTitle,
  titleClassName,
  subTitleClassName,
  action,
}: PageHeaderProps) => {
  const router = useRouter();

  const handleBack = () => {
    router.back();
  };

  const handleClose = () => {
    router.push("/");
  };

  return (
    <nav className="sticky left-0 top-0 z-20 flex w-full items-center justify-between border-b-[1.2px] border-black-50 bg-[rgba(255,_252,_252,_0.60)] px-6 py-3 backdrop-blur-[20px] md:px-14 md:pb-5 md:pt-8 xl:px-[120px]">
      <div className="flex items-center gap-x-2 md:gap-x-4">
        <Button
          className="rounded-lg bg-black-50 p-[14px]"
          onClick={handleBack}
        >
          <ArrowLeft className="h-6 w-6" />
        </Button>

        <div>
          {typeof title === "string" ? (
            <h1
              className={cn(
                "font-bold leading-[28px] text-black-900 md:text-lg md:leading-[30px] xl:text-xl xl:leading-8",
                titleClassName,
              )}
            >
              {title}
            </h1>
          ) : (
            title
          )}
          {typeof subTitle === "string" ? (
            <p
              className={cn(
                "text-[13px] leading-[18px] text-black-600",
                subTitleClassName,
              )}
            >
              {subTitle}
            </p>
          ) : (
            subTitle
          )}
        </div>
      </div>

      {action ?? (
        <Button
          className="rounded-full bg-black-50 p-1.5 md:p-3"
          onClick={handleClose}
        >
          <X className="size-5 md:size-6" />
        </Button>
      )}
    </nav>
  );
};

export default PageHeader;
