"use client";

import { useEffect, useState } from "react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { Send, X } from "lucide-react";
import { useQueryState } from "nuqs";

import { But<PERSON> } from "@acme/ui/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@acme/ui/components/ui/dialog";
import { Input } from "@acme/ui/components/ui/input";
import { Sheet, SheetContent } from "@acme/ui/components/ui/sheet";

import { MESSAGE_FETCH_INTERVAL } from "~/app/lib/constants";
import {
  ORDER_DETAILS_ADMIN_CHAT_PARAM_NAME,
  SUPPORT_CHAT_PARAM_NAME,
} from "~/app/lib/param-names";
import { useTRPC } from "~/trpc/react";
import ConversationSupportMessagesList from "./conversation-support-messages-list ";

interface SupportChatSheetProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  supportChatId?: string | null;
  setSupportChatId?: (id: string | null) => void;
  orderId?: string | null;
  setOrderId?: (id: string | null) => void;
}

const SupportChatSheet = ({
  open,
  onOpenChange,
  supportChatId: supportChatIdProp,
  setSupportChatId: setSupportChatIdProp,
  orderId: orderIdProp,
  setOrderId: setOrderIdProp,
}: SupportChatSheetProps) => {
  const [messageContent, setMessageContent] = useState<string | null>(null);
  // Always call useQueryState hooks first
  const [orderIdState, setOrderIdState] = useQueryState(
    ORDER_DETAILS_ADMIN_CHAT_PARAM_NAME,
    { clearOnDefault: true },
  );
  const [supportChatIdState, setSupportChatIdState] = useQueryState(
    SUPPORT_CHAT_PARAM_NAME,
    { clearOnDefault: true },
  );
  // Use props if provided, otherwise use state
  const orderId =
    typeof orderIdProp !== "undefined" ? orderIdProp : orderIdState;
  const setOrderId =
    typeof setOrderIdProp !== "undefined" ? setOrderIdProp : setOrderIdState;
  // Only use supportChatId if orderId is not present
  const supportChatId = orderId
    ? undefined
    : typeof supportChatIdProp !== "undefined"
      ? supportChatIdProp
      : supportChatIdState;
  const setSupportChatId = orderId
    ? undefined
    : typeof setSupportChatIdProp !== "undefined"
      ? setSupportChatIdProp
      : setSupportChatIdState;

  const trpc = useTRPC();
  const queryClient = useQueryClient();
  // Use orderId if present, else supportChatId
  const { data } = useQuery(
    trpc.conversation.getSupportConversation.queryOptions(
      orderId ? { orderId } : {},
      {
        refetchInterval: MESSAGE_FETCH_INTERVAL,
        enabled: open,
      },
    ),
  );
  const { mutate: sendNewMessage, isPending: isSendingMessage } = useMutation(
    trpc.conversation.sendSupportMessage.mutationOptions({
      onSuccess: async () => {
        await queryClient.invalidateQueries(
          trpc.conversation.getSupportConversation.queryOptions(
            orderId ? { orderId } : {},
          ),
        );
      },
    }),
  );

  const { mutate: rateSupportConversation, isPending: isRating } = useMutation(
    trpc.conversation.rateSupportConversation.mutationOptions({
      onSuccess: async () => {
        await queryClient.invalidateQueries(
          trpc.conversation.getSupportConversation.queryOptions(
            orderId ? { orderId } : {},
          ),
        );
        // Always clear chat ID and close the sheet after rating
        if (setSupportChatId) void setSupportChatId(null);
        void setOrderId(null);
        onOpenChange(false);
      },
    }),
  );

  // Add close chat mutation
  const { mutate: closeSupportConversation, isPending: isClosing } =
    useMutation(
      trpc.conversation.closeSupportConversation.mutationOptions({
        onSuccess: async () => {
          await queryClient.invalidateQueries(
            trpc.conversation.getSupportConversation.queryOptions(
              orderId ? { orderId } : {},
            ),
          );
          // After closing, the review UI will show automatically
        },
      }),
    );

  // Rating UI state
  const [hasRated, setHasRated] = useState(false);
  const [reviewText, setReviewText] = useState<string>("");
  const [selectedRating, setSelectedRating] = useState<number | null>(null);

  // Reset hasRated when a new conversation is loaded
  useEffect(() => {
    setHasRated(false);
  }, [data?.id]);

  // If conversation is closed and not rated, show rating UI
  const showRatingUI =
    !!data && data.isOpen === false && data.isRated === false && !hasRated;

  // If conversation is closed and not rated, but data is null (e.g., just closed), try to show rating UI using local state
  // (No change needed here if API always returns the latest convo)

  const handleRate = () => {
    if (!data?.id) return;
    rateSupportConversation({
      conversationId: data.id,
      rating: selectedRating,
      reviewText: reviewText.trim() || undefined,
    });
    setHasRated(true);
  };

  const [showSkipDialog, setShowSkipDialog] = useState(false);
  // Removed unused pendingClose

  const handleClose = () => {
    // Only intercept close if rating UI is visible and not rated (i.e., convo is closed and not rated)
    if (showRatingUI) {
      setShowSkipDialog(true);
      return;
    }
    // Always allow closing the sheet when chat is ongoing
    onOpenChange(false);
    if (orderId) void setOrderId(null);
    if (!orderId && setSupportChatId) void setSupportChatId(null);
  };

  const handleSendNewMessage = () => {
    if (!messageContent) return;
    sendNewMessage(
      orderId
        ? { orderId, content: messageContent }
        : { content: messageContent },
    );
    setMessageContent(null);
  };

  // Always set the query param to the conversation id if it exists and is not already set or is 'new'
  useEffect(() => {
    if (
      open &&
      data?.id &&
      setSupportChatId &&
      supportChatId !== data.id &&
      !(data.isOpen === false && data.isRated === true)
    ) {
      void setSupportChatId(data.id);
    }
  }, [
    open,
    data?.id,
    supportChatId,
    setSupportChatId,
    data?.isOpen,
    data?.isRated,
  ]);

  // If user is targeting 'new' and backend returns a closed & rated convo, treat as no active convo
  const isFreshChat =
    supportChatId === "new" &&
    !!data &&
    data.isOpen === false &&
    data.isRated === true;

  return (
    <>
      <Sheet open={open} onOpenChange={handleClose}>
        <SheetContent
          side="bottom"
          className="flex min-h-[90%] flex-col rounded-t-3xl p-0"
          hideCloseButton
        >
          {/* header */}
          <div className="flex items-center justify-between gap-4 border-b border-b-black-50 px-6 pb-3 pt-6">
            <Button
              className="rounded-full bg-black-50 p-1.5"
              onClick={handleClose}
            >
              <X />
            </Button>
            <h2 className="flex-1 font-bold leading-7 text-black-900">
              Support Chat
            </h2>
            {data && data.isOpen && !data.isRated && (
              <div className="flex justify-center">
                <Button
                  variant="destructive"
                  onClick={() => {
                    if (data.id)
                      closeSupportConversation({ conversationId: data.id });
                  }}
                  disabled={isClosing}
                >
                  {isClosing ? "Closing..." : "Close Chat"}
                </Button>
              </div>
            )}{" "}
          </div>
          {/* Show Close Chat button if conversation is open and not rated */}

          {showRatingUI ? (
            <div className="flex flex-1 flex-col items-center justify-center gap-6 p-8">
              <h3 className="text-lg font-semibold text-black-900">
                How satisfied are you with the support?
              </h3>
              <div className="flex h-fit gap-4 py-2">
                {[1, 2, 3, 4, 5].map((num) => (
                  <button
                    key={num}
                    type="button"
                    className={`h-fit p-1 text-4xl transition-transform hover:scale-125 ${selectedRating === num ? "ring-2 ring-yellow-450" : ""}`}
                    onClick={() => setSelectedRating(num)}
                    disabled={isRating}
                    aria-label={`Rate ${num}`}
                  >
                    {num === 1 && "😡"}
                    {num === 2 && "😕"}
                    {num === 3 && "😐"}
                    {num === 4 && "🙂"}
                    {num === 5 && "😄"}
                  </button>
                ))}
              </div>
              <textarea
                className="mt-4 w-full rounded-md border border-black-200 p-2 text-black-900 focus:outline-none focus:ring-2 focus:ring-yellow-450"
                placeholder="Write a review (optional)"
                value={reviewText}
                onChange={(e) => setReviewText(e.target.value)}
                rows={3}
                maxLength={500}
              />
              <div className="mt-2 flex w-full flex-row justify-center gap-4">
                <Button
                  variant="default"
                  className="px-6"
                  onClick={handleRate}
                  disabled={isRating || selectedRating === null}
                >
                  Submit
                </Button>
                <Button
                  variant="ghost"
                  className="text-black-600 underline"
                  onClick={() => {
                    if (!data.id) return;
                    rateSupportConversation({
                      conversationId: data.id,
                      rating: null,
                      reviewText: reviewText.trim() || undefined,
                    });
                    setHasRated(true);
                  }}
                  disabled={isRating}
                >
                  Skip
                </Button>
              </div>
            </div>
          ) : (
            <>
              <div className="hide-scrollbar flex max-h-[72vh] flex-1 flex-col gap-5 overflow-y-auto px-6">
                <ConversationSupportMessagesList
                  messages={isFreshChat ? [] : (data?.messages ?? [])}
                />
              </div>
              {/* footer with input box */}
              <div className="flex items-center gap-3 px-6 pb-10 pt-5">
                <Input
                  placeholder="Type your message here..."
                  className="rounded-full border-black-200 bg-black-0 px-3 py-3"
                  value={messageContent ?? ""}
                  onChange={(e) => setMessageContent(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === "Enter" && !e.shiftKey) {
                      e.preventDefault();
                      handleSendNewMessage();
                    }
                  }}
                />
                <Button
                  className="rounded-full bg-yellow-450 p-3"
                  onClick={handleSendNewMessage}
                  disabled={isSendingMessage}
                >
                  <Send />
                </Button>
              </div>
            </>
          )}
        </SheetContent>
      </Sheet>
      <Dialog open={showSkipDialog} onOpenChange={setShowSkipDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Skip Rating?</DialogTitle>
            <DialogDescription>
              Are you sure you want to close the chat without rating? You won't
              be able to rate this conversation later, and a new conversation
              will be started next time.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              //   variant="secondary"
              onClick={() => {
                setShowSkipDialog(false);
              }}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={() => {
                if (data?.id) {
                  handleRate();
                }
                setShowSkipDialog(false);
                // Also clear chat ID and close the sheet on skip
                if (setSupportChatId) void setSupportChatId(null);
                void setOrderId(null);
                onOpenChange(false);
              }}
              disabled={isRating}
            >
              Yes, Skip Rating
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default SupportChatSheet;
