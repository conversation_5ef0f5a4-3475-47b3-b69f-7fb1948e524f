"use client";

import Image from "next/image";
import { useRouter } from "next/navigation";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Pencil } from "lucide-react";
import { useQueryState } from "nuqs";
import { toast } from "sonner";

import { Button } from "@acme/ui/components/ui/button";
import { Input } from "@acme/ui/components/ui/input";

import type {
  CartItem,
  SubCategoriesWithOrWithoutParent,
} from "~/app/lib/types";
import { ADD_ITEM_TO_CART_SHEET_PARAM_NAME } from "~/app/lib/param-names";
import { useTRPC } from "~/trpc/react";

interface CartItemProps {
  cartItem?: CartItem;
  category: SubCategoriesWithOrWithoutParent["parent"];
  inputDefaultValue?: number;
  onQuantityChange?: (quantity: number) => void;
  showRemoveButton?: boolean;
  showEditButton?: boolean;
  disableInput?: boolean;
}

const CartItem = ({
  cartItem,
  category,
  inputDefaultValue,
  onQuantityChange,
  showRemoveButton,
  showEditButton,
  disableInput,
}: CartItemProps) => {
  const [, setCategoryIdToParam] = useQueryState(
    ADD_ITEM_TO_CART_SHEET_PARAM_NAME,
    { clearOnDefault: true },
  );

  const trpc = useTRPC();
  const queryClient = useQueryClient();
  const router = useRouter();

  const { mutate: removeFromCart } = useMutation(
    trpc.order.removeFromCart.mutationOptions({
      onSuccess: async (opts) => {
        await queryClient.invalidateQueries({
          queryKey: trpc.order.getCurrentCartStatistics.queryKey(),
        });
        await queryClient.invalidateQueries({
          queryKey: trpc.order.getCurrentCart.queryKey(),
        });
        await queryClient.invalidateQueries({
          queryKey: trpc.order.getCartItemByCategoryId.queryKey(),
        });

        router.refresh();
        toast.success(opts.message);
      },
      onError: (opts) => {
        toast.error(opts.message);
      },
    }),
  );

  const handleRemoveFromCart = () => {
    if (!cartItem?.id) return;
    removeFromCart({ orderItemId: cartItem.id });
  };

  const handleQuantityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    const quantity = value ? Math.max(0, parseInt(value) || 0) : 0;

    if (onQuantityChange) onQuantityChange(quantity);
  };

  const handleEdit = async () => {
    if (!category?.id) return;

    await setCategoryIdToParam(category.id);
  };

  if (!category) return null;
  return (
    <div className="flex flex-col gap-6 rounded-xl border-[1.2px] px-[14px] py-4 md:p-5 xl:rounded-2xl xl:p-8">
      <div className="flex items-start justify-between">
        <div className="flex gap-2">
          <div className="relative aspect-square w-[60px] overflow-hidden rounded-[6px] md:w-16 xl:w-[75px]">
            <Image
              src={category.image}
              alt={category.name}
              fill
              className="object-cover"
              unoptimized
            />
          </div>

          <div className="flex flex-col gap-2">
            <p className="text-sm font-semibold leading-5 text-black-700 md:text-base md:leading-6 xl:text-lg xl:leading-7">
              {category.name}
            </p>
            <div className="flex items-center gap-2">
              {/* <span className="w-fit rounded-lg bg-yellow-50 px-2 py-1.5 text-[10px] font-medium leading-3 -tracking-[0.3px] text-black-700">
                parent
              </span> */}
              <span className="w-fit rounded-lg bg-[#EBFFEE] px-2 py-1.5 text-[10px] font-medium leading-3 -tracking-[0.3px] md:text-[11px] md:leading-4 md:-tracking-[0.33px] xl:text-sm xl:leading-6 xl:-tracking-[0.42px]">
                Estimated price : ₹{category.rate}/
                {category.rateType === "PER_ITEM" ? "item" : "kg"}
              </span>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-2">
          {showEditButton && (
            <Button
              className="group rounded-sm bg-[#FFF5F4] p-2 md:size-12 md:p-3"
              onClick={handleEdit}
            >
              <Pencil className="text-yellow-850 group-hover:text-black-0 md:size-6" />
            </Button>
          )}

          {showRemoveButton && (
            <Button
              className="rounded-sm bg-[#FFF5F4] p-2 md:p-3"
              onClick={handleRemoveFromCart}
            >
              <Image
                src="/static/icons/cancel.svg"
                alt="cancel"
                height={100}
                width={100}
                className="aspect-square size-[18px] object-cover md:size-6"
                unoptimized
              />
            </Button>
          )}
        </div>
      </div>

      <div className="flex flex-col gap-2">
        <div className="flex items-center gap-3 xl:gap-5">
          <Input
            type="number"
            min={1}
            disabled={disableInput}
            defaultValue={cartItem?.quantity ?? inputDefaultValue ?? 1}
            onChange={handleQuantityChange}
            className="rounded-lg border-black-50 bg-black-0 px-4 py-3"
          />
          <div className="rounded-lg bg-teal-50 p-3 text-sm font-medium leading-5 -tracking-[0.14px] text-teal-850 xl:text-base">
            {category.rateType === "PER_ITEM" ? "items" : "kgs"}
          </div>
        </div>
        {!disableInput && (
          <span className="text-[9px] leading-3 text-black-700 xl:text-sm xl:leading-5">
            *Please enter the estimated{" "}
            {category.rateType === "PER_ITEM" ? "items" : "kgs"} of selected
            category*
          </span>
        )}
      </div>
    </div>
  );
};

export default CartItem;
