"use client";

import { Suspense, useState } from "react";
import Image from "next/image";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { ArrowLeft } from "lucide-react";
import { useQueryState } from "nuqs";
import { toast } from "sonner";

import { <PERSON><PERSON> } from "@acme/ui/components/ui/button";
import { Sheet, SheetContent } from "@acme/ui/components/ui/sheet";
import { cn } from "@acme/ui/lib/utils";

import { REQUEST_PICKUP_OPTIONS } from "~/app/lib/constants";
import { REQUEST_PICKUP_PARAM_NAME } from "~/app/lib/param-names";
import { useUserLocationStore } from "~/stores/user-store";
import { useTRPC } from "~/trpc/react";

const RequestPickupSheet = () => {
  const { userLocation } = useUserLocationStore();
  const [selectedOption, setSelectedOption] = useState<
    (typeof REQUEST_PICKUP_OPTIONS)[number]
  >(REQUEST_PICKUP_OPTIONS[1]);
  const [open, setOpen] = useQueryState(REQUEST_PICKUP_PARAM_NAME, {
    clearOnDefault: true,
  });

  const trpc = useTRPC();
  const queryClient = useQueryClient();
  const { mutate: placeQuickPickupOrder, isPending } = useMutation(
    trpc.order.placeQuickPickupOrder.mutationOptions({
      onSuccess: async (opts) => {
        toast.success(opts.message);
        await queryClient.invalidateQueries({
          queryKey: [trpc.order.getAllOrders.queryKey()],
        });

        await setOpen(null);
      },
      onError: (error) => {
        toast.error(error.message);
      },
    }),
  );

  const handlePlaceQuickPickupOrder = () => {
    if (!userLocation?.id) {
      toast.error("Please select a pickup location from your saved address.");
      return;
    }

    if (selectedOption.title === "Smart Pickup") {
      return toast.error("Smart Pickup is not available at the moment.");
    }

    placeQuickPickupOrder({ addressId: userLocation.id });
  };

  return (
    <Sheet
      open={!!open}
      onOpenChange={(val) => (val ? setOpen("yes") : setOpen(null))}
    >
      <SheetContent
        side="bottom"
        className="overflow-hidden rounded-t-3xl p-0 shadow-none"
        hideCloseButton
      >
        <div className="flex items-start gap-2 border-b-[1.2px] border-b-black-50 px-6 pb-3 pt-6 md:px-14 xl:px-[120px]">
          <Button className="bg-black-50" onClick={() => setOpen(null)}>
            <ArrowLeft className="size-4" />
          </Button>

          <div className="flex flex-col gap-1">
            <span className="font-bold leading-7 text-black-900 xl:text-lg">
              Request a Scrap Pickup
            </span>
            <span className="text-[13px] leading-[18px] text-black-600 xl:text-base">
              Choose how you&apos;d like to schedule your scrap pickup
            </span>
          </div>
        </div>

        <div className="flex flex-col gap-5 p-6 md:px-14 xl:px-[120px]">
          {REQUEST_PICKUP_OPTIONS.map((option) => (
            <PickupOption
              key={option.title}
              isSelected={selectedOption === option}
              onClick={() => {
                setSelectedOption(option);
              }}
              {...option}
            />
          ))}
        </div>

        <div className="flex items-center gap-4 border-t-[1.2px] border-t-black-50 bg-white px-6 py-4 md:px-14 md:py-8 xl:px-[120px] xl:py-10">
          <Button
            variant="disabled"
            className="flex-1 text-black-800"
            onClick={() => setOpen(null)}
          >
            Cancel
          </Button>
          <Button
            className="flex-1"
            onClick={handlePlaceQuickPickupOrder}
            disabled={isPending}
          >
            {isPending ? "Placing Order..." : "Place Order"}
          </Button>
        </div>
      </SheetContent>
    </Sheet>
  );
};

type PickupOptionProps = (typeof REQUEST_PICKUP_OPTIONS)[number] & {
  isSelected: boolean;
  onClick?: () => void;
};

const PickupOption = (props: PickupOptionProps) => {
  return (
    <div
      aria-label="button"
      onClick={props.onClick}
      className={cn(
        "flex cursor-pointer flex-col rounded-xl border-[1.5px] border-black-100 px-5 py-4",
        props.isSelected && "border-yellow-550 bg-yellow-0",
      )}
    >
      <div className="space-x-3 self-end">
        {props.isBestValue && (
          <Button className="rounded-full bg-[#000] px-3 py-1.5 font-jakarta text-[10px] leading-[14px] text-black-0 xl:text-sm">
            Best Value
          </Button>
        )}
        {props.isRecommended && (
          <Button className="rounded-full bg-yellow-500 px-3 py-1.5 font-jakarta text-[10px] leading-[14px] text-black-900 xl:text-sm">
            Recommended
          </Button>
        )}
      </div>

      <div className="flex flex-col gap-3">
        <div className="relative aspect-square w-6 xl:w-8">
          <Image
            src={props.icon}
            alt={props.title}
            fill
            className="object-cover"
            unoptimized
          />
        </div>

        <div>
          <p className="mb-1 text-sm font-semibold leading-5 text-teal-950 md:text-base xl:text-lg">
            {props.title}
          </p>
          <p className="text-[11px] leading-[18px] text-black-900 md:text-sm xl:text-base">
            {props.description}
          </p>
        </div>

        {props.subDescription && (
          <span className="text-xs font-semibold leading-4 text-teal-800 md:text-sm">
            {props.subDescription}
          </span>
        )}

        {props.pros && (
          <ul className="flex flex-col gap-1.5">
            {props.pros.map((pro) => (
              <li
                key={pro}
                className="flex items-center gap-1 text-[10px] font-semibold leading-[14px] text-[#046437] md:text-sm xl:text-base"
              >
                <Image
                  src="/static/icons/check.svg"
                  alt="check"
                  width={50}
                  height={50}
                  className="size-3 object-cover md:size-5"
                  unoptimized
                />
                {pro}
              </li>
            ))}
          </ul>
        )}
      </div>
    </div>
  );
};

const MainRequestPickupSheet = () => {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <RequestPickupSheet />
    </Suspense>
  );
};

export default MainRequestPickupSheet;
