"use client";

import {
  Building2,
  CheckCircle2,
  CreditCard,
  Smartphone,
  Trash2,
  XCircle,
} from "lucide-react";

import { cn } from "@acme/ui/cn";
import { Badge } from "@acme/ui/components/ui/badge";
import { Button } from "@acme/ui/components/ui/button";

import type { FundAccount } from "~/app/lib/types";
import AlertWrapper from "./alert-wrapper";

export const BankAccountCard = ({
  account,
  isDefault = false,
  onUpdate,
  onDelete,
}: {
  account: FundAccount;
  isDefault?: boolean;
  onUpdate?: (accountId: string) => void;
  onDelete?: (accountId: string) => void;
}) => (
  <div
    className={cn(
      "rounded-lg bg-black-50 transition-all hover:shadow-md",
      isDefault && "bg-yellow-50",
    )}
  >
    <div className="p-4">
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-3">
          <div className="flex h-10 w-10 items-center justify-center rounded-full bg-blue-100">
            <Building2 className="h-5 w-5 text-blue-600" />
          </div>
          <div className="flex-1">
            <h3 className="font-semibold text-gray-900 xl:text-xl">
              {account.bank_account?.name ?? "N/A"}
            </h3>
            <p className="text-sm text-gray-600 xl:text-base">
              {account.bank_account?.name}
            </p>
            <p className="font-mono text-sm text-gray-500 xl:text-base">
              ****{account.bank_account?.account_number.slice(-4)}
            </p>
            <p className="text-xs text-gray-400 xl:text-sm">
              IFSC: {account.bank_account?.ifsc}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-x-2">
          {account.active ? (
            <CheckCircle2 className="h-4 w-4 text-green-500" />
          ) : (
            <XCircle className="h-4 w-4 text-gray-400" />
          )}
          {isDefault ? (
            <Badge
              variant="outline"
              className="border-yellow-300 bg-yellow-100 text-yellow-800"
            >
              Default
            </Badge>
          ) : (
            onUpdate && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onUpdate(account.id)}
              >
                Set Default
              </Button>
            )
          )}
          {!isDefault && onDelete && (
            <AlertWrapper
              trigger={
                <Button
                  variant="outline"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                  }}
                  className="text-red-600 hover:bg-red-50 hover:text-red-700"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              }
              onConfirm={() => onDelete(account.id)}
              title="Delete Payment Method"
              description="Are you sure you want to delete this payment method? This action cannot be undone."
              confirmText="Delete"
              cancelText="Cancel"
            />
          )}
        </div>
      </div>
    </div>
  </div>
);

export const UPIAccountCard = ({
  account,
  isDefault = false,
  onUpdate,
  onDelete,
}: {
  account: FundAccount;
  isDefault?: boolean;
  onUpdate?: (accountId: string) => void;
  onDelete?: (accountId: string) => void;
}) => (
  <div
    className={cn(
      "rounded-lg bg-black-50 transition-all hover:shadow-md",
      isDefault && "bg-yellow-50",
    )}
  >
    <div className="p-4">
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-3">
          <div className="flex h-10 w-10 items-center justify-center rounded-full bg-purple-100">
            <Smartphone className="h-5 w-5 text-purple-600" />
          </div>
          <div className="flex-1">
            <h3 className="font-semibold text-gray-900 xl:text-xl">
              UPI Account
            </h3>

            <p className="line-clamp-1 text-sm text-gray-600 xl:text-base">
              {account.vpa?.address}
            </p>
            <p className="line-clamp-1 text-xs text-gray-400 xl:text-sm">
              @{account.vpa?.handle}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-x-2">
          {account.active ? (
            <CheckCircle2 className="h-4 w-4 text-green-500" />
          ) : (
            <XCircle className="h-4 w-4 text-gray-400" />
          )}
          {isDefault ? (
            <Badge
              variant="outline"
              className="border-yellow-300 bg-yellow-100 text-yellow-800"
            >
              Default
            </Badge>
          ) : (
            onUpdate && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onUpdate(account.id)}
              >
                Set Default
              </Button>
            )
          )}
          {!isDefault && onDelete && (
            <AlertWrapper
              trigger={
                <Button
                  variant="outline"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                  }}
                  className="text-red-600 hover:bg-red-50 hover:text-red-700"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              }
              onConfirm={() => onDelete(account.id)}
              title="Delete Payment Method"
              description="Are you sure you want to delete this payment method? This action cannot be undone."
              confirmText="Delete"
              cancelText="Cancel"
            />
          )}
        </div>
      </div>
    </div>
  </div>
);

export const CardAccountCard = ({
  account,
  isDefault = false,
  onUpdate,
  onDelete,
}: {
  account: FundAccount;
  isDefault?: boolean;
  onUpdate?: (accountId: string) => void;
  onDelete?: (accountId: string) => void;
}) => (
  <div
    className={cn(
      "rounded-lg bg-black-50 transition-all hover:shadow-md",
      isDefault && "bg-yellow-50",
    )}
  >
    <div className="p-4">
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-3">
          <div className="flex h-10 w-10 items-center justify-center rounded-full bg-green-100">
            <CreditCard className="h-5 w-5 text-green-600" />
          </div>
          <div className="flex-1">
            <h3 className="font-semibold text-gray-900">
              {account.card?.network} {account.card?.type}
            </h3>

            <p className="text-sm text-gray-600">{account.card?.name}</p>
            <p className="font-mono text-sm text-gray-500">
              ****{account.card?.last4}
            </p>
            <p className="text-xs text-gray-400">{account.card?.issuer}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          {account.active ? (
            <CheckCircle2 className="h-4 w-4 text-green-500" />
          ) : (
            <XCircle className="h-4 w-4 text-gray-400" />
          )}
          {isDefault ? (
            <Badge
              variant="outline"
              className="border-yellow-300 bg-yellow-100 text-yellow-800"
            >
              Default
            </Badge>
          ) : (
            onUpdate && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onUpdate(account.id)}
              >
                Set Default
              </Button>
            )
          )}
          {!isDefault && onDelete && (
            <AlertWrapper
              trigger={
                <Button
                  variant="outline"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                  }}
                  className="text-red-600 hover:bg-red-50 hover:text-red-700"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              }
              onConfirm={() => onDelete(account.id)}
              title="Delete Payment Method"
              description="Are you sure you want to delete this payment method? This action cannot be undone."
              confirmText="Delete"
              cancelText="Cancel"
            />
          )}
        </div>
      </div>
    </div>
  </div>
);
