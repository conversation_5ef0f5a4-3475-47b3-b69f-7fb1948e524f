import type { LucideIcon } from "lucide-react";
import React from "react";

// Base Card component
export const Card = ({
  children,
  className = "",
}: {
  children: React.ReactNode;
  className?: string;
}) => {
  return (
    <div
      className={`flex flex-col gap-5 rounded-[20px] bg-[#FBFBFB] px-4 py-5 md:px-6 md:py-7 ${className}`}
    >
      {children}
    </div>
  );
};

// Card Header with icon
export const CardHeader = ({
  title,
  icon: Icon,
}: {
  title: string;
  icon: LucideIcon | React.ReactElement;
}) => {
  return (
    <div className="flex items-center gap-3">
      <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-teal-50">
        {React.isValidElement(Icon) ? (
          Icon
        ) : (
          <Icon className="text-2xl text-teal-700" />
        )}
      </div>
      <h2 className="font-semibold leading-6 text-teal-800 md:text-lg">
        {title}
      </h2>
    </div>
  );
};

// Key-Value Pair component
export const KeyValuePair = ({
  label,
  value,
  valueClassName = "",
  labelClassName = "",
}: {
  label: string;
  value: React.ReactNode;
  valueClassName?: string;
  labelClassName?: string;
}) => {
  return (
    <div className="flex items-center justify-between py-2">
      <span
        className={`text-xs text-black-600 md:text-sm lg:text-base ${labelClassName}`}
      >
        {label}
      </span>
      <span
        className={`font-inter text-sm font-semibold text-black-800 md:text-base lg:text-lg ${valueClassName}`}
      >
        {value}
      </span>
    </div>
  );
};

// Card Section component
export const CardSection = ({
  title,
  children,
  className = "",
}: {
  title?: string;
  children: React.ReactNode;
  className?: string;
}) => {
  return (
    <div className={`mb-4 ${className}`}>
      {title && <h3 className="mb-2 text-lg font-medium">{title}</h3>}
      <div>{children}</div>
    </div>
  );
};

// Divider component
export const CardDivider = () => {
  return <div className="my-4 border-b border-black-150" />;
};
