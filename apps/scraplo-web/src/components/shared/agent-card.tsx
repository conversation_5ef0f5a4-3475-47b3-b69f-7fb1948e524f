"use client";

import Image from "next/image";
import { StarIcon } from "lucide-react";
import { useQueryState } from "nuqs";

import { Input } from "@acme/ui/components/ui/input";

import type { OrderDetail } from "~/app/lib/types";
import { DEFAULT_PROFILE_IMAGE } from "~/app/lib/constants";
import { RenderVehicleBadge } from "~/app/lib/functions";
import { ORDER_DETAILS_CHAT_PARAM_NAME } from "~/app/lib/param-names";
import useCallState from "~/hooks/use-call-state";

interface AgentOrderCardProps {
  orderId?: string;
  kabadiwala: OrderDetail["kabadiwala"];
  status: OrderDetail["status"];
}
const AgentOrderCard = ({
  kabadiwala,
  status,
  orderId,
}: AgentOrderCardProps) => {
  const { createCall } = useCallState();

  const [, setOrderId] = useQueryState(ORDER_DETAILS_CHAT_PARAM_NAME, {
    clearOnDefault: true,
  });

  const handleOpenChat = async () => {
    await setOrderId(orderId ?? null);
  };

  const handleCallAgent = async () => {
    if (!orderId) return;
    await createCall(orderId);
  };

  return (
    <div className="flex w-full flex-col gap-5 rounded-[20px] border-[1.2px] border-black-50 px-4 py-5 md:px-6 md:py-7 lg:gap-6">
      <div className="flex w-full flex-row items-center justify-between gap-[14px] md:gap-4 lg:gap-5">
        <KabadiwalaProfile
          profileImageUrl={kabadiwala.image ?? DEFAULT_PROFILE_IMAGE}
          rating={100}
        />
        <div className="flex flex-1 flex-col">
          <div className="flex flex-row justify-between">
            <p className="font-inter text-base font-semibold text-black-900 md:text-lg lg:text-xl">
              {kabadiwala.vehicles?.[0]?.vehicleNumber ?? "Unknown"}
            </p>
            <RenderVehicleBadge
              badgeType={kabadiwala.vehicles?.[0]?.vehicleType ?? "Unknown"}
            />
          </div>
          <p className="text-xs text-black-400 md:text-sm lg:text-base">
            {kabadiwala.name ?? "Unknown"}
          </p>
        </div>
      </div>

      {status === "ACTIVE" && (
        <div className="flex items-center justify-between gap-3 md:gap-4">
          <Input
            placeholder="Message Agent..."
            className="flex-1 rounded-full border border-yellow-600 bg-yellow-0 md:text-sm"
            disabled={!orderId}
            onClick={handleOpenChat}
          />
          <button
            onClick={handleCallAgent}
            className="relative aspect-square w-12 cursor-pointer rounded-full border border-yellow-600 bg-yellow-0 p-2 md:w-14"
          >
            <Image
              src="/static/icons/telephone.svg"
              alt="Telephone Icon"
              fill
              className="p-2"
              unoptimized
            />
          </button>
        </div>
      )}
    </div>
  );
};
export default AgentOrderCard;

export const KabadiwalaProfile = ({
  profileImageUrl,
  rating,
}: {
  profileImageUrl: string;
  rating: number;
}) => {
  return (
    <div className="relative h-fit w-fit">
      <div className="h-fit w-fit rounded-full border-2 border-yellow-500 p-1">
        <Image
          height={100}
          width={100}
          alt="profile image"
          className="size-9 shrink-0 rounded-full md:size-12 lg:size-14"
          src={profileImageUrl}
          unoptimized
        />
      </div>
      <div className="absolute bottom-0 right-1 flex w-fit flex-row items-center gap-1 rounded-2xl bg-yellow-900 px-[6px] py-[3px] md:px-2 md:py-1">
        <StarIcon className="size-2 md:size-3" fill="#fbcf04" color="#fbcf04" />
        <p className="font-inter text-[10px] font-medium leading-[10px] text-black-0 md:text-xs">
          {rating}
        </p>
      </div>
    </div>
  );
};
