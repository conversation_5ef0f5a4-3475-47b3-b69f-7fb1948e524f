"use client";

import React, { useCallback, useEffect, useRef, useState } from "react";
import { useRouter } from "next/navigation";
import { useQuery } from "@tanstack/react-query";
import { LucideShoppingBag } from "lucide-react";

import { useTRPC } from "~/trpc/react";

const STORAGE_KEY = "floatingCartIndicatorPosition";

interface Position {
  top: number;
  left: number;
}

const getInitialPosition = (): Position => {
  if (typeof window === "undefined") {
    return { top: 100, left: 0 };
  }

  const saved = localStorage.getItem(STORAGE_KEY);
  // eslint-disable-next-line @typescript-eslint/no-unsafe-return
  if (saved) return JSON.parse(saved);

  // Default: bottom right
  return {
    top: window.innerHeight - 340,
    left: window.innerWidth - 100,
  };
};

const FloatingCartIndicator = () => {
  const trpc = useTRPC();
  const { data: cartItems } = useQuery(
    trpc.order.getCurrentCart.queryOptions(),
  );
  const router = useRouter();

  const [position, setPosition] = useState<Position>(getInitialPosition);
  const dragging = useRef(false);
  const offset = useRef<{ x: number; y: number }>({ x: 0, y: 0 });
  const dragMoved = useRef(false);

  // Save position to localStorage
  useEffect(() => {
    if (typeof window !== "undefined") {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(position));
    }
  }, [position]);

  // Pointer events for both mouse and touch
  const handlePointerDown = useCallback((e: React.PointerEvent) => {
    dragging.current = true;
    dragMoved.current = false;
    const target = e.currentTarget as HTMLElement;
    const rect = target.getBoundingClientRect();
    offset.current = {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
    };
    target.setPointerCapture(e.pointerId);
  }, []);

  const handlePointerMove = useCallback((e: PointerEvent) => {
    if (!dragging.current) return;
    dragMoved.current = true;
    setPosition(() => {
      const left = Math.max(
        0,
        Math.min(window.innerWidth - 80, e.clientX - offset.current.x),
      );
      const top = Math.max(
        0,
        Math.min(window.innerHeight - 80, e.clientY - offset.current.y),
      );
      return { left, top };
    });
  }, []);

  const handlePointerUp = useCallback(() => {
    if (!dragging.current) return;
    dragging.current = false;
    // Only navigate if it was a click/tap, not a drag
    if (!dragMoved.current) {
      router.push("/cart");
    }
  }, [router]);

  useEffect(() => {
    window.addEventListener("pointermove", handlePointerMove);
    window.addEventListener("pointerup", handlePointerUp);
    return () => {
      window.removeEventListener("pointermove", handlePointerMove);
      window.removeEventListener("pointerup", handlePointerUp);
    };
  }, [handlePointerMove, handlePointerUp]);

  return (
    <div
      style={{
        position: "fixed",
        top: position.top,
        left: position.left,
        zIndex: 10000000,
        touchAction: "none",
      }}
      className="h-fit w-fit cursor-move select-none rounded-full border border-teal-450 bg-yellow-300 p-4 transition-all hover:scale-150 hover:border-green-700"
      onPointerDown={handlePointerDown}
    >
      <LucideShoppingBag className="relative" />
      {cartItems && cartItems.items.length > 0 && (
        <span className="absolute right-0 top-0 z-10 h-fit w-fit rounded-full bg-white px-2.5 text-lg font-bold">
          {cartItems.items.length}
        </span>
      )}
    </div>
  );
};

export default FloatingCartIndicator;
