"use client";

import Image from "next/image";
import { StarIcon } from "lucide-react";
import { useQueryState } from "nuqs";

import { Input } from "@acme/ui/components/ui/input";

import type { Admin, OrderDetail } from "~/app/lib/types";
import { DEFAULT_PROFILE_IMAGE } from "~/app/lib/constants";
import { ORDER_DETAILS_ADMIN_CHAT_PARAM_NAME } from "~/app/lib/param-names";
import { KabadiwalaProfile } from "./agent-card";

interface AdminCardProps {
  orderId?: string;
  admin: Admin;
  status: OrderDetail["status"];
}
const AdminCard = ({ admin, status, orderId }: AdminCardProps) => {
  const [, setOrderId] = useQueryState(ORDER_DETAILS_ADMIN_CHAT_PARAM_NAME, {
    clearOnDefault: true,
  });

  const handleOpenChat = async () => {
    await setOrderId(orderId ?? null);
  };

  return (
    <div className="flex w-full flex-col gap-5 rounded-[20px] border-[1.2px] border-black-50 px-4 py-5">
      <div className="flex w-full flex-row items-center justify-between gap-[14px]">
        <KabadiwalaProfile
          profileImageUrl={admin.image ?? DEFAULT_PROFILE_IMAGE}
          rating={100}
        />
        <div className="flex flex-1 flex-col">
          <div className="flex flex-row justify-between">
            <p className="font-inter text-base font-semibold text-black-900">
              {admin.name}
            </p>
          </div>
          <p className="text-xs text-black-400">{admin.name}</p>
        </div>
      </div>

      {status === "ACTIVE" && (
        <div>
          <Input
            placeholder="Message Agent..."
            className="flex-1 rounded-full border border-yellow-600 bg-yellow-0"
            disabled={!orderId}
            onClick={handleOpenChat}
          />
        </div>
      )}
    </div>
  );
};
export default AdminCard;

export const AdminProfile = ({
  profileImageUrl,
  rating,
}: {
  profileImageUrl: string;
  rating: number;
}) => {
  return (
    <div className="relative h-fit w-fit">
      <div className="h-fit w-fit rounded-full border-2 border-yellow-500 p-1">
        <Image
          height={100}
          width={100}
          alt="profile image"
          className="size-9 shrink-0 rounded-full"
          src={profileImageUrl}
          unoptimized
        />
      </div>
      <div className="absolute bottom-0 right-1 flex w-fit flex-row gap-1 rounded-2xl bg-yellow-900 px-[6px] py-[3px]">
        <StarIcon className="size-2" fill="#fbcf04" color="#fbcf04" />
        <p className="font-inter text-[10px] font-medium leading-[10px] text-black-0">
          {rating}
        </p>
      </div>
    </div>
  );
};
