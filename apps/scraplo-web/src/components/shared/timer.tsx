"use client";

import { memo, useEffect, useState } from "react";

interface TimerProps {
  expiryDate?: Date;
  onExpire?: () => Promise<void>;
}

const Timer = memo(function Timer({ expiryDate, onExpire }: TimerProps) {
  const [timeLeft, setTimeLeft] = useState<number>(0);

  useEffect(() => {
    const calculateTimeLeft = () => {
      const difference = (expiryDate?.getTime() ?? 0) - new Date().getTime();
      return difference > 0 ? Math.floor(difference / 1000) : 0;
    };

    setTimeLeft(calculateTimeLeft());

    const timer = setInterval(() => {
      const remaining = calculateTimeLeft();
      setTimeLeft(remaining);

      if (remaining <= 0) {
        clearInterval(timer);
        void onExpire?.();
      }
    }, 1000);

    return () => clearInterval(timer);
  }, [expiryDate, onExpire]);

  const getTimeDisplay = () => {
    if (timeLeft >= 60) {
      // More than or equal to 1 minute
      const minutes = Math.floor(timeLeft / 60);
      const seconds = timeLeft % 60;
      return {
        value: `${minutes}:${String(seconds).padStart(2, "0")}`,
        unit: "min",
      };
    } else {
      // Less than 1 minute
      return {
        value: String(timeLeft),
        unit: "sec",
      };
    }
  };

  const timeDisplay = getTimeDisplay();

  return (
    <>
      {timeLeft > 0 && (
        <div className="flex items-center gap-1">
          <p className="font-bold">{timeDisplay.value}</p>
          <p>{timeDisplay.unit}</p>
        </div>
      )}
    </>
  );
});

export default Timer;
