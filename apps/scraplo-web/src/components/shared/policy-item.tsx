interface PolicyItemProps {
  title: string;
  description: string;
}

const PolicyItem = ({ title, description }: PolicyItemProps) => {
  return (
    <div>
      <h2 className="mb-2.5 font-jakarta text-xl font-semibold capitalize leading-[30px] text-teal-850 xl:text-2xl xl:leading-9">
        {title}
      </h2>
      <p className="leading-7 text-black-800 xl:text-lg xl:leading-8">
        {description}
      </p>
    </div>
  );
};

export default PolicyItem;
