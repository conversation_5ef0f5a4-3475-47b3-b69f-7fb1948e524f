import { cn } from "@acme/ui/lib/utils";

interface StepIndicatorProps {
  currentStep: number;
  totalStepsCount: number;
}

const StepIndicator = ({
  currentStep,
  totalStepsCount,
}: StepIndicatorProps) => {
  return (
    <div className="flex items-center gap-2">
      {Array.from({ length: totalStepsCount }, (_, index) => {
        const isActive = index + 1 === currentStep;
        const isFirst = index === 0;
        const isLast = index === totalStepsCount - 1;

        return (
          <div
            key={index}
            className={cn(
              "h-1.5 rounded-full transition-all duration-300 ease-in-out md:h-2.5", // Base style + transition
              isActive ? "bg-black-800" : "bg-black-150", // Color
              // Width logic
              isActive
                ? "w-[30px] md:w-[60px]" // Active is largest
                : isLast
                  ? "w-1.5 md:w-2.5" // Last inactive is smallest
                  : isFirst && currentStep === totalStepsCount
                    ? "w-1.5 md:w-2.5" // First inactive is smallest ONLY if last is active
                    : "w-4 md:w-8", // All other inactive (first or middle) are medium
            )}
          />
        );
      })}
    </div>
  );
};

export default StepIndicator;
