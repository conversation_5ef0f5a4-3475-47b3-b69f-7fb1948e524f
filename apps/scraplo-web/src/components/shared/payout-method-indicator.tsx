"use client";

import Image from "next/image";
import { useQueryState } from "nuqs";

import { Button } from "@acme/ui/components/ui/button";

import { PAYOUT_METHOD_SHEET_PARAM_NAME } from "~/app/lib/param-names";

interface PayoutMethodIndicatorProps {
  payoutMethod: {
    id: string;
    type: "UPI" | "BANK_ACCOUNT";
    upiId?: string | null;
    bankName?: string | null;
    accountNumber?: string | null;
    isDefault: boolean | null;
  } | null;
}

const PayoutMethodIndicator = ({
  payoutMethod,
}: PayoutMethodIndicatorProps) => {
  const [, setPayoutMethodSheet] = useQueryState(
    PAYOUT_METHOD_SHEET_PARAM_NAME,
    { clearOnDefault: true },
  );

  return (
    <div className="rounded-lg border-[1.2px] border-black-150 p-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Image
            src={
              payoutMethod?.type === "UPI"
                ? "/static/icons/link-upi.svg"
                : payoutMethod?.type === "BANK_ACCOUNT"
                  ? "/static/icons/net-banking.svg"
                  : "/static/icons/net-banking.svg" // Default fallback
            }
            alt={payoutMethod?.type ?? "payment"}
            height={24}
            width={24}
            className="size-6 object-cover"
            unoptimized
          />
          <div>
            <h3 className="font-medium text-black-900">Payout Method</h3>
            {payoutMethod ? (
              <p className="text-sm text-black-600">
                {payoutMethod.type === "UPI"
                  ? payoutMethod.upiId
                  : `${payoutMethod.bankName} - ****${payoutMethod.accountNumber?.slice(-4)}`}
              </p>
            ) : (
              <p className="text-sm text-black-500">Not selected</p>
            )}
          </div>
        </div>

        <Button size="sm" onClick={() => setPayoutMethodSheet("yes")}>
          {payoutMethod ? "Change" : "Select"}
        </Button>
      </div>
    </div>
  );
};

export default PayoutMethodIndicator;
