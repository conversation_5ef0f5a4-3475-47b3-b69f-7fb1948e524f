"use client";

import Image from "next/image";
import Link from "next/link";
import { useQueryState } from "nuqs";

import { But<PERSON> } from "@acme/ui/components/ui/button";
import { cn } from "@acme/ui/lib/utils";

import type {
  CartItem,
  SubCategoriesWithOrWithoutParent,
} from "~/app/lib/types";
import { ADD_ITEM_TO_CART_SHEET_PARAM_NAME } from "~/app/lib/param-names";

interface BrowseCategoryCardProps {
  category: SubCategoriesWithOrWithoutParent["parent"];
  variant: "add-to-cart" | "category-card";
  parentName?: string;
  cartItem?: CartItem;
  totalSubCategories?: number;
}

const BrowseCategoryCard = ({
  category,
  variant,
  parentName,
  cartItem,
  totalSubCategories,
}: BrowseCategoryCardProps) => {
  const [, setOpen] = useQueryState(ADD_ITEM_TO_CART_SHEET_PARAM_NAME, {
    clearOnDefault: true,
  });

  if (!category) return null;

  const isInCart = !!cartItem;

  return (
    <div className="group flex gap-4 rounded-xl border border-black-100 bg-white p-4 shadow-sm transition-all duration-300 hover:border-teal-300 hover:shadow-lg md:gap-6 md:p-6 xl:gap-8 xl:p-8">
      <div className="relative aspect-square w-[100px] overflow-hidden rounded-xl md:w-[140px] xl:w-[180px]">
        <Image
          src={category.image}
          alt={category.name}
          fill
          className="object-cover transition-transform duration-300 group-hover:scale-105"
          unoptimized
        />
      </div>

      <div className="flex flex-1 flex-col justify-between gap-4 md:gap-6">
        <div className="space-y-3 md:space-y-4">
          <h3 className="font-jakarta text-lg font-bold leading-tight text-black-800 md:text-xl xl:text-2xl">
            {category.name}
          </h3>

          <div className="flex flex-col gap-2 md:flex-row md:items-center md:gap-3">
            <div className="inline-flex w-fit items-center rounded-lg bg-yellow-100 px-3 py-2 text-xs font-semibold text-yellow-800 md:text-sm">
              {parentName}
            </div>
            <div className="inline-flex w-fit items-center rounded-lg bg-teal-50 px-3 py-2 text-xs font-semibold text-teal-800 md:text-sm">
              {variant === "add-to-cart"
                ? `₹${category.rate}/${category.rateType === "PER_ITEM" ? "item" : "kg"}`
                : `${totalSubCategories} Sub Categories`}
            </div>
          </div>
        </div>

        {variant === "category-card" && (
          <Button
            className="h-12 w-full bg-teal-600 font-jakarta text-sm font-semibold text-white transition-colors hover:bg-teal-700 md:h-14 md:text-base"
            asChild
          >
            <Link href={`/categories/${category.id}`}>Explore Categories</Link>
          </Button>
        )}

        {variant === "add-to-cart" && (
          <Button
            className={cn(
              "h-12 w-full font-jakarta text-sm font-semibold transition-all duration-300 md:h-14 md:text-base",
              isInCart
                ? "bg-teal-700 text-white hover:bg-teal-800"
                : "bg-yellow-400 text-black-900 hover:bg-yellow-500",
            )}
            onClick={() => setOpen(category.id)}
          >
            {isInCart ? "✓ Added to Cart" : "Add to Cart"}
          </Button>
        )}
      </div>
    </div>
  );
};

export default BrowseCategoryCard;
