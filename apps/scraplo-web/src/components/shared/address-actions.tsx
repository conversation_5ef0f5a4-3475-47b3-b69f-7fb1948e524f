"use client";

import { useRouter } from "next/navigation";
import { useMutation } from "@tanstack/react-query";
import {
  <PERSON>C<PERSON>cle2,
  Loader2,
  MoreHorizontal,
  SquarePen,
  Trash,
} from "lucide-react";
import { toast } from "sonner";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@acme/ui/components/ui/dropdown-menu";

import type { AllSavedAddress } from "~/app/lib/types";
import { useTRPC } from "~/trpc/react";

interface AddressActionsProps {
  address: AllSavedAddress["addresses"][number];
}

const AddressActions = ({ address }: AddressActionsProps) => {
  const router = useRouter();
  const trpc = useTRPC();

  const { mutate: updateDefaultAddress, isPending } = useMutation(
    trpc.address.updateDefaultAddress.mutationOptions({
      onSuccess: (opts) => {
        router.refresh();
        toast.success(opts.message);
      },
      onError: (opts) => {
        toast.error(opts.message);
      },
    }),
  );

  const { mutate: deleteAddress, isPending: isDeleting } = useMutation(
    trpc.address.deleteAddress.mutationOptions({
      onSuccess: (opts) => {
        router.refresh();
        toast.success(opts.message);
      },
      onError: (opts) => {
        toast.error(opts.message);
      },
    }),
  );

  const handleDefaultAddress = () => {
    updateDefaultAddress({ addressId: address.id });
  };

  const handleUpdateAddress = () => {
    router.push(`/add-address?addressId=${address.id}`);
  };

  const handleDeleteAddress = () => {
    deleteAddress({ addressId: address.id });
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger>
        <MoreHorizontal />
      </DropdownMenuTrigger>
      <DropdownMenuContent side="bottom" align="end">
        {!address.isDefault && (
          <DropdownMenuItem
            className="flex items-center gap-2"
            onClick={handleDefaultAddress}
          >
            {isPending ? (
              <Loader2 className="h-4 w-4" />
            ) : (
              <CheckCircle2 className="h-4 w-4" />
            )}
            Mark Default
          </DropdownMenuItem>
        )}
        <DropdownMenuItem
          className="flex items-center gap-2"
          onClick={handleUpdateAddress}
        >
          <SquarePen className="h-4 w-4" />
          Update
        </DropdownMenuItem>
        <DropdownMenuItem
          className="flex items-center gap-2 text-destructive"
          onClick={handleDeleteAddress}
        >
          {isDeleting ? (
            <Loader2 className="h-4 w-4" />
          ) : (
            <Trash className="h-4 w-4" />
          )}
          Delete
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default AddressActions;
