import Image from "next/image";
import Link from "next/link";

import { cn } from "@acme/ui/lib/utils";

import type { TopLevelCategories } from "~/app/lib/types";

const CategoryCard = ({
  category,
  className,
}: {
  category: TopLevelCategories[number];
  className?: string;
}) => {
  return (
    <Link
      href={`/categories/${category.id}`}
      className={cn(
        "group flex w-full flex-col overflow-hidden rounded-xl border border-black-100 bg-white shadow-sm transition-all duration-300 hover:scale-[1.02] hover:border-teal-300 hover:shadow-lg",
        className,
      )}
    >
      <div className="relative aspect-[4/3] w-full overflow-hidden">
        <Image
          src={category.image}
          alt={category.name}
          fill
          className="object-cover transition-transform duration-300 group-hover:scale-105"
          unoptimized
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black-900/10 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100" />
      </div>
      <div className="flex flex-1 items-center justify-center p-3 md:p-4 xl:p-5">
        <p className="text-center font-jakarta text-sm font-semibold leading-tight text-black-800 transition-colors duration-300 group-hover:text-teal-700 md:text-base xl:text-lg">
          {category.name}
        </p>
      </div>
    </Link>
  );
};

export default CategoryCard;
