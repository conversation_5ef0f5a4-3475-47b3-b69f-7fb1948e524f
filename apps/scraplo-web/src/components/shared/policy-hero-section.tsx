import Image from "next/image";
import { format } from "date-fns";

interface PolicyHeroSectionProps {
  title: string;
  description: string;
  lastUpdatedAt: Date;
}

const PolicyHeroSection = ({
  title,
  description,
  lastUpdatedAt,
}: PolicyHeroSectionProps) => {
  return (
    <div className="flex flex-col items-center gap-6 bg-[linear-gradient(3deg,rgba(255,253,247,0.00)_2.31%,rgba(255,244,223,0.50)_98.09%)] px-6 py-10 md:px-14 xl:px-[120px]">
      <div className="flex flex-col gap-1.5">
        <h1 className="xl:text-9 text-center font-jakarta text-[30px] font-extrabold leading-[38px] -tracking-[0.3px] text-teal-800 xl:leading-[48px] xl:-tracking-[0.36px]">
          {title}
        </h1>
        <p className="text-center text-sm leading-6 text-black-600 md:text-base xl:text-lg xl:leading-[30px]">
          {description}
        </p>
      </div>
      <p className="flex w-fit items-center justify-center gap-3 rounded-[6px] bg-yellow-50 px-3 py-2 font-jakarta text-sm font-medium leading-5 text-black-600 xl:text-base xl:leading-6">
        <div className="relative aspect-square size-4 flex-shrink-0 xl:size-[18px]">
          <Image
            src="/static/icons/filled-check.svg"
            alt="check"
            fill
            className="object-contain"
            unoptimized
          />
        </div>
        Last updated on {format(lastUpdatedAt, "d MMM yyyy")}
      </p>
    </div>
  );
};

export default PolicyHeroSection;
