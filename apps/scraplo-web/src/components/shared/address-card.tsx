import Image from "next/image";

import { cn } from "@acme/ui/lib/utils";

import type { AllSavedAddress } from "~/app/lib/types";
import AddressActions from "./address-actions";

type AddressCardProps = AllSavedAddress["addresses"][number];

const AddressCard = (props: AddressCardProps) => {
  const iconPath =
    props.addressType === "HOME"
      ? "/static/icons/home-2.svg"
      : props.addressType === "WORK"
        ? "/static/icons/work.svg"
        : "/static/icons/location-3.svg";

  return (
    <div
      className={cn(
        "flex flex-col gap-2 rounded-[10px] bg-black-0 px-3 py-[14px] md:px-5 md:py-6 xl:p-8",
        props.isDefault && "bg-yellow-50",
      )}
    >
      <div className="flex items-center justify-between">
        <div className="relative aspect-square size-5 md:size-[30px] xl:size-[42px]">
          <Image
            src={iconPath}
            alt={props.name}
            fill
            className="object-cover"
            unoptimized
          />
        </div>

        <div className="flex items-center gap-2">
          {props.isDefault && (
            <span className="rounded-full bg-[#EBFFEE] px-2 py-1 text-[9px] font-medium leading-3 -tracking-[0.045px] text-teal-900 md:text-xs xl:text-sm xl:leading-8">
              Currently used
            </span>
          )}
          <AddressActions address={props} />
        </div>
      </div>

      <div className="flex flex-col gap-1">
        <p className="font-jakarta text-[15px] font-bold leading-6 -tracking-[0.15px] text-black-800 md:text-[22px] md:leading-8">
          {props.name}
        </p>
        <p className="line-clamp-3 max-w-[90%] text-xs leading-[18px] text-black-800 md:text-sm md:leading-5 xl:text-lg xl:leading-7">
          {props.display}, {props.city}, {props.state}
        </p>
      </div>
    </div>
  );
};

export default AddressCard;
