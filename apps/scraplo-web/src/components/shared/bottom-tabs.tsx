"use client";

import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useQuery } from "@tanstack/react-query";

import { cn } from "@acme/ui/lib/utils";

import { BOTTOM_TABS } from "~/app/lib/constants";
import { useTRPC } from "~/trpc/react";

const BottomTabs = () => {
  const pathName = usePathname();

  const trpc = useTRPC();
  const { data: orderNotification } = useQuery(
    trpc.notification.getOrderNotificationCount.queryOptions(),
  );

  const { data: cartNotification } = useQuery(
    trpc.notification.getCartNotificationCount.queryOptions(),
  );

  return (
    <footer className="fixed bottom-0 flex w-full items-center gap-2 border-t-2 border-black-50 bg-[rgba(255,_255,_255,_0.80)] px-6 backdrop-blur-[14px] md:hidden">
      {BOTTOM_TABS.map((item) => {
        const isActive = pathName === item.path;

        return (
          <Link
            href={item.path}
            key={item.title}
            className="relative flex flex-[33%] flex-col items-center justify-center gap-2 py-[14px]"
          >
            <div
              className={cn(
                "p-1.5",
                isActive && "rounded-full bg-yellow-450 px-4 py-2",
              )}
            >
              <div
                className={cn(
                  "relative aspect-square size-6",
                  isActive && "size-5",
                )}
              >
                <Image
                  src={item.icon}
                  alt={item.title}
                  fill
                  className="object-cover"
                  unoptimized
                />
              </div>
            </div>
            <span
              className={cn(
                "font-jakarta text-[13px] font-semibold leading-[18px] tracking-[0.13px] text-black-600",
                isActive && "text-black-900",
              )}
            >
              {item.title}
            </span>

            {orderNotification?.count &&
            orderNotification.count >= 1 &&
            item.path === "/account" ? (
              <NotificationDot count={orderNotification.count} />
            ) : null}

            {cartNotification?.count &&
            cartNotification.count &&
            item.path === "/cart" ? (
              <NotificationDot count={cartNotification.count} />
            ) : null}
          </Link>
        );
      })}
    </footer>
  );
};

export default BottomTabs;

const NotificationDot = ({ count }: { count?: number }) => {
  return (
    <span className="absolute right-8 top-3 flex aspect-square min-h-3 min-w-3 items-center justify-center rounded-full bg-teal-750 p-2 text-[9px] font-semibold leading-3 text-white">
      {count}
    </span>
  );
};
