"use client";

import { Suspense, useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import {
  skipToken,
  useMutation,
  useQuery,
  useQueryClient,
  useSuspenseQuery,
} from "@tanstack/react-query";
import { ArrowRight, ChevronDown, ChevronUp, Loader2, X } from "lucide-react";
import { useQueryState } from "nuqs";
import { toast } from "sonner";

import { Button } from "@acme/ui/components/ui/button";
import { Sheet, SheetContent } from "@acme/ui/components/ui/sheet";
import { useIsMobile } from "@acme/ui/hooks/use-mobile";
import { cn } from "@acme/ui/lib/utils";

import type { UserAddress } from "~/app/lib/types";
import { ADD_ITEM_TO_CART_SHEET_PARAM_NAME } from "~/app/lib/param-names";
import { useUserLocationStore } from "~/stores/user-store";
import { useTRPC } from "~/trpc/react";
import CartItem from "./cart-item";

const AddUpdateItemToCartSheet = () => {
  const trpc = useTRPC();
  const isMobile = useIsMobile();
  const queryClient = useQueryClient();
  const router = useRouter();
  const { userLocation, setUserLocation } = useUserLocationStore();

  const { data: savedAddresses } = useSuspenseQuery(
    trpc.address.getAllSavedAddresses.queryOptions(),
  );
  useEffect(() => {
    const defaultAddress = savedAddresses.addresses.find(
      (address) => address.isDefault,
    );
    if (defaultAddress) {
      setUserLocation(defaultAddress as UserAddress);
    }
  }, [savedAddresses, setUserLocation]);
  const [fullScreen, setFullScreen] = useState(false);
  const [quantity, setQuantity] = useState(1);
  const [categoryId, setCategoryId] = useQueryState(
    ADD_ITEM_TO_CART_SHEET_PARAM_NAME,
    {
      clearOnDefault: true,
    },
  );

  const { data, isPending } = useQuery(
    trpc.category.getCategoryById.queryOptions(
      categoryId ? { id: categoryId } : skipToken,
    ),
  );

  const { data: cartStats, isPending: isCartStatsPending } = useQuery(
    trpc.order.getCurrentCartStatistics.queryOptions(undefined, {
      staleTime: 0,
      refetchOnMount: true,
      refetchOnWindowFocus: true,
      refetchOnReconnect: true,
    }),
  );

  const { data: cartItem, isPending: isCartItemPending } = useQuery(
    trpc.order.getCartItemByCategoryId.queryOptions(
      categoryId ? { categoryId } : skipToken,
      {
        staleTime: 0,
        refetchOnMount: true,
        refetchOnWindowFocus: true,
        refetchOnReconnect: true,
      },
    ),
  );

  const { mutate: handleAddUpdateItemToCart, isPending: isAddingToCart } =
    useMutation(
      trpc.order.handleAddUpdateItemToCart.mutationOptions({
        onSuccess: async (opts) => {
          router.refresh();
          await Promise.allSettled([
            queryClient.invalidateQueries(
              trpc.order.getCurrentCartStatistics.queryFilter(),
            ),
            await queryClient.invalidateQueries(
              trpc.order.getCurrentCart.queryOptions(),
            ),
            queryClient.invalidateQueries(
              trpc.order.getCurrentCart.queryFilter(),
            ),
            queryClient.invalidateQueries(
              trpc.order.getCartItemByCategoryId.queryFilter(),
            ),
            queryClient.invalidateQueries(
              trpc.category.getSubCategoriesWithOrWithoutParent.queryFilter(),
            ),
            queryClient.invalidateQueries(
              trpc.order.getAllCartItemsMap.queryFilter(),
            ),
          ]);

          toast.success(opts.message);

          setQuantity(1);

          void setCategoryId(null);
        },
        onError: (opts) => {
          toast.error(opts.message);
        },
      }),
    );

  useEffect(() => {
    if (cartItem?.quantity) {
      setQuantity(Number(cartItem.quantity));
    } else {
      setQuantity(1);
    }
  }, [cartItem]);

  const handleAddToCart = () => {
    if (!categoryId) return;

    if (quantity <= 0) {
      toast.error("Quantity should be greater than 0");
      return;
    }

    if (!userLocation) {
      toast.error("Please select a pickup location");
      return;
    }

    handleAddUpdateItemToCart({
      categoryId,
      quantity,
    });
  };

  const handleSheetClose = async () => {
    if (!isAddingToCart) {
      await setCategoryId(null);
    }
  };

  // Loading state
  if (isPending || isCartStatsPending || isCartItemPending) {
    return (
      <Sheet
        open={!!categoryId}
        onOpenChange={(val) =>
          val ? setCategoryId("yes") : setCategoryId(null)
        }
      >
        <SheetContent
          side={isMobile ? "bottom" : "right"}
          className="flex min-h-[480px] items-center justify-center rounded-t-3xl lg:min-w-[600px] xl:min-w-[862px] xl:text-2xl"
          hideCloseButton
        >
          <Loader2 className="h-6 w-6 animate-spin" />
        </SheetContent>
      </Sheet>
    );
  }

  // Category not found
  if (!data?.category) {
    return (
      <Sheet
        open={!!categoryId}
        onOpenChange={(val) =>
          val ? setCategoryId("yes") : setCategoryId(null)
        }
      >
        <SheetContent
          side={isMobile ? "bottom" : "right"}
          className="flex min-h-[480px] items-center justify-center rounded-t-3xl xl:min-w-[862px] xl:text-2xl"
          hideCloseButton
        >
          Category Not Found
        </SheetContent>
      </Sheet>
    );
  }

  const isInCart = !!cartItem;
  const categoryRate = Number(data.category.rate) || 0;
  const newItemPrice = categoryRate * quantity;

  const calculatePrices = () => {
    const cartTotalEstimate = Number(cartStats?.estimatedPrice) || 0;

    // If item exists in cart, subtract its current price from total
    let cartBaseTotal = cartTotalEstimate;
    if (isInCart) {
      const currentItemPrice = categoryRate * Number(cartItem.quantity);
      cartBaseTotal = cartTotalEstimate - currentItemPrice;
    }

    const total = cartBaseTotal + newItemPrice;

    return {
      cartBaseTotal: parseFloat(cartBaseTotal.toFixed(2)),
      itemPrice: parseFloat(newItemPrice.toFixed(2)),
      totalPrice: parseFloat(total.toFixed(2)),
    };
  };

  const { cartBaseTotal, itemPrice, totalPrice } = calculatePrices();

  return (
    <Sheet
      open={!!categoryId}
      onOpenChange={async (open) => {
        if (!open && !isAddingToCart) await handleSheetClose();
      }}
    >
      <SheetContent
        side={isMobile ? "bottom" : "right"}
        className={cn(
          "flex min-h-[480px] flex-col gap-6 rounded-t-3xl p-0 transition-all duration-500 md:min-h-[580px] md:min-w-[600px] md:gap-9 lg:min-w-[600px] xl:min-h-full xl:min-w-[862px] xl:rounded-l-3xl",
          fullScreen && "min-h-full",
        )}
        hideCloseButton
      >
        {/* Header */}
        <div className="flex items-center justify-between border-b-[1.2px] border-b-black-50 px-6 pb-3 pt-6 md:px-14 md:pb-5 md:pt-8">
          <div className="flex items-center gap-3">
            <Button
              className="rounded-xl bg-yellow-450 p-2"
              onClick={() => setFullScreen((prev) => !prev)}
            >
              {fullScreen ? (
                <ChevronDown className="size-5 md:size-6" />
              ) : (
                <ChevronUp className="size-5 md:size-6" />
              )}
            </Button>

            <div>
              <h2 className="font-bold leading-7 text-black-900 md:text-lg md:leading-[30px]">
                {isInCart ? "Update" : "Add"} Quantity
              </h2>
              <p className="text-sm leading-5 text-black-500 md:text-base md:leading-6">
                {isInCart ? "update" : "add"} quantity of selected item
              </p>
            </div>
          </div>

          <Button
            className="rounded-full bg-black-50 p-1.5"
            onClick={handleSheetClose}
            disabled={isAddingToCart}
          >
            <X className="size-5 md:size-[25px]" />
          </Button>
        </div>

        {/* Cart Item */}
        <div className="mx-6 md:mx-14">
          <CartItem
            category={data.category}
            cartItem={cartItem}
            showRemoveButton={isInCart}
            onQuantityChange={setQuantity}
          />
        </div>

        {/* Price summary */}
        <div className="fixed bottom-0 w-full xl:static">
          {/* Price summary */}
          <div className="flex items-center justify-between rounded-t-2xl bg-yellow-150 px-6 py-3 md:px-14 md:py-4">
            <span className="flex-1 text-[13px] font-semibold leading-5 text-yellow-950 underline decoration-dotted underline-offset-2 xl:text-base">
              Estimated Price
            </span>

            <div className="flex items-center gap-1">
              {cartBaseTotal > 0 && (
                <>
                  <span className="font-jakarta text-xs font-medium leading-6 -tracking-[0.16px] text-black-900 xl:text-xl">
                    {cartBaseTotal}₹
                  </span>
                  +
                </>
              )}
              <span className="font-jakarta text-xs font-medium leading-6 -tracking-[0.16px] text-black-900 xl:text-xl">
                ₹ {itemPrice}
              </span>
              =
              <span className="font-jakarta text-xs font-extrabold leading-6 -tracking-[0.16px] text-black-900 xl:text-xl">
                ₹ {totalPrice}
              </span>
            </div>
          </div>

          {/* Action button */}
          <div className="bg-[rgba(255,_255,_255,_0.30)] px-6 py-4 pb-10 backdrop-blur-[15px] md:px-14 md:pb-14 md:pt-6">
            <Button
              className="group flex w-full items-center justify-between rounded-lg px-3 py-[14px]"
              onClick={handleAddToCart}
              disabled={isAddingToCart}
            >
              <span className="font-jakarta text-sm font-medium leading-5 -tracking-[0.14px] text-black-900 active:text-black-0 group-hover:text-black-0 xl:text-base">
                {cartStats?.numberOfItems} Item added
              </span>

              <span className="flex items-center gap-2 font-jakarta text-sm font-medium -tracking-[0.14px] text-black-900 active:text-black-0 group-hover:text-black-0 xl:text-base">
                {isAddingToCart ? (
                  <>
                    {isInCart ? "Updating" : "Adding"}{" "}
                    <Loader2 className="ml-2 h-4 w-4 animate-spin xl:size-5" />
                  </>
                ) : (
                  <>
                    {isInCart ? "Update Cart" : "Add to Cart"}
                    <ArrowRight className="size-4 xl:size-5" />
                  </>
                )}
              </span>
            </Button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
};

const AddUpdateItemToCartSheetWrapper = () => {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <AddUpdateItemToCartSheet />
    </Suspense>
  );
};

export default AddUpdateItemToCartSheetWrapper;
