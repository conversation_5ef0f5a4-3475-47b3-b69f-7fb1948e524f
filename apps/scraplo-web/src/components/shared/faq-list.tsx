"use client";

import Link from "next/link";
import { useQuery } from "@tanstack/react-query";
import { ChevronRight } from "lucide-react";

import { Editor } from "@acme/ui/editor";

import Error from "~/components/shared/error";
import FaqListSkeleton from "~/components/skeletons/faq-list-skeleton";
import { useTRPC } from "~/trpc/react";

const FaqList = () => {
  const trpc = useTRPC();
  const {
    data: faqs,
    isPending,
    isError,
  } = useQuery(trpc.faq.getTopLevelQuestions.queryOptions());

  if (isPending) {
    return <FaqListSkeleton />;
  }

  if (isError) {
    return <Error message="Failed to load FAQs" />;
  }

  return (
    <>
      {faqs.map((item) => (
        <Link
          key={item.id}
          href={`/profile/help-and-support/${item.id}`}
          className="flex items-center justify-between gap-[18px] border-b border-b-black-150 py-2"
        >
          <div className="space-y-1">
            <p className="font-jakarta text-sm font-semibold leading-[22px] text-black-700 md:text-base xl:text-lg xl:leading-6">
              {item.content}
            </p>
            <span className="font-jakarta text-xs leading-[18px] text-black-500 md:text-sm xl:text-base xl:leading-5">
              <Editor
                editorSerializedState={item.answers[0]?.content}
                editable={false}
              />
            </span>
          </div>
          <div>
            <ChevronRight className="size-4 xl:size-5" />
          </div>
        </Link>
      ))}
    </>
  );
};

export default FaqList;
