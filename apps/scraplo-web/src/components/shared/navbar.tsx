"use client";

import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";

import { cn } from "@acme/ui/lib/utils";

import { BOTTOM_TABS } from "~/app/lib/constants";

const Navbar = () => {
  const pathName = usePathname();

  return (
    <nav className="sticky top-0 z-50 flex w-full items-center justify-between border-b-2 border-b-black-150 px-6 pb-3 pt-5 backdrop-blur-[15px] md:px-14 xl:px-[120px]">
      <Link
        href="/"
        className="relative aspect-[83/44] w-[83px] md:aspect-[53/28] md:w-[106px] xl:aspect-[121/64] xl:w-[121px]"
      >
        <Image
          src="/static/logo/scraplo.svg"
          alt="logo"
          fill
          className="object-cover"
          unoptimized
        />
      </Link>

      <div className="flex items-center gap-5">
        <div className="hidden items-center gap-6 md:flex">
          {BOTTOM_TABS.map((item) => (
            <Link
              href={item.path}
              key={item.path}
              className={cn(
                "rounded-[10px] px-4 py-1.5 text-[13px] font-semibold leading-[18px] tracking-[0.13px] text-black-900 xl:text-base xl:tracking-[0.16px]",
                item.path === pathName && "bg-yellow-450",
              )}
            >
              {item.title}
            </Link>
          ))}
        </div>

        <Link href="/categories">
          <button className="relative aspect-square size-11 rounded-xl bg-black-0 md:size-[52px] xl:size-[56px]">
            <Image
              src="/static/icons/search.svg"
              alt="search"
              fill
              className="object-cover p-3 md:p-[14px] xl:p-4"
              unoptimized
            />
          </button>
        </Link>

        <Link href="/account">
          <button className="relative aspect-square size-11 rounded-xl bg-black-0 md:size-[52px] xl:size-[56px]">
            <Image
              src="/static/icons/menu.svg"
              alt="menu"
              fill
              className="object-cover p-3 md:p-[14px] xl:p-4"
              unoptimized
            />
          </button>
        </Link>
      </div>
    </nav>
  );
};

export default Navbar;
