import Link from "next/link";
import { ArrowR<PERSON> } from "lucide-react";

import { <PERSON><PERSON> } from "@acme/ui/components/ui/button";
import { cn } from "@acme/ui/lib/utils";

interface CTAProps {
  className?: string;
  innerContainerClassName?: string;
  titleClassName?: string;
  descriptionClassName?: string;
  buttonClassName?: string;
}

const CTA = ({
  className,
  innerContainerClassName,
  titleClassName,
  descriptionClassName,
  buttonClassName,
}: CTAProps) => {
  return (
    <section
      className={cn(
        "mx-auto max-w-full bg-cover bg-center bg-no-repeat px-6 md:px-14 xl:px-[120px] xl:py-16",
        className,
      )}
      style={{ backgroundImage: "url('/static/images/cta.png')" }}
    >
      <div
        className={cn(
          "flex flex-col gap-5 py-10 md:flex-row md:items-center md:justify-between md:gap-10",
          innerContainer<PERSON><PERSON>Name,
        )}
      >
        <div className="flex flex-col gap-2">
          <h2
            className={cn(
              "font-jakarta text-xl font-bold leading-8 -tracking-[0.2px] text-black-50 md:text-2xl md:leading-9 md:-tracking-[0.24px] xl:text-[32px] xl:leading-[48px] xl:-tracking-[0.32px]",
              titleClassName,
            )}
          >
            Now scrap pickup made simple
          </h2>
          <p
            className={cn(
              "text-sm leading-6 -tracking-[0.14px] text-black-150 md:text-[18px] md:leading-[30px] md:-tracking-[0.18px] xl:text-2xl xl:leading-8 xl:-tracking-[0.24px]",
              descriptionClassName,
            )}
          >
            Select your address and quickly start your pickup order for scrap
            items.
          </p>
        </div>

        <Button
          className={cn(
            "w-fit rounded-full bg-yellow-400 px-5 py-[14px] font-jakarta text-sm font-medium text-black-900",
            buttonClassName,
          )}
          asChild
        >
          <Link href="/categories">
            List scrap for pickup <ArrowRight className="ml-2 size-4" />
          </Link>
        </Button>
      </div>
    </section>
  );
};

export default CTA;
