import { ArrowR<PERSON>, <PERSON>Alert } from "lucide-react";

import { Button } from "@acme/ui/components/ui/button";
import { cn } from "@acme/ui/lib/utils";

const NeedSupportCard = ({
  onClick,
  title,
  buttonClassName,
}: {
  onClick: () => void;
  title?: string;
  buttonClassName?: string;
}) => {
  return (
    <div className="flex flex-col gap-5 rounded-[20px] bg-yellow-0 px-4 py-5">
      <div className="flex flex-row items-start gap-2 text-[#900B09]">
        <CircleAlert />
        <div>
          <h1 className="font-semibold leading-6 text-[#900B09]">
            Need Support?
          </h1>
          <p className="text-xs text-black-600">
            Take help of customer support to pickup the order easily
          </p>
        </div>
      </div>
      <Button
        className={cn(
          "items-center justify-center border-black-50 bg-[#FBFBFB]",
          buttonClassName,
        )}
        onClick={onClick}
      >
        {title ?? `Contact Support`} <ArrowRight />{" "}
      </Button>
    </div>
  );
};
export default NeedSupportCard;
