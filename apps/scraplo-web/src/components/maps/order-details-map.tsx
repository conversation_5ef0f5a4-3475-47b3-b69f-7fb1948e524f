"use client";

import { APIProvider, Map, Marker } from "@vis.gl/react-google-maps";

import { cn } from "@acme/ui/lib/utils";

import { env } from "~/env";

interface OrderDetailsMapProps {
  pickupLocationCoordinates?: {
    latitude: number;
    longitude: number;
  };
  kabadiwalaLocationCoordinates?: {
    latitude: number;
    longitude: number;
  };
}

// interface DirectionsRendererComponentProps {
//   origin: { latitude: number; longitude: number };
//   destination: { latitude: number; longitude: number };
// }

// const DirectionsRendererComponent = ({
//   origin,
//   destination,
// }: DirectionsRendererComponentProps) => {
//   const map = useMap();
//   const routesLibrary = useMapsLibrary("routes");
//   const [directionsService, setDirectionsService] =
//     useState<google.maps.DirectionsService>();
//   const [directionsRenderer, setDirectionsRenderer] =
//     useState<google.maps.DirectionsRenderer>();
//   const [, setRoutes] = useState<google.maps.DirectionsRoute[]>([]);

//   useEffect(() => {
//     if (!routesLibrary || !map) return;
//     setDirectionsService(new routesLibrary.DirectionsService());
//     setDirectionsRenderer(
//       new routesLibrary.DirectionsRenderer({ map, draggable: false }),
//     );
//   }, [routesLibrary, map]);

//   useEffect(() => {
//     if (!directionsService || !directionsRenderer) return;

//     const request: google.maps.DirectionsRequest = {
//       origin: { lat: origin.latitude, lng: origin.longitude },
//       destination: { lat: destination.latitude, lng: destination.longitude },
//       travelMode: google.maps.TravelMode.DRIVING,
//       provideRouteAlternatives: true,
//     };

//     directionsService
//       .route(request)
//       .then((response) => {
//         directionsRenderer.setDirections(response);
//         setRoutes(response.routes);
//       })
//       .catch((e) => console.error("Directions request failed: ", e));

//     return () => {
//       directionsRenderer.setMap(null);
//     };
//   }, [directionsService, directionsRenderer, origin, destination]);

//   useEffect(() => {
//     if (!directionsRenderer) return;

//     const listener = directionsRenderer.addListener(
//       "directions_changed",
//       () => {
//         const result = directionsRenderer.getDirections();
//         if (result) {
//           setRoutes(result.routes);
//         }
//       },
//     );

//     return () => {
//       google.maps.event.removeListener(listener);
//     };
//   }, [directionsRenderer]);

//   // Cleanup on component unmount
//   useEffect(() => {
//     return () => {
//       if (directionsRenderer) {
//         directionsRenderer.setMap(null);
//       }
//     };
//     // eslint-disable-next-line react-hooks/react-compiler
//     // eslint-disable-next-line react-hooks/exhaustive-deps
//   }, []);

//   return null;
// };

const OrderDetailsMap = ({
  pickupLocationCoordinates,
  kabadiwalaLocationCoordinates,
}: OrderDetailsMapProps) => {
  return (
    <APIProvider apiKey={env.NEXT_PUBLIC_GOOGLE_MAPS_KEY}>
      <Map
        defaultCenter={{
          lat: pickupLocationCoordinates?.latitude ?? 0,
          lng: pickupLocationCoordinates?.longitude ?? 0,
        }}
        defaultZoom={15}
        gestureHandling={"greedy"}
        disableDefaultUI={true}
        className={cn("h-[243px] w-[100vw]")}
      >
        {pickupLocationCoordinates && (
          <Marker
            position={{
              lat: pickupLocationCoordinates.latitude,
              lng: pickupLocationCoordinates.longitude,
            }}
          />
        )}
        {kabadiwalaLocationCoordinates && (
          <Marker
            position={{
              lat: kabadiwalaLocationCoordinates.latitude,
              lng: kabadiwalaLocationCoordinates.longitude,
            }}
          />
        )}
        {/* {pickupLocationCoordinates && kabadiwalaLocationCoordinates && (
          <DirectionsRendererComponent
            origin={pickupLocationCoordinates}
            destination={kabadiwalaLocationCoordinates}
          />
        )} */}
      </Map>
    </APIProvider>
  );
};

export default OrderDetailsMap;
