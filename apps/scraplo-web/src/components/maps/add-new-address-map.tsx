"use client";

import type {
  MapCameraChangedEvent,
  MapCameraProps,
} from "@vis.gl/react-google-maps";
import { useCallback, useEffect, useState } from "react";
import { APIProvider, Map, Marker } from "@vis.gl/react-google-maps";

import { cn } from "@acme/ui/lib/utils";

import { MAP_INITIAL_CAMERA } from "~/app/lib/constants";
import { env } from "~/env";
import {
  useUserConfirmedLocationStore,
  useUserLocationStore,
} from "~/stores/user-store";

interface AddNewAddressMapProps {
  mapClassName?: string;
}

const AddNewAddressMap = ({ mapClassName }: AddNewAddressMapProps) => {
  const { confirmedLocation } = useUserConfirmedLocationStore();
  const { userLocation } = useUserLocationStore();

  const [cameraProps, setCameraProps] =
    useState<MapCameraProps>(MAP_INITIAL_CAMERA);
  const handleCameraChange = useCallback(
    (ev: MapCameraChangedEvent) => setCameraProps(ev.detail),
    [],
  );

  useEffect(() => {
    if (userLocation) {
      setCameraProps({
        center: {
          lat: userLocation.coordinates.latitude,
          lng: userLocation.coordinates.longitude,
        },
        zoom: 15,
      });
    }

    if (confirmedLocation) {
      setCameraProps({
        center: {
          lat: confirmedLocation.coordinates.latitude,
          lng: confirmedLocation.coordinates.longitude,
        },
        zoom: 15,
      });
    }
  }, [userLocation, confirmedLocation]);

  return (
    <>
      <APIProvider apiKey={env.NEXT_PUBLIC_GOOGLE_MAPS_KEY}>
        <Map
          {...cameraProps}
          onCameraChanged={handleCameraChange}
          gestureHandling={"greedy"}
          disableDefaultUI={true}
          className={cn("h-[50vh] w-[100vw]", mapClassName)}
        >
          {userLocation && (
            <Marker
              position={{
                lat: userLocation.coordinates.latitude,
                lng: userLocation.coordinates.longitude,
              }}
            />
          )}

          {confirmedLocation && (
            <Marker
              position={{
                lat: confirmedLocation.coordinates.latitude,
                lng: confirmedLocation.coordinates.longitude,
              }}
            />
          )}
        </Map>
      </APIProvider>
    </>
  );
};

export default AddNewAddressMap;
