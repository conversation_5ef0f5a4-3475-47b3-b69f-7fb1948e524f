import { Separator } from "@acme/ui/components/ui/separator";
import { Skeleton } from "@acme/ui/components/ui/skeleton";

const OrderCardSkeleton = () => {
  return (
    <div className="flex flex-col gap-3 overflow-hidden rounded-xl border-[1.2px] border-black-50 p-4">
      {/* id and status */}
      <div className="flex items-center justify-between gap-4">
        <div>
          <Skeleton className="mb-1 h-5 w-24" />
          <Skeleton className="h-4 w-32" />
        </div>
        <Skeleton className="h-6 w-20" />
      </div>

      <Separator className="bg-black-150" />

      {/* details */}
      <div className="flex flex-col gap-[14px]">
        {/* scrap items */}
        <div className="flex flex-col gap-1">
          <Skeleton className="h-3.5 w-20" />
          <Skeleton className="h-4.5 w-full" />
        </div>

        {/* area */}
        <div className="flex flex-col gap-1">
          <Skeleton className="h-3.5 w-16" />
          <Skeleton className="h-4.5 w-full" />
        </div>

        {/* amount paid */}
        <div className="flex flex-col gap-1">
          <Skeleton className="h-3.5 w-24" />
          <Skeleton className="h-4.5 w-20" />
        </div>
      </div>

      <Separator className="bg-black-150" />

      {/* action buttons */}
      <div className="flex items-center gap-3">
        <Skeleton className="h-9 w-full" />
        <Skeleton className="h-9 w-full" />
      </div>
    </div>
  );
};

export default OrderCardSkeleton;
