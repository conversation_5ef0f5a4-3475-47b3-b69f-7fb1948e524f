import { Skeleton } from "@acme/ui/components/ui/skeleton";

const ProfileDetailsStripSkeleton = () => {
  return (
    <div className="flex items-center gap-2 bg-[rgba(252,_252,_252,_0.40)] px-6 py-4 shadow-md backdrop-blur-[30px] md:px-14 md:py-5">
      {/* profile image skeleton */}
      <div className="relative aspect-square size-12 overflow-hidden rounded-full">
        <Skeleton className="h-12 w-12" />
      </div>

      {/* name and details skeleton */}
      <div className="flex flex-1 flex-col gap-1">
        <Skeleton className="h-4 w-32" />
        <Skeleton className="h-3 w-24" />
      </div>

      {/* edit profile button skeleton */}
      <Skeleton className="h-8 w-20 rounded-lg" />
    </div>
  );
};

export default ProfileDetailsStripSkeleton;
