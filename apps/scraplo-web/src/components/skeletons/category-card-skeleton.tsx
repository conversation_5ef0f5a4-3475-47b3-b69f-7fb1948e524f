import { Skeleton } from "@acme/ui/components/ui/skeleton";

const CategoryCardSkeleton = () => {
  return (
    <div className="flex min-w-[101px] max-w-full flex-col gap-y-1.5 overflow-hidden rounded-[10px] border-[1.2px] border-black-0 md:min-w-[110px] md:max-w-[110px] xl:min-w-[200px] xl:max-w-[200px]">
      {/* Image skeleton */}
      <div className="relative aspect-[101/80] w-full">
        <Skeleton className="h-full w-full" />
      </div>
      {/* Text skeleton */}
      <div className="px-2 pb-2">
        <Skeleton className="h-[18px] w-full rounded" />
      </div>
    </div>
  );
};

export default CategoryCardSkeleton;
