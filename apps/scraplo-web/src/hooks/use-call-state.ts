"use client";

import type { Call } from "@stream-io/video-react-sdk";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { useStreamVideoClient } from "@stream-io/video-react-sdk";
import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";

import { useTRPC } from "~/trpc/react";

export const useCallState = () => {
  const router = useRouter();
  const client = useStreamVideoClient();
  const trpc = useTRPC();

  const [currentCall, setCurrentCall] = useState<Call | null>(null);
  const [isInCall, setIsInCall] = useState(false);

  const { mutateAsync: createCallAsync } = useMutation(
    trpc.streamCall.createCall.mutationOptions(),
  );

  const createCall = async (orderId: string) => {
    if (!client) {
      toast.error("Call service not available");
      return;
    }

    try {
      const { callId: generatedCallId } = await createCallAsync({
        orderId: orderId,
      });

      console.log("Generated call ID:", generatedCallId);

      const call = client.call("default", generatedCallId);
      setCurrentCall(call);

      // Enable microphone by default
      await call.microphone
        .enable()
        .then(async () => {
          console.log("Microphone enabled");
          // Join the call
          await call.join({
            ring: true,
          });
        })
        .catch((err) => {
          console.error("Failed to enable microphone:", err);
          toast.error(
            "Failed to enable microphone. Please check your browser permissions.",
          );
        });

      console.log("Successfully joined call");

      // Navigate to call page
      //   router.push(`/calls/${generatedCallId}`);
    } catch (err) {
      console.error("Failed to create call:", err);
      toast.error(err instanceof Error ? err.message : "Failed to create call");
    }
  };

  const joinCall = (callId: string) => {
    if (!client) {
      toast.error("Call service not available");
      return;
    }

    router.push(`/calls/${callId}`);
  };

  const endCall = () => {
    if (currentCall) {
      currentCall.leave().catch((err) => {
        console.error("Failed to leave call:", err);
        toast.error("Failed to leave call");
      });
      setCurrentCall(null);
      setIsInCall(false);
    }
  };

  const acceptCall = async (call: Call) => {
    try {
      await call.join();
      setIsInCall(true);
      setCurrentCall(call);
    } catch (err) {
      console.error("Failed to accept call:", err);
      toast.error("Failed to accept call");
    }
  };

  const rejectCall = (call: Call) => {
    call.leave({ reject: true, reason: "decline" }).catch((err) => {
      console.error("Failed to reject call:", err);
      toast.error("Failed to reject call");
    });
    setCurrentCall(null);
    setIsInCall(false);
  };

  return {
    currentCall,
    isInCall,
    createCall,
    joinCall,
    endCall,
    acceptCall,
    rejectCall,
  };
};

export default useCallState;
