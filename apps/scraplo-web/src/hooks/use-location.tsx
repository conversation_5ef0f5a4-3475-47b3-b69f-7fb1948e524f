"use client";

import { useCallback, useState } from "react";

import { useGoogleMaps } from "@acme/ui/context/google-maps-provider";
import { parseGoogleAddressComponents } from "@acme/ui/lib/utils";

import type { UserAddress } from "~/app/lib/types";
import { useUserLocationStore } from "~/stores/user-store";

const useLocation = () => {
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const setUserLocation = useUserLocationStore(
    (state) => state.setUserLocation,
  );

  const { isLoaded, loadError } = useGoogleMaps();

  const invokeLocationRequest = useCallback(async () => {
    if (loadError) {
      setError("Error loading Google Maps API");
      return;
    }
    if (!isLoaded) {
      setError("Google Maps API not loaded yet");
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const position = await new Promise<GeolocationPosition>(
        (resolve, reject) => {
          navigator.geolocation.getCurrentPosition(resolve, reject, {
            enableHighAccuracy: true,
            timeout: 5000,
            maximumAge: 0,
          });
        },
      );

      const { latitude, longitude } = position.coords;
      const geocoder = new window.google.maps.Geocoder();
      const location: UserAddress | null = await new Promise((resolve) => {
        void geocoder.geocode(
          { location: { lat: latitude, lng: longitude } },
          (results, status) => {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-enum-comparison
            if (status === "OK" && results && results.length > 0) {
              const result = results[0];
              if (!result) {
                resolve(null);
                return;
              }
              const addressComponents = result.address_components;
              const formatted_address = result.formatted_address;
              const {
                street,
                city,
                country,
                display,
                district,
                postalCode,
                state,
              } = parseGoogleAddressComponents(
                addressComponents,
                formatted_address,
              );
              const googlePlaceId = results[0]?.place_id;
              // Parse address components as needed for your UserAddress type
              const address: UserAddress = {
                name: "Current Location",
                addressType: "OTHER",
                landmark: "",
                localAddress: "",
                district: district ?? "", // You can parse from address_components if needed
                googleAddressComponent: result.address_components,
                display,
                street: street ?? "",
                city: city ?? "",
                state: state ?? "",
                country: country ?? "",
                postalCode: postalCode ?? undefined,
                coordinates: { latitude, longitude },
                googlePlaceId,
              };
              resolve(address);
            } else {
              resolve(null);
            }
          },
        );
      });
      setUserLocation(location);
    } catch (err) {
      console.error("Error getting user location:", err);
      setError("Error getting user location");
    } finally {
      setIsLoading(false);
    }
  }, [setUserLocation, isLoaded, loadError]);

  return {
    error,
    isLoading,
    requestLocation: invokeLocationRequest,
  };
};

export default useLocation;
