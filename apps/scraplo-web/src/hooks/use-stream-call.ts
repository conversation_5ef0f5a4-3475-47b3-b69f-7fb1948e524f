import { useRouter } from "next/navigation";
import { useStreamVideoClient } from "@stream-io/video-react-sdk";
import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";

import { useTRPC } from "~/trpc/react";

const useStreamCallActions = () => {
  const router = useRouter();
  const client = useStreamVideoClient();

  const trpc = useTRPC();
  const { mutateAsync: createCallAsync } = useMutation(
    trpc.streamCall.createCall.mutationOptions(),
  );

  const createCall = async (callId: string) => {
    if (!client) return;

    try {
      const { callId: generatedCallId } = await createCallAsync({
        orderId: callId,
      });

      console.log("generatedCallId", generatedCallId);
      const call = client.call("default", generatedCallId);
      await call.microphone.enable();
      call
        .join({
          ring: true,
        })
        .then(() => {
          console.log("joined call");
          // router.push(`/calls/${generatedCallId}`);
        })
        .catch((err) => {
          console.error(err);
          toast.error("Failed to join call");
        });
      // router.push(`/calls/${generatedCallId}`);
    } catch (err) {
      console.error(err);
      toast.error("Failed to create call");
    }
  };

  const joinCall = (callId: string) => {
    if (!client)
      return toast.error("Failed to join call, Please try again later");
    router.push(`/calls/${callId}`);
  };

  return {
    createCall,
    joinCall,
  };
};

export default useStreamCallActions;
