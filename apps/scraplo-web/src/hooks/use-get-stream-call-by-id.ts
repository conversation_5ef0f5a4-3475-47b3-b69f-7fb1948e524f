import type { Call } from "@stream-io/video-react-sdk";
import { useEffect, useState } from "react";
import { useStreamVideoClient } from "@stream-io/video-react-sdk";

const useGetStreamCallById = (callId: string) => {
  const [call, setCall] = useState<Call>();
  const [isCallLoading, setIsCallLoading] = useState(true);

  const client = useStreamVideoClient();

  useEffect(() => {
    const getCall = async () => {
      try {
        if (!client) {
          console.error("Stream Video Client is not initialized");
          return;
        }
        const { calls } = await client.queryCalls({
          filter_conditions: { id: callId },
        });

        if (calls.length > 0) setCall(calls[0]);
        else setCall(undefined);
      } catch (err) {
        console.error("Failed to fetch call:", err);
        setCall(undefined);
      } finally {
        setIsCallLoading(false);
      }
    };

    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    getCall();
    // eslint-disable-next-line react-hooks/react-compiler
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [client]);

  return { call, isCallLoading };
};

export default useGetStreamCallById;
