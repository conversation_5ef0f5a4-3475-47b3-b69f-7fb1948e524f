import type { Metadata, Viewport } from "next";
import { Inter, Plus_Jakarta_Sans } from "next/font/google";

import { cn } from "@acme/ui/lib/utils";

import "~/app/globals.css";

import { env } from "~/env";
import Providers from "~/providers/providers";

const APP_NAME = "Scraplo";
const APP_DEFAULT_TITLE = "Scraplo";
const APP_TITLE_TEMPLATE = "%s - Scraplo";
const APP_DESCRIPTION = "Scraplo description";

export const metadata: Metadata = {
  applicationName: APP_NAME,
  icons: [{ rel: "icon", url: "/favicon.png" }],
  title: {
    default: APP_DEFAULT_TITLE,
    template: APP_TITLE_TEMPLATE,
  },
  description: APP_DESCRIPTION,
  appleWebApp: {
    capable: true,
    statusBarStyle: "default",
    title: APP_DEFAULT_TITLE,
    // startUpImage: [],
  },
  formatDetection: {
    telephone: false,
  },
  openGraph: {
    type: "website",
    siteName: APP_NAME,
    title: {
      default: APP_DEFAULT_TITLE,
      template: APP_TITLE_TEMPLATE,
    },
    description: APP_DESCRIPTION,
  },
  twitter: {
    card: "summary",
    title: {
      default: APP_DEFAULT_TITLE,
      template: APP_TITLE_TEMPLATE,
    },
    description: APP_DESCRIPTION,
  },
};

export const viewport: Viewport = {
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "white" },
    { media: "(prefers-color-scheme: dark)", color: "black" },
  ],
};

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});
const jakarta = Plus_Jakarta_Sans({
  subsets: ["latin"],
  variable: "--font-jakarta",
});

export default function RootLayout(props: { children: React.ReactNode }) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        {env.NODE_ENV === "development" && (
          <script
            async
            crossOrigin="anonymous"
            src="//unpkg.com/react-scan/dist/auto.global.js"
          />
        )}
      </head>

      <body
        className={cn(
          "min-h-screen bg-background font-inter text-foreground antialiased",
          inter.variable,
          jakarta.variable,
        )}
      >
        <Providers>{props.children}</Providers>
      </body>
    </html>
  );
}
