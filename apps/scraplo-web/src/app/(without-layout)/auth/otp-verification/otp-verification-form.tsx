"use client";

import type { z } from "zod";
import { useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { REGEXP_ONLY_DIGITS_AND_CHARS } from "input-otp";
import { ArrowRight, Loader2 } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

import { Button } from "@acme/ui/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@acme/ui/components/ui/form";
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from "@acme/ui/components/ui/input-otp";
import { OtpVerificationSchema } from "@acme/validators";

import { OTP_VERIFICATION_PHONE_NUMBER_PARAM_NAME } from "~/app/lib/param-names";
import { authClient } from "~/server/auth/client";
import { useTRPC } from "~/trpc/react";
import FormTitleDescription from "../form-title-description";
import ResendButton from "./resend-button";

interface OtpVerificationFormProps {
  [OTP_VERIFICATION_PHONE_NUMBER_PARAM_NAME]: string;
}

const OtpVerificationForm = (props: OtpVerificationFormProps) => {
  const { [OTP_VERIFICATION_PHONE_NUMBER_PARAM_NAME]: phoneNumber } = props;

  const router = useRouter();
  const trpc = useTRPC();
  const trpcUtils = useQueryClient();

  const [requestResendOtp, setRequestResendOtp] = useState(false);
  const form = useForm<z.infer<typeof OtpVerificationSchema>>({
    resolver: zodResolver(OtpVerificationSchema),
    defaultValues: {
      otp: "",
    },
  });

  const resendOtpMutation = useMutation(
    trpc.auth.resendOtp.mutationOptions({
      onSuccess: () => {
        setRequestResendOtp(true);
        toast.success("OTP resent successfully. Please check your SMS.");

        void trpcUtils.invalidateQueries(
          trpc.user.getLatestOtpExpirationTime.queryFilter(),
        );
      },
      onError: (error) => {
        // Only need to handle max retry case since expired OTPs are handled in handleResendOtp
        if (error.message === "OTP retry count maxed out") {
          toast.info("Max retries reached. Requesting new OTP...");
          requestNewOtpMutation.mutate({ phoneNumber, whatsAppConsent: false });
        } else {
          toast.error("Failed to resend OTP: " + error.message);
        }
      },
    }),
  );

  const requestNewOtpMutation = useMutation(
    trpc.auth.requestOtp.mutationOptions({
      onSuccess: () => {
        setRequestResendOtp(false);
        form.reset(); // Clear the OTP input
        toast.success("New OTP sent successfully. Please check your SMS.");

        void trpcUtils.invalidateQueries(
          trpc.user.getLatestOtpExpirationTime.queryFilter(),
        );
      },
      onError: (_error) => {
        toast.error("Failed to send new OTP: " + _error.message);
      },
    }),
  );
  const onSubmit = async (data: z.infer<typeof OtpVerificationSchema>) => {
    await authClient.phoneNumber.verify(
      {
        phoneNumber: phoneNumber,
        code: data.otp,
      },
      {
        onSuccess: () => {
          toast.success("OTP verified successfully.");
          router.push("/auth/otp-verification/success");
        },
        onError: (_error) => {
          toast.error("Failed to verify OTP ");
        },
      },
    );
  };

  const { data } = useQuery(
    trpc.user.getLatestOtpExpirationTime.queryOptions({ phoneNumber }),
  );
  const handleResendOtp = () => {
    // If no timer or OTP is expired, directly request new OTP
    if (!data?.expiresAt || data.otpExpired) {
      toast.info("Requesting new OTP...");
      requestNewOtpMutation.mutate({ phoneNumber, whatsAppConsent: false });
    } else {
      // OTP is still valid, try to resend existing one (retry logic handles max attempts)
      resendOtpMutation.mutate({ phoneNumber });
    }
  };

  return (
    <div className="mx-auto w-full max-w-screen-xl md:px-16 xl:flex xl:flex-row xl:items-center xl:justify-between xl:gap-14 xl:py-14">
      {/* image */}
      <div className="hidden xl:relative xl:block xl:aspect-[338/334] xl:w-[338px]">
        <Image
          src="/static/images/how-it-work-1.svg"
          fill
          alt="login-illustration"
          className="object-contain"
          unoptimized
        />
      </div>

      <div className="flex flex-col gap-[70px]">
        {/* title & description */}
        <FormTitleDescription
          title={<>Enter Sent Code</>}
          description={
            <>
              We have send a 6-digit code on{" "}
              <span className="font-bold">+91{phoneNumber} to verify</span>
            </>
          }
        />

        {/* form */}
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <FormField
              control={form.control}
              name="otp"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Enter OTP</FormLabel>
                  <FormControl>
                    <InputOTP
                      maxLength={6}
                      pattern={REGEXP_ONLY_DIGITS_AND_CHARS}
                      onChange={field.onChange}
                    >
                      <InputOTPGroup className="flex w-full justify-between md:justify-normal md:gap-6">
                        <InputOTPSlot index={0} />
                        <InputOTPSlot index={1} />
                        <InputOTPSlot index={2} />
                        <InputOTPSlot index={3} />
                        <InputOTPSlot index={4} />
                        <InputOTPSlot index={5} />
                      </InputOTPGroup>
                    </InputOTP>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />{" "}
            <div className="mt-5 flex items-center justify-between">
              <ResendButton
                otpExpired={data?.otpExpired}
                otpExpiresAt={data?.expiresAt}
                handleResendOtp={handleResendOtp}
                isLoading={
                  resendOtpMutation.isPending || requestNewOtpMutation.isPending
                }
              />

              {/* using router.back() here instead of simple link tag which becoz we never know from what page user came to this page can be from sign-up or can be from login so for that i am relying here on router's history to navigate back to prev step. */}
              {requestResendOtp && (
                <button
                  type="button"
                  onClick={() => router.back()}
                  className="text-xs font-medium leading-[18px] text-black-800"
                >
                  Wrong Number?
                </button>
              )}
            </div>
            <Button
              type="submit"
              className="mt-10 w-full xl:mt-14"
              disabled={
                form.formState.isSubmitting ||
                resendOtpMutation.isPending ||
                requestNewOtpMutation.isPending
              }
            >
              {form.formState.isSubmitting ||
              resendOtpMutation.isPending ||
              requestNewOtpMutation.isPending ? (
                <span className="flex items-center gap-2">
                  <Loader2 className="size-5 animate-spin" />
                  Verify OTP
                </span>
              ) : (
                <span className="flex items-center gap-2">
                  Verify OTP
                  <ArrowRight className="size-5" />
                </span>
              )}
            </Button>
            <p className="pt-[18px] text-center font-jakarta text-sm leading-5 text-black-600 md:pt-8 md:text-base md:leading-6 xl:pt-8 xl:text-lg xl:leading-7">
              Didn't get the code?{" "}
              <Link href="/auth/sign-up" className="text-black-850 font-bold">
                Check SMS or try resending
              </Link>
            </p>
          </form>
        </Form>
      </div>
    </div>
  );
};

export default OtpVerificationForm;
