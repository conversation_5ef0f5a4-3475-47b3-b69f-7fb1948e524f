import { useQueryClient } from "@tanstack/react-query";

import Timer from "~/components/shared/timer";
import { useTRPC } from "~/trpc/react";

interface ResendButtonProps {
  otpExpiresAt?: Date;
  otpExpired?: boolean;
  handleResendOtp: () => void;
  isLoading?: boolean;
}

// Why is this component here?
// 1. Service worker was stuck under infinite network calls, whenever trpc fetches data in background upon invalidation it triggers page render again and because of re-render trpc again fetches data in background and this goes on. Even though i have disable the query background refetching, it still happens. Totally an unexpected behavior. By moving this component to a separate file, we can avoid the infinite loop of re-renders and network calls.

const ResendButton = ({
  otpExpiresAt,
  otpExpired,
  handleResendOtp,
  isLoading = false,
}: ResendButtonProps) => {
  const trpc = useTRPC();
  const trpcUtils = useQueryClient();
  return (
    <button
      type="button"
      onClick={handleResendOtp}
      disabled={isLoading}
      className="flex w-fit items-center rounded-2xl bg-[linear-gradient(90deg,_#EAFAEF_0%,_#FFF_93.41%)] px-2.5 py-1 text-xs leading-[18px] text-teal-850 disabled:cursor-not-allowed disabled:opacity-50"
    >
      {" "}
      <>
        {" "}
        <span className="font-semibold underline underline-offset-2 xl:text-sm">
          {isLoading
            ? "Sending..."
            : otpExpired || !otpExpiresAt
              ? "Request New OTP"
              : "Resend code"}
        </span>
        {!otpExpired && !isLoading && (
          <>
            <span className="px-1 xl:text-sm">,otp expires in</span>
            <Timer
              expiryDate={otpExpiresAt}
              onExpire={async () =>
                await trpcUtils.invalidateQueries(
                  trpc.user.getLatestOtpExpirationTime.queryFilter(),
                )
              }
            />
          </>
        )}
      </>
    </button>
  );
};

export default ResendButton;
