import { redirect } from "next/navigation";

import { OTP_VERIFICATION_PHONE_NUMBER_PARAM_NAME } from "~/app/lib/param-names";
import OtpVerificationForm from "./otp-verification-form";

interface OtpVerificationPageProps {
  searchParams: Promise<{
    [OTP_VERIFICATION_PHONE_NUMBER_PARAM_NAME]?: string;
  }>;
}

const OtpVerificationPage = async (props: OtpVerificationPageProps) => {
  const searchParams = await props.searchParams;
  const phoneNumber = searchParams[OTP_VERIFICATION_PHONE_NUMBER_PARAM_NAME];

  if (!phoneNumber) {
    return redirect("/auth/login");
  }

  return <OtpVerificationForm phoneNumber={phoneNumber} />;
};

export default OtpVerificationPage;
