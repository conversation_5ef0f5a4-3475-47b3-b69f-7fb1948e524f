"use client";

import { useEffect } from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";

const PhoneVerifiedPage = () => {
  const router = useRouter();

  useEffect(() => {
    const redirectTimer = setTimeout(() => {
      router.push("/onboarding");
    }, 2000);

    return () => clearTimeout(redirectTimer);
  }, [router]);

  return (
    <main className="flex flex-col items-center justify-center">
      <div className="w-full xl:flex xl:flex-row xl:items-center xl:justify-center xl:gap-14">
        {/* image */}
        <div className="hidden xl:relative xl:block xl:aspect-[338/334] xl:w-[338px]">
          <Image
            src="/static/images/how-it-work-1.svg"
            fill
            alt="login-illustration"
            className="object-contain"
            unoptimized
          />
        </div>

        <div className="flex flex-col items-center justify-center gap-14 rounded-[40px] xl:min-w-[582px] xl:bg-yellow-0 xl:py-14">
          <div className="relative mx-10 my-9 aspect-square h-[123px] md:h-[200px]">
            <Image
              src="/static/images/verified-check.svg"
              alt="verified"
              fill
              className="object-cover"
              unoptimized
            />
          </div>

          <div className="flex flex-col gap-2 md:gap-3">
            <h1 className="text-center font-jakarta text-2xl font-extrabold leading-[34px] -tracking-[0.18px] text-teal-850 md:text-[30px] md:leading-[42px] md:-tracking-[0.225px]">
              Phone Verified
            </h1>
            <p className="max-w-[263px] text-center text-[15px] leading-6 text-black-600 md:text-lg md:leading-[30px]">
              Your account is create, start your{" "}
              <span className="font-semibold text-black-700">
                journey of scrap to cash
              </span>
            </p>
          </div>
        </div>
      </div>
    </main>
  );
};

export default PhoneVerifiedPage;
