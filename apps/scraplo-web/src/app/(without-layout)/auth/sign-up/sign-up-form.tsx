"use client";

import type { z } from "zod";
import { useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import { ArrowRight, Loader2 } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

import type { LoginSchema } from "@acme/validators";
import { Button } from "@acme/ui/components/ui/button";
import { Checkbox } from "@acme/ui/components/ui/checkbox";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@acme/ui/components/ui/form";
import { Input } from "@acme/ui/components/ui/input";
import { SignUpSchema } from "@acme/validators";

import { OTP_VERIFICATION_PHONE_NUMBER_PARAM_NAME } from "~/app/lib/param-names";
import { authClient } from "~/server/auth/client";
import { useTRPC } from "~/trpc/react";
import FormTitleDescription from "../form-title-description";

const SignUpForm = () => {
  const router = useRouter();
  const trpc = useTRPC();
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<z.infer<typeof SignUpSchema>>({
    resolver: zodResolver(SignUpSchema),
    defaultValues: {
      phoneNumber: "",
    },
  });

  const requestOtpMutation = useMutation(
    trpc.auth.requestOtp.mutationOptions({
      onSuccess: (data) => {
        toast.success(data.message);
        router.push(
          `/auth/otp-verification?${OTP_VERIFICATION_PHONE_NUMBER_PARAM_NAME}=${form.getValues().phoneNumber}`,
        );
      },
      onError: (error) => {
        toast.error(error.message || "Failed to send OTP. Please try again.");
      },
    }),
  );

  const onSubmit = async (data: z.infer<typeof LoginSchema>) => {
    setIsLoading(true);
    await authClient.phoneNumber.sendOtp(
      {
        phoneNumber: data.phoneNumber,
      },
      {
        onSuccess: () => {
          //   await requestOtpMutation.mutateAsync(data);
          console.log("OTP sent successfully");
          setIsLoading(false);
          router.push(
            `/auth/otp-verification?${OTP_VERIFICATION_PHONE_NUMBER_PARAM_NAME}=${form.getValues().phoneNumber}`,
          );
        },
        onError: () => {
          toast.error("Failed to send OTP. Please try again.");
          setIsLoading(false);
        },
      },
    );
  };

  return (
    <div className="mx-auto w-full max-w-screen-xl md:px-16 xl:flex xl:flex-row xl:justify-between xl:gap-14">
      {/* image */}
      <div className="hidden xl:relative xl:block xl:aspect-[338/334] xl:w-[338px]">
        <Image
          src="/static/images/how-it-work-1.svg"
          fill
          alt="login-illustration"
          className="object-contain"
          unoptimized
        />
      </div>

      <div className="flex flex-col gap-[70px] md:py-14">
        {/* title & description */}
        <FormTitleDescription
          title={<>Create Your Free Account</>}
          description={
            <>
              Join <span className="font-bold"> Scraplo</span> in seconds to
              start selling scrap the easy way
            </>
          }
        />

        {/* form */}
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="">
            <div className="md:flex md:flex-col md:gap-10 xl:gap-12">
              <div className="flex flex-col gap-6 md:gap-[14px]">
                <FormField
                  control={form.control}
                  name="phoneNumber"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phone Number</FormLabel>
                      <FormControl>
                        <Input placeholder="eg: **********" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="whatsAppConsent"
                  render={({ field }) => (
                    <FormItem>
                      <FormControl>
                        <div className="flex items-center gap-2 pb-3 pt-[14px]">
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                          <p className="text-xs leading-5 text-black-600">
                            Receive updates and info via{" "}
                            <span className="font-bold">WhatsApp</span>
                          </p>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <Button
                type="submit"
                className="mt-10 w-full"
                disabled={requestOtpMutation.isPaused || isLoading}
              >
                <div className="flex items-center gap-2">
                  {requestOtpMutation.isPending || isLoading ? (
                    <span className="flex items-center gap-2">
                      <Loader2 className="size-8 animate-spin" />
                      Send OTP
                    </span>
                  ) : (
                    <>
                      <span>Send OTP </span>
                      <ArrowRight className="size-4" />
                    </>
                  )}
                </div>
              </Button>
            </div>

            <p className="pt-[18px] text-center font-jakarta text-sm leading-5 text-black-600 md:pt-8 md:text-base md:leading-6 xl:text-lg xl:leading-7">
              Already have an account?{" "}
              <Link href="/auth/login" className="text-black-850 font-bold">
                Login now
              </Link>
            </p>
          </form>
        </Form>
      </div>
    </div>
  );
};

export default SignUpForm;
