"use client";

import type { z } from "zod";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import { ArrowR<PERSON>, Loader2 } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

import { Button } from "@acme/ui/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@acme/ui/components/ui/form";
import { Input } from "@acme/ui/components/ui/input";
import { LoginSchema } from "@acme/validators";

import { OTP_VERIFICATION_PHONE_NUMBER_PARAM_NAME } from "~/app/lib/param-names";
import { useTRPC } from "~/trpc/react";
import FormTitleDescription from "../form-title-description";

const LoginForm = () => {
  const router = useRouter();
  const trpc = useTRPC();

  const form = useForm<z.infer<typeof LoginSchema>>({
    resolver: zodResolver(LoginSchema),
    defaultValues: {
      phoneNumber: "",
    },
  });

  const requestOtpMutation = useMutation(
    trpc.auth.requestOtp.mutationOptions({
      onSuccess: (data) => {
        toast.success(data.message);
        router.push(
          `/auth/otp-verification?${OTP_VERIFICATION_PHONE_NUMBER_PARAM_NAME}=${form.getValues().phoneNumber}`,
        );
      },
      onError: (error) => {
        toast.error(error.message || "Failed to send OTP. Please try again.");
      },
    }),
  );
  const onSubmit = (data: z.infer<typeof LoginSchema>) => {
    requestOtpMutation.mutate(data);
  };

  return (
    <div className="mx-auto w-full max-w-screen-xl md:px-16 xl:flex xl:flex-row xl:justify-between xl:gap-14">
      {/* image */}
      <div className="hidden xl:relative xl:block xl:aspect-[338/334] xl:w-[338px]">
        <Image
          src="/static/images/how-it-work-1.svg"
          fill
          alt="login-illustration"
          className="object-contain"
          unoptimized
        />
      </div>

      <div className="flex flex-col gap-[70px] xl:py-14">
        {/* title & description */}
        <FormTitleDescription
          title={<>Welcome Back!</>}
          description={
            <>
              Login in to continue selling your{" "}
              <span className="font-bold"> journey of cash from scrap </span>
            </>
          }
        />

        {/* form */}
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="">
            <div className="md:flex md:flex-col md:gap-10 xl:gap-12">
              <FormField
                control={form.control}
                name="phoneNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone Number</FormLabel>
                    <FormControl>
                      <Input placeholder="eg: 9999999999" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Button
                type="submit"
                className="mt-10 w-full"
                disabled={requestOtpMutation.isPending}
              >
                {requestOtpMutation.isPending ? (
                  <div className="flex items-center gap-2">
                    <Loader2 className="size-5 animate-spin" />
                    <span>Login </span>
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <span>Login </span>
                    <ArrowRight className="size-5" />
                  </div>
                )}
              </Button>
            </div>

            <p className="pt-[18px] text-center font-jakarta text-sm leading-5 text-black-600 md:pt-8 md:text-base md:leading-6 xl:text-lg xl:leading-7">
              Already have an account?{" "}
              <Link href="/auth/sign-up" className="text-black-850 font-bold">
                Sign up
              </Link>
            </p>
          </form>
        </Form>
      </div>
    </div>
  );
};

export default LoginForm;
