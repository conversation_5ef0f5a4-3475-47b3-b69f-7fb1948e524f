import AuthFooter from "./auth-footer";
import AuthHeader from "./auth-header";

interface AuthLayoutProps {
  children: React.ReactNode;
}

const AuthLayout = ({ children }: AuthLayoutProps) => {
  return (
    <main className="relative h-screen border py-10">
      <div className="mx-auto flex max-w-full flex-col gap-[70px] px-6 md:gap-10 md:px-14">
        <AuthHeader />
        {children}
      </div>
      <AuthFooter />
    </main>
  );
};

export default AuthLayout;
