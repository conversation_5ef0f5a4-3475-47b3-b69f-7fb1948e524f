interface FormTitleDescriptionProps {
  title: React.ReactNode;
  description: React.ReactNode;
}

const FormTitleDescription = ({
  title,
  description,
}: FormTitleDescriptionProps) => {
  return (
    <div className="flex flex-col gap-3">
      <h1 className="font-jakarta text-[22px] font-bold leading-[34px] -tracking-[0.165px] text-teal-950 md:text-2xl md:leading-9 xl:text-[28px] xl:leading-[38px] xl:-tracking-[0.21px]">
        {title}
      </h1>
      <p className="text-[15px] leading-6 text-black-600 md:text-lg md:leading-[30px] xl:text-xl xl:leading-8">
        {description}
      </p>
    </div>
  );
};

export default FormTitleDescription;
