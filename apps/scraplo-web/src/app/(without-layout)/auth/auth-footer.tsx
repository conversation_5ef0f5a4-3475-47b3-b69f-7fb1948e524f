"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";

const AuthFooter = () => {
  const pathName = usePathname();

  // hide the footer on login
  const hide =
    pathName === "/auth/login" || pathName === "/auth/otp-verification/success";

  if (hide) {
    return null;
  }

  return (
    <footer className="absolute bottom-10 w-full text-center text-xs leading-5 text-black-600 md:text-base md:leading-6">
      By proceeding, you agree to our{" "}
      <Link href="/terms-of-service" className="font-bold text-teal-750">
        Terms & Conditions{" "}
      </Link>{" "}
      and{" "}
      <Link href="/privacy-policy" className="font-bold text-teal-750">
        Privacy Policy
      </Link>
    </footer>
  );
};

export default AuthFooter;
