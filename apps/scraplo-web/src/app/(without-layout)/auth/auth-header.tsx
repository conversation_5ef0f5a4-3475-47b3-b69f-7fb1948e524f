"use client";

import Image from "next/image";
import { usePathname, useRouter } from "next/navigation";
import { ArrowLeft } from "lucide-react";

import { Button } from "@acme/ui/components/ui/button";
import { cn } from "@acme/ui/lib/utils";

const AuthHeader = () => {
  const router = useRouter();
  const pathName = usePathname();

  const showChangeNumberText = pathName === "/auth/otp-verification";
  const showBackButton = pathName !== "/auth/otp-verification/success";

  return (
    <nav
      className={cn(
        "flex items-center justify-between",
        !showBackButton && "justify-center",
      )}
    >
      {showBackButton && (
        <Button className="bg-black-50" onClick={() => router.back()}>
          <ArrowLeft />
          {showChangeNumberText && "Change Number"}
        </Button>
      )}

      <div className="relative aspect-[87/46] w-[87px] md:aspect-[132/70] md:w-[132px] xl:aspect-[151/80] xl:w-[151px]">
        <Image
          src="/static/logo/scraplo.svg"
          alt="logo"
          fill
          className="object-cover"
          unoptimized
        />
      </div>
    </nav>
  );
};

export default AuthHeader;
