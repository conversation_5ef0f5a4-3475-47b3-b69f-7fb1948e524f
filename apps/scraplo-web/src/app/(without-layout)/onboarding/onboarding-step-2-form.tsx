"use client";

import type { z } from "zod";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

import { addressTypeEnum } from "@acme/db/schema";
import { Button } from "@acme/ui/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@acme/ui/components/ui/form";
import GoogleAutocompleteInput from "@acme/ui/components/ui/google-autocomplete-input";
import { Input } from "@acme/ui/components/ui/input";
import { cn } from "@acme/ui/lib/utils";
import { OnboardingStepTwoSchema } from "@acme/validators";

import { useTRPC } from "~/trpc/react";

const OnboardingStepTwoForm = () => {
  const trpc = useTRPC();
  const router = useRouter();
  const form = useForm<z.infer<typeof OnboardingStepTwoSchema>>({
    resolver: zodResolver(OnboardingStepTwoSchema),
    defaultValues: {
      name: "My Address",
    },
  });

  const { mutate: step2 } = useMutation(
    trpc.onboarding.step2.mutationOptions(),
  );

  const onSubmit = (data: z.infer<typeof OnboardingStepTwoSchema>) => {
    step2(data, {
      onSuccess: (opts) => {
        toast.success(opts.message);
        router.push(`/`);
      },
      onError: (opts) => {
        toast.error(opts.message);
      },
    });
  };

  const addressType = form.watch("addressType");
  const display = form.watch("display");
  const district = form.watch("district");
  const postalCode = form.watch("postalCode");

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit, (err) => console.error(err))}
        className="flex flex-col gap-10"
      >
        <FormField
          control={form.control}
          name="localAddress"
          render={({ field }) => (
            <FormItem>
              <FormLabel>House/ Flat/ Block number</FormLabel>
              <FormControl>
                <Input placeholder="eg: Block Ab" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="landmark"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Landmark/ Road/ Area/ Locality</FormLabel>
              <FormControl>
                <Input placeholder="eg: Nearby xyz." {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <GoogleAutocompleteInput
          showSearchIcon
          showAutoDetectLocationIcon
          onLocationSelect={(location) => {
            if (location.address?.display) {
              form.setValue("display", location.address.display);
            }
            if (location.address?.street) {
              form.setValue("street", location.address.street);
            }
            if (location.address?.city) {
              form.setValue("city", location.address.city);
            }
            if (location.address?.state) {
              form.setValue("state", location.address.state);
            }
            if (location.address?.country) {
              form.setValue("country", location.address.country);
            }
            if (location.address?.postalCode) {
              form.setValue("postalCode", location.address.postalCode);
            }
            if (location.district) {
              form.setValue("district", location.district);
            } else {
              form.setValue("district", "");
            }
            if (location.googleAddressComponent) {
              form.setValue(
                "googleAddressComponent",
                location.googleAddressComponent,
              );
            }
            if (location.googlePlaceId) {
              form.setValue("googlePlaceId", location.googlePlaceId);
            }
            form.setValue("coordinates", {
              latitude: location.latitude,
              longitude: location.longitude,
            });
          }}
          onUserLocationDetect={(location) => {
            if (location.address?.display) {
              form.setValue("display", location.address.display);
            }
            if (location.address?.street) {
              form.setValue("street", location.address.street);
            }
            if (location.address?.city) {
              form.setValue("city", location.address.city);
            }
            if (location.address?.state) {
              form.setValue("state", location.address.state);
            }
            if (location.address?.country) {
              form.setValue("country", location.address.country);
            }
            if (location.address?.postalCode) {
              form.setValue("postalCode", location.address.postalCode);
            }
            if (location.district) {
              form.setValue("district", location.district);
            } else {
              form.setValue("district", "");
            }
            if (location.googleAddressComponent) {
              form.setValue(
                "googleAddressComponent",
                location.googleAddressComponent,
              );
            }
            if (location.googlePlaceId) {
              form.setValue("googlePlaceId", location.googlePlaceId);
            }
            form.setValue("coordinates", {
              latitude: location.latitude,
              longitude: location.longitude,
            });
          }}
        />

        <div className="flex gap-4">
          {/* Show postal code only after address is selected (display set), or if API fails to provide it (postalCode is empty after address selection) */}
          {(!!display || postalCode === "") && (
            <FormField
              control={form.control}
              name="postalCode"
              render={({ field }) => (
                <FormItem className="flex-1">
                  <FormLabel>Postal Code</FormLabel>
                  <FormControl>
                    <Input placeholder="eg: 110001" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}
          {/* Show district only after address is selected (display set), or if API fails to provide it (district is empty after address selection) */}
          {(!!display || district === "") && (
            <FormField
              control={form.control}
              name="district"
              render={({ field }) => (
                <FormItem className="flex-1">
                  <FormLabel>District</FormLabel>
                  <FormControl>
                    <Input placeholder="eg: South Delhi" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}
        </div>
        {/* Hidden field for googleAddressComponent */}
        <input type="hidden" {...form.register("googleAddressComponent")} />
        {/* Hidden field for googlePlaceId */}
        <input type="hidden" {...form.register("googlePlaceId")} />

        <FormField
          control={form.control}
          name="addressType"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Save as</FormLabel>
              <FormControl>
                <div className="flex items-center gap-3">
                  {addressTypeEnum.enumValues.map((item) => (
                    <button
                      type="button"
                      className={cn(
                        "flex items-center gap-1 rounded-full border border-black-100 bg-[#FDFDFD] px-3 py-2 text-[11px] font-medium capitalize leading-[14px] tracking-[0.11px] text-black-800",
                        field.value === item &&
                          "border-yellow-650 bg-[#FFFAE6]",
                      )}
                      onClick={() => field.onChange(item)}
                    >
                      {item === "HOME" && (
                        <Image
                          src="/static/icons/home-2.svg"
                          height={50}
                          width={50}
                          alt="home"
                          className="size-3 object-cover"
                          unoptimized
                        />
                      )}

                      {item === "WORK" && (
                        <Image
                          src="/static/icons/work.svg"
                          height={50}
                          width={50}
                          alt="home"
                          className="size-3 object-cover"
                          unoptimized
                        />
                      )}

                      {item === "OTHER" && (
                        <Image
                          src="/static/icons/location.svg"
                          height={50}
                          width={50}
                          alt="home"
                          className="size-3 object-cover"
                          unoptimized
                        />
                      )}

                      {item.toLowerCase()}
                    </button>
                  ))}
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {addressType === "OTHER" && (
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Name</FormLabel>
                <FormControl>
                  <Input placeholder="eg: Office." {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        )}

        <Button type="submit">Save Address</Button>
      </form>
    </Form>
  );
};

export default OnboardingStepTwoForm;
