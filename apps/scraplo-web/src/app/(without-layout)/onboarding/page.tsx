import Link from "next/link";
import { redirect } from "next/navigation";

import { tryCatch } from "@acme/validators/utils";

import { getQueryClient, trpc } from "~/trpc/server";

// This only works when somebody mistakenly tries to access the onboarding page directly. so redirect them to the last step they left with.
// This page is used to redirect the user to the correct onboarding step based on the data fetched from the db.

const OnBoardingPage = async () => {
  const trpcClient = getQueryClient();
  const { data, err } = await tryCatch(
    trpcClient.fetchQuery(trpc.onboarding.getOnboardingStep.queryOptions()),
  );

  if (err) {
    return (
      <div className="flex flex-col items-center justify-center gap-4 p-6">
        Error fetching onboarding step data.
        <Link href="/">Go Back</Link>
      </div>
    );
  }

  if (data.onBoardingCompleted) {
    console.log("redirecting to root ");

    redirect("/");
  }
  console.log("redirecting to onboaridng step ", data.onBoardingStep);

  redirect(`/onboarding/${data.onBoardingStep}`);
};

export default OnBoardingPage;
