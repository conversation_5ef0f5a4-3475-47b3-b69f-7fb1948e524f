"use client";

import type { z } from "zod";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

import { Button } from "@acme/ui/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@acme/ui/components/ui/form";
import { Input } from "@acme/ui/components/ui/input";
import { OnboardingStepOneSchema } from "@acme/validators";

import { useTRPC } from "~/trpc/react";

const OnboardingStepOneForm = () => {
  const trpc = useTRPC();
  const router = useRouter();
  const form = useForm<z.infer<typeof OnboardingStepOneSchema>>({
    resolver: zodResolver(OnboardingStepOneSchema),
    defaultValues: {
      fullName: "",
      email: "",
    },
  });

  const { mutate: step1 } = useMutation(
    trpc.onboarding.step1.mutationOptions(),
  );

  const onSubmit = (data: z.infer<typeof OnboardingStepOneSchema>) => {
    step1(data, {
      onSuccess: (opts) => {
        toast.success(opts.message);
        router.push(`/onboarding/${opts.nextStep}`);
      },
      onError: (opts) => {
        toast.error(opts.message);
      },
    });
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="flex flex-col gap-10 md:gap-12"
      >
        <div className="flex flex-col gap-5 md:gap-8">
          <FormField
            control={form.control}
            name="fullName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Your Full Name</FormLabel>
                <FormControl>
                  <Input placeholder="eg: Anil Kumar" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email </FormLabel>
                <FormControl>
                  <Input placeholder="eg: <EMAIL>" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <Button type="submit">Next</Button>
      </form>
    </Form>
  );
};

export default OnboardingStepOneForm;
