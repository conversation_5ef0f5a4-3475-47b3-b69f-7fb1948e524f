import Image from "next/image";
import Link from "next/link";

import type { OnBoardingEnum } from "@acme/db/schema";
import { tryCatch } from "@acme/validators/utils";

import { ONBOARDING_STEPS_DATA } from "~/app/lib/constants";
import { ONBOARDING_USER_FULL_NAME_PARAM_NAME } from "~/app/lib/param-names";
import { parseOnBoardingStep } from "~/app/lib/utils";
import { getQueryClient, trpc } from "~/trpc/server";
import OnBoardingHeader from "../onboarding-header";
import OnboardingStepOneForm from "../onboarding-step-1-form";
import OnboardingStepTwoForm from "../onboarding-step-2-form";

interface OnboardingStepProps {
  params?: Promise<{ step: (typeof OnBoardingEnum.enumValues)[number] }>;
  searchParams?: Promise<{ [ONBOARDING_USER_FULL_NAME_PARAM_NAME]?: string }>;
}

const OnboardingFormComponents = {
  STEP_1: <OnboardingStepOneForm />,
  STEP_2: <OnboardingStepTwoForm />,
};

const OnboardingStepPage = async ({
  params,
  searchParams,
}: OnboardingStepProps) => {
  console.log("step page reached");

  const requestedStep = (await params)?.step;
  const fullName = (await searchParams)?.[ONBOARDING_USER_FULL_NAME_PARAM_NAME];

  const trpcClient = getQueryClient();
  const { data, err } = await tryCatch(
    trpcClient.fetchQuery(trpc.onboarding.getOnboardingStep.queryOptions()),
  );

  if (err) {
    return (
      <div className="flex flex-col items-center justify-center gap-4 p-6">
        Error fetching onboarding step data.
        <Link href="/">Go Back</Link>
      </div>
    );
  }

  const { step, stepCount } = parseOnBoardingStep(
    requestedStep,
    data.onBoardingStep,
  );

  const stepData = ONBOARDING_STEPS_DATA[step];

  return (
    <main className="py-10 md:py-12">
      <div className="mx-auto flex max-w-full flex-col gap-[80px] px-6 md:px-14 xl:px-[120px]">
        <OnBoardingHeader currentStep={stepCount} />

        <div className="flex w-full items-center justify-center gap-14">
          <div className="relative hidden aspect-[338/334] min-w-[338px] xl:block">
            <Image
              src="/static/images/how-it-work-1.svg"
              alt="onboarding"
              fill
              className="object-cover"
              unoptimized
            />
          </div>

          <div className="xl:flex xl:min-w-[582px] xl:flex-col xl:gap-[70px]">
            <div className="flex flex-col gap-2 md:px-16 xl:px-0">
              {fullName && (
                <span className="font-jakarta text-sm font-medium -tracking-[0.105px] text-black-700 xl:text-[28px] xl:leading-10 xl:-tracking-[0.21px]">
                  {fullName}
                </span>
              )}

              <h1 className="font-jakarta text-[22px] font-bold leading-[34px] -tracking-[0.165px] text-teal-950 md:text-2xl md:leading-6 md:leading-[36px] xl:text-xl xl:leading-8">
                {stepData.title}
              </h1>
              <p className="text-[15px] leading-6 text-black-600 md:text-base md:leading-7">
                {stepData.description}
              </p>
            </div>

            <div className="md:px-16 xl:px-0">
              {OnboardingFormComponents[step]}
            </div>
          </div>
        </div>
      </div>
    </main>
  );
};

export default OnboardingStepPage;
