import Image from "next/image";

import { Button } from "@acme/ui/components/ui/button";

const OnboardingSuccessPage = () => {
  return (
    <div className="mx-auto flex min-h-screen max-w-full flex-col items-center justify-center gap-14 border px-6 md:px-[120px]">
      <div className="relative aspect-[87/46] w-[87px] md:aspect-[132/70] md:w-[132px]">
        <Image
          src="/static/logo/scraplo.svg"
          alt="logo"
          fill
          className="object-cover"
        />
      </div>

      <div className="flex items-center gap-[70px]">
        <div className="relative hidden aspect-[338/334] min-w-[338px] xl:block">
          <Image
            src="/static/images/how-it-work-1.svg"
            alt="onboarding"
            fill
            className="object-cover"
            unoptimized
          />
        </div>

        <div className="flex flex-col items-center justify-center gap-9 xl:max-w-[582px]">
          <div className="relative mx-10 my-9 aspect-square h-[123px] md:h-[161px]">
            <Image
              src="/static/images/verified-check.svg"
              alt="verified"
              fill
              className="object-cover"
              unoptimized
            />
          </div>

          <div className="flex flex-col gap-3 md:gap-6 xl:gap-4">
            <h1 className="text-center font-jakarta text-[22px] font-bold leading-[34px] -tracking-[0.165px] text-teal-950 md:text-[30px]">
              Thanks! <br /> Your profile is under review
            </h1>
            <p className="rounded-lg border border-black-50 bg-black-0 px-3 py-4 text-center text-sm leading-6 text-black-600 md:p-6 md:text-lg md:leading-[30px]">
              We&apos;ve received your details and documents. Our team is
              verifying everything to ensure a secure platform for everyone.
              This usually takes{" "}
              <span className="font-medium">24-48 hours</span>
            </p>

            <Button className="mt-6 w-full bg-teal-900 tracking-[0.14px] text-black-0 xl:mt-0">
              Okay, Got It
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OnboardingSuccessPage;
