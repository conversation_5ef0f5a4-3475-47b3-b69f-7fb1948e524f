"use client";

import Image from "next/image";
import { useRouter } from "next/navigation";

import { Button } from "@acme/ui/components/ui/button";

import { ONBOARDING_STEPS_DATA } from "~/app/lib/constants";
import StepIndicator from "~/components/shared/step-indicator";

interface OnBoardingHeaderProps {
  currentStep: number;
}

const OnBoardingHeader = ({ currentStep }: OnBoardingHeaderProps) => {
  const router = useRouter();

  const handleBackClick = () => {
    router.back();
  };

  return (
    <nav className="flex items-center justify-between">
      <Button className="bg-black-50" onClick={handleBackClick}>
        <Image
          src="/static/icons/left-arrow.svg"
          alt="left"
          height={100}
          width={100}
          className="size-4"
          unoptimized
        />
      </Button>

      <StepIndicator
        currentStep={currentStep}
        totalStepsCount={Object.keys(ONBOARDING_STEPS_DATA).length}
      />
    </nav>
  );
};

export default OnBoardingHeader;
