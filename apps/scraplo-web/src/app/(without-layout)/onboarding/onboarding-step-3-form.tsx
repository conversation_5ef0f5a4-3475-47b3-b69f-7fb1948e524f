"use client";

import type { z } from "zod";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@acme/ui/components/ui/accordion";
import { Button } from "@acme/ui/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@acme/ui/components/ui/form";
import { Input } from "@acme/ui/components/ui/input";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@acme/ui/components/ui/select";
import { OnboardingStepThreeSchema } from "@acme/validators";

import { useTRPC } from "~/trpc/react";

const OnboardingStepThreeForm = () => {
  const router = useRouter();
  const trpc = useTRPC();
  const form = useForm<z.infer<typeof OnboardingStepThreeSchema>>({
    resolver: zodResolver(OnboardingStepThreeSchema),
    defaultValues: {
      bankName: "",
      upiId: "",
      ifscCode: "",
      accountNumber: "",
    },
  });

  const { mutate: step3 } = useMutation(
    trpc.onboarding.step3.mutationOptions({
      onSuccess: (opts) => {
        toast.success(opts.message);
        router.push("/");
      },
      onError: (opts) => {
        toast.error(opts.message);
      },
    }),
  );

  const { mutate: endToEndFundAccountFlow, isPending } = useMutation(
    trpc.payment.endToEndFundAccountFlow.mutationOptions({
      onSuccess: (opts) => {
        if (opts.success) {
          step3();
        }
      },
      onError: (opts) => {
        toast.error(opts.message);
      },
    }),
  );

  const onSubmit = (data: z.infer<typeof OnboardingStepThreeSchema>) => {
    // First Attempt to create Razorpay contact
    endToEndFundAccountFlow(data);
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(
          (data) => onSubmit(data),
          () =>
            toast.error(
              "Please provide either a valid UPI ID or complete banking details (Bank name, IFSC code, and account number).",
            ),
        )}
        className="mt-8 flex flex-col gap-4"
      >
        <Accordion
          type="single"
          defaultValue="link-upi"
          collapsible
          className="flex flex-col gap-4"
        >
          <AccordionItem value="link-upi">
            <AccordionTrigger>
              <span className="flex items-center gap-4 text-lg font-medium -tracking-[0.09px] text-black-800">
                <Image
                  src="/static/icons/link-upi.svg"
                  alt="link-upi"
                  height={100}
                  width={100}
                  className="size-4 object-cover xl:size-6"
                  unoptimized
                />
                Link UPI
              </span>
            </AccordionTrigger>
            <AccordionContent className="flex flex-col items-start gap-4">
              <div className="flex w-full flex-col gap-4 rounded-lg border-[1.2px] border-black-150 px-4 py-3">
                <div className="flex w-full items-center gap-4">
                  <span className="text-sm font-medium text-black-800">
                    Add UPI ID
                  </span>
                </div>

                <div className="flex items-center gap-2">
                  <FormField
                    control={form.control}
                    name="upiId"
                    render={({ field }) => (
                      <FormItem className="flex-1">
                        <FormControl>
                          <Input
                            placeholder="UPI ID"
                            {...field}
                            onChange={(e) => {
                              field.onChange(e);
                              // Clear banking fields when UPI is entered
                              if (e.target.value.trim()) {
                                form.setValue("bankName", "");
                                form.setValue("ifscCode", "");
                              }
                            }}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <Button type="submit" disabled={isPending}>
                    <span className="px-2.5">Verify</span>
                  </Button>
                </div>
              </div>
            </AccordionContent>
          </AccordionItem>

          <AccordionItem value="net-banking">
            <AccordionTrigger>
              <span className="flex items-center gap-4 text-lg font-medium -tracking-[0.09px] text-black-800">
                <Image
                  src="/static/icons/net-banking.svg"
                  alt="net-banking"
                  height={100}
                  width={100}
                  className="size-4 object-cover xl:size-6"
                  unoptimized
                />
                Net Banking
              </span>
            </AccordionTrigger>
            <AccordionContent>
              <div className="flex flex-col gap-5">
                <div className="flex flex-col gap-4">
                  <FormField
                    control={form.control}
                    name="bankName"
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Select
                            value={field.value ?? undefined}
                            onValueChange={(value) => {
                              field.onChange(value);
                              if (value) {
                                form.setValue("upiId", "");
                              }
                            }}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select a bank" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectGroup>
                                <SelectItem value="hdfc-bank">
                                  HDFC Bank
                                </SelectItem>
                                <SelectItem value="icici-bank">
                                  ICICI Bank
                                </SelectItem>
                                <SelectItem value="axis-bank">
                                  Axis Bank
                                </SelectItem>
                                <SelectItem value="sbi-bank">
                                  SBI Bank
                                </SelectItem>
                                <SelectItem value="pnb-bank">
                                  PNB Bank
                                </SelectItem>
                              </SelectGroup>
                            </SelectContent>
                          </Select>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="accountNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Account Number</FormLabel>
                        <FormControl>
                          <Input placeholder="Account Number" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="ifscCode"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>IFSC Code</FormLabel>
                        <FormControl>
                          <Input placeholder="IFSC Code" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <Button type="submit" disabled={isPending}>
                  Verify
                </Button>
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </form>
    </Form>
  );
};

export default OnboardingStepThreeForm;
