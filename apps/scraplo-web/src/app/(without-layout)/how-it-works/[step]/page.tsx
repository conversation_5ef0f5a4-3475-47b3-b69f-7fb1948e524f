import Image from "next/image";
import Link from "next/link";

import { <PERSON><PERSON> } from "@acme/ui/components/ui/button";

import { HOW_IT_WORKS_STEPS } from "~/app/lib/constants";
import StepIndicator from "~/components/shared/step-indicator";

interface HowItWorksPageProps {
  params?: Promise<{ step?: keyof typeof HOW_IT_WORKS_STEPS }>;
}

const HowItWorksPage = async ({ params }: HowItWorksPageProps) => {
  const step = (await params)?.step;
  const stepData =
    // Need to disable eslint here because i am checking if somebody enters some unexpected value as step like /how-it-works/fdfd which does not exists in that case i return the first step and es<PERSON> does not understand that

    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition, @typescript-eslint/no-non-null-assertion
    HOW_IT_WORKS_STEPS[step!] ?? HOW_IT_WORKS_STEPS["1"];

  return (
    <main>
      <div className="flex flex-col gap-5 pt-[30px] md:gap-9 md:px-14 md:pt-12 xl:gap-12 xl:px-[120px] xl:pt-[70px]">
        {/* logo */}
        <nav className="px-6">
          <div className="relative aspect-[107/57] w-[107px] md:w-[132px]">
            <Image
              src="/static/logo/scraplo.svg"
              alt="scraplo"
              fill
              className="relative object-cover"
              unoptimized
            />
          </div>
        </nav>

        {/* image and text */}
        <div className="xl:flex xl:flex-row xl:justify-between xl:gap-[56px]">
          {/* image */}
          <div className="relative z-10 mx-auto aspect-square w-[244px] self-center md:w-[329px] xl:aspect-auto xl:h-[338px] xl:w-[448px]">
            <Image
              src={stepData.image}
              alt={stepData.imageAlt}
              fill
              className="object-contain"
              unoptimized
            />
          </div>

          {/* text */}
          <div className="flex flex-col gap-[50px] bg-yellow-0 bg-opacity-70 pb-10 md:gap-[64px] xl:relative xl:min-w-[582px] xl:gap-[120px] xl:px-10 xl:py-14">
            <div className="relative xl:static">
              <div className="absolute relative bottom-8 aspect-auto h-[100px] w-full xl:hidden">
                <Image
                  src="/static/images/circle-cutout-bottom.svg"
                  alt="circle"
                  fill
                  className="relative object-cover"
                  unoptimized
                />
              </div>
              <span className="absolute bottom-0 px-6 xl:top-14">
                <StepIndicator
                  currentStep={Number(step)}
                  totalStepsCount={Object.keys(HOW_IT_WORKS_STEPS).length}
                />
              </span>
            </div>

            <div className="flex flex-col gap-10 px-6 xl:gap-12">
              {/* text */}
              <div className="flex flex-col gap-6 xl:gap-10">
                <h2 className="font-jakarta text-xl font-extrabold leading-8 text-black-900 md:text-2xl xl:text-[28px] xl:leading-[36px]">
                  {stepData.title}
                </h2>
                <div>
                  <h3 className="pb-2 font-bold text-teal-900 md:text-lg xl:text-xl xl:leading-9">
                    {stepData.subTitle}
                  </h3>
                  <p className="text-sm leading-[22px] -tracking-[0.14px] text-black-700 md:text-base md:leading-6 md:-tracking-[0.16px] xl:text-lg xl:leading-7">
                    {stepData.description}
                  </p>

                  {stepData.subDescription && (
                    <p className="pt-4 text-xs font-semibold -tracking-[0.12px] text-teal-800">
                      {stepData.subDescription}
                    </p>
                  )}
                </div>
              </div>

              {/* buttons */}
              {step === "3" ? (
                <Button className="bg-teal-950 text-black-0" asChild>
                  <Link href={stepData.buttonLink}>
                    {stepData.buttonText}
                    <Image
                      src="/static/icons/right-arrow.svg"
                      alt="right"
                      height={100}
                      width={100}
                      className="size-4"
                      unoptimized
                    />
                  </Link>
                </Button>
              ) : (
                <div className="flex gap-6">
                  <Button className="flex-1 bg-black-50 text-black-800" asChild>
                    <Link href="/auth/login">Skip</Link>
                  </Button>
                  <Button className="flex-1" asChild>
                    <Link href={stepData.buttonLink}>
                      {stepData.buttonText}
                    </Link>
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </main>
  );
};

export default HowItWorksPage;
