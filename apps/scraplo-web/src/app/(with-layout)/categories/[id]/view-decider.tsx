"use client";

import { useQuery } from "@tanstack/react-query";

import BrowseCategoryCard from "~/components/shared/browse-category-card";
import PageHeader from "~/components/shared/page-header";
import { useTRPC } from "~/trpc/react";
import CategoryWithoutSubcategories from "./category-without-subcategories";

interface ViewDeciderProps {
  id: string;
}

const ViewDecider = ({ id }: ViewDeciderProps) => {
  const trpc = useTRPC();

  const { data } = useQuery(
    trpc.category.getSubCategoriesWithOrWithoutParent.queryOptions({
      id,
      withParent: true,
    }),
  );

  const { data: cartItems } = useQuery(
    trpc.order.getAllCartItemsMap.queryOptions(),
  );

  const parent = data?.parent;
  const subCategories = data?.subCategories;

  //   if (!cartItems) return <div>Loading...</div>;

  if (!subCategories || subCategories.length === 0) {
    if (!parent) return null;

    return (
      <CategoryWithoutSubcategories
        category={parent}
        parentName={parent.name}
        cartItem={cartItems?.[parent.id]}
      />
    );
  }

  return (
    <div className="flex flex-col gap-6 py-6">
      <div className="">
        <PageHeader
          title={parent?.name + " Categories"}
          subTitle={
            <span className="inline-flex items-center rounded-full bg-yellow-100 px-4 py-2 text-sm font-semibold text-yellow-800">
              {subCategories.length} Sub categories
            </span>
          }
        />
      </div>

      <div className="px-6 md:px-14 xl:px-[120px]">
        <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-2 xl:gap-8">
          {subCategories.map((item) =>
            item.children.length > 0 ? (
              <BrowseCategoryCard
                key={item.id}
                parentName={parent?.name}
                category={item}
                variant="category-card"
                totalSubCategories={item.children.length}
                cartItem={cartItems?.[item.id]}
              />
            ) : (
              <BrowseCategoryCard
                key={item.id}
                parentName={parent?.name}
                category={item}
                variant="add-to-cart"
                totalSubCategories={item.children.length}
                cartItem={cartItems?.[item.id]}
              />
            ),
          )}
        </div>
      </div>
    </div>
  );
};

export default ViewDecider;
