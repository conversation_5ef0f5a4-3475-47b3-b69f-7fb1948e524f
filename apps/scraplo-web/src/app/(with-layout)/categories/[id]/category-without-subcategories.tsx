"use client";

import { Suspense, useEffect } from "react";
import { useQueryState } from "nuqs";

import type {
  CartItem,
  SubCategoriesWithOrWithoutParent,
} from "~/app/lib/types";
import { ADD_ITEM_TO_CART_SHEET_PARAM_NAME } from "~/app/lib/param-names";
import BrowseCategoryCard from "~/components/shared/browse-category-card";
import PageHeader from "~/components/shared/page-header";

interface CategoryWithoutSubcategoriesProps {
  parentName?: string;
  category: SubCategoriesWithOrWithoutParent["parent"];
  cartItem?: CartItem;
}

const CategoryWithoutSubcategories = ({
  parentName,
  category,
  cartItem,
}: CategoryWithoutSubcategoriesProps) => {
  const [, setAddToCartItemParam] = useQueryState(
    ADD_ITEM_TO_CART_SHEET_PARAM_NAME,
    {
      clearOnDefault: true,
    },
  );

  useEffect(() => {
    if (category?.id) {
      void setAddToCartItemParam(category.id);
    }
    // No need to worry about the dependency array here because i want this to run only once when the component mounts.

    // eslint-disable-next-line react-hooks/react-compiler
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div className="flex flex-col gap-6 py-6">
      <div className="px-6 md:px-14 xl:px-[120px]">
        <PageHeader title={parentName} />
      </div>
      <div className="px-6 md:px-14 xl:px-[120px]">
        <div className="mx-auto max-w-4xl">
          <BrowseCategoryCard
            parentName={parentName}
            category={category}
            totalSubCategories={0} // for sure 0 that's why it is without subcategories
            variant="add-to-cart"
            cartItem={cartItem}
          />
        </div>
      </div>
    </div>
  );
};

const MainCategoryWithoutSubcategories = ({
  parentName,
  category,
  cartItem,
}: CategoryWithoutSubcategoriesProps) => {
  return (
    <Suspense>
      <CategoryWithoutSubcategories
        parentName={parentName}
        category={category}
        cartItem={cartItem}
      />
    </Suspense>
  );
};

export default MainCategoryWithoutSubcategories;
