import { tryCatch } from "@acme/validators/utils";

import Error from "~/components/shared/error";
import { getQueryClient, HydrateClient, trpc } from "~/trpc/server";
import ViewDecider from "./view-decider";

interface SubCategoryPageProps {
  params: Promise<{ id: string }>;
}

const SubCategoryPage = async ({ params }: SubCategoryPageProps) => {
  const id = (await params).id;

  const queryClient = getQueryClient();
  const { err } = await tryCatch(
    Promise.allSettled([
      queryClient.prefetchQuery(
        trpc.category.getSubCategoriesWithOrWithoutParent.queryOptions({
          id,
          withParent: true,
        }),
      ),
      queryClient.prefetchQuery(trpc.order.getAllCartItemsMap.queryOptions()),
    ]),
  );

  if (err) {
    return <Error />;
  }

  return (
    <HydrateClient>
      <main>
        <ViewDecider id={id} />
      </main>
    </HydrateClient>
  );
};

export default SubCategoryPage;
