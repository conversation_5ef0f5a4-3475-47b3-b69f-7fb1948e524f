import { tryCatch } from "@acme/validators/utils";

import PageHeader from "~/components/shared/page-header";
import { getQueryClient, HydrateClient, trpc } from "~/trpc/server";
import BrowseCategories from "./browse-categories";

const CategoriesPage = () => {
  const queryClient = getQueryClient();
  void tryCatch(
    queryClient.prefetchQuery(
      trpc.category.getTopLevelCategories.queryOptions({}),
    ),
  );

  return (
    <HydrateClient>
      <main className="flex flex-col gap-8 py-6">
        <PageHeader title="Browse Categories" />

        <div className="px-6 md:px-14 xl:px-[120px]">
          <BrowseCategories />
        </div>
      </main>
    </HydrateClient>
  );
};

export default CategoriesPage;
