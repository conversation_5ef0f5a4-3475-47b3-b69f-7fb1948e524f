"use client";

import { useQuery } from "@tanstack/react-query";

import CategoryCard from "~/components/shared/category-card";
import CategoryCardSkeleton from "~/components/skeletons/category-card-skeleton";
import { useTRPC } from "~/trpc/react";

const BrowseCategories = () => {
  const trpc = useTRPC();
  const { data: categories, isPending } = useQuery(
    trpc.category.getTopLevelCategories.queryOptions({}),
  );

  if (isPending) {
    return (
      <div className="grid grid-cols-2 gap-6 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5">
        {Array.from({ length: 10 }).map((_, i) => (
          <CategoryCardSkeleton key={i} />
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-2 gap-6 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5">
      {categories?.map((item) => (
        <CategoryCard key={item.id} category={item} />
      ))}
    </div>
  );
};

export default BrowseCategories;
