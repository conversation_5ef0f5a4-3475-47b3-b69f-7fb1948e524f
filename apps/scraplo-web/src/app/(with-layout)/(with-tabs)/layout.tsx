import BottomTabs from "~/components/shared/bottom-tabs";
import LocationPermissionSheet from "~/components/shared/location-permission-sheet";
import Navbar from "~/components/shared/navbar";
import RequestPickupSheet from "~/components/shared/request-pickup-sheet";

export default function RootLayout(props: { children: React.ReactNode }) {
  return (
    <>
      <Navbar />
      {props.children}
      <LocationPermissionSheet />
      <RequestPickupSheet />
      <BottomTabs />
    </>
  );
}
