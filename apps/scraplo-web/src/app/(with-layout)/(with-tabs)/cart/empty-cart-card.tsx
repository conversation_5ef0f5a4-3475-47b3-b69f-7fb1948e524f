import Image from "next/image";
import Link from "next/link";
import { Plus } from "lucide-react";

import { Button } from "@acme/ui/components/ui/button";

const EmptyCartCard = () => {
  return (
    <div className="relative flex flex-col gap-8">
      <p className="px-10 text-center font-jakarta font-semibold leading-7 text-black-700">
        “Please add scrap item to continue with order”
      </p>

      <Image
        src="/static/icons/arrow.svg"
        alt="arrow"
        height={50}
        width={50}
        className="absolute right-[35%] top-[35%] h-[42px] w-[14px] object-cover"
        unoptimized
      />

      <Button
        className="flex w-full items-center gap-2 rounded-lg border-[1.2px] border-yellow-750 bg-yellow-150 px-3 py-[14px] font-medium leading-5 tracking-[0.14px] text-black-900"
        asChild
      >
        <Link href="/categories">
          <Plus className="size-4" /> Add Item
        </Link>
      </Button>
    </div>
  );
};

export default EmptyCartCard;
