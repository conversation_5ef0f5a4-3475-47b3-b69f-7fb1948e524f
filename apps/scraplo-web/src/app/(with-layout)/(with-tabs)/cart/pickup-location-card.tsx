"use client";

import { Suspense } from "react";
import { ChevronDown } from "lucide-react";
import { useQueryState } from "nuqs";

import { LOCATION_PERMISSION_SHEET_PARAM_NAME } from "~/app/lib/param-names";
import { useUserLocationStore } from "~/stores/user-store";

const PickUpLocationCard = () => {
  const [, setLocationPermissionSheet] = useQueryState(
    LOCATION_PERMISSION_SHEET_PARAM_NAME,
    { clearOnDefault: true },
  );
  const { userLocation } = useUserLocationStore();

  return (
    <div className="flex flex-col gap-2 rounded-xl bg-black-0 p-3">
      <p className="text-xs font-semibold leading-5 text-teal-900">
        Pickup location
      </p>

      <div className="flex flex-col gap-1">
        <button
          onClick={() => setLocationPermissionSheet("yes")}
          className="flex items-center gap-1 font-jakarta font-bold leading-[22px] -tracking-[0.14px] text-black-800"
        >
          {userLocation?.name} <ChevronDown className="size-3 text-teal-700" />
        </button>

        <p className="text-[11px] leading-4 text-teal-900">
          {userLocation?.display}
        </p>
      </div>
    </div>
  );
};

const MainPickupLocationCard = () => {
  return (
    <Suspense>
      <PickUpLocationCard />
    </Suspense>
  );
};

export default MainPickupLocationCard;
