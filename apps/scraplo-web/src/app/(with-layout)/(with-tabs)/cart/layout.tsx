import { tryCatch } from "@acme/validators/utils";

import Error from "~/components/shared/error";
import { getQueryClient, HydrateClient, trpc } from "~/trpc/server";

interface CartLayoutProps {
  children: React.ReactNode;
}

const CartLayout = async ({ children }: CartLayoutProps) => {
  const queryClient = getQueryClient();
  const { err } = await tryCatch(
    queryClient.prefetchQuery(trpc.order.getCurrentCart.queryOptions()),
  );

  if (err) {
    return <Error />;
  }

  return <HydrateClient>{children}</HydrateClient>;
};

export default CartLayout;
