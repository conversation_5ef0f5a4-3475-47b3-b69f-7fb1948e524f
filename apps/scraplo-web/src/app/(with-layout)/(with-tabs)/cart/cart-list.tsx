import type { CurrentCart } from "~/app/lib/types";
import CartItem from "~/components/shared/cart-item";

interface CartListProps {
  cartItems: CurrentCart["items"];
}

const CartList = ({ cartItems }: CartListProps) => {
  return (
    <div className="flex min-h-screen flex-col gap-4 overflow-y-auto">
      {cartItems.map((item) => (
        <CartItem
          key={item.id}
          cartItem={item}
          category={item.category}
          showRemoveButton
          showEditButton
          disableInput
          inputDefaultValue={Number(item.quantity)}
        />
      ))}
    </div>
  );
};

export default CartList;
