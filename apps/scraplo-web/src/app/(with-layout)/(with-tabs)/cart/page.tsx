"use client";

import { Suspense } from "react";
import { useRouter } from "next/navigation";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useQueryState } from "nuqs";
import { toast } from "sonner";

import { <PERSON><PERSON> } from "@acme/ui/components/ui/button";

import {
  LOCATION_PERMISSION_SHEET_PARAM_NAME,
  PAYOUT_METHOD_SHEET_PARAM_NAME,
} from "~/app/lib/param-names";
import PayoutMethodIndicator from "~/components/shared/payout-method-indicator";
import { useUserLocationStore } from "~/stores/user-store";
import { useTRPC } from "~/trpc/react";
import CartList from "./cart-list";
import EmptyCartCard from "./empty-cart-card";
import PickUpLocationCard from "./pickup-location-card";

const CartPage = () => {
  const router = useRouter();
  const { userLocation } = useUserLocationStore();
  const [, setLocationPermissionSheet] = useQueryState(
    LOCATION_PERMISSION_SHEET_PARAM_NAME,
    { clearOnDefault: true },
  );
  const [, setPayoutMethodSheet] = useQueryState(
    PAYOUT_METHOD_SHEET_PARAM_NAME,
    { clearOnDefault: true },
  );

  const trpc = useTRPC();
  const queryClient = useQueryClient();
  const { data: cartData, isPending } = useQuery(
    trpc.order.getCurrentCart.queryOptions(undefined, {
      staleTime: 0,
    }),
  );

  const { mutate: placeNewOrder, isPending: isPlacingNewOrder } = useMutation(
    trpc.order.placeNewOrder.mutationOptions({
      onSuccess: async (opts) => {
        await Promise.allSettled([
          queryClient.invalidateQueries(
            trpc.order.getCurrentCartStatistics.queryFilter(),
          ),
          queryClient.invalidateQueries(
            trpc.order.getCurrentCart.queryFilter(),
          ),
          queryClient.invalidateQueries(trpc.order.getAllOrders.queryFilter()),
        ]);

        toast.success(opts.message);
        router.push("/profile/orders");
      },
    }),
  );

  // This never gonna happen because i am blocking the rendering already in layout.tsx and prefetch this query so data will be always instantly available at the time of render. so i dont need to worry about the empty state here.

  if (isPending || !cartData) {
    return (
      <div className="flex h-full items-center justify-center">
        <span className="text-sm font-semibold text-gray-500">Loading...</span>
      </div>
    );
  }

  const estimatedPrice = cartData.estimatedPrice;
  const cartItems = cartData.items;
  const payoutMethod = cartData.payoutMethod;
  const breakdown = cartData.breakdown;

  const handleNewOrder = () => {
    if (cartItems.length === 0) {
      return toast.error("Please add items to cart");
    }

    // if not present that means user has to select any of the location from the saved addresses
    if (!userLocation?.id) {
      void setLocationPermissionSheet("yes");
      return toast.warning(
        "Please select a pickup location from your saved addresses",
      );
    }

    // Check if payout method is selected
    if (!payoutMethod?.id) {
      void setPayoutMethodSheet("yes");
      return toast.warning("Please select a payout method");
    }

    // Both location and payout method are selected, place the order
    placeNewOrder({
      addressId: userLocation.id,
    });
  };

  const isNegativePayout = !!(breakdown && breakdown.payoutToCustomer < 0);

  return (
    <main className="flex flex-col py-5 xl:flex-row xl:gap-14 xl:px-[120px]">
      <div className="flex w-full flex-col gap-12 xl:min-w-[677px]">
        <div className="px-6 md:px-16 xl:px-0">
          <PickUpLocationCard />
        </div>

        <div className="px-6 md:px-16 xl:px-0">
          <PayoutMethodIndicator payoutMethod={payoutMethod ?? null} />
        </div>

        <div className="px-6 md:px-16 xl:px-0">
          {cartItems.length === 0 ? (
            <EmptyCartCard />
          ) : (
            <CartList cartItems={cartItems} />
          )}
        </div>
      </div>

      <div className="h-[110px] xl:hidden" />

      <div className="fixed bottom-[92px] w-full xl:static xl:bottom-0 xl:max-w-[393px]">
        {/* price strip */}
        {estimatedPrice ? (
          <div className="flex flex-col gap-2 rounded-t-2xl bg-yellow-150 px-6 py-3 md:px-14 md:py-4 xl:px-10">
            <div className="flex items-center gap-2.5">
              <span className="flex-1 text-[13px] font-semibold leading-5 text-yellow-950 underline decoration-dotted underline-offset-2 md:text-base md:leading-7 md:underline-offset-4">
                Estimated Price
              </span>
              <span className="font-jakarta font-extrabold leading-6 -tracking-[0.16px] text-black-900 md:text-xl md:leading-8">
                ₹ {estimatedPrice}
              </span>
            </div>
            {/* Breakdown UI */}
            {breakdown && (
              <div className="mt-2 text-xs md:text-sm">
                {breakdown.isBelowMinOrder ? (
                  <div className="mb-1 font-semibold">
                    Your order is below the minimum order value of ₹
                    {breakdown.minOrderValue}
                  </div>
                ) : null}
                <div className="flex flex-col gap-1">
                  <div className="flex justify-between">
                    <span
                      className={
                        breakdown.isBelowMinOrder
                          ? undefined
                          : "text-gray-400 line-through decoration-dashed"
                      }
                    >
                      Pickup Charge
                    </span>
                    <span
                      className={
                        breakdown.isBelowMinOrder
                          ? undefined
                          : "text-gray-400 line-through decoration-dashed"
                      }
                    >
                      ₹ {breakdown.pickupCharge}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span
                      className={
                        breakdown.isBelowMinOrder
                          ? undefined
                          : "text-gray-400 line-through decoration-dashed"
                      }
                    >
                      GST ({breakdown.gstPercent}% on Pickup)
                    </span>
                    <span
                      className={
                        breakdown.isBelowMinOrder
                          ? undefined
                          : "text-gray-400 line-through decoration-dashed"
                      }
                    >
                      ₹ {breakdown.gstAmount}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span
                      className={
                        parseInt(breakdown.handlingCharge.toString()) <= 0
                          ? undefined
                          : "text-gray-400 line-through decoration-dashed"
                      }
                    >
                      Handling Charges (₹ {breakdown.handlingCharge})
                    </span>
                    <span
                      className={
                        parseInt(breakdown.handlingCharge.toString()) <= 0
                          ? undefined
                          : "text-gray-400 line-through decoration-dashed"
                      }
                    >
                      ₹ {breakdown.handlingCharge}
                    </span>
                  </div>
                  <div className="mt-1 flex justify-between font-semibold">
                    <span>You will receive</span>
                    <span>₹ {breakdown.payoutToCustomer}</span>
                  </div>
                </div>
                {isNegativePayout && (
                  <>
                    <div className="mt-1 font-semibold">
                      Please add more items to place order
                    </div>
                  </>
                )}
              </div>
            )}
          </div>
        ) : null}

        {/* add to cart button */}
        <div className="z-10 bg-[rgba(255,_255,_255,_0.30)] px-6 py-4 backdrop-blur-[15px] md:fixed md:bottom-0 md:w-full md:px-16 md:py-6 xl:static xl:px-0">
          <Button
            disabled={
              cartItems.length === 0 || isPlacingNewOrder || isNegativePayout
            }
            className="flex w-full items-center rounded-lg px-3 py-[14px] text-center"
            onClick={handleNewOrder}
          >
            Place Order
          </Button>
        </div>
      </div>
    </main>
  );
};

const CartPageWrapper = () => {
  return (
    <Suspense>
      <CartPage />
    </Suspense>
  );
};

export default CartPageWrapper;
