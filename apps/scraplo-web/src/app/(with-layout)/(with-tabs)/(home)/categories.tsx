"use client";

import { useQuery } from "@tanstack/react-query";

import { useIsMobile } from "@acme/ui/hooks/use-mobile";

import CategoryCard from "~/components/shared/category-card";
import Error from "~/components/shared/error";
import CategoryCardSkeleton from "~/components/skeletons/category-card-skeleton";
import { useTRPC } from "~/trpc/react";

const Categories = () => {
  const isMobile = useIsMobile();

  const trpc = useTRPC();
  const {
    data: categories,
    isPending,
    isError,
  } = useQuery(
    trpc.category.getTopLevelCategories.queryOptions({
      limit: isMobile ? 5 : 4,
    }),
  );

  if (isPending) {
    return (
      <div className="container z-20 mx-auto flex max-w-full flex-col">
        <div className="grid grid-cols-3 gap-3 self-center pb-8 md:grid-cols-5">
          <CategoryCardSkeleton />
          <CategoryCardSkeleton />
          <CategoryCardSkeleton />
          <CategoryCardSkeleton />
          <CategoryCardSkeleton />
        </div>
      </div>
    );
  }

  if (isError) {
    return <Error message="Failed to load categories" />;
  }

  return (
    <div className="container z-20 mx-auto flex max-w-full flex-col">
      <div className="grid grid-cols-3 gap-3 self-center pb-8 md:grid-cols-5 md:gap-4 xl:gap-[30px]">
        {categories.map((category) => (
          <CategoryCard key={category.id} category={category} />
        ))}

        <CategoryCard
          category={{
            name: "Other Categories",
            image: "/static/images/other-category.png",
            createdAt: new Date(),
            deletedAt: null,
            id: "",
            isActive: true,
            updatedAt: new Date(),
            parentId: null,
            description: "Explore more categories",
            rate: "0",
            rateType: "PER_KG",
            tag: "",
            compensationKabadiwalaRate: "0",
            compensationRecyclerRate: "0",
          }}
        />
      </div>
    </div>
  );
};

export default Categories;
