import Image from "next/image";

import { HOW_IT_WORKS_CARDS } from "~/app/lib/constants";

const HowItWorks = () => {
  return (
    <section className="mx-auto max-w-full bg-black-0 px-6 md:px-14 xl:px-[120px]">
      <div className="flex flex-col gap-5 py-6 md:gap-6 md:py-10 xl:gap-9 xl:py-[64px]">
        {/* how it works title */}
        <div className="flex flex-col gap-1">
          <p className="text-[10px] font-medium leading-3 -tracking-[0.1px] text-yellow-950 md:text-sm md:leading-5 md:-tracking-[0.14px] xl:text-lg xl:leading-7 xl:-tracking-[0.18px]">
            HOW IT WORKS ?
          </p>

          <h2 className="font-jakarta text-[18px] font-semibold leading-[30px] -tracking-[0.18px] text-teal-850 md:text-xl md:leading-8 md:-tracking-[0.2px] xl:text-[30px] xl:leading-10 xl:-tracking-[0.3px]">
            Instant Cash Against Scrap Items <br className="md: hidden" /> in{" "}
            <span className="font-extrabold">3 Easy Steps!</span>
          </h2>
        </div>

        {/* how it works cards */}
        <div className="flex flex-col gap-[14px] md:gap-5 xl:flex-row xl:items-center xl:gap-8">
          {HOW_IT_WORKS_CARDS.map((item, idx) => (
            <HowItWorksCard key={idx} step={item} count={idx} />
          ))}
        </div>
      </div>
    </section>
  );
};

export default HowItWorks;

const HowItWorksCard = ({
  step,
  count,
}: {
  step: (typeof HOW_IT_WORKS_CARDS)[number];
  count: number;
}) => {
  return (
    <div className="flex justify-between gap-2 rounded-[20px] bg-white p-4 md:p-8 xl:flex-col xl:gap-10 xl:px-8 xl:py-6">
      <div className="flex flex-col gap-[14px] xl:gap-5">
        <div className="relative aspect-square w-6 md:w-8 xl:w-[48px]">
          <Image
            src={step.icon}
            alt={step.title}
            fill
            className="object-cover"
            unoptimized
          />
        </div>

        <div>
          <p className="font-jakarta text-[15px] font-bold leading-6 -tracking-[0.15px] text-black-800 xl:text-xl xl:leading-8 xl:-tracking-[0.2px]">
            {step.title}
          </p>
          <p className="w-[80%] text-xs leading-5 -tracking-[0.12px] text-black-600 md:w-full xl:text-base xl:leading-7 xl:-tracking-[0.16px]">
            {step.subTitle}
          </p>
        </div>
      </div>

      <div>
        <span className="overflow-hidden font-jakarta text-[50px] font-bold leading-[50px] -tracking-[0.5px] text-[#F5F4F4] md:text-[70px] md:leading-[50px] md:-tracking-[0.7px]">
          0{count + 1}
        </span>
      </div>
    </div>
  );
};
