import { getQueryClient, HydrateClient, prefetch, trpc } from "~/trpc/server";
import CTA from "../../../../components/shared/cta";
import Categories from "./categories";
import CurrentLocationIndicatorBar from "./current-location-indicator-bar";
import Faq from "./faq";
import <PERSON> from "./hero";
import HowItWorks from "./how-it-works";
import WhyChooseUs from "./why-choose-us";

const HomePage = () => {
  const queryClient = getQueryClient();
  void queryClient.prefetchQuery(
    trpc.category.getTopLevelCategories.queryOptions({ limit: 5 }),
  );
  void queryClient.prefetchQuery(trpc.faq.getTopLevelQuestions.queryOptions());
  void prefetch(trpc.address.getAllSavedAddresses.queryOptions());
  return (
    <HydrateClient>
      <main>
        <CurrentLocationIndicatorBar />
        <Hero />
        <Categories />
        <HowItWorks />
        <CTA />
        <WhyChooseUs />
        <Faq />

        {/* this div is for spacing to make whole content scrollable as the bottom tabs are fixed and the height of bottom tab is considered by browser so i am adding an calculated height to make whole content visible. */}
        <div className="h-[92px]" />
      </main>
    </HydrateClient>
  );
};

export default HomePage;
