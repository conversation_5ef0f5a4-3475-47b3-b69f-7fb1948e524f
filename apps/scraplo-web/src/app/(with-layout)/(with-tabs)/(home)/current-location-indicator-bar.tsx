"use client";

import { Suspense, useEffect } from "react";
import { useSuspenseQuery } from "@tanstack/react-query";
import { ChevronDown } from "lucide-react";
import { useQueryState } from "nuqs";

import { Button } from "@acme/ui/components/ui/button";

import type { UserAddress } from "~/app/lib/types";
import {
  LOCATION_PERMISSION_SHEET_PARAM_NAME,
  REQUEST_PICKUP_PARAM_NAME,
} from "~/app/lib/param-names";
import { useUserLocationStore } from "~/stores/user-store";
import { useTRPC } from "~/trpc/react";

const CurrentLocationIndicatorBar = () => {
  const { userLocation, setUserLocation } = useUserLocationStore();
  const trpc = useTRPC();
  const [, setPickupState] = useQueryState(REQUEST_PICKUP_PARAM_NAME, {
    clearOnDefault: true,
  });
  const [, setIsLocationPermissionSheetOpen] = useQueryState(
    LOCATION_PERMISSION_SHEET_PARAM_NAME,
    {
      clearOnDefault: true,
    },
  );
  const { data: savedAddresses } = useSuspenseQuery(
    trpc.address.getAllSavedAddresses.queryOptions(),
  );
  useEffect(() => {
    const defaultAddress = savedAddresses.addresses.find(
      (address) => address.isDefault,
    );
    if (defaultAddress) {
      setUserLocation(defaultAddress as UserAddress);
    }
  }, [savedAddresses, setUserLocation]);

  const handleLocationPermission = async () => {
    await setIsLocationPermissionSheetOpen("yes");
  };

  return (
    <div className="flex items-center justify-between gap-6 px-6 py-3 md:px-14 xl:px-[120px]">
      <div className="flex flex-col gap-1">
        <button
          onClick={handleLocationPermission}
          className="line-clamp-1 flex items-center gap-2 font-jakarta text-sm font-bold leading-[22px] -tracking-[0.14px] text-black-800 md:text-base md:leading-6 md:-tracking-[0.16px]"
        >
          {userLocation ? userLocation.name : "Add Pickup Location"}
          <ChevronDown className="size-4" />
        </button>
        <span className="line-clamp-1 text-[11px] leading-4 text-teal-900 md:text-sm md:leading-5 xl:text-[15px] xl:leading-6">
          {userLocation?.display ??
            userLocation?.localAddress ??
            "Grant Location Permission"}
        </span>
      </div>

      <Button className="bg-teal-600 p-3" onClick={() => setPickupState("yes")}>
        Request Pickup
      </Button>
    </div>
  );
};

const MainCurrentLocationIndicatorBar = () => {
  return (
    <Suspense
      fallback={
        <div className="h-12 w-full animate-pulse rounded-md bg-black-50" />
      }
    >
      <CurrentLocationIndicatorBar />
    </Suspense>
  );
};

export default MainCurrentLocationIndicatorBar;
