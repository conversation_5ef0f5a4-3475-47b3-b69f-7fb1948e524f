import Image from "next/image";

import { WHY_CHOOSE_US_CARDS } from "~/app/lib/constants";

const WhyChooseUs = () => {
  return (
    <section className="mx-auto max-w-full px-6 md:px-14 xl:px-[120px]">
      <div className="flex flex-col gap-6 py-6 md:py-10 xl:py-16">
        <h2 className="font-bold leading-[30px] -tracking-[0.18px] text-teal-850 md:text-xl md:leading-8 md:-tracking-[0.2px] xl:text-[30px] xl:leading-10 xl:-tracking-[0.3px]">
          Why Choose <PERSON> ?
        </h2>

        <div className="grid grid-cols-2 gap-[14px] xl:grid-cols-4 xl:gap-8">
          {WHY_CHOOSE_US_CARDS.map((item) => (
            <div
              key={item.title}
              className="flex min-w-[156px] max-w-full flex-col gap-4 rounded-[20px] border-[1.2px] border-[rgba(0,_51,_51,_0.10)] bg-[#F5FFFF] px-4 py-5 md:p-6 xl:p-8"
            >
              <div className="relative aspect-square w-6 md:w-8 xl:w-[48px]">
                <Image
                  src={item.icon}
                  alt={item.title}
                  fill
                  className="object-cover"
                  unoptimized
                />
              </div>

              <div className="space-y-1.5">
                <p className="font-jakarta text-[15px] font-bold leading-6 -tracking-[0.15px] text-black-800 md:text-base md:-tracking-[0.16px] xl:text-2xl xl:leading-8 xl:-tracking-[0.24px]">
                  {item.title}
                </p>
                <p className="text-sm -tracking-[0.14px] text-black-600 md:text-[15px] md:-tracking-[0.15px] xl:text-lg xl:leading-[30px] xl:-tracking-[0.18px]">
                  {item.subTitle}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default WhyChooseUs;
