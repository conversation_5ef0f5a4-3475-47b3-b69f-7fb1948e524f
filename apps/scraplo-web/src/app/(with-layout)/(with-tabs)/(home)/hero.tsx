const Hero = () => {
  return (
    <div
      className="z-10 -mb-[14px] flex flex-col gap-1 bg-cover bg-center bg-no-repeat p-6 md:gap-2"
      style={{ backgroundImage: "url(/static/images/hero.webp)" }}
    >
      <div className="flex w-fit items-center gap-2 self-center rounded-full bg-yellow-950 px-4 py-1">
        <span className="font-jakarta font-bold leading-[26px] tracking-[0.64px] text-yellow-450 md:text-lg md:leading-7 md:tracking-[0.72px] xl:text-2xl xl:leading-8 xl:tracking-[0.96px]">
          Cash
        </span>
        <span className="xl:leading-lg font-jakarta text-xs font-semibold -tracking-[0.24px] text-black-100 md:text-sm md:leading-5 md:-tracking-[0.28px] xl:leading-7 xl:-tracking-[0.36px]">
          from
        </span>
        <span className="font-jakarta font-bold leading-[26px] tracking-[0.64px] text-black-0 md:text-lg md:leading-7 md:tracking-[0.72px] xl:text-2xl xl:leading-8 xl:tracking-[0.96px]">
          Scrap
        </span>
      </div>

      <h1 className="mb-[26px] text-center font-jakarta text-[28px] font-extrabold capitalize leading-10 -tracking-[0.28px] text-teal-800 md:text-[30px] md:leading-[46px] md:-tracking-[0.3px] xl:text-[40px] xl:leading-[56px] xl:-tracking-[0.4px]">
        Sell scrap instantly with one click!
      </h1>
    </div>
  );
};

export default Hero;
