"use client";

import Image from "next/image";
import Link from "next/link";
import { useQuery } from "@tanstack/react-query";
import { ArrowRight } from "lucide-react";

import { ACCOUNT_MENU_ITEMS } from "~/app/lib/constants";
import LogoutButton from "~/components/shared/logout-button";
import { useTRPC } from "~/trpc/react";
import AccountMenuItem from "./account-menu-item";

interface AccountMenuSectionProps {
  title: string;
  items: {
    icon: string;
    title: string;
    path: string;
    hasChevron?: boolean;
    verification?: {
      show: boolean;
      text: string;
    };
  }[];
}

const AccountMenuSection = ({ title, items }: AccountMenuSectionProps) => {
  return (
    <div className="flex flex-col gap-[14px] rounded-2xl bg-black-0 px-2 py-3 xl:px-4 xl:py-[14px]">
      <h3 className="font-jakarta text-[13px] font-medium leading-[18px] text-black-500 xl:text-base xl:leading-6">
        {title}
      </h3>

      <ul className="flex flex-col gap-[10px] self-stretch xl:gap-3">
        {items.map((item) => (
          <AccountMenuItem
            key={item.title}
            icon={item.icon}
            title={item.title}
            path={item.path}
            hasChevron={item.hasChevron}
          />
        ))}
      </ul>
    </div>
  );
};

const BankAccountCTA = () => {
  const trpc = useTRPC();
  const { data } = useQuery(
    trpc.user.isAlreadyBankAccountLinked.queryOptions(),
  );

  return (
    <div className="overflow-hidden rounded-lg border-[1.2px] border-black-0 bg-[linear-gradient(93deg,rgba(229,255,255,0.80)_1.58%,rgba(255,250,230,0.80)_166.31%)] p-4 md:p-5 xl:px-9 xl:py-6">
      <div className="flex items-center gap-4">
        <div className="flex flex-1 gap-2">
          <div className="relative aspect-square size-7 md:size-[44px] xl:size-16">
            <Image
              src="/static/icons/bank.svg"
              alt="Bank"
              fill
              className="object-cover"
              unoptimized
            />
          </div>
          <div className="flex flex-col gap-1">
            <h3 className="font-jakarta text-base font-bold text-teal-850 xl:text-xl xl:leading-8">
              {data?.isLinked ? "Bank Account Linked" : "Add Bank Account"}
            </h3>
            <p className="text-[11px] leading-4 tracking-[0.11px] text-black-700 xl:text-lg xl:leading-7 xl:tracking-[0.18px]">
              {data?.isLinked
                ? "Your bank account is linked successfully."
                : "Link your bank account or UPI to receive cash directly."}
            </p>
          </div>
        </div>

        {!data?.isLinked && (
          <Link href="/profile/saved-cards-banks">
            <button className="flex items-center gap-2 rounded-lg bg-yellow-450 p-[10px] font-jakarta text-xs font-medium leading-4 tracking-[0.12px] text-black-900 md:px-4 md:py-3 md:text-sm">
              Link
              <ArrowRight className="h-4 w-4" />
            </button>
          </Link>
        )}
      </div>
    </div>
  );
};

const AccountMenuActions = () => {
  return (
    <div className="flex w-full flex-col gap-3 md:flex-row md:items-center md:gap-8">
      <LogoutButton />

      <div className="flex-1">
        <AccountMenuItem
          title="Delete Account"
          icon="/static/icons/delete.svg"
          hasChevron={false}
          path="/profile/delete-account"
          className="bg-[#FFF9F9] text-[#900B09] xl:py-4"
        />
      </div>
    </div>
  );
};

const AccountMenuItems = () => {
  return (
    <div className="flex flex-col gap-4 md:gap-6 xl:gap-8">
      <AccountMenuSection
        title={ACCOUNT_MENU_ITEMS.GENERAL_SETTINGS.title}
        items={ACCOUNT_MENU_ITEMS.GENERAL_SETTINGS.items}
      />

      <BankAccountCTA />

      <AccountMenuSection
        title={ACCOUNT_MENU_ITEMS.TERMS_CONDITIONS.title}
        items={ACCOUNT_MENU_ITEMS.TERMS_CONDITIONS.items}
      />

      <AccountMenuSection
        title={ACCOUNT_MENU_ITEMS.SUPPORT_HELP.title}
        items={ACCOUNT_MENU_ITEMS.SUPPORT_HELP.items}
      />

      <AccountMenuActions />
    </div>
  );
};

export default AccountMenuItems;
