"use client";

import Image from "next/image";
import Link from "next/link";
import { useSuspenseQuery } from "@tanstack/react-query";
import { StarIcon } from "lucide-react";

import ProfileDetailsStripSkeleton from "~/components/skeletons/profile-details-strip";
import { useTRPC } from "~/trpc/react";

const ProfileDetailsStrip = () => {
  const trpc = useTRPC();
  const {
    data: user,
    isFetching,
    error,
  } = useSuspenseQuery(trpc.user.getProfile.queryOptions());

  if (error) {
    return (
      <div className="bg-[rgba(252,_252,_252,_0.40)] px-6 py-4 shadow-md backdrop-blur-[30px] md:px-14 md:py-5 xl:px-[120px]">
        <p className="flex w-full items-center justify-center text-center text-red-500">
          Unable to fetch user profile
        </p>
      </div>
    );
  }

  if (isFetching) {
    return <ProfileDetailsStripSkeleton />;
  }

  return (
    <div className="flex items-center gap-2 bg-[rgba(252,_252,_252,_0.40)] px-6 py-4 shadow-md backdrop-blur-[30px] md:px-14 md:py-5 xl:px-[120px]">
      {/* profile image */}
      <div className="relative rounded-full border p-1">
        <div className="relative aspect-square size-12 overflow-hidden rounded-full border xl:size-[56px]">
          <Image
            src={user.image ?? "/static/images/default-user.png"}
            alt={user.fullName}
            fill
            className="object-cover"
            unoptimized
          />
        </div>
        <span className="absolute -bottom-3 left-[2px] flex flex-row items-center justify-center gap-2 rounded-full bg-black-900 p-1 text-sm text-white">
          <StarIcon fill={`rgb(252 226 105)`} color="none" className="size-4" />
          {Number(user.averageRating) > 0 ? user.averageRating : "0.0"}
        </span>{" "}
      </div>
      {/* name and details */}
      <div className="flex flex-1 flex-col gap-1">
        <span className="font-jakarta font-semibold leading-6 tracking-[0.32px] text-teal-850 xl:text-lg xl:leading-7 xl:tracking-[0.36px]">
          {user.fullName}
        </span>
        <span className="text-xs font-medium leading-4 text-black-600 xl:text-[15px] xl:leading-6">
          {user.phoneNumber}
        </span>
      </div>

      {/* edit profile button */}
      <Link href="/profile/edit">
        <button className="rounded-lg bg-yellow-50 px-3 py-2 font-jakarta text-sm font-medium leading-5 tracking-[0.14px] text-yellow-900 xl:px-5 xl:py-3 xl:text-base xl:leading-6 xl:tracking-[0.16px]">
          Edit Profile
        </button>
      </Link>
    </div>
  );
};

export default ProfileDetailsStrip;
