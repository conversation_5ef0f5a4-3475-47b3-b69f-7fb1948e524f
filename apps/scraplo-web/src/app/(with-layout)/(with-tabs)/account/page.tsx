import Image from "next/image";
import Link from "next/link";

import { Button } from "@acme/ui/components/ui/button";

import CTA from "~/components/shared/cta";
import { prefetch, trpc } from "~/trpc/server";
import AccountMenuItems from "./account-menu-items";
import ProfileDetailsStrip from "./profile-details-strip";

const AccountPage = () => {
  prefetch(trpc.user.getProfile.queryOptions());
  return (
    <main className="pb-28">
      <ProfileDetailsStrip />

      {/* navigation links */}
      <div className="flex flex-col gap-4 px-6 pt-5 md:px-16 md:pt-8 xl:px-[180px]">
        {/* buttons */}
        <div className="flex items-center gap-4 xl:gap-10">
          <Button
            variant="outline"
            className="flex-1 border-teal-800 bg-[#F7FFFF] px-4 font-inter text-sm font-medium leading-[22px] tracking-[0.28px] text-teal-850 xl:text-lg"
            asChild
          >
            <Link href="/profile/orders">
              <Image
                src="/static/icons/orders.svg"
                alt="orders"
                width={100}
                height={100}
                className="size-[18px] object-cover xl:size-6"
                unoptimized
              />
              Orders
            </Link>
          </Button>
          <Button
            variant="outline"
            className="flex-1 border-black-150 bg-black-0 px-4 font-inter text-sm font-medium leading-[22px] tracking-[0.28px] text-black-600 xl:text-lg"
            asChild
          >
            <Link href="/profile/help-and-support">
              <Image
                src="/static/icons/headphones.svg"
                alt="help"
                width={100}
                height={100}
                className="size-[18px] object-cover xl:size-6"
                unoptimized
              />
              Help Center
            </Link>
          </Button>
          <Button
            variant="outline"
            className="flex-1 border-black-150 bg-black-0 px-4 font-inter text-sm font-medium leading-[22px] tracking-[0.28px] text-black-600 xl:text-lg"
            asChild
          >
            <Link href="/profile/issues">
              <Image
                src="/static/icons/headphones.svg"
                alt="help"
                width={100}
                height={100}
                className="size-[18px] object-cover xl:size-6"
                unoptimized
              />
              Issues
            </Link>
          </Button>
        </div>

        {/* Account menu items */}
        <AccountMenuItems />

        <CTA
          className="rounded-[10px] xl:px-10 xl:py-8"
          innerContainerClassName="py-4 text-center md:text-start md:w-full"
          titleClassName="text-sm md:text-lg leading-[18px] xl:text-2xl"
          descriptionClassName="text-[11px] md:text-sm leading-[16px] px-9 md:px-0 xl:text-lg"
          buttonClassName="w-full md:w-fit px-4 py-3"
        />
      </div>
    </main>
  );
};

export default AccountPage;
