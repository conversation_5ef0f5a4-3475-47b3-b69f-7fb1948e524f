"use client";

import Image from "next/image";
import Link from "next/link";
import { ChevronRight } from "lucide-react";

import { cn } from "@acme/ui/lib/utils";

interface MenuItemProps {
  icon: string;
  title: string;
  path: string;
  hasChevron?: boolean;
  verification?: {
    show: boolean;
    text: string;
  };
  className?: string;
}

const AccountMenuItem = ({
  icon,
  title,
  path,
  className,
  hasChevron,
  verification,
}: MenuItemProps) => {
  const renderContent = () => (
    <li
      className={cn(
        "flex items-center gap-3 rounded-lg px-4 py-[10px] md:py-3",
        className,
      )}
    >
      <div className="flex flex-1 items-center gap-3">
        <div className="relative aspect-square size-[18px] md:size-5 xl:size-7">
          <Image
            src={icon}
            alt={title}
            fill
            className="object-cover"
            unoptimized
          />
        </div>
        <span className="font-inter text-[13px] font-medium leading-[18px] tracking-[0.26px] text-black-700 md:text-[15px] md:leading-6 md:tracking-[0.3px] xl:text-lg xl:leading-7 xl:tracking-[0.36px]">
          {title}
        </span>
      </div>

      {verification?.show && (
        <span className="rounded-full bg-yellow-50 px-2 py-0.5 font-jakarta text-[11px] font-medium leading-4 tracking-[0.22px] text-yellow-900">
          {verification.text}
        </span>
      )}

      {hasChevron && (
        <ChevronRight className="h-4 w-4 text-black-300 transition-all group-hover:translate-x-1 md:h-5 md:w-5 xl:w-6" />
      )}
    </li>
  );

  return <Link href={path}>{renderContent()}</Link>;
};

export default AccountMenuItem;
