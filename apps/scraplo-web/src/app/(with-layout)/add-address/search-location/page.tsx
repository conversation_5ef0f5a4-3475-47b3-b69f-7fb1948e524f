"use client";

import { useRouter } from "next/navigation";
import { ArrowLeft } from "lucide-react";

import type { DetectedLocation } from "@acme/ui/lib/types";
import GoogleAutocompleteInput from "@acme/ui/components/ui/google-autocomplete-input";

import { useUserLocationStore } from "~/stores/user-store";

const SearchLocationPage = () => {
  const router = useRouter();
  const { setUserLocation } = useUserLocationStore();

  const handleLocationSelect = (location: DetectedLocation) => {
    setUserLocation({
      name: "My Address",
      display: location.address?.display ?? "",
      landmark: location.address?.display ?? "",
      localAddress: location.address?.display ?? "",
      addressType: "OTHER",
      coordinates: {
        latitude: location.latitude,
        longitude: location.longitude,
      },
      country: location.address?.country ?? "",
      state: location.address?.state ?? "",
      city: location.address?.city ?? "",
      postalCode: location.address?.postalCode ?? "",
      street: location.address?.street ?? "",
    });

    router.push("/add-address");
  };

  const handleAutoDetectLocation = (location: DetectedLocation) => {
    setUserLocation({
      name: "My Address",
      display: location.address?.display ?? "",
      landmark: location.address?.display ?? "",
      localAddress: location.address?.display ?? "",
      addressType: "OTHER",
      coordinates: {
        latitude: location.latitude,
        longitude: location.longitude,
      },
      country: location.address?.country ?? "",
      state: location.address?.state ?? "",
      city: location.address?.city ?? "",
      postalCode: location.address?.postalCode ?? "",
      street: location.address?.street ?? "",
    });

    router.push("/add-address");
  };

  const handleBack = () => {
    router.back();
  };

  return (
    <main>
      <nav className="sticky top-0 z-50 flex w-full items-center gap-2 border-b-[1.2px] border-b-black-50 bg-[rgba(255,_252,_252,_0.60)] px-6 py-5 backdrop-blur-[20px] md:px-14 xl:px-[120px]">
        <ArrowLeft className="size-5" onClick={handleBack} />

        <p className="font-bold leading-7 text-black-900 xl:text-lg">
          Search Location
        </p>
      </nav>

      <div className="px-6 pt-6 md:px-14 xl:px-[120px]">
        <GoogleAutocompleteInput
          onLocationSelect={handleLocationSelect}
          onUserLocationDetect={handleAutoDetectLocation}
          showSearchIcon
          showClearButton
          showAutoDetectLocationIcon
        />
      </div>
    </main>
  );
};

export default SearchLocationPage;
