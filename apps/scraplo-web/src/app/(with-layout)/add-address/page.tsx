"use client";

import { Suspense, useEffect } from "react";
import dynamic from "next/dynamic";
import { skipToken, useQuery } from "@tanstack/react-query";
import { useQueryState } from "nuqs";

import { cn } from "@acme/ui/lib/utils";

import { useUserConfirmedLocationStore } from "~/stores/user-store";
import { useTRPC } from "~/trpc/react";
import LocationForm from "./location-form";
import LocationSelection from "./location-selection";

const AddNewAddressMap = dynamic(
  () => import("~/components/maps/add-new-address-map"),
  {
    ssr: false,
  },
);

const AddAddressPage = () => {
  const trpc = useTRPC();
  const [addressId] = useQueryState("addressId", { clearOnDefault: true });

  const { confirmedLocation, setConfirmedLocation } =
    useUserConfirmedLocationStore();

  const { data: addressData } = useQuery(
    trpc.address.getAddressById.queryOptions(
      addressId ? { addressId } : skipToken,
    ),
  );

  useEffect(() => {
    if (
      !addressId ||
      !addressData?.address.coordinates?.latitude ||
      !addressData.address.coordinates.longitude ||
      !addressData.address.addressType
    ) {
      return;
    }

    setConfirmedLocation({
      name: addressData.address.name,
      display: addressData.address.display,
      localAddress: addressData.address.localAddress,
      landmark: addressData.address.landmark,
      addressType: addressData.address.addressType,
      coordinates: {
        latitude: Number(addressData.address.coordinates.latitude),
        longitude: Number(addressData.address.coordinates.longitude),
      },
      country: addressData.address.country ?? "",
      state: addressData.address.state ?? "",
      city: addressData.address.city ?? "",
      postalCode: addressData.address.postalCode ?? "",
      street: addressData.address.street ?? "",
      googleAddressComponent: addressData.address.googleAddressComponent,
      district: addressData.address.district ?? "",
      googlePlaceId: addressData.address.googlePlaceId ?? "",
    });
  }, [addressData, setConfirmedLocation, addressId]);

  return (
    <>
      <AddNewAddressMap
        mapClassName={cn(
          confirmedLocation && "h-[20vh] lg:h-[30vh] xl:h-[40vh]",
        )}
      />

      {confirmedLocation || addressId ? (
        <LocationForm />
      ) : (
        <LocationSelection />
      )}
    </>
  );
};

const MainAddressPage = () => {
  return (
    <Suspense>
      <AddAddressPage />
    </Suspense>
  );
};

export default MainAddressPage;
