import type { z } from "zod";
import { useEffect } from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { skipToken, useMutation, useQuery } from "@tanstack/react-query";
import { Loader2 } from "lucide-react";
import { useQueryState } from "nuqs";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

import { addressTypeEnum } from "@acme/db/schema";
import { Button } from "@acme/ui/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@acme/ui/components/ui/form";
import { Input } from "@acme/ui/components/ui/input";
import { cn } from "@acme/ui/lib/utils";
import { AddressSchema } from "@acme/validators";

import {
  useUserConfirmedLocationStore,
  useUserLocationStore,
} from "~/stores/user-store";
import { useTRPC } from "~/trpc/react";

const LocationForm = () => {
  const [addressId] = useQueryState("addressId", { clearOnDefault: true });

  const router = useRouter();
  const { setUserLocation } = useUserLocationStore();
  const { confirmedLocation, setConfirmedLocation } =
    useUserConfirmedLocationStore();

  const isEditMode = !!addressId;

  const form = useForm<z.infer<typeof AddressSchema>>({
    resolver: zodResolver(AddressSchema),
    defaultValues: {
      name: "My Address",
      addressType: confirmedLocation?.addressType ?? "HOME",
    },
  });

  const addressType = form.watch("addressType");

  const trpc = useTRPC();

  const { data: addressData } = useQuery(
    trpc.address.getAddressById.queryOptions(
      addressId ? { addressId } : skipToken,
    ),
  );

  const { mutate: addNewAddress, isPending } = useMutation(
    trpc.address.addNewAddress.mutationOptions({
      onSuccess: (opts) => {
        toast.success(opts.message);
        form.reset();
        setUserLocation(null);
        setConfirmedLocation(null);
        router.push("/profile/saved-address");
      },
      onError: (opts) => {
        toast.error(opts.message);
      },
    }),
  );

  const { mutate: updateAddress, isPending: isUpdating } = useMutation(
    trpc.address.updateAddress.mutationOptions({
      onSuccess: (opts) => {
        toast.success(opts.message);
        form.reset();
        setUserLocation(null);
        setConfirmedLocation(null);
        router.push("/profile/saved-address");
      },
      onError: (opts) => {
        toast.error(opts.message);
      },
    }),
  );

  useEffect(() => {
    if (addressData && isEditMode) {
      if (
        !addressData.address.coordinates?.latitude ||
        !addressData.address.coordinates.longitude ||
        !addressData.address.addressType
      ) {
        return;
      }

      form.setValue("localAddress", addressData.address.localAddress);
      form.setValue("landmark", addressData.address.landmark);
      form.setValue("coordinates", addressData.address.coordinates);
      form.setValue("display", addressData.address.display);
      form.setValue("street", addressData.address.street ?? undefined);
      form.setValue("city", addressData.address.city ?? undefined);
      form.setValue("state", addressData.address.state ?? undefined);
      form.setValue("country", addressData.address.country ?? undefined);
      form.setValue("postalCode", addressData.address.postalCode ?? undefined);
      form.setValue("addressType", addressData.address.addressType);
      form.setValue("name", addressData.address.name);
      form.setValue("district", addressData.address.district ?? undefined);
      form.setValue("googlePlaceId", addressData.address.googlePlaceId ?? "");
      form.setValue(
        "googleAddressComponent",
        addressData.address.googleAddressComponent,
      );
    }

    if (confirmedLocation && !isEditMode) {
      form.setValue("coordinates", confirmedLocation.coordinates);
      form.setValue("display", confirmedLocation.display);
      form.setValue("street", confirmedLocation.street);
      form.setValue("city", confirmedLocation.city);
      form.setValue("state", confirmedLocation.state);
      form.setValue("country", confirmedLocation.country);
      form.setValue("postalCode", confirmedLocation.postalCode);
      form.setValue("district", confirmedLocation.district);
      form.setValue("googlePlaceId", confirmedLocation.googlePlaceId);
      form.setValue(
        "googleAddressComponent",
        confirmedLocation.googleAddressComponent,
      );
    }
  }, [form, addressData, isEditMode, confirmedLocation]);

  const onSubmit = (data: z.infer<typeof AddressSchema>) => {
    if (isEditMode) {
      if (!addressId) return;

      updateAddress({
        ...data,
        addressId,
      });
    } else addNewAddress(data);
  };

  return (
    <div>
      <div className="mb-4 flex items-center gap-3 border-b-[1.2px] border-black-50 px-6 py-[14px] md:px-14 xl:px-[120px]">
        <div className="relative aspect-square size-5">
          <Image
            src="/static/icons/location-3.svg"
            alt="location"
            fill
            className="object-cover"
            unoptimized
          />
        </div>

        <div className="flex flex-col gap-1">
          <h2 className="font-jakarta text-sm font-bold leading-5 text-black-700 xl:text-base">
            {confirmedLocation?.display}
          </h2>
          <p className="text-xs leading-4 text-black-500 xl:text-sm">
            {confirmedLocation?.city}, {confirmedLocation?.state}
          </p>
        </div>
      </div>

      <p className="mx-6 mb-8 rounded-lg bg-yellow-0 p-3 text-[11px] font-medium leading-[18px] text-yellow-850 md:mx-14 xl:mx-[120px] xl:text-base">
        * Enter details about your apartment to help pickup boy to reach your
        doorstep easily
      </p>

      <div className="min-h-[70vh] overflow-y-auto">
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit, (err) => console.log(err))}
            className="flex flex-col gap-5"
          >
            <FormField
              control={form.control}
              name="localAddress"
              render={({ field }) => (
                <FormItem className="px-6 md:px-14 xl:px-[120px]">
                  <FormLabel>House/ Flat/ Block number</FormLabel>
                  <FormControl>
                    <Input placeholder="eg: Block Ab" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="landmark"
              render={({ field }) => (
                <FormItem className="px-6 md:px-14 xl:px-[120px]">
                  <FormLabel>Landmark/ Road/ Area/ Locality</FormLabel>
                  <FormControl>
                    <Input placeholder="eg: Nearby xyz." {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Added Postal Code field */}
            <FormField
              control={form.control}
              name="postalCode"
              render={({ field }) => (
                <FormItem className="px-6 md:px-14 xl:px-[120px]">
                  <FormLabel>Postal Code</FormLabel>
                  <FormControl>
                    <Input placeholder="eg: 110001" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Added District field */}
            <FormField
              control={form.control}
              name="district"
              render={({ field }) => (
                <FormItem className="px-6 md:px-14 xl:px-[120px]">
                  <FormLabel>District</FormLabel>
                  <FormControl>
                    <Input placeholder="eg: South Delhi" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="addressType"
              render={({ field }) => (
                <FormItem className="px-6 md:px-14 xl:px-[120px]">
                  <FormLabel>Save as</FormLabel>
                  <FormControl>
                    <div className="flex items-center gap-3">
                      {addressTypeEnum.enumValues.map((item) => (
                        <button
                          type="button"
                          className={cn(
                            "flex items-center gap-1 rounded-full border border-black-100 bg-[#FDFDFD] px-3 py-2 text-[11px] font-medium capitalize leading-[14px] tracking-[0.11px] text-black-800",
                            field.value === item &&
                              "border-yellow-650 bg-[#FFFAE6]",
                          )}
                          onClick={() => field.onChange(item)}
                        >
                          {item === "HOME" && (
                            <Image
                              src="/static/icons/home-2.svg"
                              height={50}
                              width={50}
                              alt="home"
                              className="size-3 object-cover"
                              unoptimized
                            />
                          )}

                          {item === "WORK" && (
                            <Image
                              src="/static/icons/work.svg"
                              height={50}
                              width={50}
                              alt="home"
                              className="size-3 object-cover"
                              unoptimized
                            />
                          )}

                          {item === "OTHER" && (
                            <Image
                              src="/static/icons/location.svg"
                              height={50}
                              width={50}
                              alt="home"
                              className="size-3 object-cover"
                              unoptimized
                            />
                          )}

                          {item.toLowerCase()}
                        </button>
                      ))}
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {addressType === "OTHER" && (
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem className="px-6 md:px-14 xl:px-[120px]">
                    <FormLabel>Name</FormLabel>
                    <FormControl>
                      <Input placeholder="eg: Office." {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <div className="h-[110px]" />

            <div className="fixed bottom-0 w-full border-t-[1.2px] border-black-50 bg-[rgba(255,_255,_255,_0.30)] px-6 pb-10 pt-5 backdrop-blur-[15px] md:px-14 xl:px-[120px]">
              <Button
                type="submit"
                className="flex w-full items-center justify-center"
                disabled={isPending || isUpdating}
                variant={isPending ? "loading" : "default"}
              >
                {isPending && <Loader2 className="size-4 animate-spin" />}
                {isEditMode ? "Update" : "Save"} Address
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
};

export default LocationForm;
