"use client";

import { useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { Loader2 } from "lucide-react";

import { Button } from "@acme/ui/components/ui/button";

import useLocation from "~/hooks/use-location";
import {
  useUserConfirmedLocationStore,
  useUserLocationStore,
} from "~/stores/user-store";

const LocationSelection = () => {
  const router = useRouter();
  const { isLoading, requestLocation, error } = useLocation();
  const { setConfirmedLocation } = useUserConfirmedLocationStore();
  const { userLocation } = useUserLocationStore();

  const handleConfirmedLocation = () => {
    if (userLocation) {
      setConfirmedLocation(userLocation);
    }
  };

  const handleChangeLocation = () => {
    router.push("/add-address/search-location");
  };

  useEffect(() => {
    if (!userLocation) {
      void requestLocation();
    }
  }, [requestLocation, userLocation]);

  return (
    <>
      {isLoading && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black-900/50">
          <Loader2 className="h-8 w-8 animate-spin text-white" />
        </div>
      )}

      {/* use current location */}
      <button
        className="flex w-full items-center justify-center gap-4 bg-yellow-950 px-6 py-3 font-jakarta font-bold leading-[26px] -tracking-[0.16px] text-black-50 md:px-14 xl:px-[120px]"
        onClick={requestLocation}
      >
        <Image
          src="/static/icons/location-2.svg"
          alt="location"
          width={24}
          height={24}
          className="size-6"
          unoptimized
        />
        Use Current Location
      </button>

      {/* selected location */}
      <div className="flex flex-col gap-5 px-6 md:px-14 xl:px-[120px]">
        <div className="space-y-1 pb-3 pt-[14px]">
          <p className="font-bold leading-7 text-black-900 md:text-lg xl:text-xl">
            Select pickup location
          </p>
          <p className="text-sm leading-[21px] text-black-500 md:text-base xl:text-lg">
            Select nearby location for easier pickup for assigned agent
          </p>
        </div>

        {error && (
          <div className="flex flex-col items-center justify-center rounded-xl border bg-black-50 py-10">
            <p className="font-jakarta text-lg font-semibold leading-6 text-black-800">
              Location Permission Denied
            </p>
            <Link
              href="/add-address/search-location"
              className="font-jakarta font-semibold underline underline-offset-4"
            >
              Enter Location Manually
            </Link>
          </div>
        )}

        {userLocation && (
          <div className="flex gap-1.5 rounded-xl bg-yellow-0 p-3 md:p-4 xl:p-5">
            <div>
              <div className="relative aspect-square w-6">
                <Image
                  src="/static/icons/location-3.svg"
                  alt="location"
                  fill
                  className="object-cover"
                  unoptimized
                />
              </div>
            </div>

            <div className="flex w-full flex-col gap-2">
              <div className="flex items-center justify-between font-jakarta text-[15px] font-bold leading-6 -tracking-[0.15px] text-black-800">
                <p className="line-clamp-2 xl:text-xl">
                  {userLocation.display}
                </p>
                <button
                  className="bg-[#EBFFEE] px-2 py-1 text-[9px] leading-[14px] -tracking-[0.09px] text-teal-800 md:text-[11px] xl:text-sm"
                  onClick={handleChangeLocation}
                >
                  CHANGE
                </button>
              </div>
              <p className="text-xs leading-[18px] text-black-800 md:text-sm xl:text-base">
                {userLocation.city}
              </p>
            </div>
          </div>
        )}
      </div>

      <div className="fixed bottom-0 w-full px-6 pb-10 md:px-14 xl:px-[120px]">
        <Button
          className="w-full"
          disabled={userLocation ? false : true}
          onClick={handleConfirmedLocation}
        >
          Confirm Pickup Location
        </Button>
      </div>
    </>
  );
};

export default LocationSelection;
