"use client";

import { StreamCall } from "@stream-io/video-react-sdk";

import Call<PERSON> from "~/components/getstream-calling/call-ui";
import useGetStreamCallById from "~/hooks/use-get-stream-call-by-id";

interface CallContentProps {
  callId: string;
}

const CallContent = ({ callId }: CallContentProps) => {
  const { call, isCallLoading } = useGetStreamCallById(callId);

  if (isCallLoading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="text-center">
          <div className="mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-2 border-gray-300 border-t-blue-600"></div>
          <p className="text-gray-600">Connecting to call...</p>
        </div>
      </div>
    );
  }

  if (!call) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">Call not found or has ended</p>
        </div>
      </div>
    );
  }

  return (
    <StreamCall call={call}>
      <CallUI />
    </StreamCall>
  );
};

export default CallContent;
