"use client";

import Image from "next/image";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { Loader2 } from "lucide-react";
import { toast } from "sonner";

import { <PERSON><PERSON> } from "@acme/ui/components/ui/button";

import { DELETE_ACCOUNT_INFO } from "~/app/lib/constants";
import HelpButton from "~/components/shared/help-button";
import PageHeader from "~/components/shared/page-header";
import { useTRPC } from "~/trpc/react";
import DeleteAccountForm from "./delete-account-form";

const DeletePage = () => {
  const trpc = useTRPC();
  const queryClient = useQueryClient();
  const {
    data: isDeleteAccountRequestPending = false,
    isPending,
    isError,
  } = useQuery(trpc.user.isDeleteAccountRequestPending.queryOptions());

  const {
    mutate: revokeDeleteAccountRequest,
    isPending: isRevokingDeleteAccountRequest,
  } = useMutation(
    trpc.user.revokeDeleteAccountRequest.mutationOptions({
      onSuccess: async () => {
        await queryClient.invalidateQueries({
          queryKey: trpc.user.isDeleteAccountRequestPending.queryKey(),
        });
        toast.success("Your delete account request has been revoked.");
      },
      onError: (err) => {
        toast.error(err.message);
      },
    }),
  );

  return (
    <main className="relative flex h-screen flex-col gap-5 overscroll-auto">
      <PageHeader
        title="Delete Account"
        titleClassName="text-teal-800 text-[18px] leading-7"
        action={<HelpButton />}
      />

      {/* info points */}
      <div className="px-6 md:px-14 xl:px-[120px]">
        <div className="flex flex-col gap-3 rounded-[8px] bg-[#FFF7F7] p-3 md:p-5">
          <p className="text-[13px] font-medium leading-6 text-[#900B09] md:text-base xl:text-lg">
            {DELETE_ACCOUNT_INFO.title}
          </p>
          <ul className="flex list-inside list-disc flex-col gap-2 text-xs leading-[18px] -tracking-[0.12px] text-black-600 md:text-sm xl:text-base">
            {DELETE_ACCOUNT_INFO.important_info_points.map((point, index) => (
              <li key={index}>{point}</li>
            ))}
          </ul>
        </div>
      </div>

      {/* before proceeding points */}
      <div className="px-6 md:px-14 xl:px-[120px]">
        <div className="flex flex-col gap-3 rounded-[8px] border border-black-150 bg-black-0 p-[18px] md:p-5">
          <p className="font-jakarta text-sm font-semibold leading-5 -tracking-[0.28px] text-black-800 md:text-base xl:text-lg">
            Before proceeding, please ensure:
          </p>
          <ul className="flex flex-col gap-3 text-xs leading-[18px] -tracking-[0.12px] text-black-600 md:text-sm xl:text-base">
            {DELETE_ACCOUNT_INFO.please_ensure_points.map((point, index) => (
              <li key={index}>
                <div className="flex items-center gap-2">
                  <Image
                    src="/static/icons/check.svg"
                    alt="check"
                    width={100}
                    height={100}
                    className="aspect-square size-3 min-w-3"
                  />
                  <p>{point}</p>
                </div>
              </li>
            ))}
          </ul>
        </div>
      </div>

      {isPending ? (
        <div className="flex h-[100px] items-center justify-center px-6">
          <Loader2 className="size-5 animate-spin" />
        </div>
      ) : isError ? (
        <div className="flex h-[100px] items-center justify-center px-6 text-red-500">
          Failed to load delete account status
        </div>
      ) : isDeleteAccountRequestPending ? (
        <div className="flex flex-col items-center justify-center gap-8 px-6 text-center font-jakarta text-xl font-semibold text-black-800 md:px-14">
          Your request to delete account has already been submitted
          <Button
            onClick={() => revokeDeleteAccountRequest()}
            disabled={isRevokingDeleteAccountRequest}
            className="w-full bg-teal-850 text-black-0 md:w-auto"
          >
            {isRevokingDeleteAccountRequest && (
              <Loader2 className="mr-2 size-4 animate-spin" />
            )}
            Revoke Delete Account Request
          </Button>
        </div>
      ) : (
        <DeleteAccountForm />
      )}
    </main>
  );
};

export default DeletePage;
