"use client";

import type { z } from "zod";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

import { <PERSON><PERSON> } from "@acme/ui/components/ui/button";
import { Checkbox } from "@acme/ui/components/ui/checkbox";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@acme/ui/components/ui/form";
import { Textarea } from "@acme/ui/components/ui/textarea";
import { DeleteAccountSchema } from "@acme/validators";

import { useTRPC } from "~/trpc/react";

const DeleteAccountForm = () => {
  const form = useForm<z.infer<typeof DeleteAccountSchema>>({
    resolver: zodResolver(DeleteAccountSchema),
    defaultValues: {
      reason: "",
      confirmPermanent: false,
    },
  });

  const trpc = useTRPC();
  const queryClient = useQueryClient();
  const { mutate: deleteAccount, isPending } = useMutation(
    trpc.user.deleteAccountRequest.mutationOptions({
      onSuccess: async (opts) => {
        await queryClient.invalidateQueries(
          trpc.user.isDeleteAccountRequestPending.queryOptions(),
        );
        toast.success(opts.message);
      },
      onError: (err) => {
        toast.error(err.message);
      },
    }),
  );

  const onSubmit = (data: z.infer<typeof DeleteAccountSchema>) => {
    deleteAccount(data);
  };

  return (
    <div className="rounded-[8px]">
      <h2 className="mb-4 px-6 font-jakarta font-semibold leading-6 text-black-800 md:px-14 xl:px-[120px]">
        Verify Your Identity
      </h2>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          <FormField
            control={form.control}
            name="reason"
            render={({ field }) => (
              <FormItem className="mb-[30px] px-6 md:px-14 xl:px-[120px]">
                <FormLabel>Reason for Deletion</FormLabel>
                <FormControl>
                  <Textarea
                    className="min-h-[300px]"
                    placeholder="Please provide a reason for account deletion."
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="confirmPermanent"
            render={({ field }) => (
              <FormItem className="mx-6 mb-5 flex flex-row items-start space-x-3 space-y-0 md:mx-14 xl:mx-[120px]">
                <FormControl>
                  <Checkbox
                    className="size-5"
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
                <div className="text-sm leading-5 text-black-700">
                  <FormLabel>
                    I understand this action is permanent and cannot be undone
                  </FormLabel>
                </div>
              </FormItem>
            )}
          />

          <div className="sticky bottom-0 left-0 flex w-full flex-col gap-[14px] border-t border-t-[1.2px] border-black-50 bg-[rgba(255,_255,_255,_0.30)] px-6 pb-10 pt-4 backdrop-blur-[15px] md:px-14 xl:px-[120px]">
            <Button
              type="submit"
              className="w-full bg-[#900B09] text-black-0"
              disabled={isPending}
            >
              {isPending ? "Processing..." : "Delete Account"}
            </Button>

            <Button type="button" variant="disabled" className="w-full">
              Cancel
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default DeleteAccountForm;
