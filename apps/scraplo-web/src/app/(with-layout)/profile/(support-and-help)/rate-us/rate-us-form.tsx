"use client";

import type { z } from "zod";
import { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";

import { Button } from "@acme/ui/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@acme/ui/components/ui/form";
import { Textarea } from "@acme/ui/components/ui/textarea";
import { cn } from "@acme/ui/lib/utils";
import { RateUsFormSchema } from "@acme/validators";

import { EMOTION_EMOJIS, QUICK_SUGGESTIONS } from "~/app/lib/constants";

const MAX_STARS = 5;

const RateUsForm = () => {
  const [selectedEmojiIndex, setSelectedEmojiIndex] = useState<number | null>(
    null,
  );
  const [hoveredStar, setHoveredStar] = useState<number | null>(null);
  const [selectedStar, setSelectedStar] = useState<number | null>(null);

  const form = useForm<z.infer<typeof RateUsFormSchema>>({
    resolver: zodResolver(RateUsFormSchema),
    defaultValues: {
      sellingExperience: "",
      customerSupport: 0,
      suggestions: "",
      quickSuggestions: [],
    },
  });

  function onSubmit(data: z.infer<typeof RateUsFormSchema>) {
    console.log("Form Data:", data);
  }

  const quickSuggestions = form.watch("quickSuggestions");
  const quickSuggestionsError = form.formState.errors.quickSuggestions;

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="space-y-9 p-6 md:px-14 xl:px-[120px]"
      >
        {/* Selling Experience */}
        <FormField
          control={form.control}
          name="sellingExperience"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="font-jakarta text-sm font-bold leading-6 text-black-800">
                How was your selling experience today?
              </FormLabel>
              <FormControl>
                <div className="flex space-x-2 pt-2">
                  {EMOTION_EMOJIS.map((emoji, index) => (
                    <Button
                      key={emoji}
                      type="button"
                      variant="outline"
                      className={`h-12 w-12 rounded-full p-0 text-2xl transition-all duration-200 ease-in-out hover:scale-110 ${
                        selectedEmojiIndex === index
                          ? "scale-110 border-primary bg-primary/10 ring-2 ring-primary"
                          : "border-border"
                      } ${
                        selectedEmojiIndex !== null &&
                        selectedEmojiIndex !== index
                          ? "opacity-50"
                          : ""
                      }`}
                      onClick={() => {
                        field.onChange(emoji);
                        setSelectedEmojiIndex(index);
                      }}
                    >
                      {emoji}
                    </Button>
                  ))}
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Customer Support */}
        <FormField
          control={form.control}
          name="customerSupport"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="font-jakarta text-sm font-bold leading-6 text-black-800">
                Customer support
              </FormLabel>
              <FormControl>
                <div className="flex items-center justify-between pt-2">
                  {Array.from({ length: MAX_STARS }, (_, index) => {
                    const starValue = index + 1;
                    return (
                      <Button
                        key={starValue}
                        type="button"
                        variant="ghost"
                        className={`p-0 text-5xl transition-colors duration-150 ease-in-out md:text-[80px] xl:text-[110px] ${
                          (hoveredStar !== null && starValue <= hoveredStar) ||
                          (selectedStar !== null && starValue <= selectedStar)
                            ? "text-yellow-400" // Filled star color from image
                            : "text-gray-300" // Empty star color from image
                        }`}
                        onClick={() => {
                          field.onChange(starValue);
                          setSelectedStar(starValue);
                        }}
                        onMouseEnter={() => setHoveredStar(starValue)}
                        onMouseLeave={() => setHoveredStar(null)}
                      >
                        ★
                      </Button>
                    );
                  })}
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Suggestions Textarea */}
        <FormField
          control={form.control}
          name="suggestions"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="font-jakarta text-sm font-bold leading-6 text-black-800">
                Please provide any suggestions for improving the platform.
              </FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Type here"
                  className="min-h-[120px] resize-none rounded-lg border-border bg-background p-3 focus:border-ring focus:ring-ring"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Quick Suggestion Buttons */}
        <div className="flex flex-wrap gap-2">
          {QUICK_SUGGESTIONS.map((suggestion) => {
            const isSelected = quickSuggestions.includes(suggestion);

            return (
              <Button
                key={suggestion}
                type="button"
                variant="outline"
                className={cn(
                  "rounded-full border border-black-200 bg-black-0 px-4 py-2 text-[11px] leading-4 text-black-800 hover:text-black-800",
                  isSelected && "bg-teal-800 text-black-0",
                )}
                onClick={() => {
                  const currentSuggestions = form.getValues("quickSuggestions");
                  if (isSelected) {
                    form.setValue(
                      "quickSuggestions",
                      currentSuggestions.filter((s) => s !== suggestion),
                    );
                  } else {
                    form.setValue("quickSuggestions", [
                      ...currentSuggestions,
                      suggestion,
                    ]);
                  }
                }}
              >
                {suggestion}
              </Button>
            );
          })}

          <p className="text-xs font-medium leading-6 text-red-600">
            {quickSuggestionsError?.message}
          </p>
        </div>

        {/* Action Buttons */}
        <div className="fixed bottom-0 left-0 flex w-full flex-col space-y-3 px-6 pb-10 pt-6 backdrop-blur-[24px] md:px-14 xl:px-[120px]">
          <Button type="submit" className="w-full bg-teal-600">
            Submit
          </Button>
          <Button type="button" variant="outline" className="w-full">
            Cancel
          </Button>
        </div>
      </form>
    </Form>
  );
};

export default RateUsForm;
