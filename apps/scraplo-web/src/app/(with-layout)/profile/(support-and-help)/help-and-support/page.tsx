"use client";

import Image from "next/image";
import { useQueryState } from "nuqs";

import { Separator } from "@acme/ui/components/ui/separator";

import { CONTACT_US_INFO } from "~/app/lib/constants";
import { SUPPORT_CHAT_PARAM_NAME } from "~/app/lib/param-names";
import FaqList from "~/components/shared/faq-list";
import HelpButton from "~/components/shared/help-button";
import NeedSupportCard from "~/components/shared/need-support-card";
import PageHeader from "~/components/shared/page-header";
import SupportChatSheet from "~/components/shared/support-chat-sheet";
import { SystemConfig } from "~/components/shared/system-config";

const HelpAndSupportPage = () => {
  // Use nuqs for query state
  const [supportChatId, setSupportChatId] = useQueryState(
    SUPPORT_CHAT_PARAM_NAME,
    {
      clearOnDefault: true,
    },
  );

  return (
    <main className="flex flex-col gap-y-6">
      <PageHeader
        title="Help & Support"
        titleClassName="text-teal-800 text-[18px] leading-7"
        action={<HelpButton />}
      />

      {/* contact informations */}
      <div className="px-6 md:px-14 xl:px-[120px]">
        <div className="flex flex-col gap-5">
          <p className="px-2 text-center text-sm leading-6 text-black-800 xl:text-base">
            Feel free to reach out to us with any inquiries, feedback, or
            requests.
          </p>

          <div className="flex flex-col gap-4 xl:flex-row">
            {/* email */}
            <div className="flex items-center gap-3 rounded-[10px] bg-yellow-0 px-4 py-2 xl:flex-1 xl:items-start">
              <div className="relative aspect-square size-5 xl:size-6">
                <Image
                  src={CONTACT_US_INFO.email.icon}
                  alt="email"
                  fill
                  className="object-cover"
                  unoptimized
                />
              </div>

              <div className="space-y-1">
                <p className="font-jakarta text-xs font-semibold leading-4 text-teal-850 xl:text-base">
                  {CONTACT_US_INFO.email.title}
                </p>
                <p className="text-sm font-medium leading-5 text-black-800 xl:text-lg">
                  {/* {CONTACT_US_INFO.email.value} */}
                  <SystemConfig type="CONTACT_EMAIL" />
                </p>
              </div>
            </div>

            {/* phone */}
            <div className="flex items-center gap-3 rounded-[10px] bg-yellow-0 px-4 py-2 xl:flex-1 xl:items-start">
              <div className="relative aspect-square size-5 xl:size-6">
                <Image
                  src={CONTACT_US_INFO.phone.icon}
                  alt="phone"
                  fill
                  className="object-cover"
                  unoptimized
                />
              </div>
              <div className="space-y-1">
                <p className="font-jakarta text-xs font-semibold leading-4 text-teal-850 xl:text-base">
                  {CONTACT_US_INFO.phone.title}
                </p>
                <p className="text-sm font-medium leading-5 text-black-800 xl:text-lg">
                  {/* {CONTACT_US_INFO.phone.value} */}
                  <SystemConfig type="CONTACT_PHONE" />
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <Separator className="bg-black-150 px-6" />

      {/* quick questions  */}
      <div className="flex flex-col gap-3 px-6 md:px-14 xl:px-[120px]">
        <p className="font-jakarta text-sm font-bold leading-5 text-black-800 md:text-base xl:text-lg">
          What issue you are facing
        </p>
        <FaqList />
      </div>

      {/* need support card */}
      <div className="mx-6 mb-10 flex flex-col gap-5 rounded-3xl border-[1.2px] bg-yellow-0 px-4 py-5 md:mx-14 xl:mx-[120px]">
        <NeedSupportCard
          onClick={() => {
            void setSupportChatId(supportChatId ?? "new");
          }}
          title="Chat with us"
          buttonClassName="w-full border border-black-50 bg-black-0 text-black-900"
        />
      </div>
      <SupportChatSheet
        open={!!supportChatId}
        onOpenChange={(open) => {
          if (!open) void setSupportChatId(null);
        }}
        supportChatId={supportChatId}
        setSupportChatId={setSupportChatId}
      />
    </main>
  );
};

export default HelpAndSupportPage;
