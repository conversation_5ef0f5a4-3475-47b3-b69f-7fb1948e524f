import { redirect } from "next/navigation";

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@acme/ui/components/ui/accordion";
import { Button } from "@acme/ui/components/ui/button";
import { Editor } from "@acme/ui/editor";

import HelpButton from "~/components/shared/help-button";
import PageHeader from "~/components/shared/page-header";
import { getQueryClient, trpc } from "~/trpc/server";

interface IndividualFaqPageProps {
  params: Promise<{ faqid?: string }>;
}

const IndividualFaqPage = async ({ params }: IndividualFaqPageProps) => {
  const faqId = (await params).faqid;

  if (!faqId) {
    redirect("/");
  }

  const queryClient = getQueryClient();
  const question = await queryClient.fetchQuery(
    trpc.faq.getQuestionById.queryOptions({ id: faqId }),
  );
  const subQuestions = await queryClient.fetchQuery(
    trpc.faq.getQuestionsByParentId.queryOptions({ parentId: faqId }),
  );

  return (
    <main className="flex flex-col gap-y-6">
      <PageHeader
        title="Help & Support"
        titleClassName="text-teal-800 text-[18px] leading-7"
        action={<HelpButton />}
      />

      <div className="flex flex-col gap-4 px-6 md:px-14 xl:px-[120px]">
        <h2 className="font-jakarta text-sm font-semibold leading-5 text-teal-850 md:text-base xl:text-lg">
          {question.content}
        </h2>

        <ul className="ml-4 list-inside list-disc space-y-2">
          {question.answers.map((answer) => (
            <li
              key={answer.id}
              className="text-xs leading-relaxed text-black-500 md:text-sm xl:text-base"
            >
              <Editor editorSerializedState={answer.content} editable={false} />
            </li>
          ))}
        </ul>

        {subQuestions.length > 0 && (
          <div className="mt-5 flex flex-col gap-4">
            <h2 className="font-jakarta text-sm font-semibold leading-5 text-teal-850 md:text-base xl:text-lg">
              Related Questions
            </h2>
            <Accordion type="single" collapsible>
              <div className="flex flex-col gap-2">
                {subQuestions.map((item) => (
                  <AccordionItem key={item.id} value={`item-${item.id}`}>
                    <AccordionTrigger>{item.content}</AccordionTrigger>
                    <AccordionContent>
                      <ul className="ml-4 list-inside list-disc space-y-2">
                        {item.answers.map((answer) => (
                          <li
                            key={answer.id}
                            className="leading-relaxed text-gray-700"
                          >
                            {answer.content}
                          </li>
                        ))}
                      </ul>
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </div>
            </Accordion>
          </div>
        )}

        <div className="h-[110px]" />
      </div>

      <div className="fixed bottom-0 w-full bg-[rgba(255,_255,_255,_0.30)] px-6 pb-10 pt-5 backdrop-blur-[15px] md:px-14 xl:px-[120px]">
        <Button className="w-full">Chat with us</Button>
      </div>
    </main>
  );
};

export default IndividualFaqPage;
