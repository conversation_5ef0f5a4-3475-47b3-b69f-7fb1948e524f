"use client";

import { useState } from "react";
import Link from "next/link";
import { useQuery } from "@tanstack/react-query";

import { Button } from "@acme/ui/components/ui/button";
import {
  Tabs,
  Ta<PERSON>Content,
  Ta<PERSON>List,
  TabsTrigger,
} from "@acme/ui/components/ui/tabs";

import PageHeader from "~/components/shared/page-header";
import { useTRPC } from "~/trpc/react";
import IssueCard from "./IssueCard";

const ACTIVE_STATUSES = ["OPEN", "ACKNOWLEDGED"];
const RESOLVED_STATUSES = ["CLOSED", "REJECTED"];

const IssuesPage = () => {
  const trpc = useTRPC();
  const [tab, setTab] = useState("active");
  const { data: issues = [], isLoading } = useQuery(
    trpc.issue.getSellerIssues.queryOptions(),
  );
  console.log("issues", issues);

  const activeIssues = issues.filter((issue) =>
    ACTIVE_STATUSES.includes(issue.status),
  );
  const resolvedIssues = issues.filter((issue) =>
    RESOLVED_STATUSES.includes(issue.status),
  );

  return (
    <div className="pb-8">
      <PageHeader title="My Issues" />
      <div className="max-auto container flex min-w-full flex-col py-4">
        <div className="mb-4 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <Button
            asChild
            className="rounded-lg bg-teal-600 px-6 py-2 font-semibold text-white shadow-sm transition-colors duration-200 hover:bg-teal-700"
          >
            <Link href={"/profile/issues/new"}>New Issue</Link>
          </Button>
          <Tabs value={tab} onValueChange={setTab} className="w-full sm:w-auto">
            <TabsList className="flex gap-2 rounded-lg bg-teal-100 p-1">
              <TabsTrigger
                value="active"
                className="rounded-md px-4 py-2 font-medium transition-colors data-[state=active]:bg-teal-600 data-[state=inactive]:bg-transparent data-[state=active]:text-white data-[state=inactive]:text-teal-700"
              >
                Active Issues
              </TabsTrigger>
              <TabsTrigger
                value="resolved"
                className="rounded-md px-4 py-2 font-medium transition-colors data-[state=active]:bg-teal-600 data-[state=inactive]:bg-transparent data-[state=active]:text-white data-[state=inactive]:text-teal-700"
              >
                Resolved Issues
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
        <div className="mt-2">
          <Tabs value={tab} onValueChange={setTab} className="w-full">
            <TabsContent value="active">
              {isLoading ? (
                <div>Loading...</div>
              ) : activeIssues.length === 0 ? (
                <div className="text-muted-foreground">No active issues.</div>
              ) : (
                activeIssues.map((issue) => (
                  <IssueCard
                    key={issue.id}
                    issue={{
                      id: issue.id,
                      orderId: issue.orderId,
                      description: issue.description,
                      status: issue.status,
                      createdAt: issue.createdAt.toISOString(),
                      imageUrls: issue.imageUrls ?? [],
                    }}
                  />
                ))
              )}
            </TabsContent>
            <TabsContent value="resolved">
              {isLoading ? (
                <div>Loading...</div>
              ) : resolvedIssues.length === 0 ? (
                <div className="text-muted-foreground">No resolved issues.</div>
              ) : (
                resolvedIssues.map((issue) => (
                  <IssueCard
                    key={issue.id}
                    issue={{
                      id: issue.id,
                      orderId: issue.orderId,
                      description: issue.description,
                      status: issue.status,
                      createdAt: issue.createdAt.toISOString(),
                      imageUrls: issue.imageUrls ?? [],
                    }}
                  />
                ))
              )}
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

export default IssuesPage;
