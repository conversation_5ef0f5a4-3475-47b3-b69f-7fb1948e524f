"use client";

import { useEffect, useState } from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { generateUploadButton } from "@uploadthing/react";
import { Loader2, Trash, Upload } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";

import { Button } from "@acme/ui/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@acme/ui/components/ui/form";
import { Input } from "@acme/ui/components/ui/input";
import { Textarea } from "@acme/ui/components/ui/textarea";

import type { OurFileRouter } from "~/app/api/uploadthing/core";
import PageHeader from "~/components/shared/page-header";
import { useTRPC } from "~/trpc/react";

const UploadButton = generateUploadButton<OurFileRouter>();

const IssueFormSchema = z.object({
  orderId: z.string().min(1, "Order ID is required"),
  description: z.string().min(1, "Description is required"),
  imageUrls: z.array(z.string().url()).max(4, "Max 4 images allowed"),
});

type IssueFormValues = z.infer<typeof IssueFormSchema>;

const ISSUE_DRAFT_KEY = "issueFormDraft";

export default function IssuesPage() {
  const trpc = useTRPC();
  const queryClient = useQueryClient();
  const [images, setImages] = useState<string[]>([]);
  const [deleting, setDeleting] = useState<string | null>(null);

  const form = useForm<IssueFormValues>({
    resolver: zodResolver(IssueFormSchema),
    defaultValues: {
      orderId: "",
      description: "",
      imageUrls: [],
    },
  });
  const router = useRouter();

  // Load draft from localStorage on mount
  useEffect(() => {
    if (typeof window === "undefined") return;
    const draft = localStorage.getItem(ISSUE_DRAFT_KEY);
    if (draft) {
      const parsed = IssueFormSchema.safeParse(JSON.parse(draft));
      if (parsed.success) {
        form.reset(parsed.data);
        setImages(parsed.data.imageUrls);
      }
    }
  }, [form]);

  // Save draft to localStorage on form or image change
  useEffect(() => {
    if (typeof window === "undefined") return;
    const subscription = form.watch((values) => {
      localStorage.setItem(
        ISSUE_DRAFT_KEY,
        JSON.stringify({ ...values, imageUrls: images }),
      );
    });
    return () => subscription.unsubscribe();
  }, [form, images]);

  const createIssue = useMutation(
    trpc.issue.createIssue.mutationOptions({
      onSuccess: () => {
        toast.success("Issue submitted successfully");
        form.reset();
        setImages([]);
        localStorage.removeItem(ISSUE_DRAFT_KEY);
        void queryClient.invalidateQueries({
          queryKey: trpc.issue.getSellerIssues.queryKey(),
        });
        router.push("/profile/issues");
      },
      onError: (error) => {
        toast.error(error.message);
      },
    }),
  );

  const deleteImage = useMutation(
    trpc.utils.deleteUploadthingFileUsingFileKey.mutationOptions({
      onSuccess: (_opts, variables) => {
        setImages((prev) => prev.filter((url) => url !== variables.fileKey));
        form.setValue(
          "imageUrls",
          images.filter((url) => url !== variables.fileKey),
        );
        toast.success("Image removed");
        setDeleting(null);
      },
      onError: (error) => {
        toast.error(error.message);
        setDeleting(null);
      },
    }),
  );

  const onSubmit = (data: IssueFormValues) => {
    createIssue.mutate(
      {
        ...data,
        imageUrls: images,
      },
      {
        onSuccess: () => {
          toast.success("Issue submitted successfully");
          form.reset();
          setImages([]);
          localStorage.removeItem(ISSUE_DRAFT_KEY);
        },
        onError: (error) => {
          toast.error(error.message);
        },
      },
    );
  };

  return (
    <main className="">
      <PageHeader
        title="Report an Issue"
        titleClassName="text-teal-800 text-[18px] leading-7"
      />
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="container flex w-full min-w-full max-w-xl flex-col items-center gap-6 space-y-6"
        >
          <FormField
            control={form.control}
            name="orderId"
            render={({ field }) => (
              <FormItem className="w-full">
                <FormLabel>Order ID</FormLabel>
                <FormControl>
                  <Input placeholder="Enter Order ID" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem className="w-full">
                <FormLabel>Description</FormLabel>
                <FormControl>
                  <Textarea placeholder="Describe your issue" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <div>
            <FormLabel>Upload Images (max 4)</FormLabel>
            <div className="my-2">
              <UploadButton
                appearance={{
                  button:
                    "bg-primary text-primary-foreground hover:bg-primary/90 rounded-md px-4 py-2 text-sm font-medium shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring",
                  allowedContent: "text-muted-foreground text-xs",
                  container: "flex flex-col gap-2 w-full",
                }}
                content={{
                  button: ({ isUploading }) => (
                    <span className="flex items-center gap-2">
                      {isUploading ? (
                        <Loader2 className="animate-spin" />
                      ) : (
                        <Upload className="size-4" />
                      )}
                      {isUploading ? "Uploading..." : "Upload Images"}
                    </span>
                  ),
                  allowedContent: "JPEG, PNG or WebP (max 4MB)",
                }}
                endpoint="imageUploader"
                onClientUploadComplete={(res) => {
                  setImages(res.map((f) => f.url));
                  form.setValue(
                    "imageUrls",
                    res.map((f) => f.url),
                  );
                }}
                onUploadError={(error) => {
                  toast.error(error.message);
                }}
              />
            </div>
            <div className="mt-2 flex gap-2">
              {images.map((url) => (
                <div key={url} className="group relative">
                  <Image
                    width={80}
                    height={80}
                    src={url}
                    alt="Uploaded"
                    className="h-20 w-20 rounded border object-cover"
                    unoptimized
                  />
                  <Button
                    type="button"
                    size="icon"
                    variant="destructive"
                    className="absolute -right-2 -top-2 z-10 size-7 opacity-80 group-hover:opacity-100"
                    disabled={deleting === url || deleteImage.isPending}
                    onClick={() => {
                      setDeleting(url);
                      deleteImage.mutate({ fileKey: url });
                    }}
                  >
                    {deleting === url && deleteImage.isPending ? (
                      <Loader2 className="size-4 animate-spin" />
                    ) : (
                      <Trash className="size-4" />
                    )}
                  </Button>
                </div>
              ))}
            </div>
          </div>
          <Button
            type="submit"
            className="w-full"
            disabled={createIssue.isPending}
          >
            {createIssue.isPending ? "Submitting..." : "Submit Issue"}
          </Button>
        </form>
      </Form>
    </main>
  );
}
