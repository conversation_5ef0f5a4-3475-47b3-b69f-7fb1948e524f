import Image from "next/image";

import type { issueStatusEnum } from "@acme/db/schema";
import { Badge } from "@acme/ui/components/ui/badge";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@acme/ui/components/ui/card";

export interface IssueCardProps {
  issue: {
    id: string;
    orderId: string;
    description: string;
    status: (typeof issueStatusEnum.enumValues)[number];
    createdAt: string;
    imageUrls?: string[];
  };
}

const statusMap: Record<
  string,
  { label: string; color: string; message: string }
> = {
  OPEN: {
    label: "Open",
    color: "bg-yellow-500",
    message: "Issue has been created. Our support team will look into it.",
  },
  ACKNOWLEDGED: {
    label: "Acknowledged",
    color: "bg-blue-500",
    message:
      "Your ticket has been seen by our support team. We will look into it.",
  },
  CLOSED: {
    label: "Resolved",
    color: "bg-green-600",
    message:
      "Your issue has been resolved and have been awarded Rs. 500 in your payout account.",
  },
  REJECTED: {
    label: "Resolved",
    color: "bg-green-600",
    message: "Your issue has been resolved.",
  },
};

export function IssueCard({ issue }: IssueCardProps) {
  const status = statusMap[issue.status] ?? {
    label: issue.status,
    color: "bg-gray-300",
    message: issue.status,
  };
  return (
    <Card className="mb-4">
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-base font-medium">
          Order #{issue.orderId}
        </CardTitle>
        <Badge className={status.color + " text-white"}>{status.label}</Badge>
      </CardHeader>
      <CardContent>
        <div className="mb-2 text-sm text-muted-foreground">
          {new Date(issue.createdAt).toLocaleString()}
        </div>
        <div className="mb-2 text-sm">{issue.description}</div>
        <div className="mb-2 text-sm text-muted-foreground">
          {status.message}
        </div>
        {issue.imageUrls && issue.imageUrls.length > 0 && (
          <div className="mt-2 flex gap-2">
            {issue.imageUrls.map((url) => (
              <Image
                key={url}
                src={url}
                alt="Issue image"
                width={60}
                height={60}
                className="w-15 h-15 rounded border object-cover"
                unoptimized
              />
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

export default IssueCard;
