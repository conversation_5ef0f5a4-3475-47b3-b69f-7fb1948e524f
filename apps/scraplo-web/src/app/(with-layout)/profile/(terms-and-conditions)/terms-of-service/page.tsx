import { Suspense } from "react";

import { Editor } from "@acme/ui/editor";

import PageHeader from "~/components/shared/page-header";
import { getQueryClient, trpc } from "~/trpc/server";

const TermsAndConditionsPage = async () => {
  const client = getQueryClient();
  const data = await client.fetchQuery(
    trpc.config.getSystemConfig.queryOptions({
      key: "TERMS_AND_CONDITIONS",
    }),
  );
  return (
    <Suspense>
      <PageHeader
        title="Terms & Condition"
        titleClassName="text-teal-800 text-[18px] leading-7"
      />
      <div className="flex flex-col gap-6 px-8 py-9 md:px-14 xl:px-[120px]">
        <Editor editorSerializedState={data ?? ""} editable={false} />
      </div>
    </Suspense>
  );
};

export default TermsAndConditionsPage;
