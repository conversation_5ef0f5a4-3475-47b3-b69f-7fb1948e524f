import { Suspense } from "react";

import { Editor } from "@acme/ui/editor";

import PageHeader from "~/components/shared/page-header";
import { getQueryClient, trpc } from "~/trpc/server";

const PrivacyPolicyPage = async () => {
  const client = getQueryClient();
  const data = await client.fetchQuery(
    trpc.config.getSystemConfig.queryOptions({
      key: "PRIVACY_POLICY",
    }),
  );
  return (
    <Suspense>
      <PageHeader
        title="Privacy Policy"
        titleClassName="text-teal-800 text-[18px] leading-7"
      />
      <div className="flex flex-col gap-6 px-8 py-9 md:px-14 xl:px-[120px]">
        <Editor editorSerializedState={data ?? ""} editable={false} />
      </div>
    </Suspense>
  );
};

export default PrivacyPolicyPage;
