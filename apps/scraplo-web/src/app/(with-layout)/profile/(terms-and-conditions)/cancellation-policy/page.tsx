import { Separator } from "@acme/ui/components/ui/separator";

import { CANCELLATION_POLICY } from "~/app/lib/constants";
import HelpButton from "~/components/shared/help-button";
import PageHeader from "~/components/shared/page-header";
import PolicyHeroSection from "~/components/shared/policy-hero-section";
import PolicyItem from "~/components/shared/policy-item";

const CancellationPolicyPage = () => {
  return (
    <>
      <PageHeader
        title="Cancellation Policy"
        titleClassName="text-teal-800 text-[18px] leading-7"
        action={<HelpButton />}
      />

      <PolicyHeroSection
        title="Cancellation Policy"
        description="Customers can cancel orders up to five times. After three cancellations, no action is taken, but a warning is issued on the fourth. A fifth cancellation results in account blacklisting."
        lastUpdatedAt={new Date(Date.now())}
      />

      <div className="flex flex-col gap-6 px-8 py-9 md:px-14 xl:px-[120px]">
        <PolicyItem
          title="Cancellation Policy Overview"
          description="At Scraplo, we value our customers and understand that sometimes cancellations are necessary. However, we have established a cancellation policy to ensure fair usage of our services. Customers are allowed to cancel their orders up to five times. If an order is canceled three times without any action taken, a warning will be issued on the fourth cancellation. Should a fifth cancellation occur, the account will be blacklisted and terminated."
        />

        <Separator className="bg-black-100" />

        {CANCELLATION_POLICY.map((item) => (
          <PolicyItem
            key={item.title}
            title={item.title}
            description={item.description}
          />
        ))}
      </div>
    </>
  );
};

export default CancellationPolicyPage;
