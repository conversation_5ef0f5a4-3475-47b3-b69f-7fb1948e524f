"use client";

import { Suspense, useEffect } from "react";
import { useInfiniteQuery } from "@tanstack/react-query";
import { useQueryStates } from "nuqs";
import { useInView } from "react-intersection-observer";

import type { OrderFilterTab } from "~/app/lib/types";
import { ORDER_FILTER_TABS_PARAM_NAME } from "~/app/lib/param-names";
import { orderParsers } from "~/app/lib/search-params";
import OrderCardSkeleton from "~/components/skeletons/order-card-skeleton";
import { useTRPC } from "~/trpc/react";
import OrderCard from "./order-card";

const OrdersList = () => {
  const { ref, inView } = useInView({
    rootMargin: "100px",
    threshold: 0.1,
  });

  const [{ [ORDER_FILTER_TABS_PARAM_NAME]: selectedTab }] =
    useQueryStates(orderParsers);

  const trpc = useTRPC();
  const { data, isPending, fetchNextPage, hasNextPage, isFetchingNextPage } =
    useInfiniteQuery({
      ...trpc.order.getAllOrders.infiniteQueryOptions({
        tab: selectedTab as OrderFilterTab,
        limit: 10,
      }),
      initialPageParam: null,
      getNextPageParam: (lastPage) => lastPage.nextCursor ?? null,
    });

  useEffect(() => {
    if (inView && hasNextPage && !isFetchingNextPage) {
      void fetchNextPage();
    }
  }, [inView, hasNextPage, isFetchingNextPage, fetchNextPage]);

  const allOrders = data?.pages
    ? data.pages.flatMap((page) => page.orders)
    : [];

  if (isPending) {
    return (
      <div className="flex flex-col gap-5 px-6">
        <OrderCardSkeleton />
        <OrderCardSkeleton />
      </div>
    );
  }

  if (allOrders.length === 0) {
    return (
      <div className="flex h-[300px] w-full items-center justify-center">
        <p className="font-jakarta text-xl font-semibold text-teal-900">
          No orders found.
        </p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 gap-5 px-6 lg:grid-cols-2 xl:grid-cols-3">
      {allOrders.map((order, index) => {
        if (allOrders.length === index + 1) {
          return (
            <div ref={ref} key={order.id}>
              <OrderCard orderItem={order} />
            </div>
          );
        } else {
          return <OrderCard key={order.id} orderItem={order} />;
        }
      })}
      {isFetchingNextPage && (
        <>
          <OrderCardSkeleton />
          <OrderCardSkeleton />
          <OrderCardSkeleton />
        </>
      )}
    </div>
  );
};

const OrderListWrapper = () => {
  return (
    <Suspense>
      <OrdersList />
    </Suspense>
  );
};

export default OrderListWrapper;
