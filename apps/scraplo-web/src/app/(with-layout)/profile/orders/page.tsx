import type { SearchParams } from "nuqs/server";
import { Download } from "lucide-react";

import { Button } from "@acme/ui/components/ui/button";

import { ORDER_FILTER_TABS_PARAM_NAME } from "~/app/lib/param-names";
import { orderSearchParamsCache } from "~/app/lib/search-params";
import PageHeader from "~/components/shared/page-header";
import { getQueryClient, HydrateClient, trpc } from "~/trpc/server";
import OrderFilterTabs from "./order-filter-tabs";
import OrdersList from "./orders-list";

interface OrdersPageProps {
  searchParams: Promise<SearchParams>;
}

const OrdersPage = async ({ searchParams }: OrdersPageProps) => {
  const queryClient = getQueryClient();

  const { [ORDER_FILTER_TABS_PARAM_NAME]: currentTab } =
    await orderSearchParamsCache.parse(searchParams);

  void queryClient.prefetchQuery(
    trpc.order.getAllOrders.queryOptions({ tab: currentTab }),
  );

  return (
    <HydrateClient>
      <PageHeader
        title="Orders"
        titleClassName="text-teal-800 text-[18px] leading-7"
        action={
          <Button className="p-3">
            <Download />
          </Button>
        }
      />

      <div className="w-full lg:px-12 xl:px-[120px]">
        {/* order filter tabs */}
        <OrderFilterTabs />

        {/* order list */}
        <OrdersList />
        {/* <RateKabadiwalaSheet /> */}
      </div>
    </HydrateClient>
  );
};

export default OrdersPage;
