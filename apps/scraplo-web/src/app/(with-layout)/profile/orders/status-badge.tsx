import type { OrderItem } from "~/app/lib/types";

interface StatusBadgeProps {
  variant: OrderItem["status"];
}

const StatusBadge = ({ variant }: StatusBadgeProps) => {
  const config = {
    ACTIVE: {
      bgColor: "bg-[#018848]",
      text: "Active",
    },
    COMPLETED: {
      bgColor: "bg-black-900",
      text: "Completed",
    },
    CANCELLED: {
      bgColor: "bg-[#A30300]",
      text: "Cancelled",
    },
    PENDING: {
      bgColor: "bg-orange-500",
      text: "PENDING",
    },
  };

  const { bgColor, text } = config[variant as keyof typeof config];

  return (
    <span
      className={`rounded-[6px] ${bgColor} px-2 py-1.5 text-[11px] font-medium leading-4 -tracking-[0.33px] text-black-0`}
    >
      {text}
    </span>
  );
};

export default StatusBadge;
