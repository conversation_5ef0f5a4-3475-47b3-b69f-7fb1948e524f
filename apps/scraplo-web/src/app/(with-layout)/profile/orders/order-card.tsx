"use client";

import Link from "next/link";
import { ArrowR<PERSON> } from "lucide-react";

import { But<PERSON> } from "@acme/ui/components/ui/button";
import { Separator } from "@acme/ui/components/ui/separator";

import type { OrderItem } from "~/app/lib/types";
import StatusBadge from "./status-badge";

interface OrderCardProps {
  orderItem: OrderItem;
}

const OrderCard = ({ orderItem }: OrderCardProps) => {
  const showRateAgentButton =
    orderItem.kabadiwalaId &&
    orderItem.status !== "PENDING" &&
    orderItem.status !== "ACTIVE";

  return (
    <Link
      href={`/profile/orders/${orderItem.id}`}
      className="flex flex-col gap-2 overflow-hidden rounded-xl border-[1.2px] border-black-50 p-3 md:gap-3 md:p-4 lg:gap-4 lg:p-5"
    >
      {/* id and status */}
      <div className="flex items-center justify-between gap-2 md:gap-4 lg:gap-6">
        <div className="">
          <span className="font-jakarta text-xs font-semibold leading-[18px] text-yellow-850 md:text-sm md:leading-[21px] lg:text-base lg:leading-[24px]">
            Order #{orderItem.id}
          </span>
          <br />
          <span className="text-[10px] leading-[15px] text-black-600 md:text-xs md:leading-[18px] lg:text-sm lg:leading-[20px]">
            {orderItem.createdAt.toDateString()}
          </span>
        </div>

        <StatusBadge variant={orderItem.status} />
      </div>

      <Separator className="bg-black-150" />

      {/* details */}
      <div className="flex flex-col gap-3 md:gap-[14px] lg:gap-4">
        {/* scrap items */}
        <div className="flex flex-col gap-1">
          <span className="font-jakarta text-[10px] font-medium leading-[13px] tracking-[0.1px] text-black-500 md:text-[11px] md:leading-[14px] md:tracking-[0.11px] lg:text-xs lg:leading-[16px]">
            Scrap Items
          </span>
          <p className="text-xs font-medium leading-[16px] tracking-[0.12px] text-black-800 md:text-[13px] md:leading-[18px] md:tracking-[0.13px] lg:text-sm lg:leading-[20px]">
            {orderItem.items.map((item, idx) => (
              <span key={item.id}>
                {item.category.name}
                {idx !== orderItem.items.length - 1 && ", "}
              </span>
            ))}
          </p>
        </div>

        {/* area */}
        <div className="flex flex-col gap-1">
          <span className="font-jakarta text-[10px] font-medium leading-[13px] tracking-[0.1px] text-black-500 md:text-[11px] md:leading-[14px] md:tracking-[0.11px] lg:text-xs lg:leading-[16px]">
            Area
          </span>
          <p className="line-clamp-3 text-xs font-medium leading-[16px] tracking-[0.12px] text-black-800 md:text-[13px] md:leading-[18px] md:tracking-[0.13px] lg:text-sm lg:leading-[20px]">
            {orderItem.address.display}
          </p>
        </div>

        {/* amount paid */}
        <div className="flex flex-col gap-1">
          <span className="font-jakarta text-[10px] font-medium leading-[13px] tracking-[0.1px] text-black-500 md:text-[11px] md:leading-[14px] md:tracking-[0.11px] lg:text-xs lg:leading-[16px]">
            Amount Paid
          </span>
          <p className="line-clamp-3 text-xs font-medium leading-[16px] tracking-[0.12px] text-black-800 md:text-[13px] md:leading-[18px] md:tracking-[0.13px] lg:text-sm lg:leading-[20px]">
            {orderItem.totalAmount ? (
              <span>₹{orderItem.totalAmount}</span>
            ) : (
              "Under Review"
            )}
          </p>
        </div>
      </div>

      <Separator className="bg-black-150" />

      {/* view details button */}
      <div className="flex flex-col items-stretch gap-2 md:flex-row md:items-center md:gap-3 lg:gap-4">
        {showRateAgentButton && (
          <Button
            className="flex-1 border-yellow-650 bg-yellow-0 py-2 text-xs text-black-900 md:py-2.5 md:text-sm lg:text-base"
            // onClick={() => {
            //   void setRateParam(orderItem.id);
            // }}
          >
            Rate Agent
          </Button>
        )}

        <Button className="flex-1 bg-black-0 py-2 text-xs font-semibold leading-[16px] tracking-[0.12px] text-teal-800 md:py-2.5 md:text-[13px] md:leading-[18px] md:tracking-[0.13px] lg:text-sm lg:leading-[20px]">
          View Details{" "}
          <ArrowRight className="ml-1 h-3 w-3 md:h-4 md:w-4 lg:h-5 lg:w-5" />
        </Button>
      </div>
    </Link>
  );
};

export default OrderCard;
