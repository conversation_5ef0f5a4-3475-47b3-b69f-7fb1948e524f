"use client";

import { Suspense } from "react";
import { useQueryStates } from "nuqs";

import { cn } from "@acme/ui/lib/utils";

import type { OrderFilterTab } from "~/app/lib/types";
import { ORDER_STATUS } from "~/app/lib/constants";
import { ORDER_FILTER_TABS_PARAM_NAME } from "~/app/lib/param-names";
import { orderParsers } from "~/app/lib/search-params";

const OrderFilterTabs = () => {
  const [{ [ORDER_FILTER_TABS_PARAM_NAME]: selectedTab }, setOrderStates] =
    useQueryStates(orderParsers);

  const handleTabChange = async (tab: OrderFilterTab) => {
    await setOrderStates({ "order-filter-tab": tab });
  };

  return (
    <div className="hide-scrollbar flex w-full items-center gap-3 overflow-x-auto whitespace-nowrap px-6 pb-4 pt-5 md:gap-4 lg:gap-5">
      {ORDER_STATUS.map((status) => {
        if (status === "CART") return null;

        return (
          <button
            key={status}
            className={cn(
              "rounded-[6px] bg-black-0 px-[10px] py-2 font-jakarta text-xs font-semibold leading-4 tracking-[0.12px] text-black-600 md:px-3 md:py-2.5 md:text-sm md:leading-5 lg:px-4 lg:py-3 lg:text-base lg:leading-6",
              selectedTab === status &&
                "text-black-950 border-[1.2px] border-yellow-650 bg-yellow-0",
            )}
            onClick={() => handleTabChange(status)}
          >
            {status}
          </button>
        );
      })}
    </div>
  );
};

const OrderFilterTabsWrapper = () => {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <OrderFilterTabs />
    </Suspense>
  );
};

export default OrderFilterTabsWrapper;
