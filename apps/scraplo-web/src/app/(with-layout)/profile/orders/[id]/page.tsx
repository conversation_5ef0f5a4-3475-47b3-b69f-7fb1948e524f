import { tryCatch } from "@acme/validators/utils";

import Error from "~/components/shared/error";
import HelpButton from "~/components/shared/help-button";
import PageHeader from "~/components/shared/page-header";
import { getQueryClient, HydrateClient, prefetch, trpc } from "~/trpc/server";
import ActiveOrderDetails from "./active-order-details";
import CompletedOrderDetails from "./completed-order-details";

interface OrderDetailsPageProps {
  params: Promise<{
    id: string;
  }>;
}

const OrderDetailsPage = async ({ params }: OrderDetailsPageProps) => {
  const orderId = (await params).id;

  const queryClient = getQueryClient();
  const { data: orderData, err: orderErr } = await tryCatch(
    queryClient.fetchQuery(
      trpc.order.getOrderById.queryOptions({
        orderId: orderId,
      }),
    ),
  );
  prefetch(
    trpc.order.getOrderById.queryOptions({
      orderId: orderId,
    }),
  );
  prefetch(
    trpc.order.getEstimatedOrderTotalAmount.queryOptions({
      orderId: orderId,
    }),
  );

  if (orderErr) {
    return <Error message="Failed to load order details." />;
  }

  const DetailPageToBeRendered =
    orderData.status === "ACTIVE" || orderData.status === "PENDING" ? (
      <ActiveOrderDetails id={orderId} />
    ) : (
      <CompletedOrderDetails orderDetails={orderData} />
    );

  return (
    <HydrateClient>
      <PageHeader
        title="Order Details"
        titleClassName="text-teal-800 text-[18px] leading-7"
        action={<HelpButton />}
      />

      {DetailPageToBeRendered}
    </HydrateClient>
  );
};

export default OrderDetailsPage;
