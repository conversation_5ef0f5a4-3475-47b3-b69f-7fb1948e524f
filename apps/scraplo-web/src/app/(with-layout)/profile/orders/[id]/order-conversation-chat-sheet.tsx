"use client";

import { Suspense, useState } from "react";
import { useCall } from "@stream-io/video-react-sdk";
import {
  skipToken,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";
import { ChevronRight, Send, X } from "lucide-react";
import { useQueryState } from "nuqs";

import { Button } from "@acme/ui/components/ui/button";
import { Input } from "@acme/ui/components/ui/input";
import { Sheet, SheetContent } from "@acme/ui/components/ui/sheet";

import { MESSAGE_FETCH_INTERVAL, QUICK_ACTIONS } from "~/app/lib/constants";
import { ORDER_DETAILS_CHAT_PARAM_NAME } from "~/app/lib/param-names";
import Error from "~/components/shared/error";
import { useSession } from "~/server/auth/client";
import { useTRPC } from "~/trpc/react";
import ConversationMessagesList from "./conversation-messages-list";

const OrderConversationChatSheet = () => {
  const [messageContent, setMessageContent] = useState<string | null>(null);
  const [orderId, setOrderId] = useQueryState(ORDER_DETAILS_CHAT_PARAM_NAME, {
    clearOnDefault: true,
  });
  const client = useCall();

  const openSheet = !!orderId;

  const trpc = useTRPC();
  const queryClient = useQueryClient();
  const { data, isPending, isError } = useQuery(
    trpc.conversation.getConversationByOrderId.queryOptions(
      orderId ? { orderId } : skipToken,
      {
        refetchInterval: MESSAGE_FETCH_INTERVAL,
      },
    ),
  );

  const { mutate: sendNewMessage, isPending: isSendingMessage } = useMutation(
    trpc.conversation.sendNewMessage.mutationOptions({
      onSuccess: async () => {
        await queryClient.invalidateQueries(
          trpc.conversation.getConversationByOrderId.queryOptions(
            orderId
              ? {
                  orderId: orderId,
                }
              : skipToken,
          ),
        );
      },
    }),
  );
  const { data: loggedInUser } = useSession();
  if (!orderId) {
    return null;
  }

  if (isPending) {
    return <>Loading...</>;
  }

  if (isError) {
    return <Error />;
  }

  const handleClose = async () => {
    await setOrderId(null);
  };

  const handleCall = async () => {
    if (!client) return;
    if (!data.kabadiwala?.id || !loggedInUser?.user.id) return;

    await client.getOrCreate({
      ring: true,
      data: {
        members: [
          { user_id: loggedInUser.user.id },
          { user_id: data.kabadiwala.id },
        ],
        video: false,
      },
    });
    // Wait for the other user to accept, show calling status
  };

  const handleSendNewMessage = () => {
    if (!messageContent) return;

    sendNewMessage({
      orderId: orderId,
      content: messageContent,
    });

    setMessageContent(null);
  };

  const handleQuickActionClick = (action: string) => {
    sendNewMessage({
      orderId: orderId,
      content: action,
    });
  };
  return (
    <Sheet open={openSheet} onOpenChange={handleClose}>
      <SheetContent
        side="bottom"
        className="flex min-h-[90%] flex-col rounded-t-3xl p-0"
        hideCloseButton
      >
        {/* header */}
        <div className="flex items-center justify-between gap-4 border-b border-b-black-50 px-6 pb-3 pt-6">
          <Button
            className="rounded-full bg-black-50 p-1.5"
            onClick={handleClose}
          >
            <X />
          </Button>

          <h2 className="flex-1 font-bold leading-7 text-black-900">Chat</h2>

          {/* <button className="rounded-full border border-yellow-650 bg-yellow-0 p-3">
            <Image
              src="/static/icons/telephone.svg"
              alt="Call"
              width={20}
              height={20}
              unoptimized
            />
          </button> */}
          <Button onClick={handleCall}>Call</Button>
        </div>

        <div className="flex max-h-[72vh] flex-1 flex-col gap-5 overflow-y-auto px-6">
          <div className="w-fit rounded-[10px] bg-yellow-0 p-[14px]">
            <span className="mb-2 font-jakarta text-xs font-medium leading-[18px] text-black-600">
              Quick Actions
            </span>
            <div className="flex flex-col gap-3">
              {QUICK_ACTIONS.map((msg) => (
                <p
                  key={msg}
                  className="flex items-center justify-between gap-2 rounded-[6px] border-black-100 bg-white p-2 text-[11px] font-medium leading-4 -tracking-[0.22px] text-teal-850"
                  onClick={() => handleQuickActionClick(msg)}
                >
                  {msg}

                  <ChevronRight className="size-[10px]" />
                </p>
              ))}
            </div>
          </div>

          {/* eslint-disable-next-line @typescript-eslint/no-unnecessary-condition */}
          <ConversationMessagesList messages={data.messages?.messages ?? []} />
        </div>

        {/* footer with input box */}
        <div className="flex items-center gap-3 px-6 pb-10 pt-5">
          <Input
            placeholder="Type your message here..."
            className="rounded-full border-black-200 bg-black-0 px-3 py-3"
            value={messageContent ?? ""}
            onChange={(e) => setMessageContent(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === "Enter" && !e.shiftKey) {
                e.preventDefault();
                handleSendNewMessage();
              }
            }}
          />
          <Button
            className="rounded-full bg-yellow-450 p-3"
            onClick={handleSendNewMessage}
            disabled={isSendingMessage}
          >
            <Send />
          </Button>
        </div>
      </SheetContent>
    </Sheet>
  );
};

const OrderConversationChatSheetWrapper = () => {
  return (
    <Suspense>
      <OrderConversationChatSheet />
    </Suspense>
  );
};

export default OrderConversationChatSheetWrapper;
