"use client";

import { useState } from "react";
import Image from "next/image";
import { useQuery } from "@tanstack/react-query";
import { ArrowR<PERSON>, CircleCheck, User, Wallet } from "lucide-react";
import { useQueryState } from "nuqs";

import { <PERSON><PERSON> } from "@acme/ui/components/ui/button";
import { Separator } from "@acme/ui/components/ui/separator";

import type { OrderDetail } from "~/app/lib/types";
import { ORDER_DETAILS_RATE_PARAM_NAME } from "~/app/lib/param-names";
import AgentOrderCard from "~/components/shared/agent-card";
import ItemPriceCard from "~/components/shared/item-price-card";
import NeedSupportCard from "~/components/shared/need-support-card";
import {
  Card,
  CardDivider,
  CardHeader,
  KeyValuePair,
} from "~/components/shared/sections";
import SupportChatSheet from "~/components/shared/support-chat-sheet";
import { useTRPC } from "~/trpc/react";
import RateKabadiwalaSheet, { RatedCard } from "./rate-kabadiwala-sheet";

interface CompletedOrderDetailsProps {
  orderDetails: OrderDetail;
}

const CompletedOrderDetails = ({
  orderDetails,
}: CompletedOrderDetailsProps) => {
  const [isAdminChatOpen, setIsAdminChatOpen] = useState(false);
  const [, setRateParam] = useQueryState(ORDER_DETAILS_RATE_PARAM_NAME, {
    clearOnDefault: true,
  });
  const trpc = useTRPC();

  const { data: kabadiwalaReview } = useQuery(
    trpc.order.getKabadiwalaReviewByOrderId.queryOptions({
      orderId: orderDetails.id,
    }),
  );

  const { data: sellerReview } = useQuery(
    trpc.order.getSellerReviewByKabadiwalaByOrderId.queryOptions({
      orderId: orderDetails.id,
    }),
  );
  return (
    <main>
      <div className="flex flex-col gap-6 px-6 py-5 md:px-[64px] lg:gap-9 lg:px-[180px]">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="font-jakarta text-base font-semibold leading-6 text-black-800 md:text-lg lg:text-xl">
              Order Completed on time
            </h2>
          </div>
        </div>

        <p className="flex items-center gap-1 rounded-full bg-gradient-to-r from-[#EAFFF5] to-[#FFFFFF] px-[5px] py-1 text-center text-xs font-medium text-[#007940] md:text-sm lg:text-base">
          <CircleCheck />
          Pickup Completed
        </p>
        <Separator />

        <div className="flex flex-col gap-2 rounded-xl bg-yellow-0 p-3 md:flex-row md:items-center md:justify-between md:px-5 md:py-4">
          <h3 className="text-xs font-medium leading-5 text-black-800 md:text-sm lg:text-base">
            Share OTP to start pickup
          </h3>
          <div className="flex items-center gap-[14px]">
            {orderDetails.pickupOtp
              ?.toString()
              .split("")
              .map((digit, index) => (
                <span
                  key={index}
                  className="flex size-[30px] items-center justify-center rounded-[6px] border border-teal-850 bg-[#F1FFFF] text-center text-xs font-semibold tracking-[0.12px] text-black-900 text-teal-800 md:size-9 md:text-sm lg:size-10 lg:text-base"
                >
                  {digit}
                </span>
              ))}
          </div>
        </div>

        <div className="flex flex-col gap-1">
          <h5 className="text-xs font-semibold text-black-800 md:text-sm lg:text-base">
            Pickup from
          </h5>
          <p className="line-clamp-1 text-ellipsis text-xs text-black-600 md:text-sm lg:text-base">
            {orderDetails.seller.addresses[0]?.localAddress ??
              "No address provided"}
          </p>
        </div>

        {orderDetails.status === "COMPLETED" && (
          <AgentOrderCard
            kabadiwala={orderDetails.kabadiwala}
            status={orderDetails.status}
          />
        )}

        <div className="flex justify-between">
          <h3 className="font-semibold leading-6 text-teal-800 md:text-lg lg:text-xl">
            Items Added
          </h3>
          <p className="text-xs font-semibold leading-6 text-black-800 md:text-sm lg:text-base">
            Total : ₹{orderDetails.totalAmount ?? 0}
          </p>
        </div>

        <div className="hide-scrollbar flex max-h-[400px] flex-col gap-6 overflow-y-auto md:max-h-[500px] lg:max-h-[600px]">
          {orderDetails.items.length > 0 ? (
            orderDetails.items.map((item) => (
              <ItemPriceCard
                key={item.id}
                item={{
                  ...item,
                }}
              />
            ))
          ) : (
            <div className="flex h-24 items-center justify-center rounded-lg border-2 border-dashed bg-gray-50">
              <p className="text-gray-500">No items were in this order.</p>
            </div>
          )}
        </div>

        <div className="flex flex-col gap-6">
          <Card>
            <CardHeader
              title="Pickup Details"
              icon={
                <div>
                  <Image
                    src="/static/icons/bucket.svg"
                    alt="Pickup Details"
                    width={24}
                    height={24}
                    className="size-6"
                  />
                </div>
              }
            />
            <KeyValuePair
              label="Estimated Value"
              value={"₹" + orderDetails.totalAmount}
              valueClassName="text-teal-700"
            />
            <KeyValuePair
              label="Items"
              valueClassName="text-wrap text-ellipsis line-clamp-2"
              value={
                orderDetails.items.length > 0
                  ? Array.from(
                      new Set(
                        orderDetails.items.map(
                          (item) =>
                            item.category.parent?.name ?? item.category.name,
                        ),
                      ),
                    ).join(", ")
                  : "N/A"
              }
            />
            <KeyValuePair
              label="Weight"
              value={(() => {
                const totalWeight = orderDetails.items
                  .filter((i) => i.category.rateType === "PER_KG")
                  .reduce(
                    (sum, i) =>
                      sum + (i.category.rate ? parseInt(i.category.rate) : 0),
                    0,
                  );
                return `${totalWeight} kg`;
              })()}
            />
          </Card>
          {/* agent details card showing name vehicle type, number and time window */}
          <Card>
            <CardHeader title="Agent Details" icon={<User />} />
            <KeyValuePair
              label="Name"
              value={orderDetails.kabadiwala.name ?? "Unknown"}
            />
            <KeyValuePair
              label="Vehicle Type"
              value={`
               ${orderDetails.kabadiwala.vehicles?.[0]?.vehicleType ?? "Truck"}, ${orderDetails.kabadiwala.vehicles?.[0]?.vehicleNumber ?? "N/A"}`}
            />
            <KeyValuePair label="Time Window" value={`Today, 2-4 PM`} />
          </Card>

          <Card>
            <CardHeader title="Order Summary" icon={<Wallet />} />
            <div className="flex flex-col gap-4">
              <p className="font-jakarta text-xs font-medium text-teal-800">
                Customer Details
              </p>
              <KeyValuePair label="Name" value={orderDetails.seller.fullName} />
              <KeyValuePair
                label="Location"
                value={orderDetails.seller.addresses[0]?.localAddress}
              />
              <KeyValuePair
                label="Categories"
                value={
                  orderDetails.items.length > 0
                    ? Array.from(
                        new Set(
                          orderDetails.items.map(
                            (item) =>
                              item.category.parent?.name ?? item.category.name,
                          ),
                        ),
                      ).join(", ")
                    : "N/A"
                }
              />
              <KeyValuePair
                label="Transaction Date"
                value={new Date(orderDetails.createdAt).toLocaleDateString()}
              />
            </div>
            <CardDivider />
            <div className="flex flex-col gap-4">
              <p className="font-jakarta text-xs font-medium text-teal-800">
                Transactions
              </p>
              <KeyValuePair
                label="Amount Added to Wallet"
                value={`₹${orderDetails.amountAddedToWallet}`}
              />
              <KeyValuePair
                label="Amount Paid to Customer"
                value={`₹${orderDetails.amountPaidToCustomer}`}
              />
              <KeyValuePair
                label="Taxes"
                value={`₹${orderDetails.taxes}`}
                labelClassName="underline decoration-dashed"
              />
              <KeyValuePair
                label="Service Charges"
                value={`₹${orderDetails.serviceCharge}`}
              />
              <KeyValuePair
                label="Earnings"
                value={`₹${orderDetails.earnings}`}
                valueClassName="text-[#015D31]"
              />
            </div>
          </Card>
        </div>

        <Button className="flex items-center justify-center gap-2">
          Download Invoice as PDF <ArrowRight size={16} />
        </Button>

        {!kabadiwalaReview && (
          <Button
            className="flex items-center justify-center gap-2 border-yellow-400 bg-yellow-50"
            onClick={() => {
              void setRateParam(orderDetails.id);
            }}
          >
            Rate Agent <ArrowRight size={16} />
          </Button>
        )}
        {kabadiwalaReview && (
          <RatedCard
            rating={Number(kabadiwalaReview.rating)}
            review={kabadiwalaReview.review}
          />
        )}

        {sellerReview && (
          <RatedCard
            rating={Number(sellerReview.rating)}
            review={sellerReview.review}
          />
        )}
        <NeedSupportCard
          onClick={() => {
            setIsAdminChatOpen(true);
          }}
        />
        {/* Admin chat for this order */}
        <SupportChatSheet
          open={isAdminChatOpen}
          onOpenChange={setIsAdminChatOpen}
          orderId={orderDetails.id}
        />

        <RateKabadiwalaSheet orderId={orderDetails.id} />
      </div>
    </main>
  );
};

export default CompletedOrderDetails;
