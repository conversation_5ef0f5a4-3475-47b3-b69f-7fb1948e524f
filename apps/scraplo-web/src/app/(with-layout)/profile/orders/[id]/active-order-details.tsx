"use client";

import { useState } from "react";
import Image from "next/image";
import {
  useMutation,
  useQueryClient,
  useSuspenseQuery,
} from "@tanstack/react-query";
import { CircleCheck, Loader2 } from "lucide-react";

import type { orderApprovalStatusEnum } from "@acme/db/schema";
import { Button } from "@acme/ui/components/ui/button";
import { CardContent } from "@acme/ui/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@acme/ui/components/ui/dialog";
import { Separator } from "@acme/ui/components/ui/separator";

import type { DLCard } from "~/app/lib/types";
import OrderDetailsMap from "~/components/maps/order-details-map";
import AgentOrderCard from "~/components/shared/agent-card";
import ItemPriceCard from "~/components/shared/item-price-card";
import NeedSupportCard from "~/components/shared/need-support-card";
import { <PERSON>, <PERSON>Header, <PERSON>V<PERSON>uePair } from "~/components/shared/sections";
import SupportChatSheet from "~/components/shared/support-chat-sheet";
import { useTRPC } from "~/trpc/react";
import OrderConversationChatSheetWrapper from "./order-conversation-chat-sheet";

interface OrderDetailsProps {
  id: string;
}

const ActiveOrderDetails = ({ id: orderId }: OrderDetailsProps) => {
  const trpc = useTRPC();

  const { data: orderDetails, isError } = useSuspenseQuery(
    trpc.order.getOrderById.queryOptions(
      {
        orderId,
      },
      {
        refetchInterval: 5,
      },
    ),
  );
  const { data: estimatedTotal } = useSuspenseQuery(
    trpc.order.getEstimatedOrderTotalAmount.queryOptions(
      {
        orderId: orderId,
      },
      {
        refetchInterval: 5,
      },
    ),
  );
  const drivingDetails: DLCard | null =
    orderDetails.kabadiwala.dlVerificationResponse;
  const [isAdminChatOpen, setIsAdminChatOpen] = useState(false);

  const queryClient = useQueryClient();
  const {
    mutate: updateOrderStatus,
    isPending: isPendingUpdateApprovalStatus,
  } = useMutation(
    trpc.order.updateOrderApproveStatus.mutationOptions({
      onSuccess: async () => {
        await queryClient.invalidateQueries(
          trpc.order.getOrderById.queryOptions({ orderId }),
        );
      },
    }),
  );

  const handleUpdateApprovalStatus = (
    status: (typeof orderApprovalStatusEnum.enumValues)[number],
  ) => {
    updateOrderStatus({
      orderId,
      status: status,
    });
  };

  if (isError) {
    return (
      <div className="flex h-96 items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-semibold text-gray-900">
            Order Not Found
          </h2>
          <p className="mt-2 text-gray-600">
            The order you're looking for doesn't exist.
          </p>
        </div>
      </div>
    );
  }

  return (
    <main>
      <OrderDetailsMap
        pickupLocationCoordinates={
          orderDetails.address.coordinates ?? undefined
        }
        kabadiwalaLocationCoordinates={
          orderDetails.kabadiwala.liveLocationCoordinate ?? undefined
        }
      />

      <div className="flex flex-col gap-6 px-6 py-5 md:px-[64px] lg:gap-9 lg:px-[180px] xl:px-[240px]">
        <div className="flex items-center justify-between">
          {orderDetails.status === "PENDING" ? (
            <div>
              <h2 className="font-jakarta text-base font-semibold leading-6 text-black-800 md:text-lg lg:text-xl">
                Looking for agent...
              </h2>
              <span className="text-xs leading-4 text-black-500 md:text-sm lg:text-base">
                We are assigning a pickup agent for your order.
              </span>
            </div>
          ) : (
            <div>
              <h2 className="font-jakarta text-base font-semibold leading-6 text-black-800 md:text-lg lg:text-xl">
                Agent is on the way
              </h2>
              <span className="text-xs leading-4 text-black-500 md:text-sm lg:text-base">
                1.2km away
              </span>
            </div>
          )}

          {orderDetails.status === "ACTIVE" && (
            <span className="rounded-full bg-teal-850 px-[14px] py-1.5 text-sm font-semibold leading-6 text-yellow-00 md:text-base lg:text-lg">
              5 min
            </span>
          )}
        </div>

        {orderDetails.status === "ACTIVE" && (
          <p className="flex items-center gap-1 rounded-full bg-gradient-to-r from-[#EAFFF5] to-[#FFFFFF] px-[5px] py-1 text-center text-xs font-medium text-[#007940] md:text-sm lg:text-base">
            <CircleCheck />
            Agent nearby your location
          </p>
        )}

        {/* Order Approval Status Section */}
        {orderDetails.orderApprovalStatus && (
          <div className="flex flex-col gap-3 rounded-lg border border-gray-200 bg-gray-50 p-4 md:p-5 lg:p-6">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-semibold text-gray-800 md:text-base lg:text-lg">
                Order Approval Status
              </h3>
              <span
                className={`rounded-full px-3 py-1 text-xs font-medium md:text-sm lg:text-base ${
                  orderDetails.orderApprovalStatus ===
                  "REQUESTED_CONFIRMATION_BY_KABADIWALA"
                    ? "bg-blue-100 text-blue-800"
                    : orderDetails.orderApprovalStatus ===
                        "REQUESTED_RE_REVIEW_BY_SELLER"
                      ? "bg-orange-100 text-orange-800"
                      : orderDetails.orderApprovalStatus ===
                          "REQUEST_APPROVED_BY_SELLER"
                        ? "bg-green-100 text-green-800"
                        : "bg-red-100 text-red-800"
                }`}
              >
                {orderDetails.orderApprovalStatus ===
                  "REQUESTED_CONFIRMATION_BY_KABADIWALA" &&
                  "Pending Your Approval"}
                {orderDetails.orderApprovalStatus ===
                  "REQUESTED_RE_REVIEW_BY_SELLER" && "Re-review Requested"}
                {orderDetails.orderApprovalStatus ===
                  "REQUEST_APPROVED_BY_SELLER" && "Approved"}
                {orderDetails.orderApprovalStatus ===
                  "REQUEST_REJECTED_BY_SELLER" && "Rejected"}
              </span>
            </div>

            {/* When agent requests confirmation */}
            {orderDetails.orderApprovalStatus ===
              "REQUESTED_CONFIRMATION_BY_KABADIWALA" && (
              <>
                <div className="rounded-md bg-blue-50 p-3">
                  <p className="text-sm text-blue-800 md:text-base">
                    🔍 The agent has reviewed your items and is requesting
                    confirmation of the pickup details.
                  </p>
                </div>
                <div className="flex flex-col gap-2 sm:flex-row">
                  <Button
                    className="flex-1 bg-green-600 text-white hover:bg-green-700 md:text-base"
                    onClick={() =>
                      handleUpdateApprovalStatus("REQUEST_APPROVED_BY_SELLER")
                    }
                    disabled={isPendingUpdateApprovalStatus}
                  >
                    {isPendingUpdateApprovalStatus ? (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                      "✓"
                    )}{" "}
                    Confirm & Approve
                  </Button>
                  <Button
                    variant="outline"
                    className="flex-1 border-orange-600 text-orange-600 hover:bg-orange-50 md:text-base"
                    onClick={() =>
                      handleUpdateApprovalStatus(
                        "REQUESTED_RE_REVIEW_BY_SELLER",
                      )
                    }
                    disabled={isPendingUpdateApprovalStatus}
                  >
                    {isPendingUpdateApprovalStatus ? (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                      "📝"
                    )}{" "}
                    Request Re-review
                  </Button>
                  <Button
                    variant="outline"
                    className="flex-1 border-red-600 text-red-600 hover:bg-red-50 md:text-base"
                    onClick={() =>
                      handleUpdateApprovalStatus("REQUEST_REJECTED_BY_SELLER")
                    }
                    disabled={isPendingUpdateApprovalStatus}
                  >
                    {isPendingUpdateApprovalStatus ? (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                      "✗"
                    )}{" "}
                    Reject Order
                  </Button>
                </div>
              </>
            )}

            {/* When agent requests re-review from seller */}
            {orderDetails.orderApprovalStatus ===
              "REQUESTED_RE_REVIEW_BY_SELLER" && (
              <>
                <div className="rounded-md border border-orange-200 bg-orange-50 p-4">
                  <div className="flex items-start gap-3">
                    <div className="flex-shrink-0">
                      <span className="text-lg md:text-xl">🔄</span>
                    </div>
                    <div className="flex-1">
                      <h4 className="mb-1 text-sm font-medium text-orange-800 md:text-base">
                        Re-review Requested
                      </h4>
                      <p className="mb-3 text-sm text-orange-700 md:text-base">
                        You have requested a re-review of the order items. The
                        agent will now review your request and get back to you
                        with updated details or confirmation.
                      </p>
                      <div className="rounded bg-orange-100 p-2 text-xs text-orange-600 md:text-sm">
                        ⏳ Please wait while the agent processes your re-review
                        request. You will be notified once they respond.
                      </div>
                    </div>
                  </div>
                </div>
              </>
            )}

            {/* When seller has approved */}
            {orderDetails.orderApprovalStatus ===
              "REQUEST_APPROVED_BY_SELLER" && (
              <div className="rounded-md bg-green-100 p-3">
                <p className="text-sm text-green-800 md:text-base">
                  ✅ You have approved this order. The agent will proceed with
                  payment.
                </p>
              </div>
            )}

            {/* When seller has rejected */}
            {orderDetails.orderApprovalStatus ===
              "REQUEST_REJECTED_BY_SELLER" && (
              <div className="rounded-md bg-red-100 p-3">
                <p className="text-sm text-red-800 md:text-base">
                  ❌ You have rejected this order request.
                </p>
              </div>
            )}
          </div>
        )}

        <Separator />

        <div className="flex flex-col gap-2 rounded-xl bg-yellow-0 p-3 md:flex-row md:items-center md:justify-between md:px-5 md:py-4">
          <h3 className="text-xs font-medium leading-5 text-black-800 md:text-sm lg:text-base">
            Share OTP to start pickup
          </h3>
          <div className="flex items-center gap-[14px]">
            {orderDetails.pickupOtp
              ?.toString()
              .split("")
              .map((digit, index) => (
                <span
                  key={index}
                  className="flex size-[30px] items-center justify-center rounded-[6px] border border-teal-850 bg-[#F1FFFF] text-center text-xs font-semibold tracking-[0.12px] text-black-900 text-teal-800 md:size-9 md:text-sm lg:size-10 lg:text-base"
                >
                  {digit}
                </span>
              ))}
          </div>
        </div>

        <div className="flex flex-col gap-1">
          <h5 className="text-xs font-semibold text-black-800 md:text-sm lg:text-base">
            Pickup from
          </h5>
          <p className="line-clamp-1 text-ellipsis text-xs text-black-600 md:text-sm lg:text-base">
            {orderDetails.seller.addresses[0]?.localAddress ??
              "No address provided"}
          </p>
        </div>

        {orderDetails.status === "ACTIVE" && (
          <AgentOrderCard
            orderId={orderDetails.id}
            kabadiwala={orderDetails.kabadiwala}
            status={orderDetails.status}
          />
        )}
        {}
        {orderDetails.status === "ACTIVE" && drivingDetails && (
          <Dialog>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm">
                View Driving License Info
              </Button>
            </DialogTrigger>
            <DialogContent className="w-full max-w-[500px]">
              <DialogHeader>
                <DialogTitle>Driving License Details</DialogTitle>
              </DialogHeader>
              <Card className="">
                <CardContent className="flex flex-row items-center gap-4">
                  {drivingDetails.user_image ? (
                    <Image
                      src={drivingDetails.user_image}
                      alt={drivingDetails.user_full_name}
                      className="h-[120px] w-[120px] rounded-full border object-cover"
                      width={120}
                      height={120}
                    />
                  ) : orderDetails.kabadiwala.image ? (
                    <Image
                      src={orderDetails.kabadiwala.image}
                      alt={orderDetails.kabadiwala.name ?? ""}
                      className="h-[120px] w-[120px] rounded-full border object-cover"
                      width={120}
                      height={120}
                    />
                  ) : (
                    <div
                      className="flex h-[120px] w-[120px] items-center justify-center rounded-full border text-2xl font-bold text-white"
                      style={{
                        background: Math.random() > 0.5 ? "red" : "blue",
                      }}
                      //   style={{ background: stringToColor(orderDetails.kabadiwala.name) }}
                    >
                      {orderDetails.kabadiwala.name?.charAt(0) ?? "U"}
                    </div>
                  )}
                  <div className="flex flex-col gap-2">
                    <p className="text-2xl font-bold">
                      {drivingDetails.user_full_name}
                    </p>
                    <p className="text-lg font-bold">
                      DL Number: {drivingDetails.dl_number}
                    </p>
                  </div>
                </CardContent>
              </Card>
            </DialogContent>
          </Dialog>
        )}

        <div className="flex justify-between">
          <h3 className="font-semibold leading-6 text-teal-800 md:text-lg lg:text-xl">
            Items Added
          </h3>
          <p className="text-xs font-semibold leading-6 text-black-800 md:text-sm lg:text-base">
            Estimated Total : ₹{estimatedTotal}
          </p>
        </div>

        <div className="hide-scrollbar flex max-h-[400px] flex-col gap-6 overflow-y-auto md:max-h-[500px] lg:max-h-[600px]">
          {orderDetails.items.length > 0 ? (
            orderDetails.items.map((item) => (
              <ItemPriceCard
                key={item.id}
                item={{
                  ...item,
                }}
              />
            ))
          ) : (
            <div className="flex h-24 items-center justify-center rounded-lg border-2 border-dashed bg-gray-50">
              <p className="text-gray-500">No items added to this order yet.</p>
            </div>
          )}
        </div>

        <div className="flex flex-col gap-6">
          <Card>
            <CardHeader
              title="Pickup Details"
              icon={
                <div>
                  <Image
                    src="/static/icons/bucket.svg"
                    alt="Pickup Details"
                    width={24}
                    height={24}
                    className="size-6"
                  />
                </div>
              }
            />
            <KeyValuePair
              label="Estimated Value"
              value={"₹" + estimatedTotal}
              valueClassName="text-teal-700"
            />
            <KeyValuePair
              label="Items"
              valueClassName="text-wrap text-ellipsis line-clamp-2"
              value={
                orderDetails.items.length > 0
                  ? Array.from(
                      new Set(
                        orderDetails.items.map(
                          (item) =>
                            item.category.parent?.name ?? item.category.name,
                        ),
                      ),
                    ).join(", ")
                  : "N/A"
              }
            />
            <KeyValuePair
              label="Weight"
              value={(() => {
                const totalWeight = orderDetails.items
                  .filter((i) => i.category.rateType === "PER_KG")
                  .reduce(
                    (sum, i) =>
                      sum + (i.category.rate ? parseInt(i.category.rate) : 0),
                    0,
                  );
                return `${totalWeight} kg`;
              })()}
            />
          </Card>

          {orderDetails.status === "ACTIVE" && (
            <Card>
              <CardHeader
                title="Agent Details"
                icon={
                  <div>
                    <Image
                      src="/static/icons/user-head.svg"
                      alt="Agent Details"
                      width={24}
                      height={24}
                      className="size-6"
                    />
                  </div>
                }
              />

              <KeyValuePair label="Name" value={orderDetails.kabadiwala.name} />

              <KeyValuePair
                label="Vehicle Type"
                value={`
               ${orderDetails.kabadiwala.vehicles?.[0]?.vehicleType}, ${orderDetails.kabadiwala.vehicles?.[0]?.vehicleNumber}`}
              />
              {/* <KeyValuePair label="Time Window" value={`Today, 2-4 PM`} /> */}
            </Card>
          )}
        </div>

        <Button className="bg-[#FEE9E7] text-[#900B09]">Cancel Order</Button>

        <NeedSupportCard
          onClick={() => {
            setIsAdminChatOpen(true);
          }}
        />

        {/* Add Chat with Admin button if admin exists for this order */}
      </div>

      <OrderConversationChatSheetWrapper />
      {/* Admin chat for this order */}
      <SupportChatSheet
        open={isAdminChatOpen}
        onOpenChange={setIsAdminChatOpen}
        orderId={orderDetails.id}
      />
    </main>
  );
};

export default ActiveOrderDetails;
