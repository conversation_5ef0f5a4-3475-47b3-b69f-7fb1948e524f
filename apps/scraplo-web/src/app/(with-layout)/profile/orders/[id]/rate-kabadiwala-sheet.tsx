"use client";

import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Star, X } from "lucide-react";
import { useQueryState } from "nuqs";

import { Button } from "@acme/ui/components/ui/button";
import { Sheet, SheetContent } from "@acme/ui/components/ui/sheet";
import { Textarea } from "@acme/ui/components/ui/textarea";

import { ORDER_DETAILS_RATE_PARAM_NAME } from "~/app/lib/param-names";
import { useTRPC } from "~/trpc/react";

interface RateKabadiwalaSheetProps {
  orderId: string;
}

const MAX_REVIEW_LENGTH = 500;

const StarRating = ({
  value,
  onChange,
  disabled,
}: {
  value: number;
  onChange: (v: number) => void;
  disabled?: boolean;
}) => {
  const [hovered, setHovered] = useState<number | null>(null);
  return (
    <div className="flex gap-1">
      {[1, 2, 3, 4, 5].map((star) => (
        <button
          key={star}
          type="button"
          className="group"
          disabled={disabled}
          onMouseEnter={() => setHovered(star)}
          onMouseLeave={() => setHovered(null)}
          onClick={() => onChange(star)}
        >
          <Star
            className={`size-7 transition-colors ${
              (hovered ?? value) >= star
                ? "fill-yellow-400 stroke-yellow-400"
                : "fill-none stroke-black-200"
            } group-hover:scale-110`}
          />
        </button>
      ))}
    </div>
  );
};

export const RatedCard = ({
  rating,
  review,
}: {
  rating: number;
  review?: string | null;
}) => (
  <div className="flex flex-col gap-2 rounded-xl border border-black-100 bg-white p-4 shadow-sm">
    <div className="flex items-center gap-2">
      <span className="font-semibold text-black-900">You rated:</span>
      <div className="flex gap-0.5">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`size-5 ${rating >= star ? "fill-yellow-400 stroke-yellow-400" : "fill-none stroke-black-200"}`}
          />
        ))}
      </div>
    </div>
    {review && <div className="mt-1 text-sm text-black-700">{review}</div>}
  </div>
);

const RateKabadiwalaSheet = ({ orderId }: RateKabadiwalaSheetProps) => {
  // Query param for sheet open/close
  const [rateParam, setRateParam] = useQueryState(
    ORDER_DETAILS_RATE_PARAM_NAME,
    { clearOnDefault: true },
  );
  const trpc = useTRPC();
  const queryClient = useQueryClient();

  // Form state
  const [rating, setRating] = useState<number>(0);
  const [reviewText, setReviewText] = useState<string>("");
  const [error, setError] = useState<string | null>(null);

  const { mutate: submitReview, isPending: isSubmitting } = useMutation(
    trpc.order.createKabadiwalaReview.mutationOptions({
      onSuccess: async () => {
        await queryClient.invalidateQueries({
          queryKey: trpc.order.getKabadiwalaReviewByOrderId.queryKey({
            orderId,
          }),
        });
        void setRateParam(null);
      },
      onError: (err) => {
        setError(err.message || "Failed to submit review");
      },
    }),
  );

  // Sheet content
  const handleClose = async () => {
    await setRateParam(null);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    if (rating < 1 || rating > 5) {
      setError("Please select a rating between 1 and 5 stars.");
      return;
    }
    if (reviewText.length > MAX_REVIEW_LENGTH) {
      setError("Review must be 500 characters or less.");
      return;
    }
    submitReview({ orderId, rating, review: reviewText });
  };

  return (
    <Sheet open={rateParam ? true : false} onOpenChange={handleClose}>
      <SheetContent
        side="bottom"
        className="flex min-h-[60%] flex-col rounded-t-3xl p-0"
        hideCloseButton
      >
        {/* Header */}
        <div className="flex items-center justify-between gap-4 border-b border-b-black-50 px-6 pb-3 pt-6">
          <Button
            className="rounded-full bg-black-50 p-1.5"
            onClick={handleClose}
          >
            <X />
          </Button>
          <h2 className="flex-1 font-bold leading-7 text-black-900">
            Rate Agent
          </h2>
          <div className="w-8" />
        </div>

        <form
          className="flex flex-1 flex-col gap-6 px-6 py-6"
          onSubmit={handleSubmit}
        >
          <div className="flex flex-col gap-2">
            <label className="font-medium text-black-800">Your Rating</label>
            <StarRating value={rating} onChange={setRating} />
          </div>
          <div className="flex flex-col gap-2">
            <label htmlFor="review" className="font-medium text-black-800">
              Optional Review
            </label>
            <Textarea
              id="review"
              maxLength={MAX_REVIEW_LENGTH}
              value={reviewText}
              onChange={(e) => setReviewText(e.target.value)}
              placeholder="Share your experience (optional)"
              className="min-h-[80px] resize-none rounded-lg border-black-200 bg-black-0 px-3 py-2"
              disabled={isSubmitting}
            />
            <div className="text-right text-xs text-black-400">
              {reviewText.length}/{MAX_REVIEW_LENGTH}
            </div>
          </div>
          {error && <div className="text-sm text-red-600">{error}</div>}
          <Button
            type="submit"
            className="mt-2 rounded-lg bg-yellow-450 px-4 py-2 font-semibold text-black-900"
            disabled={isSubmitting}
          >
            {isSubmitting ? "Submitting..." : "Submit Review"}
          </Button>
        </form>
      </SheetContent>
    </Sheet>
  );
};

export default RateKabadiwalaSheet;
