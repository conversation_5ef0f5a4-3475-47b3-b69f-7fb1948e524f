import { ChevronUp, Plus, X } from "lucide-react";

import { But<PERSON> } from "@acme/ui/components/ui/button";
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetTrigger,
} from "@acme/ui/components/ui/sheet";

import HelpButton from "~/components/shared/help-button";
import PageHeader from "~/components/shared/page-header";
import { getQueryClient, trpc } from "~/trpc/server";
import AddPaymentMethodForm from "./add-payment-method-form";
import SavedCardsBanksList from "./saved-cards-banks-list";

const SavedCardsBanksPage = () => {
  const queryClient = getQueryClient();
  void queryClient.prefetchQuery(
    trpc.payment.getAllFundAccounts.queryOptions(),
  );

  return (
    <>
      <PageHeader
        title="Saved Cards / Banks"
        titleClassName="text-teal-800 text-[18px] leading-7"
        action={<HelpButton />}
      />

      <SavedCardsBanksList />

      <div className="h-[110px]" />

      <AddPaymentMethodSheet />
    </>
  );
};

export default SavedCardsBanksPage;

const AddPaymentMethodSheet = () => {
  return (
    <Sheet>
      <SheetTrigger asChild>
        <div className="fixed bottom-0 w-full bg-[rgba(255,255,255,0.8)] px-6 pb-10 pt-5 backdrop-blur-[15px] md:px-14 xl:px-[120px]">
          <Button className="w-full">
            <Plus className="size-5" /> Add Payment Method
          </Button>
        </div>
      </SheetTrigger>
      <SheetContent
        side="bottom"
        hideCloseButton
        className="min-h-[70%] rounded-t-2xl p-0"
      >
        <div className="flex items-center justify-between border-b-[1.2px] border-b-black-50 px-6 pb-3 pt-6">
          <div className="flex items-center gap-3">
            <Button className="rounded-xl bg-yellow-450 p-2">
              <ChevronUp className="size-5" />
            </Button>

            <div>
              <h2 className="font-bold leading-7 text-black-900">
                Add Payment Method
              </h2>
              <p className="text-sm leading-5 text-black-500">
                Add payment method
              </p>
            </div>
          </div>

          <SheetClose>
            <Button className="rounded-full bg-black-50 p-1.5">
              <X className="size-5" />
            </Button>
          </SheetClose>
        </div>

        <AddPaymentMethodForm />
      </SheetContent>
    </Sheet>
  );
};
