"use client";

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { CreditCard, Loader2 } from "lucide-react";
import { toast } from "sonner";

import Error from "~/components/shared/error";
import {
  BankAccountCard,
  UPIAccountCard,
} from "~/components/shared/payment-info-card";
import { useTRPC } from "~/trpc/react";

const SavedCardsBanksList = () => {
  const trpc = useTRPC();
  const queryClient = useQueryClient();

  const {
    data: fundAccountsData,
    isPending,
    isError,
  } = useQuery(trpc.payment.getAllFundAccounts.queryOptions());

  const {
    mutate: updateDefaultPaymentMethod,
    isPending: isUpdateingDefaultPaymentMethod,
  } = useMutation(
    trpc.payment.updateDefaultPaymentMethod.mutationOptions({
      onSuccess: async (opts) => {
        await queryClient.invalidateQueries(
          trpc.payment.getAllFundAccounts.queryOptions(),
        );
        toast.success(opts.message);
      },
      onError: (error) => {
        toast.error(error.message);
      },
    }),
  );

  const { mutate: deletePaymentMethod, isPending: isDeletingPaymentMethod } =
    useMutation(
      trpc.payment.deletePaymentMethod.mutationOptions({
        onSuccess: async (opts) => {
          await queryClient.invalidateQueries(
            trpc.payment.getAllFundAccounts.queryOptions(),
          );
          toast.success(opts.message);
        },
        onError: (error) => {
          toast.error(error.message);
        },
      }),
    );

  const handleDefaultPaymentUpdate = (accountId: string) => {
    if (isUpdateingDefaultPaymentMethod) return;
    updateDefaultPaymentMethod({ fundAccountId: accountId });
  };

  const handleDeletePaymentMethod = (accountId: string) => {
    if (isDeletingPaymentMethod) return;
    deletePaymentMethod({ fundAccountId: accountId });
  };

  if (isPending) {
    return (
      <div className="flex h-96 items-center justify-center">
        <Loader2 className="size-5 animate-spin" />
      </div>
    );
  }

  if (isError) {
    return <Error message="Failed to load saved cards and banks." />;
  }

  const fundAccounts = fundAccountsData.fundAccounts;

  if (fundAccounts.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <div className="rounded-full bg-gray-100 p-6">
          <CreditCard className="h-12 w-12 text-gray-400" />
        </div>
        <h3 className="mt-4 text-lg font-medium text-gray-900">
          No payment methods
        </h3>
        <p className="mt-2 text-sm text-gray-500">
          You haven't added any cards or bank accounts yet.
        </p>
      </div>
    );
  }

  return (
    <div className="relative px-6 md:px-14 xl:px-[120px]">
      {isUpdateingDefaultPaymentMethod && (
        <div className="absolute inset-0 z-20 flex h-screen items-center justify-center bg-black-150 bg-black-150/50 py-4">
          <Loader2 className="size-5 animate-spin" />
        </div>
      )}

      <div className="mt-4 flex flex-col gap-4 xl:grid xl:grid-cols-3">
        {fundAccounts.map((account) => {
          switch (account.account_type) {
            case "bank_account":
              return (
                <BankAccountCard
                  key={account.id}
                  account={account}
                  isDefault={
                    account.id === fundAccountsData.defaultFundAccountId
                  }
                  onUpdate={handleDefaultPaymentUpdate}
                  onDelete={handleDeletePaymentMethod}
                />
              );
            case "vpa":
              return (
                <UPIAccountCard
                  key={account.id}
                  account={account}
                  isDefault={
                    account.id === fundAccountsData.defaultFundAccountId
                  }
                  onUpdate={handleDefaultPaymentUpdate}
                  onDelete={handleDeletePaymentMethod}
                />
              );
            default:
              return null;
          }
        })}
      </div>
    </div>
  );
};

export default SavedCardsBanksList;
