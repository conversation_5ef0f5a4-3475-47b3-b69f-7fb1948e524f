import { tryCatch } from "@acme/validators/utils";

import Error from "~/components/shared/error";
import HelpButton from "~/components/shared/help-button";
import PageHeader from "~/components/shared/page-header";
import { getQueryClient, trpc } from "~/trpc/server";
import EditProfileForm from "./edit-profile-form";

const ProfileMainPage = async () => {
  const queryClient = getQueryClient();
  const { data: profile, err } = await tryCatch(
    queryClient.fetchQuery(trpc.user.getProfile.queryOptions()),
  );

  if (err) {
    return <Error />;
  }

  return (
    <>
      <PageHeader
        title="Edit Profile"
        titleClassName="text-teal-800 text-[18px] leading-7"
        action={<HelpButton />}
      />
      <EditProfileForm profile={profile} />
    </>
  );
};

export default ProfileMainPage;
