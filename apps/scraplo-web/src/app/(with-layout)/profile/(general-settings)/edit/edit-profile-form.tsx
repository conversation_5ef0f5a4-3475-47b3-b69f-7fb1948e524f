"use client";

import type { z } from "zod";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import { Loader2 } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

import { Button } from "@acme/ui/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@acme/ui/components/ui/form";
import { Input } from "@acme/ui/components/ui/input";
import { ProfileUpdateSchema } from "@acme/validators";

import type { Profile } from "~/app/lib/types";
import { useTRPC } from "~/trpc/react";

interface EditProfileFormProps {
  profile: Profile;
}

const EditProfileForm = ({ profile }: EditProfileFormProps) => {
  const router = useRouter();
  const form = useForm<z.infer<typeof ProfileUpdateSchema>>({
    resolver: zodResolver(ProfileUpdateSchema),
    // if email and phone number are not verified, setting them to undefined
    defaultValues: {
      fullName: profile.fullName,
      email: profile.emailVerified ? profile.email : undefined,
      phoneNumber: profile.phoneNumberVerified
        ? (profile.phoneNumber ?? undefined)
        : undefined,
    },
  });

  const trpc = useTRPC();
  const { mutate: updateProfile, isPending } = useMutation(
    trpc.user.updateProfile.mutationOptions({
      onSuccess: (opts) => {
        toast.success(opts.message);
        router.refresh();
      },
      onError: (error) => {
        toast.error(error.message);
      },
    }),
  );

  const onSubmit = (values: z.infer<typeof ProfileUpdateSchema>) => {
    updateProfile(values);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="pt-5 xl:pt-10">
        <div className="flex flex-col gap-[30px] px-6 md:px-14 xl:px-[120px]">
          <FormField
            control={form.control}
            name="fullName"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Full Name</FormLabel>
                <FormControl>
                  <Input placeholder="John Doe" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="phoneNumber"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Phone Number</FormLabel>
                <FormControl>
                  <Input placeholder="eg: 1234567890" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email (optional)</FormLabel>
                <FormControl>
                  <Input placeholder="eg: <EMAIL>" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="fixed bottom-0 w-full px-6 pb-10 md:px-14 xl:px-[120px]">
          <Button type="submit" className="w-full" disabled={isPending}>
            {isPending && <Loader2 className="mr-1 h-4 w-4 animate-spin" />}
            {isPending ? "Updating Profile" : "Update Profile"}
          </Button>
        </div>
      </form>
    </Form>
  );
};

export default EditProfileForm;
