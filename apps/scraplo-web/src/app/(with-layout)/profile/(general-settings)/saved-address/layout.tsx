import React from "react";

import AddAddressButton from "~/components/shared/add-address-button";
import HelpButton from "~/components/shared/help-button";
import PageHeader from "~/components/shared/page-header";

interface SavedAddressLayoutProps {
  children: React.ReactNode;
}

const SavedAddressLayout = ({ children }: SavedAddressLayoutProps) => {
  return (
    <main>
      <PageHeader
        title="Saved Address"
        titleClassName="text-teal-800 text-[18px] leading-7"
        action={<HelpButton />}
      />
      {children}

      <AddAddressButton />
    </main>
  );
};

export default SavedAddressLayout;
