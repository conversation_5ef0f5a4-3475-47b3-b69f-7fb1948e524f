import { tryCatch } from "@acme/validators/utils";

import AddressCard from "~/components/shared/address-card";
import Error from "~/components/shared/error";
import { getQueryClient, trpc } from "~/trpc/server";

const SavedAddressPage = async () => {
  const trpcClient = getQueryClient();
  const { data, err } = await tryCatch(
    trpcClient.fetchQuery(trpc.address.getAllSavedAddresses.queryOptions()),
  );

  if (err) {
    return <Error />;
  }

  return (
    <div className="flex min-h-screen flex-col gap-4 overflow-y-auto px-6 pt-6 md:px-14 xl:px-[180px]">
      <p className="text-xs leading-5 text-black-600 md:text-base md:leading-6 xl:text-xl xl:leading-8">
        {data.addresses.length} saved address
      </p>

      {data.addresses.map((item) => (
        <AddressCard key={item.id} {...item} />
      ))}

      <div className="h-[110px]" />
    </div>
  );
};

export default SavedAddressPage;
