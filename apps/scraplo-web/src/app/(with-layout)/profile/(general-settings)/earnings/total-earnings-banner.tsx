interface TotalEarningsBannerProps {
  totalEarnings: number;
}

const TotalEarningsBanner = ({ totalEarnings }: TotalEarningsBannerProps) => {
  return (
    <div
      className="mx-6 mt-5 flex h-[135px] flex-col items-center justify-center gap-2 rounded-xl bg-teal-850 bg-cover bg-center bg-no-repeat px-6 py-5 md:mx-14 xl:mx-[120px] xl:px-10 xl:py-8"
      style={{
        backgroundImage: "url('/static/images/earnings-banner-bg.png')",
      }}
    >
      <h2 className="text-sm font-medium leading-5 -tracking-[0.07px] text-black-0 md:text-base md:leading-6 xl:text-xl xl:leading-8">
        Total Earnings
      </h2>
      <p className="font-jakarta text-[40px] font-extrabold leading-10 text-black-0 xl:text-[48px] xl:leading-[56px]">
        ₹{totalEarnings}
      </p>
    </div>
  );
};

export default TotalEarningsBanner;
