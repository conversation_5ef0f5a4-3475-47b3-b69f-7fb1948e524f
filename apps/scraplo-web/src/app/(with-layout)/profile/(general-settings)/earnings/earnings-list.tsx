import { Separator } from "@acme/ui/components/ui/separator";
import { cn } from "@acme/ui/lib/utils";

const EarningsList = () => {
  return (
    <div className="mt-6 flex flex-col gap-4 px-6 md:px-14 xl:px-[120px]">
      <h2 className="text-xs font-medium leading-5 text-black-700 md:text-sm md:leading-6 xl:text-lg xl:leading-7">
        Recent Transactions
      </h2>
      <div className="flex flex-col gap-2.5">
        <EarningCard
          orderId="Order123456"
          amount={100}
          variant="profit"
          orderDate={new Date()}
        />

        <Separator className="bg-black-100" />

        <EarningCard
          orderId="Order431233"
          amount={100}
          variant="loss"
          orderDate={new Date()}
        />
      </div>
    </div>
  );
};

export default EarningsList;

interface EarningCardProps {
  orderId: string;
  amount: number;
  orderDate: Date;
  variant?: "profit" | "loss";
}

const EarningCard = ({
  orderId,
  amount,
  orderDate,
  variant,
}: EarningCardProps) => {
  return (
    <div className="flex items-center justify-between gap-6 px-3 py-2 xl:px-4 xl:py-2.5">
      {/* left */}
      <div className="flex flex-col gap-1">
        <p className="text-[13px] font-medium leading-[18px] text-teal-900 md:text-sm md:leading-5 xl:text-lg xl:leading-7">
          Order #{orderId}
        </p>
        <p className="text-xs leading-4 text-black-600 xl:text-[14px] xl:leading-[22px]">
          Id: 24234ejtk232!dj{" "}
        </p>
      </div>

      {/* right */}
      <div className="flex flex-col items-end gap-1">
        <span
          className={cn(
            "w-fit rounded bg-green-100 px-1.5 py-1 text-sm font-semibold leading-5 text-[#038145] md:text-base md:leading-6 xl:text-lg xl:leading-7",
            variant === "loss" && "bg-red-100 text-red-600",
          )}
        >
          {variant === "profit" ? "+ " : "- "}₹ {amount}
        </span>
        <span className="text-[10px] font-medium leading-[13px] text-black-500 md:text-xs md:leading-4 xl:text-sm xl:leading-5">
          {orderDate.toDateString()}
        </span>
      </div>
    </div>
  );
};
