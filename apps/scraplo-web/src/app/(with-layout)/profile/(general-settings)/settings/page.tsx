import Image from "next/image";

import { Separator } from "@acme/ui/components/ui/separator";
import { Switch } from "@acme/ui/components/ui/switch";

import HelpButton from "~/components/shared/help-button";
import PageHeader from "~/components/shared/page-header";

interface SettingItemProps {
  iconPath: string;
  title: string;
  description: string;
}

const SettingsPage = () => {
  return (
    <>
      <PageHeader
        title="Settings"
        titleClassName="text-teal-800 text-[18px] leading-7"
        action={<HelpButton />}
      />

      <div className="space-y-6 px-6 py-5 md:px-14 xl:px-[120px]">
        {/* General Settings Section */}
        <h2 className="mb-3 font-jakarta text-[13px] font-semibold leading-6 text-black-500 md:text-sm xl:text-base xl:leading-6">
          General Settings
        </h2>

        <SettingItem
          iconPath="/static/icons/notification.svg"
          title="Notification"
          description="Toggle on to mute the notification, you will not receive any notification related to app"
        />

        {/* Divider */}
        <Separator className="bg-black-100" />

        {/* Chat Settings Section */}
        <div className="space-y-6">
          <h2 className="mb-3 font-jakarta text-[13px] font-semibold leading-6 text-black-500 md:text-sm">
            Chat Settings
          </h2>

          <SettingItem
            iconPath="/static/icons/notification.svg"
            title="Notification"
            description="Toggle on to mute the notification, you will not receive any notification related to app"
          />

          <SettingItem
            iconPath="/static/icons/microphone.svg"
            title="Microphone access"
            description="Toggle on to mute the notification, you will not receive any notification related to app"
          />
        </div>
      </div>
    </>
  );
};

export default SettingsPage;

const SettingItem = ({ iconPath, title, description }: SettingItemProps) => {
  return (
    <div className="flex items-center justify-between">
      <div className="flex items-start gap-3">
        <div className="relative aspect-square size-6 flex-shrink-0 pt-1 xl:size-8">
          <Image
            src={iconPath}
            alt={title}
            fill
            className="object-contain"
            unoptimized
          />
        </div>
        <div>
          <h3 className="mb-0.5 text-[13px] font-medium leading-[18px] text-black-800 md:text-sm xl:text-base xl:leading-6">
            {title}
          </h3>
          <p className="max-w-md text-[11px] leading-[18px] text-black-500 md:max-w-lg md:text-xs xl:max-w-full xl:text-sm xl:leading-5">
            {description}
          </p>
        </div>
      </div>
      <div>
        <Switch />
      </div>
    </div>
  );
};
