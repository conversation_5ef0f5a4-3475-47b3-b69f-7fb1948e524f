import type { <PERSON>ada<PERSON>, Viewport } from "next";
import { Inter, Plus_Jakarta_Sans } from "next/font/google";

import { cn } from "@acme/ui/lib/utils";

import "@stream-io/video-react-sdk/dist/css/styles.css";
import "~/app/globals.css";

import { redirect } from "next/navigation";

import { tryCatch } from "@acme/validators/utils";

import CallManager from "~/components/getstream-calling/call-manager";
import AddUpdateItemToCartSheet from "~/components/shared/add-update-item-to-cart-sheet";
import FloatingCartIndicator from "~/components/shared/floating-cart-indicator";
import PayoutMethodSheet from "~/components/shared/payout-method-sheet";
import { env } from "~/env";
import OneSignalProvider from "~/providers/onesignal-provider";
import Providers from "~/providers/providers";
import { getSession } from "~/server/auth";
import { getQueryClient, prefetch, trpc } from "~/trpc/server";

const APP_NAME = "Scraplo";
const APP_DEFAULT_TITLE = "Scraplo";
const APP_TITLE_TEMPLATE = "%s - Scraplo";
const APP_DESCRIPTION = "Scraplo description";

export const metadata: Metadata = {
  applicationName: APP_NAME,
  icons: [{ rel: "icon", url: "/favicon.png" }],
  title: {
    default: APP_DEFAULT_TITLE,
    template: APP_TITLE_TEMPLATE,
  },
  description: APP_DESCRIPTION,
  appleWebApp: {
    capable: true,
    statusBarStyle: "default",
    title: APP_DEFAULT_TITLE,
    // startUpImage: [],
  },
  formatDetection: {
    telephone: false,
  },
  openGraph: {
    type: "website",
    siteName: APP_NAME,
    title: {
      default: APP_DEFAULT_TITLE,
      template: APP_TITLE_TEMPLATE,
    },
    description: APP_DESCRIPTION,
  },
  twitter: {
    card: "summary",
    title: {
      default: APP_DEFAULT_TITLE,
      template: APP_TITLE_TEMPLATE,
    },
    description: APP_DESCRIPTION,
  },
};

export const viewport: Viewport = {
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "white" },
    { media: "(prefers-color-scheme: dark)", color: "black" },
  ],
};

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});
const jakarta = Plus_Jakarta_Sans({
  subsets: ["latin"],
  variable: "--font-jakarta",
});

export default async function RootLayout(props: { children: React.ReactNode }) {
  const session = await getSession();

  if (session?.user.id) {
    const queryClient = getQueryClient();
    prefetch(trpc.address.getAllSavedAddresses.queryOptions());

    const { data, err } = await tryCatch(
      queryClient.fetchQuery(trpc.onboarding.getOnboardingStep.queryOptions()),
    );

    if (err) {
      console.log("Failed to get seller onboarding status", err);
    }

    if (!data?.onBoardingCompleted) {
      redirect("/onboarding");
    }
  }

  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        {env.NODE_ENV === "development" && (
          <script
            async
            crossOrigin="anonymous"
            src="//unpkg.com/react-scan/dist/auto.global.js"
          />
        )}
      </head>

      <body
        className={cn(
          "relative min-h-screen bg-background font-inter text-foreground antialiased",
          inter.variable,
          jakarta.variable,
        )}
      >
        <Providers>
          <OneSignalProvider>
            {props.children}
            <FloatingCartIndicator />

            <AddUpdateItemToCartSheet />
            <PayoutMethodSheet />
            <CallManager />
          </OneSignalProvider>
        </Providers>
      </body>
    </html>
  );
}
