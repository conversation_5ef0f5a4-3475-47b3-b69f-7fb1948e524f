import { z } from "zod";

import { OnBoardingEnum } from "@acme/db/schema";

const getOnBoardingStep = (
  step?: (typeof OnBoardingEnum.enumValues)[number] | null,
) => {
  switch (step) {
    case "STEP_1":
      return {
        stepCount: 1,
        step: OnBoardingEnum.enumValues[0],
      };

    case "STEP_2":
      return {
        stepCount: 2,
        step: OnBoardingEnum.enumValues[1],
      };

    // case "STEP_3":
    //   return {
    //     stepCount: 3,
    //     step: OnBoardingEnum.enumValues[2],
    //   };

    default:
      return {
        stepCount: 1,
        step: OnBoardingEnum.enumValues[0],
      };
  }
};

export const parseOnBoardingStep = (
  step?: (typeof OnBoardingEnum.enumValues)[number],
  dbStep?: (typeof OnBoardingEnum.enumValues)[number] | null,
) => {
  const validateStep = z
    .enum(OnBoardingEnum.enumValues)
    .safeParse(step?.toUpperCase());
  if (!validateStep.success) {
    return {
      stepCount: 1,
      step: OnBoardingEnum.enumValues[0],
    };
  }

  const { step: requestedStep, stepCount } = getOnBoardingStep(
    validateStep.data,
  );
  const { stepCount: dbStepCount } = getOnBoardingStep(dbStep);

  // If the requested step is greater than the db step, return the db step, so that user cannot skip steps in between which he/she has not completed
  if (stepCount >= dbStepCount) {
    return getOnBoardingStep(dbStep);
  }

  return {
    stepCount: stepCount,
    step: requestedStep,
  };
};

// export const getUserLocation = async ({
//   latitude,
//   longitude,
// }: {
//   latitude: number;
//   longitude: number;
// }): Promise<UserAddress | null> => {
//   const url = `https://maps.googleapis.com/maps/api/geocode/json?latlng=${latitude},${longitude}&key=${env.NEXT_PUBLIC_GOOGLE_MAPS_KEY}`;

//   const { data: response, err } = await tryCatch<
//     AxiosResponse<NominatimResponse>,
//     AxiosError<NominatimResponse>
//   >(
//     axios.get<NominatimResponse>(
//       url, //`https://nominatim.openstreetmap.org/reverse?format=json&lat=${latitude}&lon=${longitude}`,
//     ),
//   );
//   console.log("res:", response);

//   if (err) {
//     if (axios.isAxiosError(err)) {
//       const axiosError = err;
//       if (axiosError.response) {
//         console.error(
//           `Nominatim API error: Status ${axiosError.response.status}`,
//           axiosError.response.data,
//         );
//         if (axiosError.response.data.error) {
//           console.error(
//             `Nominatim error message: ${axiosError.response.data.error}`,
//           );
//         }
//       } else if (axiosError.request) {
//         console.error(
//           "Error getting address from coordinates: No response received from Nominatim.",
//           axiosError.request,
//         );
//       } else {
//         console.error(
//           "Error getting address from coordinates: Error setting up request.",
//           axiosError.message,
//         );
//       }
//     } else {
//       console.error("Error getting address from coordinates: ", err);
//     }
//     return null;
//   }

//   const addressData = response.data;
//   console.log("[address data osm]", addressData);

//   if (addressData.error) {
//     console.error(`Nominatim API error: ${addressData.error}`);
//     return null;
//   }

//   if (!addressData.display_name || !addressData.address) {
//     console.error("Incomplete address data received from Nominatim.");
//     return null;
//   }

//   const { address: nominatimAddr, display_name } = addressData;

//   const address: UserAddress = {
//     name: "Current Location",
//     addressType: "OTHER",
//     landmark: "",
//     localAddress: "",
//     district: nominatimAddr.suburb ?? nominatimAddr.district ?? "",
//     googleAddressComponent: [],
//     display: display_name,
//     street: nominatimAddr.road,
//     city:
//       nominatimAddr.city ??
//       nominatimAddr.town ??
//       nominatimAddr.village ??
//       nominatimAddr.county,
//     state: nominatimAddr.state,
//     country: nominatimAddr.country,
//     postalCode: nominatimAddr.postcode,
//     coordinates: { latitude, longitude },
//   };

//   return address;
// };
