import { orderStatusEnum } from "@acme/db/schema";

import type { OrderFilterTab } from "./types";

export const OTP_EXPIRATION_TIME_IN_SECONDS = 300; // 5 minutes
export const OTP_LENGTH = 6; // 6 digits
export const MESSAGE_FETCH_INTERVAL = 10000; // 10 seconds
export const MAX_OTP_ATTEMPTS = 3; // Maximum verification attempts per OTP
export const OTP_RESEND_COOLDOWN_SECONDS = 60; // Minimum time between resend requests

export const MAP_INITIAL_CAMERA = {
  center: { lat: 22, lng: 77 },
  zoom: 3,
};

export const REFETCH_TIME_INTERVAL_FOR_ORDER_DETAILS_PAGE = 10000; // 10 seconds

export const DEFAULT_PROFILE_IMAGE = "/static/images/default-user.png";

export const QUICK_ACTIONS = [
  "Are you coming ?",
  "Call me when reach at doorstep",
  "I am waiting for you",
];

export const CONTACT_US_INFO = {
  email: {
    title: "Email",
    value: "<EMAIL>",
    icon: "/static/icons/email.svg",
  },
  phone: {
    title: "Contact Number",
    value: "7043807238",
    icon: "/static/icons/phone.svg",
  },
};

export const HOW_IT_WORKS_STEPS = {
  "1": {
    image: "/static/images/how-it-work-1.svg",
    imageAlt: "how-it-work-1",
    title: "Sell Your Scrap",
    subTitle: "Select Scrap Items",
    description:
      "Select scrap type & approximate weight. Get an instant estimated price",
    subDescription: null,
    buttonText: "Next",
    buttonLink: "/how-it-works/2",
  },
  "2": {
    image: "/static/images/how-it-work-2.svg",
    imageAlt: "how-it-work-2",
    title: "Scrap Pickup Request",
    subTitle: "Pickup Assigned",
    description:
      "Confirm your address. We'll assign a Kabadiwala you can track on a map.",
    subDescription: null,
    buttonText: "Next",
    buttonLink: "/how-it-works/3",
  },
  "3": {
    image: "/static/images/how-it-work-3.svg",
    imageAlt: "how-it-work-3",
    title: "Get Paid Instantly",
    subTitle: "Weigh, Confirm & Get Paid",
    description: "Easy collection, complete the job & Earn.",
    subDescription: "Ready to turn your scrap into cash?",
    buttonText: "Start Selling Now",
    buttonLink: "/auth/sign-up",
  },
};

export const ONBOARDING_STEPS_DATA = {
  STEP_1: {
    title: "A few quick Details",
    description: "This help us know who’s joining and where you work",
  },
  STEP_2: {
    title: "Your Address Location",
    description: "Your neighbourhood (this helps us find kabadiwala near you)",
  },
  STEP_3: {
    title: "Add Account to Receive Payment",
    description:
      "Connect to a verified account to help us to transfer payment directly to linked account",
  },
};

export const BOTTOM_TABS = [
  {
    icon: "/static/icons/home.svg",
    title: "Home",
    path: "/",
  },
  {
    icon: "/static/icons/account.svg",
    title: "Account",
    path: "/account",
  },
  {
    icon: "/static/icons/cart.svg",
    title: "Cart",
    path: "/cart",
  },
] as const;

export const REQUEST_PICKUP_OPTIONS = [
  {
    icon: "/static/icons/wallet.svg",
    title: "Quick Pickup",
    description: "An agent will collect the scrap directly without listing",
    subDescription: "This might take time in assigning",
    pros: null,
    isBestValue: false,
    isRecommended: false,
  },
  {
    icon: "/static/icons/wallet.svg",
    title: "Smart Pickup",
    description:
      "List your scrap items for a 10% value boost and enjoy exclusive bonuses:",
    subDescription: null,
    pros: [
      "Instant Price Estimation",
      "Priority pickup scheduling",
      "Unlock bonus rewards",
    ],
    isBestValue: true,
    isRecommended: true,
  },
] as const;

export const HOW_IT_WORKS_CARDS = [
  {
    icon: "/static/icons/list.svg",
    title: "Select scrap items",
    subTitle: "Select all the scrap items that you want to sell",
  },
  {
    icon: "/static/icons/location.svg",
    title: "Choose your pickup location",
    subTitle:
      "Enter/select your address location for doorstep pickup of scrap items",
  },
  {
    icon: "/static/icons/wallet.svg",
    title: "Confirmation & Transaction",
    subTitle:
      "When the Delivery partner arrives, share the OTP and hand over the scrap for quick weighing and paying.",
  },
] as const;

export const WHY_CHOOSE_US_CARDS = [
  {
    icon: "/static/icons/thumbs-up.svg",
    title: "Price",
    subTitle: "Get the best price.",
  },
  {
    icon: "/static/icons/calender.svg",
    title: "Trust",
    subTitle: "All scrap pickers are verified.",
  },
  {
    icon: "/static/icons/clock.svg",
    title: "Convenience",
    subTitle: "Order with one click.",
  },
  {
    icon: "/static/icons/clock.svg",
    title: "Fast Pick - up",
    subTitle: "An agent within 5 km will quickly pick up your order.",
  },
];

export const ACCOUNT_MENU_ITEMS = {
  GENERAL_SETTINGS: {
    title: "General Settings",
    items: [
      {
        icon: "/static/icons/user.svg",
        title: "Edit Profile",
        path: "/profile/edit",
        hasChevron: true,
      },
      {
        icon: "/static/icons/card.svg",
        title: "Saved Banks / UPIs",
        path: "/profile/saved-cards-banks",
        hasChevron: true,
        verification: {
          show: true,
          text: "VERIFY NOW",
        },
      },
      {
        icon: "/static/icons/earnings.svg",
        title: "Earnings",
        path: "/profile/earnings",
        hasChevron: true,
      },
      {
        icon: "/static/icons/address.svg",
        title: "Saved Address",
        path: "/profile/saved-address",
        hasChevron: true,
      },
      {
        icon: "/static/icons/settings.svg",
        title: "Settings",
        path: "/profile/settings",
        hasChevron: true,
      },
    ],
  },
  TERMS_CONDITIONS: {
    title: "Terms & Conditions",
    items: [
      {
        icon: "/static/icons/privacy-policy.svg",
        title: "Privacy Policy",
        path: "/profile/privacy-policy",
        hasChevron: true,
      },
      {
        icon: "/static/icons/cancellation-policy.svg",
        title: "Cancellation Policy",
        path: "/profile/cancellation-policy",
        hasChevron: true,
      },
      {
        icon: "/static/icons/document.svg",
        title: "Terms & Conditions",
        path: "/profile/terms-of-service",
        hasChevron: true,
      },
    ],
  },
  SUPPORT_HELP: {
    title: "Support & Help",
    items: [
      {
        icon: "/static/icons/headphones.svg",
        title: "Help & Support",
        path: "/profile/help-and-support",
        hasChevron: true,
      },
      //   {
      //     icon: "/static/icons/faq.svg",
      //     title: "FAQ's",
      //     path: "/profile/faqs",
      //     hasChevron: true,
      //   },
      {
        icon: "/static/icons/rate.svg",
        title: "Rate Us",
        path: "/profile/rate-us",
        hasChevron: true,
      },
    ],
  },
};

export const PRIVACY_POLICY = [
  {
    title: "1. Information We Collect",
    description:
      "We collect personal information from customers, such as names, addresses, and contact details, to facilitate scrap collection and payment. Agents provide similar information to ensure efficient service delivery.",
  },
];

export const TERMS_AND_CONDITIONS = [
  {
    title: "1. Eligibility",
    description:
      "To use our Platform, you must be at least 18 years old and legally capable of entering into binding agreements. By using the Platform, you confirm that you meet these eligibility criteria.",
  },
];

export const CANCELLATION_POLICY = [
  {
    title: "1. Cancellation Limitations",
    description:
      "To maintain a smooth operation, customers must be aware that they can only cancel orders five times. By using our services, you acknowledge and agree to these cancellation limitations.",
  },
];

export const ORDER_STATUS: OrderFilterTab[] = [
  "ALL",
  ...orderStatusEnum.enumValues,
] as const;

export const DELETE_ACCOUNT_INFO = {
  title: "Important: This action cannot be undone   ",
  important_info_points: [
    "All your data, including profile, listings and history will be permanently deleted",
    "Active listings will be removed from the marketplace",
    "You'll lose access to all sales records and earnings history",
    "Pending transactions will be cancelled",
    "Message history with buyers will be erased",
  ],
  please_ensure_points: [
    "All active sales are completed",
    "You've withdrawn all earnings",
    "You've downloaded any data you want to keep",
  ],
};

export const EMOTION_EMOJIS = ["😞", "🙁", "😐", "🙂", "😄"];
export const QUICK_SUGGESTIONS = [
  "Better Service",
  "Improve Features",
  "Fair Pricing",
];

export const HELP_AND_SUPPORT_ISSUES = [
  {
    title: "I want to manage my orders",
    description: "Is it accessible",
  },
  {
    title: "I want to manage my orders",
    description: "Is it accessible",
  },
  {
    title: "I want to manage my orders",
    description: "Is it accessible",
  },
];
