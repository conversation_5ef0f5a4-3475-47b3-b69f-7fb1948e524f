import { createSearchParamsCache, parseAsStringLiteral } from "nuqs/server";

import { ORDER_FILTER_TABS_PARAM_NAME } from "~/app/lib/param-names";

const ORDER_FILTER_TABS = [
  "ALL",
  "CART",
  "ACTIVE",
  "PENDING",
  "COMPLETED",
  "CANCELLED",
] as const;

export const orderParsers = {
  [ORDER_FILTER_TABS_PARAM_NAME]:
    parseAsStringLiteral(ORDER_FILTER_TABS).withDefault("ALL"),
};

export const orderSearchParamsCache = createSearchParamsCache(orderParsers);
