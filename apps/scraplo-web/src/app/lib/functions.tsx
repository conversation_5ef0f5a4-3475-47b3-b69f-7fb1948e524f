import { StreamVideoClient } from "@stream-io/video-react-sdk";
import {
  Bike,
  Car,
  Package,
  ShoppingCart,
  Trash2,
  Truck,
  TruckIcon,
  Wrench,
} from "lucide-react";

import { vehicleTypeEnum } from "@acme/db/schema";

export const getVehicleIcon = ({
  vehicleType,
  iconSize = 16,
  iconColor = "#1B2228",
}: {
  vehicleType?: string;
  iconSize?: number;
  iconColor?: string;
}) => {
  const iconProps = { size: iconSize, color: iconColor };

  switch (vehicleType?.toUpperCase()) {
    case "BICYCLE":
      return <Bike {...iconProps} />;
    case "CYCLE_RICKSHAW":
      return <Bike {...iconProps} />;
    case "HANDCART":
    case "PUSHCART":
      return <ShoppingCart {...iconProps} />;
    case "MOTORCYCLE_WITH_SIDECAR":
      return <Bike {...iconProps} />;
    case "THREE_WHEELER_CARGO_VEHICLE":
      return <Car {...iconProps} />;
    case "MINI_TRUCK":
    case "PICKUP_TRUCK":
      return <Truck {...iconProps} />;
    case "FLATBED_TRUCK":
    case "TIPPER_TRUCK":
    case "DUMP_TRUCK":
      return <TruckIcon {...iconProps} />;
    case "GRAPPLE_TRUCK":
      return <Package {...iconProps} />;
    case "GARBAGE_TRUCK":
      return <Trash2 {...iconProps} />;
    case "TOW_TRUCK":
      return <Wrench {...iconProps} />;
    case "BOX_TRUCK":
      return <Package {...iconProps} />;
    case "VAN":
      return <Car {...iconProps} />;
    default:
      // Default to a generic truck icon
      return <Truck {...iconProps} />;
  }
};

export const getVehicleDisplayName = (vehicleType?: string): string => {
  switch (vehicleType?.toUpperCase()) {
    case "BICYCLE":
      return "Bicycle";
    case "CYCLE_RICKSHAW":
      return "Cycle Rickshaw";
    case "HANDCART":
      return "Handcart";
    case "PUSHCART":
      return "Pushcart";
    case "MOTORCYCLE_WITH_SIDECAR":
      return "Motorcycle with Sidecar";
    case "THREE_WHEELER_CARGO_VEHICLE":
      return "Three-Wheeler Cargo";
    case "MINI_TRUCK":
      return "Mini Truck";
    case "PICKUP_TRUCK":
      return "Pickup Truck";
    case "FLATBED_TRUCK":
      return "Flatbed Truck";
    case "TIPPER_TRUCK":
      return "Tipper Truck";
    case "DUMP_TRUCK":
      return "Dump Truck";
    case "GRAPPLE_TRUCK":
      return "Grapple Truck";
    case "GARBAGE_TRUCK":
      return "Garbage Truck";
    case "TOW_TRUCK":
      return "Tow Truck";
    case "BOX_TRUCK":
      return "Box Truck";
    case "VAN":
      return "Van";
    default:
      return "Vehicle";
  }
};

// Legacy support - keeping for backward compatibility
export const VehicleTypeIcons = vehicleTypeEnum.enumValues.map((type) => ({
  type: getVehicleDisplayName(type),
  enumValue: type,
  icon: getVehicleIcon({ vehicleType: type }),
}));

export const RenderVehicleBadge = ({ badgeType }: { badgeType: string }) => {
  // Check if badgeType matches one of the enum values
  const isValidVehicleType = vehicleTypeEnum.enumValues.includes(
    badgeType as (typeof vehicleTypeEnum.enumValues)[number],
  );

  if (!isValidVehicleType) {
    return null;
  }

  const vehicleIcon = getVehicleIcon({ vehicleType: badgeType });
  const vehicleDisplayName = getVehicleDisplayName(badgeType);

  return (
    <div className="flex items-center gap-2 rounded-full border border-[#495F6E33] bg-black-0 px-3 py-1">
      <div className="flex items-center justify-center">{vehicleIcon}</div>
      <span className="text-xs font-medium text-[#1B2228]">
        {vehicleDisplayName}
      </span>
    </div>
  );
};

interface GetStreamClient {
  user: {
    id: string;
    name: string;
  };
  token: string;
}

export const getStreamClient = ({ user, token }: GetStreamClient) => {
  const client = new StreamVideoClient({
    apiKey: "qegmxedvwzrb",
    user: user,
    token: token,
  });

  return client;
};
