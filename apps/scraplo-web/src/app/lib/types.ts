import type { z } from "zod";

import type { AddressSchema, OrderFilterTabsSchema } from "@acme/validators";

import type { RouterOutputs } from "~/server/api";

export interface NominatimAddress {
  road?: string;
  city?: string;
  town?: string;
  village?: string; // Adding village as another possibility for city
  county?: string; // Sometimes city might be in county
  state?: string;
  country?: string;
  postcode?: string;
}

export interface NominatimResponse {
  display_name?: string;
  address?: NominatimAddress;
  error?: string; // Nominatim can return an error object
}

export type UserAddress = z.infer<typeof AddressSchema> & { id?: string };

export type TopLevelCategories =
  RouterOutputs["category"]["getTopLevelCategories"];

export type SubCategoriesWithOrWithoutParent =
  RouterOutputs["category"]["getSubCategoriesWithOrWithoutParent"];

export type AllSavedAddress = RouterOutputs["address"]["getAllSavedAddresses"];

export type CurrentCart = RouterOutputs["order"]["getCurrentCart"];

export type CartItem = RouterOutputs["order"]["getCartItemByCategoryId"];

export type Profile = RouterOutputs["user"]["getProfile"];

export type OrderItem =
  RouterOutputs["order"]["getAllOrders"]["orders"][number];

export type OrderFilterTab = z.infer<typeof OrderFilterTabsSchema.shape.tab>;

export type OrderDetail = RouterOutputs["order"]["getOrderById"];

export type OrderConversation =
  RouterOutputs["conversation"]["getConversationByOrderId"];

export type SupportConversation =
  RouterOutputs["conversation"]["getSupportConversation"];

export type SupportMessage =
  NonNullable<SupportConversation>["messages"][number];

export type FundAccount =
  RouterOutputs["payment"]["getAllFundAccounts"]["fundAccounts"][number];

export interface Admin {
  name: string;
  id: string;
  image: string | null;
}
export interface DLAdvanceApiAddress {
  addressLine1: string;
  completeAddress: string;
  country: string;
  district: string;
  pin: string;
  state: string;
  type: string;
}

export interface DLAdvanceApiValidity {
  from: string;
  to: string;
}

export interface DLAdvanceApiStatusDetails {
  from: string;
  remarks: string;
  to: string;
}

export interface DLAdvanceApiVehicleCategoryDetail {
  cov: string;
  expiryDate: string;
  issueDate: string;
}

export interface DLAdvanceApiResult {
  user_address: DLAdvanceApiAddress[];
  user_blood_group: string;
  dl_number: string;
  user_dob: string;
  endorse_date: string;
  endorse_number: string;
  expiry_date: string;
  father_or_husband: string;
  issued_date: string;
  non_transport_validity: DLAdvanceApiValidity;
  state: string;
  status: string;
  status_details: DLAdvanceApiStatusDetails;
  transport_validity: DLAdvanceApiValidity;
  user_full_name: string;
  user_image: string;
  vehicle_category_details: DLAdvanceApiVehicleCategoryDetail[];
}
export type DLCard = Pick<
  DLAdvanceApiResult,
  "dl_number" | "user_image" | "user_full_name"
>;

export interface DLAdvanceApiResponse {
  api_category: string;
  api_name: string;
  billable: boolean;
  txn_id: string;
  message: string;
  status: number;
  result: DLAdvanceApiResult | null;
  datetime: string;
}
