"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { Loader2 } from "lucide-react";
import OneSignal from "react-onesignal";

import { Button } from "@acme/ui/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@acme/ui/components/ui/card";

import { env } from "~/env";
import { useSession } from "~/server/auth/client";

// Storage key for notification permission status
const STORAGE_KEY = "scraplo_notification_permission_status";

export default function OneSignalProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const { data: session } = useSession();
  const [isInitialized, setIsInitialized] = useState(false);
  const router = useRouter();

  const [permissionStatus, setPermissionStatus] =
    useState<NotificationPermission | null>(null);

  useEffect(() => {
    // Check notification permission status and load from storage
    const checkPermissionStatus = () => {
      if (typeof window !== "undefined" && "Notification" in window) {
        const storedStatus = localStorage.getItem(STORAGE_KEY);
        if (storedStatus) {
          setPermissionStatus(storedStatus as NotificationPermission);
        } else {
          setPermissionStatus(Notification.permission);
        }
      }
    };

    checkPermissionStatus();
  }, []);

  // Effect to sync OneSignal permission status with localStorage
  useEffect(() => {
    if (!isInitialized) return;

    const syncPermissionStatus = () => {
      try {
        const oneSignalPermission = OneSignal.Notifications.permission;
        const isSubscribed = OneSignal.User.PushSubscription.optedIn;
        const newStatus = isSubscribed === true ? "granted" : "denied";

        console.log("OneSignal Debug:", {
          permission: oneSignalPermission,
          isSubscribed,
          newStatus,
          timestamp: new Date().toISOString(),
        });

        setPermissionStatus(newStatus);
        localStorage.setItem(STORAGE_KEY, newStatus);
      } catch (error) {
        console.error("Failed to sync OneSignal permission status:", error);
      }
    };

    // Initial sync
    syncPermissionStatus();

    // Set up interval to check for permission changes
    const interval = setInterval(syncPermissionStatus, 2000);

    return () => clearInterval(interval);
  }, [isInitialized]);

  useEffect(() => {
    // Ensure this code runs only on the client side
    if (!session?.user) return;
    if (typeof window !== "undefined") {
      OneSignal.init({
        appId: env.NEXT_PUBLIC_ONESIGNAL_CUSTOMER_APP_ID,
        safari_web_id: env.NEXT_PUBLIC_ONESIGNAL_CUSTOMER_APP_SAFARI_WEB_ID,
        notifyButton: {
          enable: session.user.email ? true : false,
          prenotify: true,
          showCredit: false,
          text: {
            "tip.state.unsubscribed": "Subscribe to notifications",
            "tip.state.subscribed": "You are subscribed to notifications",
            "tip.state.blocked": "You have blocked notifications",
            "message.prenotify": "Click to subscribe to notifications",
            "message.action.subscribing": "Subscribing...",
            "message.action.subscribed": "Thanks for subscribing!",
            "message.action.resubscribed":
              "You are now subscribed to notifications",
            "message.action.unsubscribed":
              "You will no longer receive notifications",
            "dialog.main.title": "Manage your notification settings",
            "dialog.main.button.subscribe": "SUBSCRIBE",
            "dialog.main.button.unsubscribe": "UNSUBSCRIBE",
            "dialog.blocked.title": "Unblock Notifications",
            "dialog.blocked.message":
              "Follow these instructions to allow notifications:",
          },
        },
        allowLocalhostAsSecureOrigin:
          // eslint-disable-next-line no-restricted-properties
          process.env.NODE_ENV === "development" ? true : false,
        autoRegister: session.user.email ? true : false,
        autoResubscribe: session.user.email ? true : false,
        persistNotification: session.user.email ? true : false,
      })
        .then(async () => {
          console.log("OneSignal initialized");
          await OneSignal.login(session.user.id);
          setIsInitialized(true);

          // Check if user has granted notification permission and is subscribed
          const permission = OneSignal.Notifications.permission;
          const isSubscribed = OneSignal.User.PushSubscription.optedIn;

          console.log("OneSignal Initialization Debug:", {
            permission,
            isSubscribed,
            timestamp: new Date().toISOString(),
          });

          if (isSubscribed === true) {
            setPermissionStatus("granted");
          } else {
            setPermissionStatus("denied");
          }
        })
        .catch((error) => {
          console.error("OneSignal initialization failed", error);
        });
    }
  }, [session?.user]);

  // If OneSignal is not initialized yet, render children normally
  if (!isInitialized) {
    return (
      <div className="flex min-h-screen items-center justify-center p-4">
        <Loader2 className="h-6 w-6 animate-spin text-blue-500" />
      </div>
    );
  }

  // If user has dismissed the dialog and permission is still denied, show permission message
  if (permissionStatus === "denied") {
    return (
      <div className="flex min-h-screen items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-yellow-100">
              <svg
                className="h-6 w-6 text-yellow-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 17h5l-5 5v-5zM9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"
                />
              </svg>
            </div>
            <CardTitle className="text-xl">
              Notification Permission Required
            </CardTitle>
            <CardDescription>
              You haven't granted notification permission, which may cause our
              app to not function properly.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4 text-center">
            <p className="text-sm text-muted-foreground">
              Click the floating bell icon and subscribe to continue using our
              app.
            </p>
            <p className="text-md font-semibold text-muted-foreground">
              if you dont see the bell icon, please refresh the page.
            </p>
            <Button onClick={() => router.refresh()}>
              {" "}
              Refresh Permission
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return <>{children}</>;
}
