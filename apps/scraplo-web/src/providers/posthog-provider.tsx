// app/providers.tsx
"use client";

import { useEffect } from "react";
import posthog from "posthog-js";
import { PostHog<PERSON>rovider as PHProvider } from "posthog-js/react";

import { env } from "~/env";

export function PostHogProvider({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    posthog.init(env.NEXT_PUBLIC_POSTHOG_KEY, {
      api_host: env.NEXT_PUBLIC_POSTHOG_HOST || "https://eu.i.posthog.com",
      person_profiles: "identified_only", // or 'always' to create profiles for anonymous users as well
      defaults: "2025-05-24",
    });
  }, []);
  // eslint-disable-next-line no-restricted-properties
  if (process.env.NODE_ENV === "production") {
    return <PHProvider client={posthog}>{children}</PHProvider>;
  } else {
    return <>{children}</>;
  }
}
