import { NuqsAdapter } from "nuqs/adapters/next/app";

import { Toaster } from "@acme/ui/components/ui/sonner";
import { GoogleMapsProvider } from "@acme/ui/context/google-maps-provider";

import { env } from "~/env";
import { TRPCReactProvider } from "~/trpc/react";
import { HydrateClient } from "~/trpc/server";
import { PostHogProvider } from "./posthog-provider";
import StreamVideoProvider from "./stream-provider";

// NOTE: If in case we need to use some other provider which need to be "use client" on top then i would recommend to create a new file for that provider in /providers dir and import it here.

// Main purpose of this file is to wrap the app with all the providers we need. and keep the layout.tsx clean.

const Providers = ({ children }: { children: React.ReactNode }) => {
  return (
    <PostHogProvider>
      <TRPCReactProvider>
        <NuqsAdapter>
          <GoogleMapsProvider apiKey={env.NEXT_PUBLIC_GOOGLE_MAPS_KEY}>
            <StreamVideoProvider>
              <HydrateClient>{children}</HydrateClient>
            </StreamVideoProvider>
          </GoogleMapsProvider>
        </NuqsAdapter>
        <Toaster richColors position="top-right" />
      </TRPCReactProvider>
    </PostHogProvider>
  );
};

export default Providers;
