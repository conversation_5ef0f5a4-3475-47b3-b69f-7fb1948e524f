"use client";

import React, { useEffect, useState } from "react";
import {
  <PERSON>Call,
  StreamTheme,
  StreamVideo,
  StreamVideoClient,
  useCall,
} from "@stream-io/video-react-sdk";
import { Loader2 } from "lucide-react";

import { env } from "~/env";
import { useSession } from "~/server/auth/client";

// Suggested unified approach for web
const StreamVideoProvider = ({ children }: { children: React.ReactNode }) => {
  const [streamVideoClient, setStreamVideoClient] =
    useState<StreamVideoClient>();
  const { data: session, isPending } = useSession();
  const call = useCall();

  useEffect(() => {
    if (!session?.user.id || !session.user.streamToken) return;

    console.log(
      "NEXT_PUBLIC_GETSTREAM_API_KEY",
      env.NEXT_PUBLIC_GETSTREAM_API_KEY,
      session.user.streamToken,
    );

    const client = new StreamVideoClient({
      // eslint-disable-next-line
      apiKey: env.NEXT_PUBLIC_GETSTREAM_API_KEY ?? "qegmxedvwzrb",
      user: {
        id: session.user.id,
        name: session.user.name || "User",
      },
      token: session.user.streamToken,
    });

    setStreamVideoClient(client);

    // Cleanup function
    return () => {
      client.disconnectUser().catch((err) => {
        console.error("Error disconnecting user", err);
      });
    };
  }, [session?.user]);

  if (isPending) {
    return (
      <div className="flex min-h-screen items-center justify-center p-4">
        <Loader2 className="h-6 w-6 animate-spin text-blue-500" />
      </div>
    );
  }

  if (!session?.user) {
    return <>{children}</>;
  }

  if (!streamVideoClient) {
    return (
      <div className="flex min-h-screen items-center justify-center p-4">
        <Loader2 className="h-6 w-6 animate-spin text-blue-500" />
      </div>
    );
  }

  return (
    <StreamVideo client={streamVideoClient}>
      <StreamTheme as="main" className="my-custom-root-class">
        <StreamCall call={call}>{children}</StreamCall>
      </StreamTheme>
    </StreamVideo>
  );
};

export default StreamVideoProvider;
