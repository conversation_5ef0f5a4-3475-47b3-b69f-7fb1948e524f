import { fileURLToPath } from "url";
/** @type {import("next").NextConfig} */

import withSerwistInit from "@serwist/next";
import create<PERSON>iti from "jiti";

// Import env files to validate at build time. Use jiti so we can load .ts files in here.
createJiti(fileURLToPath(import.meta.url))("./src/env");

const nextConfig = {
  reactStrictMode: true,
  compiler: {
    // Remove all console logs
    removeConsole: process.env.NODE_ENV === "development" ? false : true,
  },
  images: {
    dangerouslyAllowSVG: true,
    remotePatterns: [
      {
        hostname: "*.ufs.sh",
        protocol: "https",
      },
      {
        hostname: "utfs.io",
        protocol: "https",
      },
      {
        hostname: "*.pexels.com",
        protocol: "https",
      },
    ],
  },

  /** Enables hot reloading for local packages without a build step */
  transpilePackages: [
    "@acme/kabadiwala-api",
    "@acme/kabadiwala-auth",
    "@acme/db",
    "@acme/ui",
    "@acme/validators",
  ],

  async rewrites() {
    return [
      {
        source: "/ingest/static/:path*",
        destination: "https://eu-assets.i.posthog.com/static/:path*",
      },
      {
        source: "/ingest/:path*",
        destination: "https://eu.i.posthog.com/:path*",
      },
      {
        source: "/ingest/decide",
        destination: "https://eu.i.posthog.com/decide",
      },
    ];
  },

  // This is required to support PostHog trailing slash API requests
  skipTrailingSlashRedirect: true,

  /** We already do linting and typechecking as separate tasks in CI */
  //   eslint: { ignoreDuringBuilds: true },
  //   typescript: { ignoreBuildErrors: true },
};

// Determine the final config based on environment
let finalConfig;

if (process.env.NODE_ENV === "production") {
  const withSerwist = withSerwistInit({
    // Fix the path to point to src/app/sw.ts instead of app/sw.ts
    swSrc: "src/app/sw.ts",
    swDest: "public/sw.js",
  });

  finalConfig = withSerwist({ nextConfig });
} else {
  finalConfig = nextConfig;
}

// Single default export
export default finalConfig;
