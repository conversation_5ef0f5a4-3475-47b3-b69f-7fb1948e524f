{"name": "@acme/scraplo-web", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "pnpm with-env next build", "clean": "git clean -xdf .cache .next .turbo node_modules", "dev": "pnpm with-env next dev -p 3002 --turbopack", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "start": "pnpm with-env next start", "typecheck": "tsc --noEmit", "with-env": "dotenv -e ../../.env --"}, "prettier": "@acme/prettier-config", "dependencies": {"@acme/core": "workspace:*", "@acme/db": "workspace:*", "@acme/mail": "workspace:*", "@acme/msg91": "workspace:*", "@acme/onesignal": "workspace:*", "@acme/razorpay-sdk": "workspace:*", "@acme/ui": "workspace:*", "@acme/validators": "workspace:*", "@googlemaps/js-api-loader": "^1.16.8", "@onesignal/node-onesignal": "5.0.0-alpha-02", "@serwist/next": "^9.0.14", "@stream-io/node-sdk": "^0.4.25", "@stream-io/video-react-sdk": "^1.18.6", "@t3-oss/env-nextjs": "^0.13.0", "@tanstack/react-query": "catalog:", "@trpc/client": "catalog:", "@trpc/server": "catalog:", "@uidotdev/usehooks": "^2.4.1", "@vis.gl/react-google-maps": "^1.5.2", "axios": "^1.9.0", "bcryptjs": "3.0.2", "geist": "^1.3.1", "next": "^15.2.3", "nuqs": "^2.4.3", "posthog-js": "^1.257.0", "posthog-node": "^5.3.1", "razorpay": "^2.9.6", "react": "catalog:react19", "react-dom": "catalog:react19", "react-intersection-observer": "^9.16.0", "react-onesignal": "^3.2.2", "superjson": "2.2.2", "zod": "catalog:", "zustand": "^5.0.4"}, "devDependencies": {"@acme/eslint-config": "workspace:*", "@acme/prettier-config": "workspace:*", "@acme/tailwind-config": "workspace:*", "@acme/tsconfig": "workspace:*", "@types/node": "^22.14.1", "@types/react": "catalog:react19", "@types/react-dom": "catalog:react19", "dotenv-cli": "^8.0.0", "eslint": "catalog:", "jiti": "^1.21.7", "prettier": "catalog:", "serwist": "^9.0.14", "tailwindcss": "catalog:", "typescript": "catalog:"}}