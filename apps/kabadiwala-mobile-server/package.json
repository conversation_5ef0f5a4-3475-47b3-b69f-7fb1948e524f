{"name": "kabadiwala-server", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "pnpm with-env next build", "clean": "git clean -xdf .cache .next .turbo node_modules", "dev": "pnpm with-env next dev --port 8080 --turbopack", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "start": "pnpm with-env next start", "typecheck": "tsc --noEmit", "with-env": "dotenv -e ../../.env --"}, "prettier": "@acme/prettier-config", "dependencies": {"@acme/kabadiwala-api": "workspace:*", "@acme/kabadiwala-auth": "workspace:*", "@t3-oss/env-nextjs": "^0.13.0", "@uploadthing/react": "^7.3.0", "next": "^15.2.3", "uploadthing": "^7.6.0"}, "devDependencies": {"@acme/eslint-config": "workspace:*", "@acme/prettier-config": "workspace:*", "@acme/tsconfig": "workspace:*", "@types/node": "^22.14.1", "@types/react": "catalog:react19", "dotenv-cli": "^8.0.0", "eslint": "catalog:", "jiti": "^1.21.7", "prettier": "catalog:", "typescript": "catalog:"}}