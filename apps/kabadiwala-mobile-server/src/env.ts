import { createEnv } from "@t3-oss/env-nextjs";
import { vercel } from "@t3-oss/env-nextjs/presets-zod";
import { z } from "zod";

import { env as apiEnv } from "@acme/kabadiwala-api/env";
import { env as authEnv } from "@acme/kabadiwala-auth/env";

export const env = createEnv({
  extends: [authEnv, apiEnv, vercel()],
  shared: {
    NODE_ENV: z
      .enum(["development", "production", "test"])
      .default("development"),
    ONESIGNAL_KABADIWALA_KEY: z.string().nonempty(),
  },
  experimental__runtimeEnv: {
    NODE_ENV: process.env.NODE_ENV,
    ONESIGNAL_KABADIWALA_KEY: process.env.ONESIGNAL_KABADIWALA_KEY,
    // NEXT_PUBLIC_CLIENTVAR: process.env.NEXT_PUBLIC_CLIENTVAR,
  },
  skipValidation:
    !!process.env.CI || process.env.npm_lifecycle_event === "lint",
});
