import type {
  Address,
  DetectedLocation,
  GoogleLocationDetailResult,
  GoogleLocationResult,
  NormalizeQuery,
  Query,
} from "@/types/types";
import queryString from "query-string";

const BASE_URL = "https://maps.googleapis.com/maps/api/place";

const normalizeQuery = (query: Query): NormalizeQuery => {
  const { lat, lng, ...rest } = query;

  // The latitude/longitude around which to retrieve place information. This must be specified as latitude,longitude.
  let location;

  // If one of the value is provide lat/lng both must be there
  if ((lat && !lng) || (lng && !lat)) {
    throw new Error("Query: Location must have both lat & lng");
  }

  if (lat && lng) {
    location = `${lat},${lng}`;
  }

  return {
    ...rest,
    location,
  };
};

export class GoogleService {
  public static async search(
    term: string,
    query: Query,
    proxyUrl?: string,
  ): Promise<{
    predictions: GoogleLocationResult[];
    status: string;
  }> {
    const url = `${BASE_URL}/autocomplete/json?&input=${encodeURIComponent(
      term,
    )}&${queryString.stringify({ ...normalizeQuery(query) })}${
      query.strictBounds ? "&strictbounds" : ""
    }`;

    const _url = proxyUrl ? proxyUrl + url : url;

    const res = await fetch(_url);

    if (!res.ok) {
      throw new Error(res.statusText);
    }

    return res.json();
  }

  public static async searchDetails(
    placeid: string,
    query: Query & { fields?: string },
  ): Promise<GoogleLocationDetailResult> {
    const url = `${BASE_URL}/details/json?${queryString.stringify({
      ...normalizeQuery(query),
      placeid,
    })}`;

    const res = await fetch(url);

    const resJson: {
      status: string;
      result: GoogleLocationDetailResult;
    } = await res.json();

    if (!resJson.status) {
      throw new Error(res.statusText);
    }

    return Promise.resolve(resJson.result);
  }

  /**
   * Gets location details and formats them like the web implementation
   * @param placeId The Google Place ID to fetch details for
   * @param query Query parameters for the API request
   * @returns DetectedLocation with formatted address data
   */
  public static async getLocationDetails(
    placeId: string,
    query: Query & { fields?: string },
  ): Promise<DetectedLocation> {
    try {
      const placeDetails = await this.searchDetails(placeId, query);

      if (!placeDetails.geometry?.location) {
        throw new Error("No location data found in place details");
      }

      const { lat, lng } = placeDetails.geometry.location;
      const address = this.extractAddressFromComponents(placeDetails);

      return {
        latitude: lat,
        longitude: lng,
        address,
      };
    } catch (error) {
      console.error("Error getting location details:", error);
      throw error;
    }
  }

  /**
   * Extracts address components from Google Place details
   * @param placeDetails The Google Place details result
   * @returns Formatted Address object
   */
  private static extractAddressFromComponents(
    placeDetails: GoogleLocationDetailResult,
  ): Address {
    const components = placeDetails.address_components || [];

    // Initialize address with default values
    const address: Address = {
      display: placeDetails.formatted_address || "",
      street: null,
      city: null,
      state: null,
      country: null,
      postalCode: null,
    };

    // Extract address components
    for (const component of components) {
      const types = component.types;

      if (types.includes("street_number") || types.includes("route")) {
        // Combine street number and route if both exist
        if (address.street) {
          address.street += types.includes("street_number")
            ? ` ${component.long_name}`
            : `, ${component.long_name}`;
        } else {
          address.street = component.long_name;
        }
      }

      if (types.includes("locality") || types.includes("sublocality")) {
        address.city = component.long_name;
      }

      if (types.includes("administrative_area_level_1")) {
        address.state = component.long_name;
      }

      if (types.includes("country")) {
        address.country = component.long_name;
      }

      if (types.includes("postal_code")) {
        address.postalCode = component.long_name;
      }
    }

    return address;
  }

  /**
   * Gets location data from coordinates using OpenStreetMap Nominatim
   * This mirrors the web implementation's approach for address lookup
   * @param latitude Latitude coordinate
   * @param longitude Longitude coordinate
   * @returns DetectedLocation with address data
   */
  public static async getLocationFromCoordinates(
    latitude: number,
    longitude: number,
  ): Promise<DetectedLocation> {
    try {
      // Use Nominatim to get address details just like the web implementation
      const response = await fetch(
        `https://nominatim.openstreetmap.org/reverse?format=json&lat=${latitude}&lon=${longitude}&addressdetails=1`,
        {
          headers: {
            "Accept-Language": "en",
          },
        },
      );

      if (!response.ok) {
        throw new Error(`Failed to fetch address: ${response.statusText}`);
      }

      const addressData = await response.json();
      console.log("[api] address data", addressData);

      const address: Address = {
        display: addressData.display_name,
        street: addressData.address.road,
        city:
          addressData.address.city ||
          addressData.address.town ||
          addressData.address.village,
        state: addressData.address.state,
        country: addressData.address.country,
        postalCode: addressData.address.postcode,
      };

      return {
        latitude,
        longitude,
        address,
      };
    } catch (error) {
      console.error("Error getting address from coordinates:", error);
      // Return coordinates without address if lookup fails
      return {
        latitude,
        longitude,
        address: null,
      };
    }
  }
}
