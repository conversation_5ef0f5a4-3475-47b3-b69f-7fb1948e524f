import { Env } from "@/lib/env";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { StreamVideoClient } from "@stream-io/video-client";

interface GetStreamClient {
  user: {
    id: string;
    name: string;
  };
  token: string;
}

export const getStreamClient = ({ user, token }: GetStreamClient) => {
  AsyncStorage.setItem("@userId", user.id).catch(console.error);
  AsyncStorage.setItem("@userName", user.name).catch(console.error);
  AsyncStorage.setItem("@getStreamToken", token).catch(console.error);
  const client = new StreamVideoClient({
    apiKey: Env.GET_STREAM_API_KEY,
    user: user,
    token: token,
  });

  return client;
};
