import type { <PERSON><PERSON><PERSON><PERSON>, OrderData } from "@/types/types";
import React from "react";
import Svg, { <PERSON>lip<PERSON><PERSON>, Defs, G, Path, Rect } from "react-native-svg";
import * as WebBrowser from "expo-web-browser";
import StepOneForm from "@/components/shared/onboarding/step-1-form";
import StepTwoForm from "@/components/shared/onboarding/step-2-form";
import StepThreeForm from "@/components/shared/onboarding/step-3-form";
import StepFourForm from "@/components/shared/onboarding/step-4-form";
import StepFiveForm from "@/components/shared/onboarding/step-5-form";
import StepSixForm from "@/components/shared/onboarding/step-6-form";
import HowItWorkImage1 from "@assets/images/how-it-works-1.png";
import HowItWorkImage2 from "@assets/images/how-it-works-2.png";
import HowItWorkImage3 from "@assets/images/how-it-works-3.png";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import AntDesign from "@expo/vector-icons/AntDesign";

export const CHECK_FOR_ACTIVE_ORDERS_INTERVAL = 30000; // 30 seconds
export const GET_USER_LIVE_LOCATION_INTERVAL = 120000; // 2 minutes
export const GET_NEW_MESSAGES_IN_SUPPORT_CHAT = 10000; // 10 seconds

export const ImagePlaceholder =
  "data:image/png;base64,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";

export const HOW_IT_WORKS_STEPS = {
  "1": {
    image: HowItWorkImage1,
    imageAlt: "how-it-work-1",
    title: "Join Our Team & Get Ready",
    subTitle: "Quick signup and verification",
    description: "Takes fewer seconds to create account",
    subDescription: null,
    buttonText: "Next",
    buttonLink: "/how-it-works/2",
  },
  "2": {
    image: HowItWorkImage2,
    imageAlt: "how-it-work-2",
    title: "Get Pickup Requests & Accept Jobs",
    subTitle: "Find Pickup Near You",
    description:
      "Simply tap “Active” in the app when you are ready to work . Top up your wallet, now you are ready ....",
    subDescription: null,
    buttonText: "Next",
    buttonLink: "/how-it-works/3",
  },
  "3": {
    image: HowItWorkImage3,
    imageAlt: "how-it-work-3",
    title: "Pick up, Deliver & Get Paid",
    subTitle: "Find Pickup Near You",
    description: "Easy collection, complete the job & Earn",
    subDescription: "Ready to boost your earnings?",
    buttonText: "Start Earning Now",
    buttonLink: "/(without-layout)/auth/sign-up",
  },
} as const;

export const ONBOARDING_STEPS_DATA = {
  STEP_1: {
    title: "A Few Quick Details",
    description: "This help us know who’s joining and where you work",
    form: StepOneForm,
  },
  STEP_2: {
    title: "Tell Us About Your Vehicle",
    description:
      "How do you get around? This will help us find jobs that are a good fit for your vehicle",
    form: StepTwoForm,
  },
  STEP_3: {
    title: "Let’s Confirm It’s You",
    description:
      "Uploading your Aadhaar to makes sure you get paid correctly. Your documents are safe with us",
    form: StepThreeForm,
  },
  STEP_4: {
    title: "Let’s Confirm It’s You",
    description:
      "Please enter your Driving Licence to makes sure you get paid correctly. Your information is safe with us",
    form: StepFourForm,
  },
  STEP_5: {
    title: "Police Verification Document",
    description:
      "Upload a copy of police verification document that is not older than 6 months",
    form: StepFiveForm,
  },
  STEP_6: {
    title: "Your Main Collection Area Address",
    description:
      "Your neighbourhood or area in Greater Noida (this helps us find jobs near you)",
    form: StepSixForm,
  },
};

export const DRAWER_NAVIGATION_URLS: DrawerNav[] = [
  {
    sectionTitle: "General Settings",
    sectionsLinks: [
      {
        name: "Home",
        link: "/home",
        linkIcon: <AntDesign name="home" size={16} color="#333333" />,
      },
      {
        name: "Your Profile",
        link: "/general-settings/profile",
        linkIcon: <AntDesign name="user" size={16} color="#333333" />,
      },
      {
        name: "Documents",
        link: "/general-settings/documents",
        linkIcon: (
          <Svg width="18" height="18" viewBox="0 0 18 18" fill="none">
            <Path
              d="M9.5625 5.0625H5.625C5.31394 5.0625 5.0625 4.8105 5.0625 4.5C5.0625 4.1895 5.31394 3.9375 5.625 3.9375H9.5625C9.87356 3.9375 10.125 4.1895 10.125 4.5C10.125 4.8105 9.87356 5.0625 9.5625 5.0625Z"
              fill="#333333"
            />
            <Path
              d="M11.25 7.875H5.625C5.31394 7.875 5.0625 7.623 5.0625 7.3125C5.0625 7.002 5.31394 6.75 5.625 6.75H11.25C11.5611 6.75 11.8125 7.002 11.8125 7.3125C11.8125 7.623 11.5611 7.875 11.25 7.875Z"
              fill="#333333"
            />
            <Path
              d="M8.92125 10.125H5.625C5.31394 10.125 5.0625 9.873 5.0625 9.5625C5.0625 9.252 5.31394 9 5.625 9H8.92125C9.23231 9 9.48375 9.252 9.48375 9.5625C9.48375 9.873 9.23231 10.125 8.92125 10.125Z"
              fill="#333333"
            />
            <Path
              d="M7.57125 12.375H5.625C5.31394 12.375 5.0625 12.123 5.0625 11.8125C5.0625 11.502 5.31394 11.25 5.625 11.25H7.57125C7.88231 11.25 8.13375 11.502 8.13375 11.8125C8.13375 12.123 7.88231 12.375 7.57125 12.375Z"
              fill="#333333"
            />
            <Path
              d="M7.57125 15.75H5.0625C3.51169 15.75 2.25 14.4883 2.25 12.9375V3.375C2.25 1.82419 3.51169 0.5625 5.0625 0.5625H11.8125C13.3633 0.5625 14.625 1.82419 14.625 3.375V7.9875C14.625 8.298 14.3736 8.55 14.0625 8.55C13.7514 8.55 13.5 8.298 13.5 7.9875V3.375C13.5 2.44463 12.7429 1.6875 11.8125 1.6875H5.0625C4.13213 1.6875 3.375 2.44463 3.375 3.375V12.9375C3.375 13.8679 4.13213 14.625 5.0625 14.625H7.57125C7.88231 14.625 8.13375 14.877 8.13375 15.1875C8.13375 15.498 7.88231 15.75 7.57125 15.75Z"
              fill="#333333"
            />
            <Path
              opacity="0.7"
              d="M12.9375 17.4375C10.7668 17.4375 9 15.6712 9 13.5C9 11.3288 10.7668 9.5625 12.9375 9.5625C15.1082 9.5625 16.875 11.3288 16.875 13.5C16.875 15.6712 15.1082 17.4375 12.9375 17.4375ZM12.9375 10.6875C11.3867 10.6875 10.125 11.9492 10.125 13.5C10.125 15.0508 11.3867 16.3125 12.9375 16.3125C14.4883 16.3125 15.75 15.0508 15.75 13.5C15.75 11.9492 14.4883 10.6875 12.9375 10.6875Z"
              fill="#333333"
            />
            <Path
              opacity="0.7"
              d="M12.5625 14.9246C12.4185 14.9246 12.2745 14.8694 12.1648 14.7598L11.415 14.0099C11.195 13.79 11.195 13.4345 11.415 13.2146C11.6349 12.9946 11.9904 12.9946 12.2103 13.2146L12.5625 13.5667L13.665 12.4648C13.8849 12.2448 14.2404 12.2448 14.4603 12.4648C14.6803 12.6847 14.6803 13.0408 14.4603 13.2601L12.9601 14.7603C12.8505 14.87 12.7065 14.9246 12.5625 14.9246Z"
              fill="#333333"
            />
          </Svg>
        ),
      },
      {
        name: "Earnings",
        link: "/general-settings/earnings",
        linkIcon: (
          <Svg width="18" height="18" viewBox="0 0 18 18" fill="none">
            <Path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M9 2.8125C5.58274 2.8125 2.8125 5.58274 2.8125 9C2.8125 12.4172 5.58274 15.1875 9 15.1875C12.4172 15.1875 15.1875 12.4172 15.1875 9C15.1875 8.36355 15.0916 7.75057 14.9138 7.17412C14.8223 6.87724 14.9888 6.56238 15.2856 6.47083C15.5825 6.3793 15.8974 6.54574 15.9889 6.84261C16.1994 7.52527 16.3125 8.24993 16.3125 9C16.3125 13.0386 13.0386 16.3125 9 16.3125C4.96142 16.3125 1.6875 13.0386 1.6875 9C1.6875 4.96142 4.96142 1.6875 9 1.6875C9.51818 1.6875 10.0244 1.74149 10.5131 1.84435C10.8171 1.90834 11.0117 2.20665 10.9477 2.51065C10.8837 2.81464 10.5854 3.00922 10.2814 2.94523C9.86843 2.85831 9.43988 2.8125 9 2.8125ZM16.3821 2.24401C16.6018 2.46368 16.6018 2.81983 16.3821 3.0395L13.3821 6.0395C13.1624 6.25917 12.8063 6.25917 12.5866 6.0395L11.0866 4.5395C10.867 4.31984 10.867 3.96368 11.0866 3.74401C11.3063 3.52434 11.6624 3.52434 11.8821 3.74401L12.9844 4.84626L15.5866 2.24401C15.8063 2.02434 16.1624 2.02434 16.3821 2.24401Z"
              fill="#333333"
            />
            <Path
              opacity="0.7"
              d="M11.0625 7.1626H10.2375C10.1625 6.9001 10.0125 6.6376 9.8625 6.4126H11.0625C11.2875 6.4126 11.4375 6.2626 11.4375 6.0376C11.4375 5.8126 11.2875 5.6626 11.0625 5.6626H6.9375C6.7125 5.6626 6.5625 5.8126 6.5625 6.0376C6.5625 6.2626 6.7125 6.4126 6.9375 6.4126H8.25C8.7375 6.4126 9.225 6.7126 9.4125 7.1626H6.9375C6.7125 7.1626 6.5625 7.3126 6.5625 7.5376C6.5625 7.7626 6.7125 7.9126 6.9375 7.9126H9.525C9.45 8.5501 8.8875 9.0376 8.25 9.0376H6.9375H6.9C6.8625 9.0376 6.8625 9.0376 6.825 9.0376C6.7875 9.0376 6.7875 9.0751 6.75 9.0751C6.75 9.0751 6.7125 9.0751 6.7125 9.1126C6.675 9.1501 6.6375 9.1876 6.6375 9.2251C6.6375 9.2626 6.6375 9.2626 6.6375 9.3001V9.3751V9.4126C6.6375 9.4501 6.675 9.5251 6.675 9.5626C6.675 9.5626 6.675 9.6001 6.7125 9.6001L8.9625 12.2251C9.1125 12.3751 9.3375 12.4126 9.4875 12.2626C9.6375 12.1126 9.675 11.8876 9.525 11.7376L7.7625 9.7876H8.25C9.3 9.7876 10.2 8.9626 10.3125 7.9126H11.0625C11.2875 7.9126 11.4375 7.7626 11.4375 7.5376C11.4375 7.3126 11.2875 7.1626 11.0625 7.1626Z"
              fill="#333333"
              stroke="#333333"
              stroke-width="0.18"
            />
          </Svg>
        ),
      },
      {
        name: "Wallet",
        link: "/general-settings/wallet",
        linkIcon: (
          <Svg width="18" height="18" viewBox="0 0 18 18" fill="none">
            <Path
              opacity="0.7"
              d="M1.78499 13.9205C1.81355 13.9205 1.84321 13.9183 1.87233 13.9137C2.1794 13.8659 2.38924 13.578 2.34144 13.2712C2.21785 12.4805 2.48482 11.6675 3.05556 11.0976C3.71274 10.4444 4.65729 10.1977 5.54265 10.4392C5.39475 10.5597 5.30518 10.7455 5.33412 10.9482C5.37422 11.2286 5.61482 11.431 5.89058 11.431C5.91694 11.431 5.94386 11.4291 5.97078 11.4253L7.1617 11.255C7.46554 11.1967 7.66589 10.927 7.63905 10.6186L7.46876 9.42794C7.42482 9.12059 7.13862 8.90554 6.83211 8.95086C6.54385 8.99202 6.34661 9.24628 6.35798 9.53107C6.22096 9.4713 6.07992 9.41997 5.93507 9.3774C4.63759 8.98436 3.23024 9.33785 2.26124 10.3005C1.43617 11.1251 1.05055 12.3003 1.22963 13.4448C1.27302 13.7225 1.51253 13.9205 1.78499 13.9205Z"
              fill="#333333"
            />
            <Path
              opacity="0.7"
              d="M2.27179 16.3918C2.31189 16.6725 2.55249 16.875 2.82825 16.875C2.85462 16.875 2.88153 16.873 2.9079 16.8695C3.19636 16.8283 3.39425 16.5737 3.38261 16.2886C3.51974 16.3484 3.66091 16.3999 3.80603 16.4426C4.1565 16.5487 4.51465 16.6006 4.87006 16.6006C5.83136 16.6006 6.77234 16.2224 7.47986 15.5192C8.30658 14.6922 8.68506 13.5549 8.51752 12.3986C8.47302 12.091 8.18628 11.8781 7.88031 11.9223C7.57325 11.9668 7.36011 12.2522 7.40461 12.5595C7.52051 13.3615 7.25794 14.1501 6.68555 14.7225C6.02895 15.3753 5.0834 15.6221 4.19859 15.3809C4.34642 15.2604 4.43593 15.0746 4.40699 14.8721C4.36304 14.5645 4.07685 14.35 3.77033 14.3948L2.57941 14.5648C2.27813 14.6224 2.0748 14.8949 2.10205 15.2009L2.27179 16.3918Z"
              fill="#333333"
            />
            <Path
              d="M16.8144 14.6228V5.5665C16.8144 4.97186 16.3304 4.48792 15.7361 4.48792H15.0055V2.72516C15.0055 1.84296 14.2875 1.125 13.4053 1.125H4.48499C3.24628 1.125 2.23828 2.133 2.23828 3.3717V8.28836C2.23828 8.599 2.48987 8.85086 2.80078 8.85086C3.11169 8.85086 3.36328 8.599 3.36328 8.28836V5.30279C3.70197 5.49962 4.0835 5.61292 4.48499 5.61292H15.6894V7.40176H12.1133C11.2328 7.40176 10.5159 8.11835 10.5159 8.9989V11.1901C10.5159 12.0707 11.2328 12.7873 12.1133 12.7873H15.6894V14.5764H9.4184C9.10748 14.5764 8.8559 14.8282 8.8559 15.1389C8.8559 15.4495 9.10748 15.7014 9.4184 15.7014H15.7361C16.3304 15.7014 16.8144 15.2174 16.8144 14.6228ZM3.68793 4.15558C3.48193 3.95261 3.36328 3.66696 3.36328 3.3717C3.36328 2.75317 3.86646 2.25 4.48499 2.25H13.4053C13.6674 2.25 13.8805 2.46313 13.8805 2.72516V4.48792H4.48499C4.18561 4.48792 3.90381 4.37119 3.68793 4.15558ZM12.1133 11.6623C11.853 11.6623 11.6409 11.4505 11.6409 11.1901V10.657H13.0983C13.4092 10.657 13.6608 10.4052 13.6608 10.0945C13.6608 9.78387 13.4092 9.53201 13.0983 9.53201H11.6409V8.9989C11.6409 8.73853 11.853 8.52676 12.1133 8.52676H15.6894V11.6623H12.1133Z"
              fill="#333333"
            />
          </Svg>
        ),
      },
      {
        name: "Schedule Hours",
        link: "/general-settings/schedule-hours",
        linkIcon: (
          <Svg width="18" height="18" viewBox="0 0 18 18" fill="none">
            <Path
              d="M13.5003 2.8125H12.5628V2.25C12.5628 2.10082 12.5035 1.95774 12.398 1.85225C12.2925 1.74676 12.1495 1.6875 12.0003 1.6875C11.8511 1.6875 11.708 1.74676 11.6025 1.85225C11.497 1.95774 11.4378 2.10082 11.4378 2.25V2.8125H6.56277V2.25C6.56277 2.10082 6.50351 1.95774 6.39802 1.85225C6.29253 1.74676 6.14946 1.6875 6.00027 1.6875C5.85109 1.6875 5.70801 1.74676 5.60252 1.85225C5.49703 1.95774 5.43777 2.10082 5.43777 2.25V2.8125H4.50027C4.12028 2.76846 3.73524 2.81091 3.37397 2.93668C3.0127 3.06244 2.68454 3.26827 2.41404 3.53877C2.14355 3.80927 1.93772 4.13742 1.81195 4.4987C1.68618 4.85997 1.64373 5.24501 1.68777 5.625V13.5C1.64373 13.88 1.68618 14.265 1.81195 14.6263C1.93772 14.9876 2.14355 15.3157 2.41404 15.5862C2.68454 15.8567 3.0127 16.0626 3.37397 16.1883C3.73524 16.3141 4.12028 16.3565 4.50027 16.3125H13.5003C13.8803 16.3565 14.2653 16.3141 14.6266 16.1883C14.9878 16.0626 15.316 15.8567 15.5865 15.5862C15.857 15.3157 16.0628 14.9876 16.1886 14.6263C16.3144 14.265 16.3568 13.88 16.3128 13.5V5.625C16.3568 5.24501 16.3144 4.85997 16.1886 4.4987C16.0628 4.13742 15.857 3.80927 15.5865 3.53877C15.316 3.26827 14.9878 3.06244 14.6266 2.93668C14.2653 2.81091 13.8803 2.76846 13.5003 2.8125ZM4.50027 3.9375H5.43777V4.5C5.43777 4.64918 5.49703 4.79226 5.60252 4.89775C5.70801 5.00324 5.85109 5.0625 6.00027 5.0625C6.14946 5.0625 6.29253 5.00324 6.39802 4.89775C6.50351 4.79226 6.56277 4.64918 6.56277 4.5V3.9375H11.4378V4.5C11.4378 4.64918 11.497 4.79226 11.6025 4.89775C11.708 5.00324 11.8511 5.0625 12.0003 5.0625C12.1495 5.0625 12.2925 5.00324 12.398 4.89775C12.5035 4.79226 12.5628 4.64918 12.5628 4.5V3.9375H13.5003C14.683 3.9375 15.1878 4.44225 15.1878 5.625V6.1875H2.81277V5.625C2.81277 4.44225 3.31752 3.9375 4.50027 3.9375ZM13.5003 15.1875H4.50027C3.31752 15.1875 2.81277 14.6827 2.81277 13.5V7.3125H15.1878V13.5C15.1878 14.6827 14.683 15.1875 13.5003 15.1875Z"
              fill="#333333"
            />
            <Path
              opacity="0.7"
              d="M11.3951 9.78276C11.3669 9.71449 11.3254 9.65247 11.2731 9.60023C11.2206 9.54794 11.1583 9.50655 11.0897 9.47846C11.0211 9.45036 10.9477 9.43611 10.8736 9.43653C10.7994 9.43695 10.7262 9.45203 10.6579 9.48089C10.5897 9.50976 10.5278 9.55185 10.4759 9.60473L8.37587 11.7047L7.52312 10.8527C7.41649 10.7534 7.27545 10.6993 7.12973 10.7018C6.984 10.7044 6.84496 10.7635 6.7419 10.8665C6.63884 10.9696 6.57981 11.1086 6.57724 11.2543C6.57466 11.4001 6.62876 11.5411 6.72812 11.6477L7.98062 12.9002C8.03274 12.9526 8.09469 12.9941 8.1629 13.0224C8.23111 13.0508 8.30425 13.0654 8.37812 13.0654C8.45198 13.0654 8.52512 13.0508 8.59333 13.0224C8.66155 12.9941 8.72349 12.9526 8.77562 12.9002L11.2731 10.396C11.3254 10.3437 11.3669 10.2817 11.3951 10.2134C11.4234 10.1452 11.438 10.072 11.438 9.99811C11.438 9.92421 11.4234 9.85103 11.3951 9.78276Z"
              fill="#333333"
            />
          </Svg>
        ),
      },
      {
        name: "Vehicle",
        link: "/general-settings/vehicle",
        linkIcon: (
          <Svg width="18" height="18" viewBox="0 0 18 18" fill="none">
            <G clip-Path="url(#clip0_554_17105)">
              <Path
                d="M13.5951 10.7017C12.344 10.7017 11.3262 11.7195 11.3262 12.9706C11.3262 14.2217 12.344 15.2395 13.5951 15.2395C14.8464 15.2395 15.864 14.2217 15.864 12.9706C15.864 11.7195 14.8462 10.7017 13.5951 10.7017ZM13.5951 14.105C12.9694 14.105 12.4606 13.5962 12.4606 12.9706C12.4606 12.3449 12.9694 11.8361 13.5951 11.8361C14.2207 11.8361 14.7295 12.3449 14.7295 12.9706C14.7295 13.5962 14.2207 14.105 13.5951 14.105Z"
                fill="#333333"
              />
              <Path
                d="M5.84313 10.7017C4.59203 10.7017 3.57422 11.7195 3.57422 12.9706C3.57422 14.2217 4.59203 15.2395 5.84313 15.2395C7.09424 15.2395 8.11205 14.2217 8.11205 12.9706C8.11205 11.7195 7.09424 10.7017 5.84313 10.7017ZM5.84313 14.105C5.21749 14.105 4.70868 13.5962 4.70868 12.9706C4.70868 12.3449 5.21749 11.8361 5.84313 11.8361C6.4686 11.8361 6.97759 12.3449 6.97759 12.9706C6.97759 13.5962 6.46877 14.105 5.84313 14.105Z"
                fill="#333333"
              />
              <Path
                d="M15.1232 4.20738C15.0268 4.01585 14.8307 3.89502 14.6163 3.89502H11.6289V5.02948H14.2665L15.8111 8.10157L16.8249 7.5918L15.1232 4.20738Z"
                fill="#333333"
              />
              <Path
                d="M11.8926 12.4224H7.60059V13.5568H11.8926V12.4224Z"
                fill="#333333"
              />
              <Path
                d="M4.14099 12.4224H2.17463C1.86132 12.4224 1.60742 12.6763 1.60742 12.9896C1.60742 13.3029 1.86136 13.5568 2.17463 13.5568H4.14103C4.45434 13.5568 4.70824 13.3029 4.70824 12.9896C4.70824 12.6763 4.4543 12.4224 4.14099 12.4224Z"
                fill="#333333"
              />
              <Path
                d="M17.8811 8.95485L16.7654 7.51788C16.6582 7.37947 16.4928 7.29853 16.3175 7.29853H12.1956V3.32795C12.1956 3.01464 11.9417 2.76074 11.6284 2.76074H2.17463C1.86132 2.76074 1.60742 3.01468 1.60742 3.32795C1.60742 3.64123 1.86136 3.89516 2.17463 3.89516H11.0612V7.86575C11.0612 8.17906 11.3151 8.43296 11.6284 8.43296H16.0397L16.8658 9.49707V12.4225H15.2965C14.9831 12.4225 14.7292 12.6764 14.7292 12.9897C14.7292 13.303 14.9832 13.5569 15.2965 13.5569H17.433C17.7463 13.5569 18.0002 13.3029 18.0003 12.9897V9.30276C18.0003 9.17683 17.9583 9.05431 17.8811 8.95485Z"
                fill="#333333"
              />
              <G opacity="0.7">
                <Path
                  d="M4.10225 9.54834H1.49299C1.17968 9.54834 0.925781 9.80227 0.925781 10.1156C0.925781 10.4289 1.17971 10.6828 1.49299 10.6828H4.10222C4.41553 10.6828 4.66943 10.4288 4.66943 10.1156C4.66947 9.80227 4.41553 9.54834 4.10225 9.54834Z"
                  fill="#333333"
                />
              </G>
              <G opacity="0.7">
                <Path
                  d="M5.40756 7.31738H0.567211C0.253934 7.31738 0 7.57132 0 7.88463C0 8.19794 0.253934 8.45184 0.567211 8.45184H5.40756C5.72087 8.45184 5.97477 8.19791 5.97477 7.88463C5.97477 7.57135 5.72087 7.31738 5.40756 7.31738Z"
                  fill="#333333"
                />
              </G>
              <G opacity="0.7">
                <Path
                  d="M6.33334 5.08594H1.49299C1.17968 5.08594 0.925781 5.33987 0.925781 5.65315C0.925781 5.96646 1.17971 6.22036 1.49299 6.22036H6.33334C6.64665 6.22036 6.90055 5.96643 6.90055 5.65315C6.90059 5.33987 6.64665 5.08594 6.33334 5.08594Z"
                  fill="#333333"
                />
              </G>
            </G>
            <Defs>
              <ClipPath id="clip0_554_17105">
                <Rect width="18" height="18" fill="white" />
              </ClipPath>
            </Defs>
          </Svg>
        ),
      },
      {
        name: "Settings",
        link: "/general-settings/settings",
        linkIcon: <AntDesign name="setting" size={16} color="#333333" />,
      },
    ],
  },
  {
    sectionTitle: "Terms & Conditions",
    sectionsLinks: [
      {
        name: "Privacy Policy",
        link: "/terms/privacy-policy",
        linkIcon: (
          <MaterialCommunityIcons
            name="shield-alert-outline"
            size={16}
            color="#333333"
          />
        ),
        onPress: async () => {
          await WebBrowser.openBrowserAsync(
            "https://scraplo-seller-web.vercel.app/profile/privacy-policy",
          );
        },
      },
      {
        name: "Terms of Service",
        link: "/terms/terms-and-conditions",
        linkIcon: (
          <MaterialCommunityIcons
            name="shield-check-outline"
            size={16}
            color="#333333"
          />
        ),
        onPress: async () => {
          await WebBrowser.openBrowserAsync(
            "https://scraplo-seller-web.vercel.app/profile/terms-of-service",
          );
        },
      },
    ],
  },
  {
    sectionTitle: "Support & Help",
    sectionsLinks: [
      {
        name: "Help & Support",
        link: "/contact-support",
        linkIcon: (
          <MaterialCommunityIcons name="headset" size={16} color="#333333" />
        ),
        // onPress: async () => {
        //   await WebBrowser.openBrowserAsync(
        //     "https://scraplo-seller-web.vercel.app/profile/help-and-support",
        //   );
        // },
      },
      //   {
      //     name: "FAQ's",
      //     link: "/support/faq",
      //     linkIcon: (
      //       <MaterialCommunityIcons
      //         name="message-question-outline"
      //         size={16}
      //         color="#333333"
      //       />
      //     ),
      //   },
      {
        name: "Rate Us",
        link: "/support/rate-us",
        linkIcon: (
          <Svg width="18" height="18" viewBox="0 0 18 18" fill="none">
            <G clipPath="url(#clip0_591_4454)">
              <Path
                d="M11.2668 7.92178C11.2668 7.64221 11.0401 7.41553 10.7605 7.41553H5.1918C4.91223 7.41553 4.68555 7.64221 4.68555 7.92178C4.68555 8.20134 4.91223 8.42803 5.1918 8.42803H10.7605C11.0401 8.42803 11.2668 8.20134 11.2668 7.92178Z"
                fill="#333333"
              />
              <Path
                d="M5.19082 9.97217C4.91126 9.97217 4.68457 10.1989 4.68457 10.4784C4.68457 10.758 4.91126 10.9847 5.19082 10.9847H8.14338C8.42295 10.9847 8.64963 10.758 8.64963 10.4784C8.64963 10.1989 8.42295 9.97217 8.14338 9.97217H5.19082Z"
                fill="#333333"
              />
              <Path
                d="M14.6029 7.95012C14.3233 7.95012 14.0966 8.17681 14.0966 8.45637V12.5508C14.0966 13.3169 13.4734 13.9402 12.7073 13.9402H9.32101C9.21133 13.9402 9.10445 13.9756 9.0167 14.042L6.09283 16.2408C6.01858 16.2965 5.94883 16.2734 5.91395 16.2543C5.87851 16.2363 5.82001 16.1919 5.82227 16.1013L5.87851 14.4644C5.88301 14.3272 5.83183 14.1939 5.73676 14.0954C5.6417 13.9964 5.51008 13.9407 5.37283 13.9407H3.02102C2.25489 13.9407 1.63164 13.3181 1.63164 12.5514V5.85762C1.63164 5.08868 2.25489 4.46318 3.02102 4.46318H9.25914C9.5387 4.46318 9.76539 4.2365 9.76539 3.95693C9.76539 3.67737 9.5387 3.45068 9.25914 3.45068H3.02102C1.69689 3.45068 0.619141 4.53068 0.619141 5.85818V12.5514C0.619141 13.8755 1.69689 14.9538 3.02102 14.9538H4.84858L4.81033 16.0687C4.79683 16.5266 5.04039 16.9428 5.44708 17.1537C5.62089 17.2443 5.8082 17.2887 5.99383 17.2887C6.24301 17.2887 6.48995 17.2083 6.70033 17.0514L9.48977 14.9538H12.7067C14.0308 14.9538 15.1086 13.8761 15.1086 12.5514V8.45693C15.1091 8.17681 14.8825 7.95012 14.6029 7.95012Z"
                fill="#333333"
              />
              <Path
                opacity="0.7"
                d="M17.3392 3.12801C17.2475 2.84676 17.022 2.63639 16.7357 2.56551L15.4419 2.2432L14.7354 1.11257C14.4221 0.609699 13.6042 0.610824 13.2915 1.11257L12.585 2.2432L11.2912 2.56551C11.0049 2.63639 10.7788 2.84676 10.6877 3.12801C10.5965 3.40926 10.655 3.71245 10.8452 3.93914L11.7024 4.95951L11.609 6.28926C11.5882 6.58401 11.7187 6.86357 11.9578 7.03739C12.1063 7.14539 12.2807 7.20107 12.4573 7.20107C12.5647 7.20107 12.6733 7.18026 12.7773 7.13807L14.0137 6.63857L15.2495 7.13807C15.5235 7.24945 15.8295 7.2112 16.0691 7.03795C16.3082 6.86414 16.4387 6.58457 16.4184 6.28926L16.325 4.95951L17.1823 3.93857C17.3718 3.71245 17.4309 3.40926 17.3392 3.12801ZM15.4177 4.46507C15.3333 4.56576 15.2912 4.69514 15.3007 4.82676L15.3907 6.10364L14.2038 5.62382C14.1431 5.59907 14.0784 5.5867 14.0143 5.5867C13.9502 5.5867 13.8855 5.59907 13.8247 5.62382L12.6384 6.10364L12.7278 4.82676C12.7368 4.6957 12.6947 4.56576 12.6108 4.46507L11.7879 3.4852L13.0299 3.17526C13.157 3.1432 13.2673 3.06389 13.3365 2.95251L14.0148 1.86745L14.6932 2.95251C14.763 3.06389 14.8727 3.14376 14.9998 3.17526L16.2418 3.4852L15.4177 4.46507Z"
                fill="#333333"
              />
            </G>
            <Defs>
              <ClipPath id="clip0_591_4454">
                <Rect width="18" height="18" fill="white" />
              </ClipPath>
            </Defs>
          </Svg>
        ),
        onPress: async () => {
          await WebBrowser.openBrowserAsync(
            "https://scraplo-seller-web.vercel.app/profile/rate-us",
          );
        },
      },
    ],
  },
];

export const ORDER_DATA: OrderData = {
  orderId: "KW38291",
  location: "Near City Mall, Sector 5",
  distanceAway: "3.2 km away",
  estimatedScrapValue: 1500,
  walletBalance: 2000,
  requiredBalance: 1500,
  expiresInTime: new Date(Date.now() + 360000), // 1 hour from now
  vehicleType: "truck",
};
