import React from "react";
import { SafeAreaView } from "react-native-safe-area-context";
import AntDesign from "@expo/vector-icons/AntDesign";

import { cn } from "@acme/ui/lib/cn";

interface SpinnerLoadingProps {
  className?: string;
}

const SpinnerLoading = ({ className }: SpinnerLoadingProps) => {
  return (
    <SafeAreaView
      className={cn("flex-1 items-center justify-center", className)}
    >
      <AntDesign
        name="loading1"
        size={24}
        color="black"
        className="animate-spin"
      />
    </SafeAreaView>
  );
};

export default SpinnerLoading;
