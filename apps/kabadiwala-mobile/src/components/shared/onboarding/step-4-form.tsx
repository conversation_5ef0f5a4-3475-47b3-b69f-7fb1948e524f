import type { z } from "zod";
import React from "react";
import { Text, View } from "react-native";
import { showMessage } from "react-native-flash-message";
import { router } from "expo-router";
import { Button } from "@/components/ui/button";
import { ControlledDateTimePicker } from "@/components/ui/date-time-picker";
import { ControlledInput } from "@/components/ui/input";
import { trpc } from "@/utils/api";
import AntDesign from "@expo/vector-icons/AntDesign";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import { subYears } from "date-fns";
import { useForm } from "react-hook-form";

import { KabadiwalaOnboardingStepFourSchema } from "@acme/validators/kabadiwala";

type StepFourFormDataType = z.infer<typeof KabadiwalaOnboardingStepFourSchema>;

const StepFourForm = () => {
  const form = useForm<StepFourFormDataType>({
    resolver: zodResolver(KabadiwalaOnboardingStepFourSchema),
    defaultValues: {
      dlNumber: "",
    },
  });

  const { mutate: skipStep4, isPending: isSkipping } = useMutation(
    trpc.onboarding.skipStep4.mutationOptions({
      onSuccess: (otps) => {
        showMessage({
          message: otps.message,
          description: otps.subMessage,
          type: "success",
        });
        router.push(`/(without-layout)/onboarding/${otps.nextStep}`);
      },
      onError: (error) => {
        showMessage({
          message: "Error",
          description: error.message || "Something went wrong",
          type: "danger",
        });
      },
    }),
  );

  const { mutate: step4, isPending } = useMutation(
    trpc.onboarding.step_4.mutationOptions({
      onSuccess: (otps) => {
        showMessage({
          message: otps.message,
          description: otps.subMessage,
          type: "success",
        });
        router.push(`/(without-layout)/onboarding/${otps.nextStep}`);
      },
      onError: (error) => {
        showMessage({
          message: "Error",
          description: error.message || "Something went wrong",
          type: "danger",
        });
      },
    }),
  );

  const onSubmit = (data: StepFourFormDataType) => {
    step4(data);
  };

  const handleSkipPress = () => {
    skipStep4();
  };

  const maxDate = subYears(new Date(), 18);

  return (
    <View className="relative flex-col gap-10">
      <View className="flex flex-col gap-6">
        <ControlledInput
          name="dlNumber"
          label="Driving Licence"
          control={form.control}
          placeholder="eg: AB12CD34"
        />

        <ControlledDateTimePicker
          name="dob"
          label="Date of birth"
          control={form.control}
          maxDate={maxDate}
        />
      </View>

      <View className="flex flex-row items-center gap-3">
        <Button
          className="w-full flex-1"
          variant="outline"
          onPress={handleSkipPress}
          disabled={isSkipping || isPending}
        >
          {isSkipping && (
            <AntDesign
              name="loading1"
              size={16}
              color="black"
              className="mr-2 animate-spin"
            />
          )}
          <Text>Skip</Text>
        </Button>

        <Button
          onPress={form.handleSubmit(onSubmit)}
          className="w-full flex-1"
          disabled={isPending || isSkipping}
          variant={isPending || isSkipping ? "disabled" : "default"}
        >
          {isPending && (
            <AntDesign
              name="loading1"
              size={16}
              color="black"
              className="mr-2 animate-spin"
            />
          )}
          <Text className="mr-2">Save</Text>
          <AntDesign name="arrowright" size={20} color="black" />
        </Button>
      </View>
    </View>
  );
};

export default StepFourForm;
