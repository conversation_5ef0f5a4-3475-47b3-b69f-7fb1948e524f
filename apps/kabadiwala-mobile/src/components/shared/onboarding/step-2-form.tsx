import type { FieldErrors } from "react-hook-form";
import type { z } from "zod";
import React from "react";
import { Text, View } from "react-native";
import { showMessage } from "react-native-flash-message";
import { router } from "expo-router";
import { Button } from "@/components/ui/button";
import { ControlledInput } from "@/components/ui/input";
import { ControlledSelect } from "@/components/ui/select";
import { trpc } from "@/utils/api";
import AntDesign from "@expo/vector-icons/AntDesign";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useForm } from "react-hook-form";

import { KabadiwalaOnboardingStepTwoSchema } from "@acme/validators/kabadiwala";

const VehicleTypeFieldValues =
  KabadiwalaOnboardingStepTwoSchema.shape.vehicleType.options;

const getVehicleTypeOptions = () => {
  return VehicleTypeFieldValues.map((item) => {
    return {
      label: item.split("_").join(" ").toLocaleLowerCase(),
      //   label: item,
      value: item,
    };
  });
};

const StepTwoForm = () => {
  const { data: step2Details, isPending: isFetchingStep2Details } = useQuery(
    trpc.onboarding.getStep_2Details.queryOptions(),
  );

  const form = useForm<z.infer<typeof KabadiwalaOnboardingStepTwoSchema>>({
    resolver: zodResolver(KabadiwalaOnboardingStepTwoSchema),
    defaultValues: {
      vehicleType: step2Details?.vehicle?.vehicleType,
      vehicleNumber: step2Details?.vehicle?.vehicleNumber ?? undefined,
    },
  });

  const queryClient = useQueryClient();
  const { mutate: step2, isPending } = useMutation(
    trpc.onboarding.step_2.mutationOptions({
      onSuccess: async (otps) => {
        await queryClient.invalidateQueries(
          trpc.onboarding.getStep_2Details.queryOptions(),
        );
        showMessage({
          message: otps.message,
          description: otps.subMessage,
          type: "success",
        });
        router.push(`/(without-layout)/onboarding/${otps.nextStep}`);
      },
      onError: (error) => {
        showMessage({
          message: "Error",
          description: error.message || "Something went wrong",
          type: "danger",
        });
      },
    }),
  );

  const onSubmit = (
    data: z.infer<typeof KabadiwalaOnboardingStepTwoSchema>,
  ) => {
    step2(data);
  };

  const onError = (
    error: FieldErrors<z.infer<typeof KabadiwalaOnboardingStepTwoSchema>>,
  ) => {
    console.log(error);
    showMessage({
      message: "Error",
      description: "Please check if all fields are correct.",
      type: "danger",
    });
  };

  React.useEffect(() => {
    if (step2Details) {
      form.reset({
        vehicleType: step2Details.vehicle?.vehicleType,
        vehicleNumber: step2Details.vehicle?.vehicleNumber ?? undefined,
      });
    }
  }, [step2Details, form]);

  return (
    <View className="relative flex-col gap-10">
      {/* Loading Overlay */}
      {isFetchingStep2Details && (
        <View className="absolute inset-0 z-50 flex items-center justify-center rounded-lg bg-gray-500/20">
          <View className="flex items-center justify-center rounded-lg bg-white p-4 shadow-lg">
            <AntDesign
              name="loading1"
              size={24}
              color="black"
              className="animate-spin"
            />
          </View>
        </View>
      )}

      <View className="flex flex-col gap-6">
        <ControlledSelect
          name="vehicleType"
          label="Vehicle Type"
          control={form.control}
          options={getVehicleTypeOptions()}
        />

        <ControlledInput
          name="vehicleNumber"
          label="Your Vehicle Number"
          control={form.control}
          placeholder="eg: AB12CD34"
        />
      </View>

      <Button
        onPress={form.handleSubmit(onSubmit, onError)}
        className="mt-10 w-full"
        disabled={isPending}
        variant={isPending ? "disabled" : "default"}
      >
        {isPending && (
          <AntDesign
            name="loading1"
            size={16}
            color="black"
            className="mr-2 animate-spin"
          />
        )}
        <Text className="mr-2">Next</Text>
        <AntDesign name="arrowright" size={20} color="black" />
      </Button>
    </View>
  );
};

export default StepTwoForm;
