import type { z } from "zod";
import React, { useEffect, useState } from "react";
import { Alert, Pressable, Text, View } from "react-native";
import { showMessage } from "react-native-flash-message";
import { router } from "expo-router";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useModal } from "@/components/ui/modal";
import { trpc } from "@/utils/api";
import { useImageUploader } from "@/utils/uploadthing";
import AntDesign from "@expo/vector-icons/AntDesign";
import Feather from "@expo/vector-icons/Feather";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useForm } from "react-hook-form";

import { cn } from "@acme/ui/lib/cn";
import { KabadiwalaOnboardingStepThreeSchema } from "@acme/validators/kabadiwala";

import ImageModal from "../image-modal";

type StepThreeFormDataType = z.infer<
  typeof KabadiwalaOnboardingStepThreeSchema
>;

const StepThreeForm = () => {
  const [previewImage, setPreviewImage] = useState<{
    url: string;
    title: string;
  } | null>(null);

  const {
    present: presentImagePreview,
    dismiss: dismissImagePreview,
    ref: imagePreviewRef,
  } = useModal();

  const form = useForm<StepThreeFormDataType>({
    resolver: zodResolver(KabadiwalaOnboardingStepThreeSchema),
    defaultValues: {
      frontOfAdharCardImageUrl: "",
      backOfAdharCardImageUrl: "",
      frontOfAdharCardFileKey: "",
      backOfAdharCardFileKey: "",
      adharCardNumber: "",
    },
  });

  const { data: step3Details, isPending: isFetchingStep3Details } = useQuery(
    trpc.onboarding.getStep_3Details.queryOptions(),
  );

  const { mutate: step3, isPending } = useMutation(
    trpc.onboarding.step_3.mutationOptions({
      onSuccess: (otps) => {
        showMessage({
          message: otps.message,
          type: "success",
        });
        router.push(`/(without-layout)/onboarding/${otps.nextStep}`);
      },
      onError: (error) => {
        showMessage({
          message: error.message || "Something went wrong",
          type: "danger",
        });
      },
    }),
  );

  const { mutate: deleteFile, isPending: isDeletingFile } = useMutation(
    trpc.utils.deleteUploadthingFileUsingFileKey.mutationOptions(),
  );

  const {
    openImagePicker: openFrontImagePicker,
    isUploading: isUploadingFrontImage,
  } = useImageUploader("imageUploader", {
    onClientUploadComplete: (res) => {
      const imageUrl = res[0]?.ufsUrl;
      const fileKey = res[0]?.key;

      if (!imageUrl || !fileKey) {
        return;
      }

      form.setValue("frontOfAdharCardImageUrl", imageUrl);
      form.setValue("frontOfAdharCardFileKey", fileKey);
      showMessage({
        message: "Front Aadhar card uploaded successfully!",
        type: "success",
      });
    },
    onUploadError: () => {
      showMessage({
        message: "Front Aadhar card upload failed.",
        type: "danger",
      });
    },
  });

  const {
    openImagePicker: openBackImagePicker,
    isUploading: isUploadingBackImage,
  } = useImageUploader("imageUploader", {
    onClientUploadComplete: (res) => {
      const imageUrl = res[0]?.ufsUrl;
      const fileKey = res[0]?.key;

      if (!imageUrl || !fileKey) {
        return;
      }

      form.setValue("backOfAdharCardImageUrl", imageUrl);
      form.setValue("backOfAdharCardFileKey", fileKey);
      showMessage({
        message: "Back Aadhar card uploaded successfully!",
        type: "success",
      });
    },
    onUploadError: () => {
      showMessage({
        message: "Back Aadhar card upload failed.",
        type: "danger",
      });
    },
  });

  const handleFrontImageUpload = async () => {
    const frontFileKey = form.getValues("frontOfAdharCardFileKey");

    if (frontFileKey) {
      deleteFile({ fileKey: frontFileKey });
    }

    await openFrontImagePicker({
      source: "camera",
      onInsufficientPermissions: () => {
        Alert.alert(
          "No Permissions",
          "You need to grant permission to your Photos to use this",
          [{ text: "Dismiss" }, { text: "Open Settings" }],
        );
      },
    });
  };

  const handleBackImageUpload = async () => {
    const backFileKey = form.getValues("backOfAdharCardFileKey");

    if (backFileKey) {
      deleteFile({ fileKey: backFileKey });
    }

    await openBackImagePicker({
      source: "camera",
      onInsufficientPermissions: () => {
        Alert.alert(
          "No Permissions",
          "You need to grant permission to your Photos to use this",
          [{ text: "Dismiss" }, { text: "Open Settings" }],
        );
      },
    });
  };

  const onSubmit = (data: StepThreeFormDataType) => {
    step3(data);
  };

  useEffect(() => {
    if (step3Details) {
      form.reset({
        frontOfAdharCardImageUrl:
          step3Details.frontOfAdharCardImageUrl ?? undefined,
        backOfAdharCardImageUrl:
          step3Details.backOfAdharCardImageUrl ?? undefined,
        frontOfAdharCardFileKey:
          step3Details.frontOfAdharCardFileKey ?? undefined,
        backOfAdharCardFileKey:
          step3Details.backOfAdharCardFileKey ?? undefined,
        adharCardNumber: step3Details.adharCardNumber ?? undefined,
      });
    }
  }, [step3Details, form]);

  const frontImageUrl = form.watch("frontOfAdharCardImageUrl");
  const backImageUrl = form.watch("backOfAdharCardImageUrl");
  const adharNumber = form.watch("adharCardNumber");

  const frontImageError = form.formState.errors.frontOfAdharCardImageUrl;
  const backImageError = form.formState.errors.backOfAdharCardImageUrl;
  const adharNumberError = form.formState.errors.adharCardNumber;

  // Function to open image preview
  const openImagePreview = (imageUrl: string, title: string) => {
    setPreviewImage({ url: imageUrl, title });
    presentImagePreview();
  };

  const isFormValid = frontImageUrl && backImageUrl && adharNumber;
  const isUploading = isUploadingFrontImage || isUploadingBackImage;

  console.log(frontImageUrl, backImageUrl, adharNumber);

  return (
    <View className="relative flex-col gap-10">
      {/* Loading Overlay */}
      {isFetchingStep3Details && (
        <View className="absolute inset-0 z-50 flex items-center justify-center rounded-lg bg-gray-500/20">
          <View className="flex items-center justify-center rounded-lg bg-white p-4 shadow-lg">
            <AntDesign
              name="loading1"
              size={24}
              color="black"
              className="animate-spin"
            />
          </View>
        </View>
      )}

      <View className="flex flex-col gap-6">
        <DocumentUploadField
          fieldName="frontOfAdharCardImageUrl"
          label="Front of Aadhar Card"
          fileUrl={frontImageUrl}
          error={frontImageError}
          onUpload={handleFrontImageUpload}
          isUploading={isUploadingFrontImage}
          onPreview={openImagePreview}
        />

        <DocumentUploadField
          fieldName="backOfAdharCardImageUrl"
          label="Back of Aadhar Card"
          fileUrl={backImageUrl}
          error={backImageError}
          onUpload={handleBackImageUpload}
          isUploading={isUploadingBackImage}
          onPreview={openImagePreview}
        />

        <View>
          <Text className="font-jakarta-medium text-text-600 mb-2 text-base font-medium">
            Aadhar Card Number
          </Text>
          <Input
            value={adharNumber}
            onChangeText={(text) => form.setValue("adharCardNumber", text)}
            placeholder="Enter your Aadhar card number"
            keyboardType="numeric"
            maxLength={12}
          />
          {adharNumberError && (
            <Text className="font-inter-regular mt-1 text-xs text-red-500">
              {adharNumberError.message}
            </Text>
          )}
        </View>
      </View>

      <Button
        onPress={form.handleSubmit(onSubmit)}
        className="mt-10 w-full"
        disabled={isPending || !isFormValid || isUploading || isDeletingFile}
        variant={
          isPending || !isFormValid || isUploading || isDeletingFile
            ? "disabled"
            : "default"
        }
      >
        {(isPending || isUploading || isDeletingFile) && (
          <AntDesign
            name="loading1"
            size={16}
            color="black"
            className="mr-2 animate-spin"
          />
        )}
        <Text className="mr-2">Save & Continue</Text>
        <AntDesign name="arrowright" size={20} color="black" />
      </Button>

      {/* Image Preview Modal */}
      {previewImage && (
        <ImageModal
          imageUrl={previewImage.url}
          ref={imagePreviewRef}
          dismiss={dismissImagePreview}
          title={previewImage.title}
        />
      )}
    </View>
  );
};

export default StepThreeForm;

interface DocumentFieldProps {
  fieldName: keyof StepThreeFormDataType;
  label: string;
  fileUrl: string | undefined;
  error?: Record<string, unknown>;
  onUpload: () => Promise<void>;
  isUploading: boolean;
  onPreview?: (imageUrl: string, title: string) => void;
}

const DocumentUploadField = ({
  label,
  fileUrl,
  error,
  onUpload,
  isUploading,
  onPreview,
}: DocumentFieldProps) => {
  if (fileUrl) {
    return (
      <View>
        <View className="flex-row items-center justify-between">
          <Text className="font-jakarta-medium text-text-600 mb-2 text-base font-medium">
            {label}
          </Text>
          <Text className="font-inter-semibold flex flex-row items-center gap-1 text-[10px] font-semibold text-teal-750">
            <Feather name="check" size={12} color="#007F80" />
            Uploaded
          </Text>
        </View>

        <Pressable onPress={onUpload} disabled={isUploading}>
          <Text className="rounded-lg border border-black-50 bg-black-0 px-4 py-[14px] text-black-600">
            Tap to change the uploaded document
          </Text>
        </Pressable>

        <Pressable
          onPress={() => onPreview?.(fileUrl, label)}
          className="w-full flex-row items-center"
        >
          <Text className="font-inter-regular mt-1 text-xs text-black-400">
            Tap to preview the uploaded document
          </Text>
        </Pressable>
      </View>
    );
  }

  return (
    <Pressable onPress={onUpload} disabled={isUploading}>
      <Text
        className={cn(
          "font-jakarta-medium text-text-600 mb-2 text-base font-medium",
          error && "text-red-500",
        )}
      >
        {label}
      </Text>
      <View
        className={cn(
          "mb-1.5 flex h-[120px] items-center justify-center rounded-lg border-[1.2px] border-black-100 bg-black-0",
          error && "border-red-500",
        )}
      >
        {isUploading ? (
          <AntDesign
            name="loading1"
            size={24}
            color="black"
            className="animate-spin"
          />
        ) : (
          <Text
            className={cn(
              "font-inter-medium text-black-400",
              error && "text-red-500",
            )}
          >
            Upload Image
          </Text>
        )}
      </View>
      <Text
        className={cn(
          "font-inter-regular text-xs text-black-400",
          error && "text-red-500",
        )}
      >
        {isUploading
          ? "Uploading..."
          : "Make sure the picture and numbers are clear!"}
      </Text>
    </Pressable>
  );
};
