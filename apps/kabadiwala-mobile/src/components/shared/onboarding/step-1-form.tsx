import type { z } from "zod";
import React from "react";
import { Alert, Image, Pressable, Text, View } from "react-native";
import { showMessage } from "react-native-flash-message";
import { router } from "expo-router";
import { Button } from "@/components/ui/button";
import { ControlledInput } from "@/components/ui/input";
import { trpc } from "@/utils/api";
import { useImageUploader } from "@/utils/uploadthing";
import AntDesign from "@expo/vector-icons/AntDesign";
import Feather from "@expo/vector-icons/Feather";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useForm } from "react-hook-form";

import { cn } from "@acme/ui/lib/cn";
import { KabadiwalaOnboardingStepOneSchema } from "@acme/validators/kabadiwala";

const StepOneForm = () => {
  const form = useForm<z.infer<typeof KabadiwalaOnboardingStepOneSchema>>({
    resolver: zodResolver(KabadiwalaOnboardingStepOneSchema),
    defaultValues: {
      imageUrl: "",
      imageFileKey: "",
      fullName: "",
      email: "",
    },
  });

  const imageUrl = form.watch("imageUrl");
  const fileKey = form.watch("imageFileKey");
  const photoUploadError = form.formState.errors.imageUrl?.message;

  const { data: step1Details, isPending: isFetchingStep1Details } = useQuery(
    trpc.onboarding.getStep_1Details.queryOptions(),
  );

  const { mutate: step1, isPending } = useMutation(
    trpc.onboarding.step_1.mutationOptions(),
  );

  const { mutate: deleteFile, isPending: isDeletingFile } = useMutation(
    trpc.utils.deleteUploadthingFileUsingFileKey.mutationOptions(),
  );

  const { openImagePicker, isUploading } = useImageUploader("imageUploader", {
    onClientUploadComplete: (res) => {
      const imageUrl = res[0]?.ufsUrl;
      const imageFileKey = res[0]?.key;

      if (!imageUrl || !imageFileKey) {
        return;
      }

      form.setValue("imageUrl", imageUrl);
      form.setValue("imageFileKey", imageFileKey);
      showMessage({
        message: "Success",
        description: "Photo uploaded successfully!",
        type: "success",
      });
    },
    onUploadError: () =>
      showMessage({
        message: "Error",
        description: "Photo upload failed.",
        type: "danger",
      }),
  });

  const handleImageUpload = async () => {
    if (fileKey) {
      // this means the user has already uploaded an image and now trying to upload a new one so delete the previous image
      deleteFile({ fileKey });
    }

    await openImagePicker({
      source: "camera",
      onInsufficientPermissions: () => {
        Alert.alert(
          "No Permissions",
          "You need to grant permission to your Photos to use this",
          [{ text: "Dismiss" }, { text: "Open Settings" }],
        );
      },
    });
  };

  const onSubmit = (
    data: z.infer<typeof KabadiwalaOnboardingStepOneSchema>,
  ) => {
    step1(data, {
      onSuccess: (opts) => {
        showMessage({
          message: opts.message,
          description: opts.subMessage,
          type: "success",
        });
        router.push({
          pathname: "/(without-layout)/onboarding/[step]",
          params: {
            fullName: data.fullName,
            step: opts.nextStep,
          },
        });
      },
      onError: (error) => {
        showMessage({
          message: "Error",
          description: error.message,
          type: "danger",
        });
      },
    });
  };

  React.useEffect(() => {
    if (step1Details) {
      form.reset({
        fullName: step1Details.fullName,
        email: step1Details.email,
        imageUrl: step1Details.imageUrl ?? undefined,
        imageFileKey: step1Details.imageKey ?? undefined,
      });
    }
  }, [form, step1Details]);

  return (
    <View className="relative flex-col gap-10">
      {/* Loading Overlay */}
      {isFetchingStep1Details && (
        <View className="absolute inset-0 z-50 flex items-center justify-center rounded-lg bg-gray-500/20">
          <View className="flex items-center justify-center rounded-lg bg-white p-4 shadow-lg">
            <AntDesign
              name="loading1"
              size={24}
              color="black"
              className="animate-spin"
            />
          </View>
        </View>
      )}

      <View className="flex flex-col gap-6">
        <Pressable onPress={handleImageUpload}>
          <View className="flex flex-row gap-4">
            <View
              className={cn(
                "flex h-[70px] w-[70px] items-center justify-center rounded-full border border-dashed border-black-250 bg-black-0",
                photoUploadError && "border-red-500",
              )}
            >
              {imageUrl ? (
                <Image
                  source={{ uri: imageUrl }}
                  style={{ width: 70, height: 70, borderRadius: 35 }}
                />
              ) : (
                <Feather
                  name="user"
                  size={24}
                  color={photoUploadError ? "red" : "#666666"}
                />
              )}
            </View>
            <View className="flex flex-1 flex-col gap-[10px]">
              <Text
                className={cn(
                  "font-jakarta-medium text-[13px] font-medium leading-[18px] text-black-800",
                  photoUploadError && "text-red-500",
                )}
              >
                {isUploading || isDeletingFile
                  ? "Uploading..."
                  : "Upload Your Photo"}
              </Text>
              <Text
                className={cn(
                  "font-inter-regular text-[11px] leading-[18px] text-black-400",
                  photoUploadError && "text-red-500",
                )}
              >
                Please upload your recent photo to verify it with your document
              </Text>
            </View>
          </View>
        </Pressable>

        <ControlledInput
          name="fullName"
          label="Your Full Name"
          control={form.control}
          placeholder="eg: Anil Kumar"
        />

        <ControlledInput
          name="email"
          label="Your Email (optional)"
          control={form.control}
          placeholder="eg: <EMAIL>"
        />
      </View>

      <Button
        onPress={form.handleSubmit(onSubmit)}
        className="mt-10 w-full"
        disabled={isPending}
        variant={isPending ? "disabled" : "default"}
      >
        {isPending && (
          <AntDesign
            name="loading1"
            size={16}
            color="black"
            className="mr-2 animate-spin"
          />
        )}
        <Text className="mr-2">Next</Text>
        <AntDesign name="arrowright" size={20} color="black" />
      </Button>
    </View>
  );
};

export default StepOneForm;
