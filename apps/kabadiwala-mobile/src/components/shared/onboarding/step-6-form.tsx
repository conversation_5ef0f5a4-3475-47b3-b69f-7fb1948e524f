import type { AutocompleteInputRef } from "@/components/shared/google/autocomplete-input";
import type { DetectedLocation } from "@/types/types";
import type { z } from "zod";
import React, { useRef } from "react";
import { ActivityIndicator, Text, View } from "react-native";
import { showMessage } from "react-native-flash-message";
import { router } from "expo-router";
import AutocompleteInput from "@/components/shared/google/autocomplete-input";
import { Button } from "@/components/ui/button";
import { trpc } from "@/utils/api";
import AntDesign from "@expo/vector-icons/AntDesign";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useForm } from "react-hook-form";

import { KabadiwalaOnboardingStepSixSchema } from "@acme/validators/kabadiwala";

const StepSixForm = () => {
  const form = useForm<z.infer<typeof KabadiwalaOnboardingStepSixSchema>>({
    resolver: zodResolver(KabadiwalaOnboardingStepSixSchema),
    defaultValues: {},
  });

  const autocompleteRef = useRef<AutocompleteInputRef>(null);

  const { data: step6Details, isPending: isFetchingStep6Details } = useQuery(
    trpc.onboarding.getStep_6Details.queryOptions(),
  );

  const queryClient = useQueryClient();
  const { mutate: step6, isPending } = useMutation(
    trpc.onboarding.step_6.mutationOptions({
      onSuccess: async () => {
        await queryClient.invalidateQueries(
          trpc.onboarding.getOnboardingAndActiveAccDetails.queryOptions(),
        );
      },
    }),
  );

  React.useEffect(() => {
    if (step6Details?.address) {
      form.setValue("address", {
        name: step6Details.address.name,
        coordinates: {
          latitude: Number(step6Details.address.coordinates?.latitude),
          longitude: Number(step6Details.address.coordinates?.longitude),
        },
        addressType: step6Details.address.addressType ?? "OTHER",
        street: step6Details.address.street ?? undefined,
        city: step6Details.address.city ?? undefined,
        state: step6Details.address.state ?? undefined,
        country: step6Details.address.country ?? undefined,
        postalCode: step6Details.address.postalCode ?? undefined,
        localAddress: step6Details.address.localAddress,
        display: step6Details.address.display,
        landmark: step6Details.address.landmark,
      });

      if (autocompleteRef.current && step6Details.address.display) {
        autocompleteRef.current.setValue(step6Details.address.display);
      }
    }
  }, [form, step6Details]);

  const handleLocationSelect = async (location: DetectedLocation) => {
    form.setValue("address", {
      name: "Selected Location",
      street: location.address?.street ?? "",
      city: location.address?.city ?? "",
      state: location.address?.state ?? "",
      country: location.address?.country ?? "",
      postalCode: location.address?.postalCode ?? "",
      coordinates: {
        latitude: location.latitude,
        longitude: location.longitude,
      },
      addressType: "OTHER",
      localAddress: location.address?.display ?? "",
      display: location.address?.display ?? "",
      landmark: location.address?.display ?? "",
    });

    // This triggers validation after setting the value
    await form.trigger("address");
    console.log("Selected location details:", location);
  };

  const onSubmit = (
    data: z.infer<typeof KabadiwalaOnboardingStepSixSchema>,
  ) => {
    step6(data, {
      onSuccess: (opts) => {
        showMessage({
          message: "Onboarding completed successfully",
          description: "You can now start using the app.",
          type: "success",
        });
        router.push("/(without-layout)/onboarding/success");
      },
      onError: (error) => {
        showMessage({
          message: error.message || "Something went wrong",
          type: "danger",
        });
      },
    });
  };

  const addressError = form.formState.errors.address?.display?.message;

  return (
    <View className="relative flex-col gap-10">
      {/* Loading Overlay */}
      {isFetchingStep6Details && (
        <View className="absolute inset-0 z-50 flex items-center justify-center rounded-lg bg-gray-500/20">
          <View className="flex items-center justify-center rounded-lg bg-white p-4 shadow-lg">
            <AntDesign
              name="loading1"
              size={24}
              color="black"
              className="animate-spin"
            />
          </View>
        </View>
      )}

      <View>
        <AutocompleteInput
          ref={autocompleteRef}
          label="Address"
          placeholder="eg: Gurgaon"
          onLocationSelect={handleLocationSelect}
          onCurrentLocationSelect={handleLocationSelect}
          showAutoDetect={true}
          autoDetectText="Autodetect my location"
          error={addressError}
          maxSuggestions={3}
        />
      </View>

      <Button
        className="rounded-lg py-3"
        onPress={form.handleSubmit(onSubmit)}
        disabled={isPending}
        variant={isPending ? "disabled" : "default"}
      >
        {isPending && (
          <ActivityIndicator size="small" color="#fff" className="mr-2" />
        )}
        <Text className="text-center font-semibold text-white">Finish</Text>
      </Button>
    </View>
  );
};

export default StepSixForm;
