import { Text, View } from "react-native";

interface FormTitleDescriptionProps {
  title: string;
  description: string;
  fullName?: string;
}

const FormTitleDescription = ({
  title,
  description,
  fullName,
}: FormTitleDescriptionProps) => {
  return (
    <View className="flex flex-col gap-3">
      <Text className="font-jakarta-medium leading-5 -tracking-[0.105px] text-black-700">
        {fullName}
      </Text>
      <Text className="font-jakarta-bold text-[22px] font-bold leading-[34px] -tracking-[0.165px] text-teal-950">
        {title}
      </Text>
      <Text className="font-inter-regular text-[15px] leading-6 text-black-600">
        {description}
      </Text>
    </View>
  );
};

export default FormTitleDescription;
