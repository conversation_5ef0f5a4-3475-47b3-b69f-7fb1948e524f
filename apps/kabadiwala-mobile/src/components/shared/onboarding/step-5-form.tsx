import type { z } from "zod";
import React, { useEffect, useState } from "react";
import { Alert, Pressable, Text, View } from "react-native";
import { showMessage } from "react-native-flash-message";
import { router } from "expo-router";
import { Button } from "@/components/ui/button";
import { useModal } from "@/components/ui/modal";
import { trpc } from "@/utils/api";
import { useImageUploader } from "@/utils/uploadthing";
import AntDesign from "@expo/vector-icons/AntDesign";
import Feather from "@expo/vector-icons/Feather";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useForm } from "react-hook-form";

import { cn } from "@acme/ui/lib/cn";
import { KabadiwalaOnboardingStepFiveSchema } from "@acme/validators/kabadiwala";

import ImageModal from "../image-modal";

type StepFiveFormDataType = z.infer<typeof KabadiwalaOnboardingStepFiveSchema>;

const StepFiveForm = () => {
  const [previewImage, setPreviewImage] = useState<{
    url: string;
    title: string;
  } | null>(null);

  const {
    present: presentImagePreview,
    dismiss: dismissImagePreview,
    ref: imagePreviewRef,
  } = useModal();

  const form = useForm<StepFiveFormDataType>({
    resolver: zodResolver(KabadiwalaOnboardingStepFiveSchema),
    defaultValues: {
      policeVerificationDocumentUrl: "",
      policeVerificationDocumentFileKey: "",
    },
  });

  // Fetch existing data
  const { data: step5Details, isPending: isFetchingStep4Details } = useQuery(
    trpc.onboarding.getStep_5Details.queryOptions(),
  );

  // Submit mutation
  const { mutate: step5, isPending } = useMutation(
    trpc.onboarding.step_5.mutationOptions({
      onSuccess: (otps) => {
        showMessage({
          message: otps.message,
          description: otps.subMessage,
          type: "success",
        });
        router.push(`/(without-layout)/onboarding/${otps.nextStep}`);
      },
      onError: (error) => {
        showMessage({
          message: error.message || "Something went wrong",
          type: "danger",
        });
      },
    }),
  );

  const { mutate: deleteFile, isPending: isDeletingFile } = useMutation(
    trpc.utils.deleteUploadthingFileUsingFileKey.mutationOptions(),
  );

  const {
    openImagePicker: openDocumentPicker,
    isUploading: isUploadingDocument,
  } = useImageUploader("imageUploader", {
    onClientUploadComplete: (res) => {
      const imageUrl = res[0]?.ufsUrl;
      const fileKey = res[0]?.key;

      if (!imageUrl || !fileKey) {
        return;
      }

      form.setValue("policeVerificationDocumentUrl", imageUrl);
      form.setValue("policeVerificationDocumentFileKey", fileKey);
      showMessage({
        message: "Document uploaded successfully",
        description: "You can now proceed to the next step.",
        type: "success",
      });
    },
    onUploadError: () => {
      showMessage({
        message: "Upload failed",
        description: "Please try again.",
        type: "danger",
      });
    },
  });

  const handleDocumentUpload = async () => {
    const documentFileKey = form.getValues("policeVerificationDocumentFileKey");

    if (documentFileKey) {
      // Delete previous document first
      deleteFile({ fileKey: documentFileKey });
    }

    await openDocumentPicker({
      source: "library",
      onInsufficientPermissions: () => {
        Alert.alert(
          "No Permissions",
          "You need to grant permission to your Photos to use this",
          [{ text: "Dismiss" }, { text: "Open Settings" }],
        );
      },
    });
  };

  const onSubmit = (data: StepFiveFormDataType) => {
    step5(data);
  };

  useEffect(() => {
    if (step5Details) {
      if (step5Details.policeVerificationDocumentUrl) {
        form.reset({
          policeVerificationDocumentUrl:
            step5Details.policeVerificationDocumentUrl,
          policeVerificationDocumentFileKey:
            step5Details.policeVerificationDocumentFileKey ?? undefined,
        });
      }
    }
  }, [step5Details, form]);

  const documentUrl = form.watch("policeVerificationDocumentUrl");
  const documentError = form.formState.errors.policeVerificationDocumentUrl;

  // Function to open image preview
  const openImagePreview = (imageUrl: string, title: string) => {
    setPreviewImage({ url: imageUrl, title });
    presentImagePreview();
  };

  return (
    <View className="relative flex-col gap-10">
      {/* Loading Overlay */}
      {isFetchingStep4Details && (
        <View className="absolute inset-0 z-50 flex items-center justify-center rounded-lg bg-gray-500/20">
          <View className="flex items-center justify-center rounded-lg bg-white p-4 shadow-lg">
            <AntDesign
              name="loading1"
              size={24}
              color="black"
              className="animate-spin"
            />
          </View>
        </View>
      )}

      <View className="flex flex-col gap-6">
        <DocumentUploadField
          fieldName="policeVerificationDocumentUrl"
          label="Police Verification Document"
          fileUrl={documentUrl}
          error={documentError}
          onUpload={handleDocumentUpload}
          isUploading={isUploadingDocument}
          onPreview={openImagePreview}
        />
      </View>

      <Button
        onPress={form.handleSubmit(onSubmit)}
        className="mt-10 w-full"
        disabled={
          isPending || !documentUrl || isUploadingDocument || isDeletingFile
        }
        variant={
          isPending || !documentUrl || isUploadingDocument || isDeletingFile
            ? "disabled"
            : "default"
        }
      >
        {(isPending || isUploadingDocument || isDeletingFile) && (
          <AntDesign
            name="loading1"
            size={16}
            color="black"
            className="mr-2 animate-spin"
          />
        )}
        <Text className="mr-2">{documentUrl ? "Send for Check" : "Save"}</Text>
        <AntDesign name="arrowright" size={20} color="black" />
      </Button>

      {/* Image Preview Modal */}
      {previewImage && (
        <ImageModal
          imageUrl={previewImage.url}
          ref={imagePreviewRef}
          dismiss={dismissImagePreview}
          title={previewImage.title}
        />
      )}
    </View>
  );
};

export default StepFiveForm;

interface DocumentFieldProps {
  fieldName: keyof StepFiveFormDataType;
  label: string;
  fileUrl: string | undefined;
  error?: Record<string, unknown>;
  onUpload: () => Promise<void>;
  isUploading: boolean;
  onPreview?: (imageUrl: string, title: string) => void;
}

const DocumentUploadField = ({
  label,
  fileUrl,
  error,
  onUpload,
  isUploading,
  onPreview,
}: DocumentFieldProps) => {
  if (fileUrl) {
    return (
      <View>
        <View className="flex-row items-center justify-between">
          <Text className="font-jakarta-medium text-text-600 mb-2 text-base font-medium">
            {label}
          </Text>
          <Text className="font-inter-semibold flex flex-row items-center gap-1 text-[10px] font-semibold text-teal-750">
            <Feather name="check" size={12} color="#007F80" />
            Uploaded
          </Text>
        </View>

        <Pressable onPress={onUpload} disabled={isUploading}>
          <Text className="rounded-lg border border-black-50 bg-black-0 px-4 py-[14px] text-black-600">
            Tap to change the uploaded document
          </Text>
        </Pressable>

        <Pressable
          onPress={() => onPreview?.(fileUrl, label)}
          className="w-full flex-row items-center"
        >
          <Text className="font-inter-regular mt-1 text-xs text-black-400">
            Tap to preview the uploaded document
          </Text>
        </Pressable>
      </View>
    );
  }

  return (
    <Pressable onPress={onUpload} disabled={isUploading}>
      <Text
        className={cn(
          "font-jakarta-medium text-text-600 mb-2 text-base font-medium",
          error && "text-red-500",
        )}
      >
        {label}
      </Text>
      <View
        className={cn(
          "mb-1.5 flex h-[120px] items-center justify-center rounded-lg border-[1.2px] border-black-100 bg-black-0",
          error && "border-red-500",
        )}
      >
        {isUploading ? (
          <AntDesign
            name="loading1"
            size={24}
            color="black"
            className="animate-spin"
          />
        ) : (
          <Text
            className={cn(
              "font-inter-medium text-black-400",
              error && "text-red-500",
            )}
          >
            Upload Image
          </Text>
        )}
      </View>
      <Text
        className={cn(
          "font-inter-regular text-xs text-black-400",
          error && "text-red-500",
        )}
      >
        {isUploading
          ? "Uploading..."
          : "Make sure the picture and numbers are clear!"}
      </Text>
    </Pressable>
  );
};
