import type { VariantProps } from "tailwind-variants";
import React from "react";
import { Text, View } from "react-native";
import { tv } from "tailwind-variants";

// Define the tag variants using tailwind-variants
const tag = tv({
  slots: {
    container:
      "border flex items-center justify-center px-2 py-0.75 rounded-full",
    text: "text-center font-inter-medium text-[8px] font-medium leading-3",
  },
  variants: {
    variant: {
      warning: {
        container: "border-[#C8A504]",
        text: "text-[#322901]",
      },
      success: {
        container: "border-[#008000]",
        text: "text-[#004000]",
      },
      info: {
        container: "border-[#007AFF]",
        text: "text-[#003C7D]",
      },
      error: {
        container: "border-[#FF0000]",
        text: "text-[#7D0000]",
      },
    },
    size: {
      default: {
        container: "px-2 py-0.75",
        text: "text-[8px]",
      },
      sm: {
        container: "px-1.5 py-0.5",
        text: "text-[6px]",
      },
      lg: {
        container: "px-3 py-1",
        text: "text-[10px]",
      },
    },
  },
  defaultVariants: {
    variant: "info",
    size: "default",
  },
});

export interface TagProps extends VariantProps<typeof tag> {
  /**
   * Text to display in the tag
   */
  text: string;

  /**
   * Additional className for container
   */
  className?: string;

  /**
   * Additional className for text
   */
  textClassName?: string;
}

/**
 * A simple tag component that can be used to highlight information
 */
const Tag = React.forwardRef<View, TagProps>(
  (
    {
      text,
      variant = "info",
      size = "default",
      className = "",
      textClassName = "",
      ...props
    },
    ref,
  ) => {
    const styles = React.useMemo(() => tag({ variant, size }), [variant, size]);

    return (
      <View ref={ref} className={styles.container({ className })} {...props}>
        <Text className={styles.text({ className: textClassName })}>
          {text}
        </Text>
      </View>
    );
  },
);

Tag.displayName = "Tag";

export default Tag;
