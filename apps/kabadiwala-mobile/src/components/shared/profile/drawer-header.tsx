import { Text, View } from "react-native";
import { Image } from "expo-image";
import useOnDuty from "@/hooks/use-on-duty";
import useProfile from "@/hooks/use-profile";
import { getVehicleIcon } from "@/utils/functions";
import UserIcon from "@assets/icons/user.png";
import { AntDesign } from "@expo/vector-icons";

import DutySwitch from "../duty-switch";
import Error from "../error";
import SkeletonLoading from "../skeleton-loading";

const DrawerHeader = () => {
  const { data, isPending, isError } = useProfile();

  const { isOnDuty } = useOnDuty();

  if (isPending) {
    return (
      <View className="px-4">
        <SkeletonLoading type="default" />
      </View>
    );
  }

  if (isError) {
    return (
      <View className="px-4">
        <Error title="Failed to load profile" message="Try again later." />
      </View>
    );
  }

  return (
    <View className="gap-3">
      <View className="gap-3 px-6">
        <View className="flex-row items-center gap-2">
          <View className="aspect-square min-w-[44px] overflow-hidden rounded-full border-2 border-yellow-500">
            {data.image ? (
              <Image
                source={data.image}
                style={{
                  width: 44,
                  height: 44,
                }}
                contentFit="cover"
              />
            ) : (
              <Image
                source={UserIcon}
                style={{
                  width: 44,
                  height: 44,
                }}
                contentFit="cover"
              />
            )}
          </View>
          <View className="flex-1">
            <Text className="font-jakarta text-base font-semibold">
              {data.name}
            </Text>
            <View className="flex-row items-center justify-between">
              <Text className="text-xs font-medium text-black-600">
                {data.phoneNumber}
              </Text>
              <View className="h-fit w-fit flex-row items-center justify-center gap-1 rounded-3xl bg-yellow-900 px-[6px] py-[3px]">
                <AntDesign name="star" size={8} color="yellow" />
                <Text className="font-inter-regular text-[10px] font-semibold leading-[10px] tracking-[0.1px] text-white">
                  {Number(data.averageRating ?? 0).toFixed(1)}
                </Text>
              </View>
            </View>
          </View>
        </View>
        <View className="flex-row gap-3">
          {data.vehicles.map((vehicle) => (
            <View
              className="flex-row items-center gap-1 rounded-3xl bg-black-0 px-3 py-1 text-xs font-medium text-black-800"
              key={vehicle.id}
            >
              {getVehicleIcon({
                vehicleType: vehicle.vehicleType,
                iconSize: 12,
              })}
              <Text className="text-xs font-medium text-black-800">
                {vehicle.vehicleType.toLowerCase().replaceAll("_", " ")}
              </Text>
            </View>
          ))}
        </View>
      </View>
      <View className="flex-1 flex-row justify-between bg-[#006033] px-6 py-3">
        {isOnDuty ? (
          <Text className="text-white">You are on duty</Text>
        ) : (
          <Text className="text-white">You are off duty</Text>
        )}
        <DutySwitch />
      </View>
    </View>
  );
};
export default DrawerHeader;
