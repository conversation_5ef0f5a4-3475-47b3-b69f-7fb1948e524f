import type { VariantProps } from "tailwind-variants";
import React from "react";
import { Pressable, Text, View } from "react-native";
import { AntDesign } from "@expo/vector-icons";
import { tv } from "tailwind-variants";

// Define the section item variants using tailwind-variants
const sectionItem = tv({
  slots: {
    container:
      "flex-row items-center self-stretch px-4 py-2.5 rounded-lg gap-3",
    iconContainer: "w-4.5 h-4.5 items-start justify-center",
    title:
      "font-inter-medium text-[13px] font-medium leading-[18px] tracking-[0.26px] flex-1 flex-shrink px-2",
    arrow: "w-4 h-4 items-center justify-center",
  },
  variants: {
    variant: {
      normal: {
        container: "bg-transparent",
        title: "text-black-900",
      },
      dangerous: {
        container: "bg-[#FFF9F9]",
        title: "text-[#900B09]",
      },
      custom: {
        container: "bg-white",
        title: "text-black-900",
      },
    },
    size: {
      default: {
        container: "py-2.5",
      },
      sm: {
        container: "py-2",
        title: "text-xs",
      },
      lg: {
        container: "py-3",
      },
    },
    pressed: {
      true: {
        container: "bg-teal-600",
      },
    },
  },
  defaultVariants: {
    variant: "normal",
    size: "default",
    pressed: false,
  },
});

export type SectionItemVariant = "normal" | "dangerous" | "custom";

export interface SectionItemProps extends VariantProps<typeof sectionItem> {
  title: string;
  icon?: React.ReactNode;
  iconColor?: string;
  showArrow?: boolean;
  onPress?: () => void;
  children?: React.ReactNode;
  className?: string;
  textClassName?: string;
}

const SectionItem = React.forwardRef<View, SectionItemProps>(
  (
    {
      title,
      icon,
      variant = "normal",
      size = "default",
      showArrow = true,
      onPress,
      children,
      className = "",
      textClassName = "",
    },
    ref,
  ) => {
    const [pressed, setPressed] = React.useState(false);

    const styles = React.useMemo(
      () => sectionItem({ variant, size, pressed }),
      [variant, size, pressed],
    );

    return (
      <Pressable
        ref={ref}
        onPress={onPress}
        onPressIn={() => setPressed(true)}
        onPressOut={() => setPressed(false)}
        className={styles.container({ className })}
      >
        {/* Icon */}
        <View className={styles.iconContainer()}>{icon}</View>

        {/* Title */}
        <Text className={styles.title({ className: textClassName })}>
          {title}
        </Text>

        {/* Custom content */}
        {children}

        {/* Arrow */}
        {showArrow && <AntDesign name="right" size={16} color="#999999" />}
      </Pressable>
    );
  },
);

SectionItem.displayName = "SectionItem";

export default SectionItem;
