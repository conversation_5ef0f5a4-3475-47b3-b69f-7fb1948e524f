import type { VariantProps } from "tailwind-variants";
import React from "react";
import { Text, View } from "react-native";
import { tv } from "tailwind-variants";

// Define the section group variants using tailwind-variants
const sectionGroup = tv({
  slots: {
    container: "bg-black-0 px-2 py-3 gap-[10px]  rounded-lg",
    title:
      "text-[11px] leading-[14px] font-medium font-jakarta-medium   mb-2 flex-1  text-black-500",
  },
  variants: {
    spacing: {
      default: {
        container: "mb-4",
      },
      compact: {
        container: "mb-2",
        title: "mb-1",
      },
      loose: {
        container: "mb-6",
        title: "mb-3",
      },
    },
  },
  defaultVariants: {
    spacing: "default",
  },
});

export interface SectionGroupProps extends VariantProps<typeof sectionGroup> {
  /**
   * Title of the section to display as a header
   */
  title?: string;

  /**
   * Child elements to render within the section group
   */
  children: React.ReactNode;

  /**
   * Additional className for container
   */
  className?: string;

  /**
   * Additional className for title
   */
  titleClassName?: string;

  /**
   * Additional className for content
   */
  contentClassName?: string;
}

/**
 * A component to group section items together with an optional header
 */
const SectionGroup = React.forwardRef<View, SectionGroupProps>(
  (
    {
      title,
      children,
      spacing = "default",
      className = "",
      titleClassName = "",
      contentClassName = "",
      ...props
    },
    ref,
  ) => {
    const styles = React.useMemo(() => sectionGroup({ spacing }), [spacing]);

    return (
      <View ref={ref} className={styles.container({ className })} {...props}>
        {title && (
          <Text className={styles.title({ className: titleClassName })}>
            {title}
          </Text>
        )}
        {children}
      </View>
    );
  },
);

SectionGroup.displayName = "SectionGroup";

export default SectionGroup;
