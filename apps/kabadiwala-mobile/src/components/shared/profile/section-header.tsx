import type { NativeStackHeaderProps } from "@react-navigation/native-stack";
import { Pressable, Text, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { router } from "expo-router";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import { DrawerActions } from "@react-navigation/native";

const SectionHeader = (props: { props: NativeStackHeaderProps }) => {
  const { options, navigation } = props.props;

  return (
    <SafeAreaView className="-pb-safe-offset-16 flex-row items-center justify-between bg-white px-4">
      <View className="flex-row items-center gap-2">
        <Pressable
          onPress={() => navigation.dispatch(DrawerActions.toggleDrawer())}
          className="rounded-lg bg-black-50 p-2.5"
        >
          <MaterialIcons name="menu-open" size={20} color="black" />
        </Pressable>
        <Text className="font-jakarta text-lg font-bold text-teal-800">
          {options.title}
        </Text>
      </View>
      <Text
        onPress={() => router.push("/support/help")}
        className="text-cs rounded-lg bg-[#0033330A] p-2 text-teal-900"
      >
        Help
      </Text>
    </SafeAreaView>
  );
};
export default SectionHeader;
