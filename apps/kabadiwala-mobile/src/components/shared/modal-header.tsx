import React from "react";
import { Text, View } from "react-native";
import { But<PERSON> } from "@/components/ui/button";
import { Feather, Ionicons } from "@expo/vector-icons";

import { cn } from "@acme/ui/lib/cn";

interface ModalHeaderProps {
  onDismiss?: () => void;
  title?: string;
  subTitle?: string;
  containerClassName?: string;
}

export const ModalHeader = ({
  onDismiss,
  title,
  subTitle,
  containerClassName,
}: ModalHeaderProps) => {
  return (
    <View
      className={cn(
        `w-full flex-row items-center justify-between border-b border-black-50 px-4 pb-4 pt-8`,
        containerClassName,
      )}
    >
      <View className="flex-row items-center gap-3">
        <Button className="h-fit w-fit min-w-fit max-w-fit bg-yellow-400 p-2">
          <Ionicons name="chevron-up" size={24} color="black" />
        </Button>
        <View>
          <Text className="font-jakarta-bold text-teal-850">{title}</Text>
          <Text className="font-inter-medium text-xs text-black-500">
            {subTitle}
          </Text>
        </View>
      </View>

      {onDismiss && (
        <Button
          className="h-fit flex-shrink-0 shrink-0 rounded-full bg-black-50 p-[6px]"
          onPress={onDismiss}
        >
          <Feather name="x" size={20} color="black" />
        </Button>
      )}
    </View>
  );
};
