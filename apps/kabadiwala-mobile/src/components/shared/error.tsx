import React from "react";
import { Text, TouchableOpacity, View } from "react-native";
import { Ionicons } from "@expo/vector-icons";

import { cn } from "@acme/ui/cn";

interface ErrorProps {
  title?: string;
  message?: string;
  onRetry?: () => void;
  containerStyle?: string;
}

const Error: React.FC<ErrorProps> = ({
  title = "Something went wrong",
  message = "An unexpected error occurred. Please try again later.",
  onRetry,
  containerStyle,
}) => {
  return (
    <View className={cn("flex-1", containerStyle)}>
      <View className="w-full max-w-md rounded-xl bg-red-50 p-6 shadow-sm">
        <View className="mb-4 items-center">
          <View className="mb-2 h-16 w-16 items-center justify-center rounded-full bg-red-100">
            <Ionicons name="alert-circle" size={40} color="#EF4444" />
          </View>
        </View>

        <Text className="mb-2 text-center text-lg font-bold text-red-600">
          {title}
        </Text>

        <Text className="mb-6 text-center text-gray-700">{message}</Text>

        {onRetry && (
          <TouchableOpacity
            onPress={onRetry}
            className="rounded-lg bg-red-600 py-3"
            activeOpacity={0.8}
          >
            <Text className="text-center font-medium text-white">
              Try Again
            </Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

export default Error;
