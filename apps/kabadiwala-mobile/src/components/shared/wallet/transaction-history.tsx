import React from "react";
import { FlatList, Text, View } from "react-native";
import { trpc } from "@/utils/api";
import { useInfiniteQuery } from "@tanstack/react-query";

import type {
  kabadiwalaTransactionForEnum,
  kabadiwalaTransactionTypeEnum,
} from "@acme/db/schema";
import { cn } from "@acme/ui/lib/cn";

import Error from "../error";
import SkeletonLoading from "../skeleton-loading";

const WalletTransactionHistory = () => {
  const {
    data,
    isPending,
    isError,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useInfiniteQuery({
    ...trpc.wallet.getTransactionHistory.infiniteQueryOptions({
      limit: 10,
    }),
    initialPageParam: null,
    getNextPageParam: (lastPage) => lastPage.nextCursor ?? null,
  });

  const allTransactions = data?.pages
    ? data.pages.flatMap((page) => page.history)
    : [];

  const handleLoadMore = async () => {
    if (hasNextPage && !isFetchingNextPage) {
      await fetchNextPage();
    }
  };

  const renderTransaction = ({
    item,
    index,
  }: {
    item: (typeof allTransactions)[number];
    index: number;
  }) => (
    <View key={item.id}>
      <WalletTransactionCard
        orderId={item.id}
        amount={Number(item.amount)}
        variant={item.transactionFor}
        orderDate={item.createdAt}
        transactionType={item.transactionType}
      />
      {index < allTransactions.length - 1 && (
        <View className="h-[1px] w-full bg-black-100" />
      )}
    </View>
  );

  const renderFooter = () => {
    if (isFetchingNextPage) {
      return (
        <View className="py-4">
          <SkeletonLoading type="details" />
        </View>
      );
    }
    return null;
  };

  if (isPending) {
    return (
      <View className="mt-5 flex-1">
        <SkeletonLoading type="details" />
      </View>
    );
  }

  if (isError) {
    return (
      <View className="mt-5 flex-1">
        <Error
          title="Failed to load transaction history"
          message="Please try again later."
          onRetry={handleLoadMore}
        />
      </View>
    );
  }

  return (
    <View className="mt-6 flex-1">
      <FlatList
        data={allTransactions}
        renderItem={renderTransaction}
        keyExtractor={(item) => item.id}
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.5}
        ListFooterComponent={renderFooter}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ flexGrow: 1 }}
        className="flex flex-col gap-2.5"
      />
    </View>
  );
};

export default WalletTransactionHistory;

interface WalletTransactionCardProps {
  orderId: string;
  amount: number;
  orderDate: Date;
  variant: (typeof kabadiwalaTransactionForEnum.enumValues)[number];
  transactionType: (typeof kabadiwalaTransactionTypeEnum.enumValues)[number];
}

const WalletTransactionCard = ({
  orderId,
  amount,
  orderDate,
  variant,
  transactionType,
}: WalletTransactionCardProps) => {
  return (
    <View className="flex flex-row items-center justify-between gap-6 px-3 py-2">
      {/* left */}
      <View className="flex flex-col gap-1">
        <Text className="text-[13px] font-medium leading-[18px] text-teal-900">
          {variant === "ORDER_PAYMENT"
            ? "Order Payment"
            : variant === "WALLET_TOPUP"
              ? "Wallet Top-up"
              : variant === "WALLET_WITHDRAWAL"
                ? "Wallet Withdrawal"
                : variant === "EARNINGS"
                  ? "Earnings"
                  : "Other Transaction"}
        </Text>
        <Text className="text-xs leading-4 text-black-600">Id: {orderId}</Text>
      </View>

      {/* right */}
      <View className="flex flex-col items-end gap-1">
        <Text
          className={cn(
            "w-fit rounded bg-green-100 px-1.5 py-1 text-sm font-semibold leading-5 text-[#038145]",
            transactionType === "DEBIT" && "bg-red-100 text-red-600",
          )}
        >
          {transactionType === "CREDIT" ? "+ " : "- "}₹ {amount}
        </Text>
        <Text className="text-[10px] font-medium leading-[13px] text-black-500">
          {orderDate.toDateString()}
        </Text>
      </View>
    </View>
  );
};
