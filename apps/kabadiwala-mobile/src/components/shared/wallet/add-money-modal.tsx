import type { z } from "zod";
import React, { useEffect } from "react";
import { Text, View } from "react-native";
import { showMessage } from "react-native-flash-message";
import { KeyboardAwareScrollView } from "react-native-keyboard-controller";
import RazorpayCheckout from "react-native-razorpay";
import { Button } from "@/components/ui/button";
import { ControlledInput } from "@/components/ui/input";
import { Env } from "@/lib/env";
import { trpc } from "@/utils/api";
import { AntDesign } from "@expo/vector-icons";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useForm } from "react-hook-form";

import { AddMoneySchema } from "@acme/validators/kabadiwala";

import { ModalHeader } from "../modal-header";

interface AddMoneyModalProps {
  onDismiss: () => void;
}

const AddMoneyModal = ({ onDismiss }: AddMoneyModalProps) => {
  const { data: minimumWalletBalance } = useQuery(
    trpc.wallet.getMinimumWalletBalanceNeededToAddMoney.queryOptions(),
  );

  const form = useForm<z.infer<typeof AddMoneySchema>>({
    resolver: zodResolver(AddMoneySchema),
    defaultValues: {
      amount: minimumWalletBalance
        ? Number(minimumWalletBalance.minimumBalance)
        : 1000,
      currency: "INR",
    },
  });

  const queryClient = useQueryClient();

  const { mutate: createOrder, isPending } = useMutation(
    trpc.payment.createOrder.mutationOptions({
      onSuccess: async (opts) => {
        await handleRazorpayCheckout({
          orderId: opts.orderId,
          name: opts.name,
          amount: opts.amount,
          currency: opts.currency,
          description: opts.description,
          transactionId: opts.transactionId,
        });
      },
    }),
  );

  const { mutate: verifyPayment, isPending: isVerifying } = useMutation(
    trpc.payment.verifyPayment.mutationOptions({
      onSuccess: async (opts) => {
        showMessage({
          message: opts.message,
          type: "success",
        });
        await Promise.allSettled([
          queryClient.invalidateQueries(
            trpc.user.getWalletBalance.queryOptions(),
          ),
          queryClient.invalidateQueries(
            trpc.wallet.getTransactionHistory.queryOptions(),
          ),
        ]);

        form.reset();
        onDismiss();
      },
      onError: (opts) => {
        showMessage({
          message: opts.message,
          type: "danger",
        });
      },
    }),
  );

  const onSubmit = (data: z.infer<typeof AddMoneySchema>) => {
    createOrder(data);
  };

  const handleRazorpayCheckout = async (options: {
    orderId: string;
    name: string;
    amount: number;
    currency: string;
    description: string;
    transactionId: string;
  }) => {
    const res = await RazorpayCheckout.open({
      order_id: options.orderId,
      key: Env.RAZORPAY_KEY_ID,
      name: options.name,
      amount: options.amount,
      currency: options.currency,
      description: options.description,
    });

    verifyPayment({
      transactionId: options.transactionId,
      razorpayPaymentId: res.razorpay_payment_id,
      razorpayOrderId: options.orderId,
      razorpaySignature: res.razorpay_signature,
    });
  };

  useEffect(() => {
    if (minimumWalletBalance) {
      form.reset({
        amount: Number(minimumWalletBalance.minimumBalance),
      });
    }
  }, [minimumWalletBalance, form]);

  return (
    <KeyboardAwareScrollView>
      <ModalHeader
        title="Add Money"
        subTitle="Enter amount to add"
        onDismiss={onDismiss}
      />

      <View className="p-4">
        <ControlledInput
          name="amount"
          label="Amount"
          defaultValue={form.getValues("amount").toString()}
          keyboardType="numeric"
          control={form.control}
          placeholder="Enter amount"
        />

        <Button
          onPress={form.handleSubmit(onSubmit)}
          className="mt-6 w-full"
          disabled={isPending || isVerifying}
          variant={isPending ? "disabled" : "default"}
        >
          {(isPending || isVerifying) && (
            <AntDesign
              name="loading1"
              size={16}
              color="black"
              className="mr-2 animate-spin"
            />
          )}
          <Text className="mr-2">
            {isPending || isVerifying ? "Adding..." : "Add Money"}
          </Text>
        </Button>
      </View>
    </KeyboardAwareScrollView>
  );
};

export default AddMoneyModal;
