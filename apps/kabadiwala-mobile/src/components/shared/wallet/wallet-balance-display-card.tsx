import React from "react";
import { Text, View } from "react-native";
import { Button } from "@/components/ui/button";
import useWallet from "@/hooks/use-wallet";

import Error from "../error";
import SkeletonLoading from "../skeleton-loading";

interface WalletBalanceDisplayCardProps {
  onTopUpPress?: () => void;
}

const WalletBalanceDisplayCard = ({
  onTopUpPress,
}: WalletBalanceDisplayCardProps) => {
  const { data, isPending, isError } = useWallet();

  if (isPending) {
    return <SkeletonLoading type="details" />;
  }

  if (isError) {
    return (
      <Error
        title="Failed to load wallet balance"
        message="Please try again later."
      />
    );
  }

  return (
    <View className="flex gap-y-6 rounded-[18px] border border-[rgba(242_242_242_1)] bg-[rgba(255_254_249_1)] px-4 py-[14px]">
      <View className="flex gap-4">
        <View className="flex flex-row items-center justify-between">
          <Text className="text-base font-medium text-gray-900">
            Wallet Balance
          </Text>
          <Text className="font-inter-medium rounded-md bg-[#EBFFEE] p-2 text-[10px] font-medium leading-3 text-teal-900">
            Current Balance
          </Text>
        </View>
        <Text className="font-jakarta-extrabold text-4xl text-teal-800">
          ₹{data.walletBalance}
        </Text>
      </View>
      <View className="flex flex-row gap-x-2">
        <Button
          variant="default"
          className="flex-1"
          label="Top Up"
          onPress={onTopUpPress}
        />
        <Button
          variant="default"
          className="flex-1 bg-[rgba(0_178_179_0.10)]"
          label="Withdraw"
          disabled={Number(data.walletBalance) < 1000}
        />
      </View>
    </View>
  );
};

export default WalletBalanceDisplayCard;
