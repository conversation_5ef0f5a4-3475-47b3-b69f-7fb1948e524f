import React from "react";
import { Text, TouchableOpacity, View } from "react-native";
import { Ionicons } from "@expo/vector-icons";

interface Notification {
  id: string;
  title: string;
  content: string;
  metadata: Record<string, unknown>;
  createdAt: Date;
}

interface NotificationCardProps {
  notification: Notification;
  onPress: () => void;
}

const NotificationCard: React.FC<NotificationCardProps> = ({
  notification,
  onPress,
}) => {
  const hasLink = Boolean(notification.metadata.link);
  const priority = notification.metadata.priority as string;
  const type = notification.metadata.type as string;

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
    const diffInHours = Math.floor(diffInMinutes / 60);
    const diffInDays = Math.floor(diffInHours / 24);

    if (diffInMinutes < 1) return "Just now";
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInHours < 24) return `${diffInHours}h ago`;
    if (diffInDays < 7) return `${diffInDays}d ago`;

    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
    });
  };

  const getNotificationIcon = () => {
    switch (type) {
      case "order":
        return "receipt-outline";
      case "payment":
        return "wallet-outline";
      case "system":
        return "settings-outline";
      case "promotion":
        return "gift-outline";
      default:
        return "notifications-outline";
    }
  };

  const getPriorityStyles = () => {
    switch (priority) {
      case "high":
        return {
          borderColor: "#FF6B6B",
          iconColor: "#FF6B6B",
          bgColor: "bg-red-50",
        };
      case "medium":
        return {
          borderColor: "#FFB946",
          iconColor: "#FFB946",
          bgColor: "bg-yellow-50",
        };
      default:
        return {
          borderColor: "#00CCCC",
          iconColor: "#00CCCC",
          bgColor: "bg-teal-50",
        };
    }
  };

  const priorityStyles = getPriorityStyles();

  const CardWrapper = hasLink ? TouchableOpacity : View;

  return (
    <CardWrapper
      onPress={hasLink ? onPress : undefined}
      activeOpacity={hasLink ? 0.7 : 1}
      className="mx-4"
    >
      <View
        className={`rounded-2xl border-l-4 bg-white p-4 shadow-sm ${priorityStyles.bgColor}`}
        style={{ borderLeftColor: priorityStyles.borderColor }}
      >
        <View className="flex-row items-start gap-3">
          {/* Icon */}
          {/* <View
            className="mt-0.5 h-10 w-10 items-center justify-center rounded-full"
            style={{ backgroundColor: priorityStyles.borderColor + "20" }}
          >
            <Ionicons
              name={getNotificationIcon()}
              size={20}
              color={priorityStyles.iconColor}
            />
          </View> */}

          {/* Content */}
          <View className="flex-1 gap-2">
            <View className="flex-row items-start justify-between">
              <Text
                className="font-jakarta-semibold flex-1 text-base text-black-800"
                numberOfLines={2}
              >
                {notification.title}
              </Text>
              {hasLink && (
                <Ionicons
                  name="chevron-forward"
                  size={16}
                  color="#666666"
                  className="ml-2 mt-0.5"
                />
              )}
            </View>

            <Text
              className="font-inter text-sm leading-5 text-black-600"
              numberOfLines={3}
            >
              {notification.content}
            </Text>

            <View className="flex-row items-center justify-between">
              <Text className="font-inter text-xs text-black-400">
                {formatTimeAgo(notification.createdAt)}
              </Text>

              {priority && (
                <View
                  className="rounded-full px-2 py-1"
                  style={{ backgroundColor: priorityStyles.borderColor + "15" }}
                >
                  <Text
                    className="font-inter text-xs font-medium capitalize"
                    style={{ color: priorityStyles.iconColor }}
                  >
                    {priority}
                  </Text>
                </View>
              )}
            </View>
          </View>
        </View>
      </View>
    </CardWrapper>
  );
};

export default NotificationCard;
