import React from "react";
import { Image, Text, View } from "react-native";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import CrossIcon from "@assets/icons/cross.png";
import PencilIcon from "@assets/icons/pencil.png";

import type { categoryRateTypeEnum } from "@acme/db/schema";

interface OrderItemCardProps {
  name: string;
  rate: number;
  rateType: (typeof categoryRateTypeEnum.enumValues)[number] | null;
  image: string;

  quantity?: string;
  onQuantityChange?: (quantity: string) => void;
  onEditPress?: () => void;
  onDeletePress?: () => void;

  showInputBox?: boolean;
  showEditIcon?: boolean;
  showDeleteIcon?: boolean;
  showCalculatedPrice?: boolean;

  disabledDeleteIcon?: boolean;
  disabledEditIcon?: boolean;
}

const OrderItemCard = (props: OrderItemCardProps) => {
  return (
    <View className="flex-col gap-6 rounded-xl border border-black-50 p-4">
      <View className="flex-row items-center justify-between gap-3">
        <View className="flex-row items-center gap-2">
          <Image
            // source={props.image}
            // contentFit="cover"
            // placeholder={{ blurhash: ImagePlaceholder }}
            source={{ uri: props.image }}
            className="aspect-square h-14 w-14 rounded-md"
          />

          <View className="flex-col gap-2">
            <Text className="font-inter-semibold text-lg font-semibold leading-5">
              {props.name}
            </Text>

            <View className="w-fit rounded-md bg-[#EBFFEE] px-2 py-1">
              <Text className="font-inter-medium text-xs font-medium text-black-700">
                Rate: ₹{props.rate}{" "}
                {props.rateType === "PER_ITEM" ? " / item" : " / kg"}
              </Text>
            </View>
          </View>
        </View>
        {/* actions */}
        <View className="flex-row items-center gap-2">
          {props.showEditIcon ? (
            <Button
              className="rounded-xl bg-black-50"
              onPress={props.onEditPress}
              disabled={props.disabledEditIcon}
            >
              <Image source={PencilIcon} className="h-6 w-6" />
            </Button>
          ) : null}
          {props.showDeleteIcon ? (
            <Button
              className="rounded-xl bg-black-50"
              onPress={props.onDeletePress}
              disabled={props.disabledDeleteIcon}
            >
              <Image source={CrossIcon} className="h-6 w-6" />
            </Button>
          ) : null}
        </View>
      </View>

      <View className="flex-row items-start gap-3">
        {props.showInputBox ? (
          <View style={{ flex: 1 }}>
            <Input
              placeholder={
                props.rateType === "PER_ITEM"
                  ? "Enter item quantity"
                  : "Enter weight in kg"
              }
              className="w-full"
              value={props.quantity ?? ""}
              onChangeText={(value) => {
                props.onQuantityChange?.(value);
              }}
              keyboardType="numeric"
            />
          </View>
        ) : null}
        {props.showCalculatedPrice ? (
          <View style={{ minWidth: 50 }} className="rounded-lg bg-teal-50 p-3">
            <Text className="font-inter-medium text-sm font-medium leading-5 text-teal-850">
              ₹{" "}
              {((parseFloat(props.quantity ?? "0") || 0) * props.rate).toFixed(
                2,
              )}
            </Text>
          </View>
        ) : null}
      </View>
    </View>
  );
};

export default OrderItemCard;
