import type { IncomingOrder } from "@/types/types";
import React from "react";
import { Text, View } from "react-native";
import { Badge } from "@/components/ui/badge";
import useConfig from "@/hooks/use-config";
import useWallet from "@/hooks/use-wallet";
import { calculateDistanceToOrder, formatDistance } from "@/utils/functions";
import { useLocationStore } from "@/utils/store";

interface OrderModalOfferDetailsProps {
  incomingOrder: IncomingOrder;
}

export const OrderModalOfferDetails = ({
  incomingOrder,
}: OrderModalOfferDetailsProps) => {
  const { location } = useLocationStore();
  const { data: wallet } = useWallet();
  const { minWalletBalanceRequiredToAcceptOrder } = useConfig();

  const distance = calculateDistanceToOrder(location, incomingOrder?.address);
  const formattedDistance = formatDistance(distance);

  return (
    <View className="w-full items-center justify-center gap-4">
      <View className="w-full flex-row items-center justify-between">
        <Text className="line-clamp-3 max-w-[60%] text-ellipsis text-[13px] font-medium leading-[18px] text-black-600">
          {incomingOrder?.address.display}
        </Text>
        <Badge
          label={formattedDistance}
          variant="secondary"
          className="rounded-full"
        />
      </View>

      <View className="w-full gap-3 rounded-xl bg-black-0 px-[14px] py-4">
        <View className="flex gap-2">
          <View className="flex-row items-center justify-between gap-2">
            <Text>Estimated Scrap Value</Text>
            <Text className="font-jakarta-bold">
              {incomingOrder?.totalAmount
                ? `₹ ${incomingOrder.totalAmount}`
                : "to be calculated"}
            </Text>
          </View>
          <View className="flex-row items-center justify-between gap-2">
            <Text>Your Wallet Balance</Text>
            <Text className="font-jakarta-bold">₹ {wallet?.walletBalance}</Text>
          </View>
        </View>
        <View className="h-[1px] w-full bg-black-100" />
        <View className="flex-row items-center justify-between gap-2">
          <Text>Required Balance </Text>
          <Text className="font-jakarta-bold text-red-600">
            ₹ {minWalletBalanceRequiredToAcceptOrder?.value}
          </Text>
        </View>
      </View>
    </View>
  );
};
