import type { BottomSheetModal } from "@gorhom/bottom-sheet";
import React, { useState } from "react";
import { Text, TextInput, TouchableOpacity, View } from "react-native";
import { KeyboardAwareScrollView } from "react-native-keyboard-controller";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Modal } from "@/components/ui/modal";
import useActiveOrder from "@/hooks/use-active-order";
import { trpc } from "@/utils/api";
import { authClient } from "@/utils/auth";
import { Feather, Ionicons } from "@expo/vector-icons";
import { FlashList } from "@shopify/flash-list";
import { skipToken, useMutation, useQuery } from "@tanstack/react-query";

import Error from "../error";
import SkeletonLoading from "../skeleton-loading";

export const ChatModal = ({
  ref,
  dismiss,
  onCallPress,
  isOpen,
}: {
  ref: React.RefObject<BottomSheetModal | null>;
  dismiss: () => void;
  onCallPress?: () => void;
  isOpen: boolean;
}) => {
  const { data: session } = authClient.useSession();
  const [input, setInput] = useState("");
  const { orderData } = useActiveOrder();

  const {
    data: messages,
    isPending,
    isError,
    refetch,
  } = useQuery(
    trpc.conversation.getMessages.queryOptions(
      orderData?.id && isOpen ? { orderId: orderData.id } : skipToken,
      {
        refetchInterval: isOpen ? 5000 : false, // Only refetch when modal is open
        enabled: isOpen && !!orderData?.id, // Only fetch when modal is open and orderId exists
      },
    ),
  );

  const { mutate: sendMessageToCustomer } = useMutation(
    trpc.conversation.sendMessageToCustomer.mutationOptions(),
  );

  const handleSend = () => {
    if (!input.trim() || !orderData?.id) return;

    sendMessageToCustomer({
      orderId: orderData.id,
      message: input.trim(),
    });

    setInput("");
  };

  return (
    <Modal
      ref={ref}
      title="Chat"
      snapPoints={["95%"]}
      handleComponent={() => (
        <View className="flex w-full flex-row items-center justify-between px-6 pb-3 pt-6">
          <View>
            <Button
              onPress={dismiss}
              variant="ghost"
              className="h-fit w-fit rounded-full bg-black-0 p-2"
            >
              <Feather name="x" size={24} color="black" />
            </Button>
          </View>
          <View>
            <Text>Chat</Text>
          </View>
          <View>
            <Button
              onPress={onCallPress}
              className="h-fit w-fit rounded-full border-yellow-600 bg-yellow-0 p-2"
            >
              <Ionicons name="call-outline" size={20} color="black" />
            </Button>
          </View>
        </View>
      )}
    >
      <View style={{ flex: 1, backgroundColor: "#fff" }}>
        {isPending ? (
          <View className="flex-1 px-4 py-2">
            <SkeletonLoading type="list" itemClassName="w-full" />
          </View>
        ) : isError ? (
          <View
            style={{
              flex: 1,
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Error
              title="Error"
              message="Failed to load messages"
              onRetry={refetch}
            />
          </View>
        ) : (
          <KeyboardAwareScrollView
            contentContainerStyle={{
              flexGrow: 1,
              justifyContent: "flex-end",
              padding: 16,
            }}
            keyboardShouldPersistTaps="handled"
          >
            <FlashList
              data={messages}
              renderItem={({ item }) => (
                <View
                  style={{
                    flexDirection: "row",
                    justifyContent:
                      session?.user.id === item.senderId
                        ? "flex-end"
                        : "flex-start",
                    alignItems: "flex-end",
                    marginVertical: 4,
                  }}
                >
                  <View
                    style={{
                      backgroundColor:
                        session?.user.id === item.senderId
                          ? "#00d3d6"
                          : "#fdf8e7",
                      borderRadius: 24,
                      paddingVertical: 10,
                      paddingHorizontal: 18,
                      maxWidth: "75%",
                    }}
                  >
                    <Text
                      style={{
                        color:
                          session?.user.id === item.senderId ? "#222" : "#222",
                        fontSize: 18,
                      }}
                    >
                      {item.content}
                    </Text>
                  </View>
                </View>
              )}
              keyExtractor={(item) => item.id}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={{ paddingBottom: 12 }}
              estimatedItemSize={50}
            />
            <View
              style={{
                flexDirection: "row",
                alignItems: "center",
                borderTopWidth: 1,
                borderColor: "#eee",
                padding: 8,
                backgroundColor: "#fff",
              }}
            >
              <TextInput
                value={input}
                onChangeText={setInput}
                placeholder="Hello Customer..."
                style={{
                  flex: 1,
                  borderRadius: 24,
                  backgroundColor: "#f6f6f6",
                  paddingHorizontal: 16,
                  paddingVertical: 10,
                  fontSize: 16,
                  marginRight: 8,
                }}
                returnKeyType="send"
                onSubmitEditing={handleSend}
              />
              <TouchableOpacity onPress={handleSend} style={{ padding: 8 }}>
                <Text
                  style={{ color: "#00b3b3", fontWeight: "bold", fontSize: 16 }}
                >
                  Send
                </Text>
              </TouchableOpacity>
            </View>
          </KeyboardAwareScrollView>
        )}
      </View>
    </Modal>
  );
};
