import React from "react";
import { Linking, Text, View } from "react-native";
import { showMessage } from "react-native-flash-message";
import { router } from "expo-router";
import { Button } from "@/components/ui/button";
import useActiveOrder from "@/hooks/use-active-order";
import Ionicons from "@expo/vector-icons/Ionicons";

const ActiveOrderStrip = () => {
  const { orderData, haveActiveOrder } = useActiveOrder();

  if (!haveActiveOrder) return null;

  const handleNavigate = () => {
    const latitude = orderData?.address.coordinates?.latitude;
    const longitude = orderData?.address.coordinates?.longitude;

    if (!latitude || !longitude) {
      showMessage({
        message: "Order location not available.",
        type: "danger",
      });
    }

    const url = `https://www.google.com/maps/dir/?api=1&destination=${latitude},${longitude}`;

    Linking.canOpenURL(url)
      .then((supported) => {
        if (supported) {
          return Linking.openURL(url);
        } else {
          console.log("Don't know how to open this URL: " + url);
          showMessage({
            message: "Unable to open maps. Please try again later.",
            type: "danger",
          });
        }
      })
      .catch((err) => console.error("An error occurred", err));
  };

  const handleDetails = () => {
    router.push("/order-details");
  };

  return (
    <View className="mx-4 rounded-xl border border-black-100 px-3 py-4">
      <View className="flex-col gap-4">
        <View className="mr-4 w-fit flex-1 flex-row items-center gap-2">
          <View className="rounded-lg bg-teal-50 p-2">
            <Ionicons name="location-outline" size={24} color="black" />
          </View>
          <View>
            <Text className="font-jakarta-semibold text-base font-semibold text-teal-850">
              Active Order
            </Text>
            <Text className="font-jakarta-regular text-sm text-black-600">
              {orderData?.address.display}
            </Text>
          </View>
        </View>

        <View className="flex-row gap-2">
          <Button
            onPress={handleDetails}
            className="flex-1 rounded-xl bg-[#EEFFFF]"
          >
            <Text className="font-jakarta-medium font-medium text-teal-850">
              View Details
            </Text>
          </Button>

          <Button
            onPress={handleNavigate}
            className="flex-1 rounded-xl bg-teal-650"
          >
            <Text className="font-jakarta-medium font-medium text-black-900">
              Navigate
            </Text>
          </Button>
        </View>
      </View>
    </View>
  );
};

export default ActiveOrderStrip;
