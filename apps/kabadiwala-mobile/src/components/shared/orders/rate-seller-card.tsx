import React, { useState } from "react";
import {
  ActivityIndicator,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import { showMessage } from "react-native-flash-message";
import { trpc } from "@/utils/api";
import { AntDesign } from "@expo/vector-icons";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

interface RateSellerCardProps {
  orderId: string;
  sellerId: string;
}

const RateSellerCard = ({ orderId, sellerId }: RateSellerCardProps) => {
  const [rating, setRating] = useState(0);
  const [reviewText, setReviewText] = useState("");

  const {
    data: review,
    isLoading,
    isError,
  } = useQuery(
    trpc.order.getSellerReviewByOrderId.queryOptions({ orderId, sellerId }),
  );

  const queryClient = useQueryClient();
  const { mutate: giveReview, isPending } = useMutation(
    trpc.order.giveReviewToSeller.mutationOptions({
      onSuccess: async (opts) => {
        showMessage({
          message: opts.message,
          type: "success",
        });
        await queryClient.invalidateQueries(
          trpc.order.getSellerReviewByOrderId.queryOptions({
            orderId,
            sellerId,
          }),
        );
        setRating(0);
        setReviewText("");
      },
      onError: (error) => {
        showMessage({
          message: error.message,
          type: "danger",
        });
      },
    }),
  );

  const handleSubmitReview = () => {
    if (rating === 0) {
      showMessage({
        message: "Please select a rating",
        type: "warning",
      });
      return;
    }

    giveReview({
      orderId: orderId,
      rating: rating,
      review: reviewText.trim() || undefined,
    });
  };

  const renderStars = (
    currentRating: number,
    onPress?: (rating: number) => void,
    readonly = false,
  ) => {
    return (
      <View className="flex-row items-center space-x-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <TouchableOpacity
            key={star}
            onPress={() => !readonly && onPress?.(star)}
            disabled={readonly || isPending}
            className={`${readonly ? "opacity-100" : "opacity-80"}`}
          >
            <AntDesign
              name={star <= currentRating ? "star" : "staro"}
              size={readonly ? 16 : 28}
              color={star <= currentRating ? "#FBCF04" : "#CCCCCC"}
            />
          </TouchableOpacity>
        ))}
        {readonly && (
          <Text className="font-inter-medium ml-2 text-sm text-black-600">
            ({currentRating}/5)
          </Text>
        )}
      </View>
    );
  };

  if (isLoading) {
    return (
      <View className="mb-4 rounded-lg border border-black-150 bg-white p-4">
        <View className="flex-row items-center justify-center py-8">
          <ActivityIndicator size="small" color="#009999" />
          <Text className="font-inter-regular ml-2 text-sm text-black-600">
            Loading review...
          </Text>
        </View>
      </View>
    );
  }

  if (isError) {
    return (
      <View className="mb-4 rounded-lg border border-red-200 bg-white p-4">
        <Text className="font-inter-regular text-center text-sm text-red-600">
          Failed to load review
        </Text>
      </View>
    );
  }

  // Show existing review
  if (review) {
    return (
      <View className="mb-4 rounded-lg border border-black-150 bg-white p-4">
        <View className="mb-3 flex-row items-center">
          <AntDesign name="checkcircle" size={20} color="#009999" />
          <Text className="font-inter-semibold ml-2 text-base text-black-800">
            Your Review
          </Text>
        </View>

        <View className="mb-3">
          {renderStars(Number(review.rating), undefined, true)}
        </View>

        {review.review && (
          <View className="mb-3 rounded-lg bg-black-50 p-3">
            <Text className="font-inter-regular text-sm leading-5 text-black-700">
              "{review.review}"
            </Text>
          </View>
        )}

        <Text className="font-inter-regular text-xs text-black-500">
          Reviewed on {new Date(review.createdAt).toLocaleDateString()}
        </Text>
      </View>
    );
  }

  // Show rating form
  return (
    <View className="mb-2 rounded-lg border border-black-150 bg-white p-4">
      <View className="mb-4 flex-row items-center">
        <AntDesign name="star" size={20} color="#FBCF04" />
        <Text className="font-inter-semibold ml-2 text-base text-black-800">
          Rate the Seller
        </Text>
      </View>

      <View className="mb-4">
        <Text className="font-inter-medium mb-2 text-sm text-black-700">
          How was your experience?
        </Text>
        {renderStars(rating, setRating)}
      </View>

      <View className="mb-4">
        <Text className="font-inter-medium mb-2 text-sm text-black-700">
          Write a review (optional)
        </Text>
        <TextInput
          value={reviewText}
          onChangeText={setReviewText}
          placeholder="Share your experience with other users..."
          placeholderTextColor="#999999"
          multiline
          numberOfLines={4}
          maxLength={500}
          className="font-inter-regular min-h-[80px] rounded-lg border border-black-200 bg-white p-3 text-sm text-black-800"
          style={{ textAlignVertical: "top" }}
          editable={!isPending}
        />
        <Text className="font-inter-regular mt-1 text-right text-xs text-black-500">
          {reviewText.length}/500
        </Text>
      </View>

      <TouchableOpacity
        onPress={handleSubmitReview}
        disabled={isPending || rating === 0}
        className={`flex-row items-center justify-center rounded-lg px-4 py-3 ${
          isPending || rating === 0 ? "bg-black-200" : "bg-teal-700"
        }`}
      >
        {isPending ? (
          <>
            <ActivityIndicator size="small" color="#FFFFFF" />
            <Text className="font-inter-semibold ml-2 text-sm text-white">
              Submitting...
            </Text>
          </>
        ) : (
          <Text className="font-inter-semibold text-sm text-white">
            Submit Review
          </Text>
        )}
      </TouchableOpacity>
    </View>
  );
};

export default RateSellerCard;
