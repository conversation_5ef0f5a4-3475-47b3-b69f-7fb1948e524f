import type { Category } from "@/types/types";
import React from "react";
import { Image, Text, View } from "react-native";
import { Link } from "expo-router";

interface CategoryCardProps {
  category: Category;
}

const CategoryCard = ({ category }: CategoryCardProps) => {
  return (
    <View className="mb-3 flex-1 overflow-hidden rounded-xl border border-black-200">
      <Link
        href={{
          pathname: "/add-item-to-order/[id]",
          params: { id: category.id },
        }}
        className="flex-1"
      >
        <Image
          source={{ uri: category.image }}
          className="h-[80px] w-full rounded-lg"
        />
      </Link>
      <Text className="font-jakarta-semibold py-4 text-center text-sm font-semibold leading-[18px]">
        {category.name}
      </Text>
    </View>
  );
};

export default CategoryCard;
