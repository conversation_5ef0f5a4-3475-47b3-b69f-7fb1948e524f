import type { Category } from "@/types/types";
import React, { useMemo } from "react";
import { Image, Text, View } from "react-native";
import { Link } from "expo-router";
import { Button } from "@/components/ui/button";
import useActiveOrder from "@/hooks/use-active-order";

interface BrowseCategoryCardProps {
  category: Category;
  parentName?: string;
  variant: "add-to-order" | "view-items";
  onAddUpdateInOrderPress?: () => void;
}

const BrowseCategoryCard = ({
  category,
  variant,
  onAddUpdateInOrderPress,
}: BrowseCategoryCardProps) => {
  const { orderData } = useActiveOrder();

  const orderItem = useMemo(() => {
    return orderData?.items.find((item) => item.categoryId === category.id);
  }, [orderData?.items, category.id]);

  return (
    <View className="flex-row gap-3 rounded-xl border border-black-50 bg-white p-3">
      <View className="overflow-hidden rounded-lg">
        <Image
          source={{ uri: category.image }}
          className="aspect-square h-[132px] w-[132px] rounded-lg"
        />
      </View>
      <View className="flex-1 flex-col justify-between">
        <View className="flex-col gap-2">
          <Text className="font-jakarta-semibold text-base font-semibold">
            {category.name}
          </Text>
          <Text className="font-inter-medium w-fit bg-[#EBFFEE] px-2 py-[6px] text-sm font-medium text-black-700">
            {category.rateType === "PER_ITEM"
              ? `Per Item: ₹${category.rate}`
              : `Per Kg: ₹${category.rate}`}
          </Text>
        </View>

        {variant === "add-to-order" &&
          (orderItem ? (
            <Button className="bg-teal-850" onPress={onAddUpdateInOrderPress}>
              <Text className="text-teal-50">Update Quantity</Text>
            </Button>
          ) : (
            <Button className="bg-yellow-350" onPress={onAddUpdateInOrderPress}>
              <Text className="text-black-900">Add to Order</Text>
            </Button>
          ))}
        {variant === "view-items" && (
          <Button className="bg-yellow-350">
            <Link
              href={{
                pathname: "/add-item-to-order/[id]",
                params: { id: category.id },
              }}
            >
              <Text className="text-black-900">View Items</Text>
            </Link>
          </Button>
        )}
      </View>
    </View>
  );
};

export default BrowseCategoryCard;
