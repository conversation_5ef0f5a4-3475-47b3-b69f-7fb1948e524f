import type { IncomingOrder } from "@/types/types";
import type { BottomSheetModal } from "@gorhom/bottom-sheet";
import React from "react";
import { Text, View } from "react-native";
import { ScrollView } from "react-native-gesture-handler";
import { router } from "expo-router";
import { Button } from "@/components/ui/button";
import { Modal } from "@/components/ui/modal";
import useConfig from "@/hooks/use-config";
import useWallet from "@/hooks/use-wallet";

import { OrderModalHeader } from "./order-modal-header";
import { OrderModalOfferDetails } from "./order-modal-offer-details";

interface IncommingOrderPickupRequestModalProps {
  ref: React.RefObject<BottomSheetModal | null>;
  incomingOrder: IncomingOrder;
  onDismiss: () => void;
  onReject: () => void;
  onAccept?: () => void;
}

export const IncommingOrderPickupRequestModal = ({
  ref,
  incomingOrder,
  onDismiss,
  onReject,
  onAccept,
}: IncommingOrderPickupRequestModalProps) => {
  const { data: wallet } = useWallet();
  const { minWalletBalanceRequiredToAcceptOrder } = useConfig();

  const isWalletBalanceSufficient =
    Number(wallet?.walletBalance ?? 0) >=
    Number(minWalletBalanceRequiredToAcceptOrder?.value ?? 0);

  const handleAddMoneyNavigation = () => {
    router.push("/general-settings/wallet");
    onDismiss();
  };

  return (
    <Modal ref={ref} onDismiss={onDismiss}>
      <ScrollView contentContainerClassName="flex-1 pb-20">
        <View className="flex-1 gap-5 px-5">
          <OrderModalHeader
            orderId={incomingOrder?.id}
            headerText={
              incomingOrder?.totalAmount
                ? "Order Request"
                : "Quick Pickup Request"
            }
            onDismiss={onReject}
          />
          <OrderModalOfferDetails incomingOrder={incomingOrder} />
        </View>
        <View className="gap-3 border border-black-50 px-5 py-3">
          {isWalletBalanceSufficient ? (
            <Button label="Accept" onPress={onAccept} />
          ) : (
            <>
              <Text className="text-center text-[14px] font-medium text-red-600">
                Insufficient wallet balance to accept this order
              </Text>
              <Button
                label="Add Money"
                variant="outline"
                onPress={handleAddMoneyNavigation}
              />
            </>
          )}
        </View>
      </ScrollView>
    </Modal>
  );
};
