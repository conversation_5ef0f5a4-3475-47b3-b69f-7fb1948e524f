import React, { useEffect, useMemo, useState } from "react";
import { Text, View } from "react-native";
import { showMessage } from "react-native-flash-message";
import { KeyboardAvoidingView } from "react-native-keyboard-controller";
import { Button } from "@/components/ui/button";
import useActiveOrder from "@/hooks/use-active-order";
import { trpc } from "@/utils/api";
import AntDesign from "@expo/vector-icons/AntDesign";
import {
  skipToken,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";

import Error from "../error";
import SkeletonLoading from "../skeleton-loading";
import OrderItemCard from "./order-item-card";
import { OrderModalHeader } from "./order-modal-header";

interface AddItemToOrderModalContentProps {
  itemId: string | null;
  onDismiss: () => void;
}

const AddItemToOrderModalContent = ({
  itemId,
  onDismiss,
}: AddItemToOrderModalContentProps) => {
  const [quantity, setQuantity] = useState<string>("");
  const { orderData } = useActiveOrder();

  const queryClient = useQueryClient();

  const orderItem = useMemo(() => {
    return orderData?.items.find((item) => item.categoryId === itemId);
  }, [orderData?.items, itemId]);

  // ISSUE: The issue is with the query and useMemo find thing. Even though i have orderItem found in the activeOrder still the query runs and i have tried many to stop the query in case of orderItem being available but it still runs. i tried using skipToken and enabled both ways but it still runs the query. I need to find a way to stop the query from running if orderItem is already available in the activeOrder. Right now both are running and one is unncessary which may case performance issues in the future.

  const {
    data: category,
    isPending,
    isFetching: isRefetching,
    isError,
    refetch,
  } = useQuery(
    trpc.category.getCategoryById.queryOptions(
      itemId
        ? {
            id: itemId,
          }
        : skipToken,
    ),
  );

  // Use orderItem data if available, otherwise use API data
  const categoryData = useMemo(() => {
    if (orderItem) {
      return orderItem.category;
    }
    return category?.category;
  }, [orderItem, category]);

  const { mutate: addUpdateItemInOrder, isPending: isAddingUpdatingItem } =
    useMutation(
      trpc.order.addUpdateItemInOrder.mutationOptions({
        onSuccess: async (opts) => {
          await queryClient.invalidateQueries(
            trpc.order.getActiveOrder.queryOptions(),
          );
          await queryClient.invalidateQueries(
            trpc.order.getEstimatedOrderTotalAmount.queryOptions(
              orderData?.id ? { orderId: orderData.id } : skipToken,
            ),
          );
          showMessage({
            message: opts.message,
            type: "success",
          });
          onDismiss();
        },
        onError: (error) => {
          showMessage({
            message: error.message || "Failed to add/update item in order",
            type: "danger",
          });
        },
      }),
    );

  const { mutate: removeItemFromOrder, isPending: isRemovingItem } =
    useMutation(
      trpc.order.removeItemFromOrder.mutationOptions({
        onSuccess: async (opts) => {
          await queryClient.invalidateQueries(
            trpc.order.getActiveOrder.queryOptions(),
          );
          await queryClient.invalidateQueries(
            trpc.order.getEstimatedOrderTotalAmount.queryOptions(
              orderData?.id ? { orderId: orderData.id } : skipToken,
            ),
          );
          showMessage({
            message: opts.message,
            type: "success",
          });
          onDismiss();
        },
        onError: (error) => {
          showMessage({
            message: error.message || "Failed to remove item from order",
            type: "danger",
          });
        },
      }),
    );

  useEffect(() => {
    if (orderItem?.quantity) {
      setQuantity(orderItem.quantity.toString());
    }
  }, [orderItem?.quantity]);

  if (!itemId) return null;

  if (isPending || isRefetching) {
    return (
      <View className="p-6 px-4">
        <SkeletonLoading type="default" />
      </View>
    );
  }

  if (isError || (!orderItem && !category.category)) {
    return (
      <Error
        title="Error loading item details"
        message="Failed to load item details"
        onRetry={refetch}
      />
    );
  }

  const handleQuantityChange = (newQuantity: string) => {
    setQuantity(newQuantity);
  };

  const handleAddUpdateToOrder = () => {
    if (!categoryData || !orderData?.id) return;

    if (!quantity.trim()) {
      showMessage({
        message: "Please enter a quantity",
        type: "danger",
      });
      return;
    }

    const parsedQuantity = parseFloat(quantity);
    if (isNaN(parsedQuantity) || parsedQuantity < 1) {
      showMessage({
        message: "Quantity must be at least 1",
        type: "danger",
      });
      return;
    }

    addUpdateItemInOrder({
      categoryId: categoryData.id,
      orderId: orderData.id,
      quantity: parsedQuantity,
    });
  };

  const handleRemoveItemFromOrder = () => {
    if (!orderItem || !orderData?.id) return;

    removeItemFromOrder({
      orderId: orderData.id,
      categoryId: orderItem.categoryId,
    });
  };

  return (
    <KeyboardAvoidingView>
      <View className="flex-col gap-6 px-4">
        <OrderModalHeader
          headerText={orderItem ? "Update Item" : "Add Item"}
          orderId={orderData?.id}
          onDismiss={onDismiss}
        />

        <OrderItemCard
          name={categoryData?.name ?? ""}
          rate={Number(categoryData?.rate ?? 0)}
          rateType={categoryData?.rateType ?? null}
          image={categoryData?.image ?? ""}
          quantity={quantity}
          onQuantityChange={handleQuantityChange}
          showEditIcon={false}
          showInputBox={true}
          showCalculatedPrice={true}
          showDeleteIcon={!!orderItem}
          onDeletePress={handleRemoveItemFromOrder}
        />

        <Button
          onPress={handleAddUpdateToOrder}
          className={`${orderItem ? "bg-teal-850" : "bg-yellow-350"}`}
          disabled={isAddingUpdatingItem || isRemovingItem}
        >
          {isAddingUpdatingItem || isRemovingItem ? (
            <AntDesign
              name="loading1"
              size={18}
              color={orderItem ? "white" : "black"}
              className="mr-2 animate-spin"
            />
          ) : null}
          <Text className={`${orderItem ? "text-teal-50" : "text-black-900"}`}>
            {orderItem ? "Update Quantity" : "Add to Order"}
          </Text>
        </Button>
      </View>
    </KeyboardAvoidingView>
  );
};

export default AddItemToOrderModalContent;
