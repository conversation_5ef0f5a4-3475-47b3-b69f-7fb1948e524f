import React from "react";
import { Linking, Text } from "react-native";
import { showMessage } from "react-native-flash-message";
import { Button } from "@/components/ui/button";
import { trpc } from "@/utils/api";
import { useQuery } from "@tanstack/react-query";

const NavigateToScraphubButton = () => {
  const { data: scraphubAddress } = useQuery(
    trpc.order.getScraphubAddress.queryOptions(),
  );

  const handleNavigate = () => {
    const latitude = scraphubAddress?.coordinates?.latitude;
    const longitude = scraphubAddress?.coordinates?.longitude;

    if (!latitude || !longitude) {
      showMessage({
        message: "Order location not available.",
        type: "danger",
      });
    }

    const url = `https://www.google.com/maps/dir/?api=1&destination=${latitude},${longitude}`;

    Linking.canOpenURL(url)
      .then((supported) => {
        if (supported) {
          return Linking.openURL(url);
        } else {
          console.log("Don't know how to open this URL: " + url);
          showMessage({
            message: "Unable to open maps. Please try again later.",
            type: "danger",
          });
        }
      })
      .catch((err) => console.error("An error occurred", err));
  };

  if (!scraphubAddress) {
    return null;
  }

  return (
    <Button
      onPress={handleNavigate}
      className="mb-4 rounded-lg bg-teal-650 px-4 py-3"
    >
      <Text>Navigate to Scraphub</Text>
    </Button>
  );
};

export default NavigateToScraphubButton;
