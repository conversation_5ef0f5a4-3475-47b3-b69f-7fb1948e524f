import React from "react";
import { Text, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { router } from "expo-router";
import { Button } from "@/components/ui/button";
import useActiveOrder from "@/hooks/use-active-order";
import { Ionicons } from "@expo/vector-icons";

interface OrderDetailsHeaderProps {
  title?: string;
}

const OrderDetailsHeader = ({
  title = "Order Details",
}: OrderDetailsHeaderProps) => {
  const { orderData } = useActiveOrder();

  const handleHelpPress = () => {
    if (orderData?.id) {
      router.push({
        pathname: "/contact-support",
        params: { orderId: orderData.id },
      });
    } else {
      router.push("/contact-support");
    }
  };

  return (
    <SafeAreaView className="-pb-safe-offset-20 flex-row items-center justify-between bg-white">
      {/* Header with back button */}
      <View className="flex-row items-center justify-between px-5 py-3">
        <View className="w-fit flex-1 flex-row items-center gap-2">
          <Button
            variant="loading"
            className="bg-black-0"
            onPress={() => router.back()}
          >
            <Ionicons name="chevron-back" size={24} color="#000" />
          </Button>
          <View className="">
            <Text className="font-jakarta-semibold text-start text-lg text-teal-800">
              {title}
            </Text>
            <Text className="text-xs text-[#454545]">
              Order #{orderData?.id}
            </Text>
          </View>
        </View>
        <Button
          onPress={handleHelpPress}
          className="h-fit w-fit bg-[#0033330A]"
        >
          <Text className="">Help</Text>
        </Button>
      </View>
    </SafeAreaView>
  );
};

export default OrderDetailsHeader;
