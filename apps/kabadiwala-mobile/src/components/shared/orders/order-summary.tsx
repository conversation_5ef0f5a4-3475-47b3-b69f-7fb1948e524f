import { Text, View } from "react-native";

interface OrderSummaryViewProps {
  pickupLocation?: string;
  estimatedValue?: string;
  weight?: string;
  timeWindow?: string;
  vehicleType?: "truck" | "bike" | "car";
}
const OrderSummaryView = ({
  summaryItem,
}: {
  summaryItem: OrderSummaryViewProps;
}) => {
  return (
    <View className="w-full gap-3 rounded-xl bg-yellow-0 px-3 py-4">
      {summaryItem.pickupLocation && (
        <View className="flex-row justify-between">
          <Text className="font-inter text-sm text-black-600">
            Pickup Location:
          </Text>
          <Text className="font-inter-semibold text-sm text-black-800">
            {summaryItem.pickupLocation}
          </Text>
        </View>
      )}
      {summaryItem.estimatedValue && (
        <View className="flex-row justify-between">
          <Text className="font-inter text-sm text-black-600">
            Estimated Value:
          </Text>
          <Text className="font-inter-semibold text-sm text-black-800">
            {summaryItem.estimatedValue}
          </Text>
        </View>
      )}
      {summaryItem.weight && (
        <View className="flex-row justify-between">
          <Text className="font-inter text-sm text-black-600">Weight:</Text>
          <Text className="font-inter-semibold text-sm text-black-800">
            {summaryItem.weight}
          </Text>
        </View>
      )}
      {summaryItem.timeWindow && (
        <View className="flex-row justify-between">
          <Text className="font-inter text-sm text-black-600">
            Time Window:
          </Text>
          <Text className="font-inter-semibold text-sm text-black-800">
            {summaryItem.timeWindow}
          </Text>
        </View>
      )}
      {summaryItem.vehicleType && (
        <View className="flex-row justify-between">
          <Text className="font-inter text-sm text-black-600">
            Vehicle Type:
          </Text>
          <Text className="font-inter-semibold text-sm text-black-800">
            {summaryItem.vehicleType}
          </Text>
        </View>
      )}
    </View>
  );
};
export default OrderSummaryView;
