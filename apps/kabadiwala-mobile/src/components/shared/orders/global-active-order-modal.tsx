import { useEffect } from "react";
import { router, usePathname } from "expo-router";
import ActiveOrderModalContent from "@/components/shared/orders/active-order-modal-content";
import { useModal } from "@/components/ui/modal";
import useActiveOrder from "@/hooks/use-active-order";

const GlobalActiveOrderModal = () => {
  const pathname = usePathname();
  const { haveActiveOrder } = useActiveOrder();
  const { present, dismiss, ref } = useModal();

  useEffect(() => {
    if (haveActiveOrder && pathname === "/orders") {
      present();
    }
  }, [haveActiveOrder, pathname, present]);

  const handleViewDetails = () => {
    dismiss();
    router.replace("/order-details");
  };

  const handleModalDismiss = () => {
    dismiss();
  };

  return (
    <ActiveOrderModalContent
      ref={ref}
      dismiss={handleModalDismiss}
      onViewDetails={handleViewDetails}
    />
  );
};

export default GlobalActiveOrderModal;
