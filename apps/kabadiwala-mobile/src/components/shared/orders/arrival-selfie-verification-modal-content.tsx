import React from "react";
import { Al<PERSON>, Linking, Text, View } from "react-native";
import { showMessage } from "react-native-flash-message";
import { ScrollView } from "react-native-gesture-handler";
import { SafeAreaView } from "react-native-safe-area-context";
import { Image } from "expo-image";
import * as ImagePicker from "expo-image-picker";
import { router } from "expo-router";
import { Button } from "@/components/ui/button";
import useActiveOrder from "@/hooks/use-active-order";
import { trpc } from "@/utils/api";
import { useImageUploader } from "@/utils/uploadthing";
import Entypo from "@expo/vector-icons/Entypo";
import { useMutation, useQueryClient } from "@tanstack/react-query";

import { ModalHeader } from "../modal-header";

interface ArrivalSelfieVerificationModalContentProps {
  onDismiss: () => void;
}

const ArrivalSelfieVerificationModalContent = ({
  onDismiss,
}: ArrivalSelfieVerificationModalContentProps) => {
  const [selfieData, setSelfieData] = React.useState<{
    selfieImageUrl: string | null;
    selfieFileKey: string | null;
  }>({
    selfieImageUrl: null,
    selfieFileKey: null,
  });

  const queryClient = useQueryClient();

  const { orderData } = useActiveOrder();
  const { mutate: deleteFile, isPending: isDeletingFile } = useMutation(
    trpc.utils.deleteUploadthingFileUsingFileKey.mutationOptions(),
  );

  const { mutate: verifySelfie, isPending: isVerifyingSelfie } = useMutation(
    trpc.order.verifySelfie.mutationOptions(),
  );

  const { openImagePicker, isUploading: isUploadingDocument } =
    useImageUploader("imageUploader", {
      onClientUploadComplete: (res) => {
        const imageUrl = res[0]?.ufsUrl;
        const fileKey = res[0]?.key;

        if (!imageUrl || !fileKey) {
          return;
        }

        setSelfieData({
          selfieImageUrl: imageUrl,
          selfieFileKey: fileKey,
        });
      },
      onUploadError: () => {
        showMessage({
          message: "Selfie upload failed.",
          type: "danger",
        });
      },
    });

  const handleSelfieUpload = async () => {
    if (selfieData.selfieFileKey) {
      // Delete previous document first
      deleteFile({ fileKey: selfieData.selfieFileKey });
    }

    // Request camera permissions
    const { status } = await ImagePicker.requestCameraPermissionsAsync();

    if (status !== "granted") {
      Alert.alert(
        "No Camera Permissions",
        "You need to grant permission to your camera to take a selfie",
        [
          { text: "Dismiss" },
          {
            text: "Open Settings",
            onPress: () => Linking.openSettings(),
          },
        ],
      );
      return;
    }

    await openImagePicker({
      source: "camera", // "library" or "camera",
      allowsEditing: true,
      quality: 0.8,
      onInsufficientPermissions: () => {
        Alert.alert(
          "No Camera Permissions",
          "You need to grant permission to your camera to take a selfie",
          [
            { text: "Dismiss" },
            {
              text: "Open Settings",
              onPress: () => Linking.openSettings(),
            },
          ],
        );
      },
      onCancel: () => {
        showMessage({
          message: "Selfie capture cancelled",
          type: "info",
        });
      },
    });
  };

  const handleVerifySelfie = () => {
    if (!selfieData.selfieFileKey || !selfieData.selfieImageUrl) {
      showMessage({
        message: "Please take a selfie first",
        type: "info",
      });
      return;
    }

    if (!orderData) {
      showMessage({
        message: "No active order found",
        type: "danger",
      });
      return;
    }

    verifySelfie(
      {
        orderId: orderData.id,
        selfieImageUrl: selfieData.selfieImageUrl,
        selfieFileKey: selfieData.selfieFileKey,
      },
      {
        onSuccess: async (opts) => {
          showMessage({
            message: opts.message,
            type: "success",
          });
          await queryClient.invalidateQueries(
            trpc.order.getActiveOrder.queryOptions(),
          );
          onDismiss();
          router.replace("/collect-items-price");
        },
        onError: (opts) => {
          showMessage({
            message: opts.message,
            type: "danger",
          });
        },
      },
    );
  };

  return (
    <SafeAreaView
      style={{ flex: 1 }}
      className="-pt-safe-offset-[250px] flex-1"
    >
      <ScrollView contentContainerClassName="flex-1">
        <ModalHeader
          title="Take Selfie"
          subTitle="Please take photo of yourself to continue with pickup"
        />

        <View className="flex flex-1 items-center justify-center gap-9 px-6">
          <View className="flex h-[327px] w-[327px] items-center justify-center overflow-hidden rounded-full border border-dashed border-black-700 bg-black-0">
            <View className="flex items-center justify-center gap-2">
              {selfieData.selfieImageUrl ? (
                <Image
                  source={{ uri: selfieData.selfieImageUrl }}
                  style={{ width: 327, height: 327, borderRadius: 163.5 }}
                />
              ) : (
                <>
                  <Entypo name="camera" size={48} color="#DDD" />
                  <Text className="font-inter-regular text-sm leading-[21px] text-black-600">
                    Take a selfie to continue
                  </Text>
                </>
              )}
            </View>
          </View>
          <View className="flex w-full gap-4">
            <Button
              onPress={handleSelfieUpload}
              className="w-full bg-teal-600 text-black-900"
              disabled={
                isUploadingDocument || isDeletingFile || isVerifyingSelfie
              }
            >
              <Text>
                {selfieData.selfieFileKey ? "Retake Selfie" : "Take Selfie"}
              </Text>
            </Button>

            {selfieData.selfieFileKey && (
              <Button
                onPress={handleVerifySelfie}
                className="w-full bg-teal-900"
                disabled={
                  isVerifyingSelfie || isUploadingDocument || isDeletingFile
                }
              >
                <Text className="text-black-0">
                  {isVerifyingSelfie ? "Verifying..." : "Continue"}
                </Text>
              </Button>
            )}
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default ArrivalSelfieVerificationModalContent;
