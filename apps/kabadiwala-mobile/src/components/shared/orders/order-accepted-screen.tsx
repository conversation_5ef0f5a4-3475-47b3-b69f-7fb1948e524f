import React from "react";
import { Text, View } from "react-native";
import { Button } from "@/components/ui/button";

export const OrderAcceptedScreen = ({
  onStartPickup,
}: {
  onStartPickup: () => void;
}) => {
  return (
    <View className="flex-1 items-center justify-center gap-4">
      <Text className="font-jakarta-bold text-lg text-teal-850">
        Order Accepted
      </Text>
      <Text className="text-sm text-black-500">
        You have accepted the order. Please proceed to pickup.
      </Text>
      <Button label="Start Pickup" onPress={onStartPickup} />
    </View>
  );
};
