import type { HandleComponentProps } from "@/types/types";
import React from "react";
import { Text, View } from "react-native";
import { Feather } from "@expo/vector-icons";

import { Timer } from "../timer";

export const HandleComponent = ({ expiresInTime }: HandleComponentProps) => {
  return (
    <View className="absolute -top-5 left-[25%] transform flex-row items-center justify-between gap-1 rounded-3xl border border-red-700 bg-[#FEE9E7] bg-white px-4 py-2">
      <Feather name="clock" size={24} color="black" />
      <Text>Expires in</Text>
      <Timer className="text-red-700" targetDate={expiresInTime} />
    </View>
  );
};
