import type { Order } from "@/types/types";
import React from "react";
import { Text, TouchableOpacity, View } from "react-native";
import { router } from "expo-router";
import Entypo from "@expo/vector-icons/Entypo";

import { cn } from "@acme/ui/lib/cn";

interface OrderCardProps {
  orderItem: Order;
}

const OrderCard = ({ orderItem }: OrderCardProps) => {
  const handleViewDetails = () => {
    if (orderItem?.status === "ACTIVE") {
      router.push("/order-details");
    } else {
      router.push({
        pathname: "/order-history-details",
        params: { orderId: orderItem?.id },
      });
    }
  };

  return (
    <View className="mb-4 flex flex-col gap-3 overflow-hidden rounded-xl border-[1.2px] border-gray-200 bg-white p-4">
      {/* id and status */}
      <View className="flex flex-row items-center justify-between gap-4">
        <View>
          <Text className="text-sm font-semibold leading-[21px] text-yellow-800">
            Order #{orderItem?.id}
          </Text>
          <Text className="text-xs leading-[18px] text-gray-500">
            {orderItem?.createdAt.toDateString()}
          </Text>
        </View>
        <View>
          <Text
            className={cn(
              "rounded-lg p-2 text-[10px] font-semibold uppercase leading-[18px] tracking-[0.13px] text-gray-500",
              orderItem?.status === "ACTIVE"
                ? "bg-green-700 text-white"
                : "bg-black-700 text-white",
            )}
          >
            {orderItem?.status}
          </Text>
        </View>
      </View>

      <View className="h-px bg-gray-200" />

      {/* details */}
      <View className="flex flex-col gap-[14px]">
        {/* scrap items */}
        <View className="flex flex-col gap-1">
          <Text className="text-[11px] font-medium uppercase leading-[14px] tracking-[0.11px] text-gray-400">
            Scrap Items
          </Text>
          <Text className="text-[13px] font-medium leading-[18px] tracking-[0.13px] text-gray-800">
            {orderItem?.items && orderItem.items.length > 0
              ? orderItem.items
                  .map(
                    (item, idx) =>
                      item.category.name +
                      (idx !== orderItem.items.length - 1 ? ", " : ""),
                  )
                  .join("")
              : "No items Found"}
          </Text>
        </View>

        {/* area */}
        <View className="flex flex-col gap-1">
          <Text className="text-[11px] font-medium uppercase leading-[14px] tracking-[0.11px] text-gray-400">
            Area
          </Text>
          <Text
            className="text-[13px] font-medium leading-[18px] tracking-[0.13px] text-gray-800"
            numberOfLines={3}
          >
            {orderItem?.address.display}
          </Text>
        </View>

        {/* amount paid */}
        <View className="flex flex-col gap-1">
          <Text className="text-[11px] font-medium uppercase leading-[14px] tracking-[0.11px] text-gray-400">
            Amount Paid
          </Text>
          <Text
            className="text-[13px] font-medium leading-[18px] tracking-[0.13px] text-gray-800"
            numberOfLines={3}
          >
            {orderItem?.totalAmount}
          </Text>
        </View>
      </View>

      <View className="h-px bg-gray-200" />

      <TouchableOpacity
        className="flex-1 flex-row items-center justify-center gap-1 rounded-lg bg-gray-50 px-4 py-3"
        onPress={handleViewDetails}
      >
        <Text className="font-jakarta-semibold text-[13px] font-semibold leading-[18px] tracking-[0.13px] text-teal-800">
          View Details
        </Text>
        <Entypo name="chevron-right" size={24} color="black" />
      </TouchableOpacity>
    </View>
  );
};

export default OrderCard;
