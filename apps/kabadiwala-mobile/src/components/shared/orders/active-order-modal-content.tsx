import type { BottomSheetModal } from "@gorhom/bottom-sheet";
import React from "react";
import { Text, View } from "react-native";
import Svg, { ClipPath, Defs, G, Path, Rect } from "react-native-svg";
import { Button } from "@/components/ui/button";
import useActiveOrder from "@/hooks/use-active-order";
import { calculateDistanceToOrder, formatDistance } from "@/utils/functions";
import { useLocationStore } from "@/utils/store";

import { Modal } from "../../ui/modal";
import ActiveOrderMap from "../google/active-order-map";
import { OrderModalHeader } from "./order-modal-header";
import OrderSummaryView from "./order-summary";

interface ActiveOrderModalContentProps {
  dismiss: () => void;
  onViewDetails: () => void;
  ref: React.Ref<BottomSheetModal | null>;
}

const ActiveOrderModalContent = ({
  dismiss,
  onViewDetails,
  ref,
}: ActiveOrderModalContentProps) => {
  const { orderData } = useActiveOrder();
  const { location } = useLocationStore();

  // Calculate distance between user location and order address
  const distance = calculateDistanceToOrder(location, orderData?.address);
  const formattedDistance = formatDistance(distance);

  return (
    <Modal
      ref={ref}
      snapPoints={["80%", "90%"]}
      enablePanDownToClose
      onDismiss={dismiss}
      handleComponent={null}
      backgroundStyle={{ backgroundColor: "#fff" }}
    >
      <View className="jus flex-1 gap-6 px-6 pb-4">
        <OrderModalHeader
          headerText="Active Order"
          orderId={orderData?.id}
          onDismiss={dismiss}
        />

        <View className="flex gap-6">
          {/* Map placeholder */}
          <View className="mb-4 overflow-hidden rounded-lg">
            <ActiveOrderMap />
          </View>

          {/* Order summary */}
          <OrderSummaryView
            summaryItem={{
              pickupLocation: formattedDistance,
              estimatedValue: orderData?.totalAmount ?? undefined,
              //   timeWindow: "ASAP",
              //   vehicleType: "truck",
            }}
          />

          {/* Quick actions */}
          <MapLocationButton
            userLocation={location?.address?.display ?? "Current Location"}
            destinationLocation={orderData?.address.display ?? ""}
          />
        </View>

        <Button
          label="View  Details"
          onPress={onViewDetails}
          className="w-full"
        />
      </View>
    </Modal>
  );
};

export default ActiveOrderModalContent;

const MapLocationButton = ({
  userLocation,
  destinationLocation,
}: {
  userLocation: string;
  destinationLocation: string;
}) => {
  return (
    <View className="flex gap-3">
      <View className="flex-row items-center justify-normal gap-2 rounded-full bg-black-0 py-2 pl-2 pr-4">
        <View className="h-fit w-fit rounded-full bg-white p-3">
          <Svg width="20" height="21" viewBox="0 0 20 21" fill="none">
            <G clipPath="url(#clip0_349_2034)">
              <Path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M15.8337 7.99999C15.8337 10.9387 13.6606 13.3699 10.8337 13.7743V17.1667C10.8337 17.6269 10.4606 18 10.0003 18C9.54009 18 9.16699 17.6269 9.16699 17.1667V13.7743C6.34008 13.3699 4.16699 10.9387 4.16699 7.99999C4.16699 4.77833 6.77866 2.16666 10.0003 2.16666C13.222 2.16666 15.8337 4.77833 15.8337 7.99999ZM10.0003 12.1667C12.3015 12.1667 14.167 10.3012 14.167 7.99999C14.167 5.6988 12.3015 3.83332 10.0003 3.83332C7.69914 3.83332 5.83366 5.6988 5.83366 7.99999C5.83366 10.3012 7.69914 12.1667 10.0003 12.1667Z"
                fill="#C00F0C"
              />
              <Path
                d="M10.0003 20.5C10.4606 20.5 10.8337 20.1269 10.8337 19.6667C10.8337 19.2064 10.4606 18.8333 10.0003 18.8333C9.54009 18.8333 9.16699 19.2064 9.16699 19.6667C9.16699 20.1269 9.54009 20.5 10.0003 20.5Z"
                fill="#C00F0C"
              />
            </G>
            <Defs>
              <ClipPath id="clip0_349_2034">
                <Rect
                  width="20"
                  height="20"
                  fill="white"
                  transform="translate(0 0.5)"
                />
              </ClipPath>
            </Defs>
          </Svg>
        </View>
        <View className="flex-1 gap-1">
          <Text className="font-inter text-xs text-[#14232E9E]">
            Your location
          </Text>
          <Text className="font-inter-semibold text-ellipsis text-sm text-black-800">
            {userLocation}
          </Text>
        </View>
      </View>
      <View className="flex-row items-center justify-normal gap-2 rounded-full bg-black-0 py-2 pl-2 pr-4">
        <View className="h-fit w-fit rounded-full bg-white p-3">
          <Svg width="20" height="21" viewBox="0 0 20 21" fill="none">
            <G clipPath="url(#clip0_349_2050)">
              <Path
                d="M10.8337 1.33333C10.8337 1.79357 10.4606 2.16667 10.0003 2.16667C9.54009 2.16667 9.16699 1.79357 9.16699 1.33333C9.16699 0.873096 9.54009 0.5 10.0003 0.5C10.4606 0.5 10.8337 0.873096 10.8337 1.33333Z"
                fill="#009951"
              />
              <Path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M10.0003 18.8333C13.222 18.8333 15.8337 16.2217 15.8337 13C15.8337 10.0613 13.6606 7.6301 10.8337 7.22574V3.83333C10.8337 3.3731 10.4606 3 10.0003 3C9.54009 3 9.16699 3.3731 9.16699 3.83333V7.22574C6.34008 7.6301 4.16699 10.0613 4.16699 13C4.16699 16.2217 6.77866 18.8333 10.0003 18.8333ZM10.0003 17.1667C12.3015 17.1667 14.167 15.3012 14.167 13C14.167 10.6988 12.3015 8.83333 10.0003 8.83333C7.69914 8.83333 5.83366 10.6988 5.83366 13C5.83366 15.3012 7.69914 17.1667 10.0003 17.1667Z"
                fill="#009951"
              />
            </G>
            <Defs>
              <ClipPath id="clip0_349_2050">
                <Rect
                  width="20"
                  height="20"
                  fill="white"
                  transform="translate(0 0.5)"
                />
              </ClipPath>
            </Defs>
          </Svg>
        </View>
        <View className="flex-1 gap-1">
          <Text className="font-inter text-xs text-[#14232E9E]">
            Routing to
          </Text>
          <Text className="font-inter-semibold text-ellipsis text-sm text-black-800">
            {destinationLocation}
          </Text>
        </View>
      </View>
    </View>
  );
};
