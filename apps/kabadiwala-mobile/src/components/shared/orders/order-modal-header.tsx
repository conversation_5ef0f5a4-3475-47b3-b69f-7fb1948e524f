import React from "react";
import { Text, View } from "react-native";
import { But<PERSON> } from "@/components/ui/button";
import { Feather, Ionicons } from "@expo/vector-icons";

import { cn } from "@acme/ui/lib/cn";

interface OrderModalHeaderProps {
  orderId?: string;
  onDismiss: () => void;
  headerText?: string;
  containerClassName?: string;
}

export const OrderModalHeader = ({
  orderId,
  onDismiss,
  headerText,
  containerClassName,
}: OrderModalHeaderProps) => {
  return (
    <View
      className={cn(
        `w-full flex-row items-center justify-between border-b border-black-50 px-1 pb-4 pt-8`,
        containerClassName,
      )}
    >
      <View className="flex-row items-center gap-3">
        <Button className="h-fit w-fit min-w-fit max-w-fit bg-yellow-400 p-2">
          <Ionicons name="chevron-up" size={24} color="black" />
        </Button>
        <View>
          <Text className="font-jakarta-bold text-teal-850">{headerText}</Text>
          <Text className="font-inter-medium text-xs text-black-500">
            Order #{orderId}
          </Text>
        </View>
      </View>
      <Button
        className="h-fit flex-shrink-0 shrink-0 rounded-full bg-black-50 p-[6px]"
        onPress={onDismiss}
      >
        <Feather name="x" size={20} color="black" />
      </Button>
    </View>
  );
};
