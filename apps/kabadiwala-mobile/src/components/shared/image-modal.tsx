import type { BottomSheetModal } from "@gorhom/bottom-sheet";
import { Pressable, Text, View } from "react-native";
import { Image } from "expo-image";
import { ImagePlaceholder } from "@/lib/constant";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";

import { Modal } from "../ui/modal";

const ImageModal = ({
  imageUrl,
  ref,
  dismiss,
  title,
}: {
  imageUrl: string;
  ref: React.RefObject<BottomSheetModal | null>;
  dismiss: () => void;
  title: string;
}) => {
  return (
    <Modal ref={ref} onDismiss={dismiss}>
      <View className="flex-1 p-4">
        <View className="mb-4 flex-row items-center justify-between">
          <Text className="font-jakarta-medium text-lg">{title}</Text>
          <Pressable onPress={dismiss}>
            <MaterialIcons name="close" size={24} color="black" />
          </Pressable>
        </View>

        <View className="flex-1 items-center justify-center">
          <Image
            source={imageUrl}
            style={{ width: "100%", height: 400 }}
            placeholder={ImagePlaceholder}
            contentFit="contain"
          />
        </View>
      </View>
    </Modal>
  );
};

export default ImageModal;
