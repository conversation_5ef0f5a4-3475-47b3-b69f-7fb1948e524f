import React, { useEffect, useRef } from "react";
import { Animated, View } from "react-native";

import { cn } from "@acme/ui/lib/cn";

interface SkeletonLoadingProps {
  containerClassName?: string;
  itemClassName?: string;
  count?: number;
  itemHeight?: string;
  gap?: string;
  type?: "default" | "profile" | "list" | "card" | "details" | "form";
}

const SkeletonLoading: React.FC<SkeletonLoadingProps> = ({
  containerClassName,
  itemClassName,
  count = 3,
  itemHeight = "h-12",
  gap = "mb-2",
  type = "default",
}) => {
  const opacity = useRef(new Animated.Value(0.3)).current;

  useEffect(() => {
    const animation = Animated.loop(
      Animated.sequence([
        Animated.timing(opacity, {
          toValue: 0.6,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(opacity, {
          toValue: 0.3,
          duration: 800,
          useNativeDriver: true,
        }),
      ]),
    );

    animation.start();

    return () => {
      animation.stop();
    };
  }, [opacity]);

  const renderProfileSkeleton = () => {
    return (
      <View className="flex-1">
        <View className="mb-8 flex-row items-center gap-4">
          {/* Avatar */}
          <Animated.View
            className="h-24 w-24 rounded-full bg-gray-200"
            style={{ opacity }}
          />

          {/* Name and info */}
          <View className="flex-1 gap-2">
            <Animated.View
              className="h-6 w-3/4 rounded-lg bg-gray-200"
              style={{ opacity }}
            />
            <Animated.View
              className="h-4 w-1/2 rounded-lg bg-gray-200"
              style={{ opacity }}
            />
          </View>
        </View>

        {/* Form fields */}
        {[...Array(4)].map((_, i) => (
          <View key={i} className="mb-6">
            <Animated.View
              className="mb-2 h-4 w-1/3 rounded-lg bg-gray-200"
              style={{ opacity }}
            />
            <Animated.View
              className="h-12 w-full rounded-lg bg-gray-200"
              style={{ opacity }}
            />
          </View>
        ))}

        {/* Button */}
        <Animated.View
          className="mt-4 h-12 w-full rounded-lg bg-gray-200"
          style={{ opacity }}
        />
      </View>
    );
  };

  const renderListSkeleton = () => {
    return (
      <View className="flex-1">
        {/* Header */}
        <Animated.View
          className="mb-6 h-8 w-1/2 rounded-lg bg-gray-200"
          style={{ opacity }}
        />

        {/* List items */}
        {[...Array(6)].map((_, i) => (
          <View key={i} className="mb-4 flex-row items-center py-2">
            <Animated.View
              className="mr-3 h-16 w-16 rounded-lg bg-gray-200"
              style={{ opacity }}
            />
            <View className="flex-1 gap-2">
              <Animated.View
                className="h-5 w-3/4 rounded-lg bg-gray-200"
                style={{ opacity }}
              />
              <Animated.View
                className="h-4 w-1/2 rounded-lg bg-gray-200"
                style={{ opacity }}
              />
            </View>
          </View>
        ))}
      </View>
    );
  };

  const renderCardSkeleton = () => {
    return (
      <View className="flex-1">
        {/* Grid of cards */}
        <View className="flex-row flex-wrap justify-between">
          {[...Array(4)].map((_, i) => (
            <View key={i} className="mb-4 w-[48%]">
              <Animated.View
                className="h-40 rounded-t-lg bg-gray-200"
                style={{ opacity }}
              />
              <View className="gap-2 p-2">
                <Animated.View
                  className="h-5 w-full rounded-lg bg-gray-200"
                  style={{ opacity }}
                />
                <Animated.View
                  className="h-4 w-2/3 rounded-lg bg-gray-200"
                  style={{ opacity }}
                />
                <Animated.View
                  className="mt-1 h-6 w-1/3 rounded-lg bg-gray-200"
                  style={{ opacity }}
                />
              </View>
            </View>
          ))}
        </View>
      </View>
    );
  };

  const renderDetailsSkeleton = () => {
    return (
      <View className="flex-1">
        {/* Hero image */}
        <Animated.View
          className="mb-4 h-48 w-full rounded-lg bg-gray-200"
          style={{ opacity }}
        />

        {/* Title */}
        <Animated.View
          className="mb-4 h-8 w-3/4 rounded-lg bg-gray-200"
          style={{ opacity }}
        />

        {/* Metadata */}
        <View className="mb-6 flex-row">
          <Animated.View
            className="mr-2 h-5 w-24 rounded-lg bg-gray-200"
            style={{ opacity }}
          />
          <Animated.View
            className="h-5 w-24 rounded-lg bg-gray-200"
            style={{ opacity }}
          />
        </View>

        {/* Description */}
        <View className="mb-6 gap-2">
          <Animated.View
            className="h-4 w-full rounded-lg bg-gray-200"
            style={{ opacity }}
          />
          <Animated.View
            className="h-4 w-full rounded-lg bg-gray-200"
            style={{ opacity }}
          />
          <Animated.View
            className="h-4 w-3/4 rounded-lg bg-gray-200"
            style={{ opacity }}
          />
        </View>

        {/* Additional content */}
        <View className="gap-4">
          <Animated.View
            className="h-24 w-full rounded-lg bg-gray-200"
            style={{ opacity }}
          />
          <Animated.View
            className="h-24 w-full rounded-lg bg-gray-200"
            style={{ opacity }}
          />
        </View>
      </View>
    );
  };

  const renderFormSkeleton = () => {
    return (
      <View className="flex-1">
        {/* Form title */}
        <Animated.View
          className="mb-8 h-8 w-1/2 rounded-lg bg-gray-200"
          style={{ opacity }}
        />

        {/* Form fields */}
        {[...Array(5)].map((_, i) => (
          <View key={i} className="mb-6">
            <Animated.View
              className="mb-2 h-4 w-1/3 rounded-lg bg-gray-200"
              style={{ opacity }}
            />
            <Animated.View
              className="h-12 w-full rounded-lg bg-gray-200"
              style={{ opacity }}
            />
          </View>
        ))}

        {/* Checkbox */}
        <View className="mb-6 flex-row items-center">
          <Animated.View
            className="mr-2 h-5 w-5 rounded bg-gray-200"
            style={{ opacity }}
          />
          <Animated.View
            className="h-4 w-2/3 rounded-lg bg-gray-200"
            style={{ opacity }}
          />
        </View>

        {/* Button */}
        <Animated.View
          className="mt-4 h-12 w-full rounded-lg bg-gray-200"
          style={{ opacity }}
        />
      </View>
    );
  };

  const renderDefaultSkeleton = () => {
    const items = [];
    for (let i = 0; i < count; i++) {
      items.push(
        <Animated.View
          key={i}
          className={cn(
            "w-full rounded-lg bg-gray-200",
            itemHeight,
            gap,
            itemClassName,
          )}
          style={{ opacity }}
        />,
      );
    }
    return <View className="flex-1">{items}</View>;
  };

  const renderSkeleton = () => {
    switch (type) {
      case "profile":
        return renderProfileSkeleton();
      case "list":
        return renderListSkeleton();
      case "card":
        return renderCardSkeleton();
      case "details":
        return renderDetailsSkeleton();
      case "form":
        return renderFormSkeleton();
      default:
        return renderDefaultSkeleton();
    }
  };

  return (
    <View className={cn("flex-1", containerClassName)}>{renderSkeleton()}</View>
  );
};

export default SkeletonLoading;
