import React, { useEffect, useState } from "react";
import { Pressable, Text, TextInput, View } from "react-native";
import { Modal, useModal } from "@/components/ui/modal";
import { Feather, MaterialIcons } from "@expo/vector-icons";

interface RatingBottomSheetProps {
  isVisible: boolean;
  onClose: () => void;
  onSubmit: (rating: number | null, reviewText?: string) => void;
  isLoading?: boolean;
}

const RatingBottomSheet: React.FC<RatingBottomSheetProps> = ({
  isVisible,
  onClose,
  onSubmit,
  isLoading = false,
}) => {
  const [selectedRating, setSelectedRating] = useState<number | null>(null);
  const [reviewText, setReviewText] = useState("");
  const { ref, present, dismiss } = useModal();

  const handleSubmit = () => {
    onSubmit(selectedRating, reviewText.trim() || undefined);
    setSelectedRating(null);
    setReviewText("");
    onClose();
  };

  const handleSkip = () => {
    onSubmit(null, reviewText.trim() || undefined);
    setSelectedRating(null);
    setReviewText("");
    onClose();
  };

  const handleClose = () => {
    setSelectedRating(null);
    setReviewText("");
    onClose();
  };

  const getRatingIcon = (rating: number) => {
    const isSelected = selectedRating === rating;
    const iconProps = {
      size: 36,
      color: isSelected ? "#FFA500" : "#9CA3AF",
    };

    switch (rating) {
      case 1:
        return (
          <MaterialIcons name="sentiment-very-dissatisfied" {...iconProps} />
        );
      case 2:
        return <MaterialIcons name="sentiment-dissatisfied" {...iconProps} />;
      case 3:
        return <MaterialIcons name="sentiment-neutral" {...iconProps} />;
      case 4:
        return <MaterialIcons name="sentiment-satisfied" {...iconProps} />;
      case 5:
        return <MaterialIcons name="sentiment-very-satisfied" {...iconProps} />;
      default:
        return <MaterialIcons name="sentiment-neutral" {...iconProps} />;
    }
  };

  const handleRatingPress = (rating: number) => {
    if (isLoading) return;
    setSelectedRating(rating);
  };

  useEffect(() => {
    if (isVisible) {
      present();
    } else {
      dismiss();
    }
  }, [isVisible, present, dismiss]);

  return (
    <Modal
      ref={ref}
      snapPoints={["75%"]}
      onDismiss={handleClose}
      enablePanDownToClose
    >
      <View className="flex-1 px-6 pb-8">
        {/* Header */}
        <View className="mb-8 items-center pt-4">
          <View className="mb-4 rounded-full bg-teal-50 p-4">
            <Feather name="star" size={32} color="#006666" />
          </View>
          <Text className="font-inter-semibold mb-2 text-center text-xl text-black-800">
            How satisfied are you with the support?
          </Text>
          <Text className="text-center font-inter text-sm text-black-500">
            Your feedback helps us improve our service
          </Text>
        </View>

        {/* Rating */}
        <View className="mb-6 flex-row justify-center">
          {[1, 2, 3, 4, 5].map((rating) => {
            const isSelected = selectedRating === rating;
            return (
              <Pressable
                key={rating}
                onPress={() => handleRatingPress(rating)}
                disabled={isLoading}
                className={`mx-2 rounded-full p-3 ${
                  isSelected ? "bg-orange-50" : "bg-gray-50"
                }`}
                style={
                  isSelected && {
                    borderWidth: 2,
                    borderColor: "#FB923C",
                  }
                }
              >
                {getRatingIcon(rating)}
              </Pressable>
            );
          })}
        </View>

        {/* Review Text */}
        <View className="mb-8">
          <Text className="font-inter-medium mb-3 text-sm text-black-700">
            Write a review (optional)
          </Text>
          <TextInput
            value={reviewText}
            onChangeText={setReviewText}
            placeholder="Share your experience with our support team..."
            multiline
            numberOfLines={4}
            maxLength={500}
            className="rounded-2xl border border-gray-200 bg-gray-50 p-4 text-black-800"
            style={{
              textAlignVertical: "top",
              minHeight: 100,
            }}
            editable={!isLoading}
          />
          <Text className="mt-2 text-right font-inter text-xs text-gray-500">
            {reviewText.length}/500
          </Text>
        </View>

        {/* Action Buttons */}
        <View className="flex-row justify-center gap-4">
          <Pressable
            onPress={handleSubmit}
            disabled={isLoading || selectedRating === null}
            className={`flex-1 rounded-2xl py-4 ${
              selectedRating === null || isLoading
                ? "bg-gray-200"
                : "bg-teal-600"
            }`}
          >
            <Text
              className={`font-inter-semibold text-center text-base ${
                selectedRating === null || isLoading
                  ? "text-gray-500"
                  : "text-white"
              }`}
            >
              {isLoading ? "Submitting..." : "Submit Rating"}
            </Text>
          </Pressable>

          <Pressable
            onPress={handleSkip}
            disabled={isLoading}
            className="flex-1 rounded-2xl border-2 border-gray-200 py-4"
          >
            <Text className="font-inter-semibold text-center text-base text-gray-600">
              Skip
            </Text>
          </Pressable>
        </View>
      </View>
    </Modal>
  );
};

export default RatingBottomSheet;
