import type { RelativePathString } from "expo-router";
import React from "react";
import { Text, TouchableOpacity, View } from "react-native";
import { router } from "expo-router";
import { Feather } from "@expo/vector-icons";

interface ContactSupportCardProps {
  orderId?: string;
}

const ContactSupportCard: React.FC<ContactSupportCardProps> = ({ orderId }) => {
  const handlePress = () => {
    const path = orderId
      ? `/contact-support?orderId=${orderId}`
      : "/contact-support";
    router.push(path as RelativePathString);
  };

  return (
    <TouchableOpacity
      onPress={handlePress}
      className="flex flex-row items-center gap-4 rounded-2xl border border-black-50 bg-black-0 p-4"
    >
      <View className="flex h-12 w-12 items-center justify-center rounded-full bg-teal-50">
        <Feather name="headphones" size={24} color="#006666" />
      </View>
      <View className="flex-1">
        <Text className="font-inter-semibold text-base text-black-800">
          {orderId ? "Need help with this order?" : "Need help?"}
        </Text>
        <Text className="font-inter text-sm text-black-500">
          {orderId
            ? "Contact support for assistance with this order"
            : "Contact our support team for any questions"}
        </Text>
      </View>
      <Feather name="chevron-right" size={20} color="#9CA3AF" />
    </TouchableOpacity>
  );
};

export default ContactSupportCard;
