import type { Coordinates } from "@/types/types";
import React, { useC<PERSON>back, useEffect, useMemo, useRef } from "react";
import { Image, View } from "react-native";
import MapView, { Marker, PROVIDER_GOOGLE } from "react-native-maps";
import useActiveOrder from "@/hooks/use-active-order";
import { useLocationStore } from "@/utils/store";
import OrderIcon from "@assets/icons/order-location.png";
import TruckIcon from "@assets/icons/truck.png";

import { cn } from "@acme/ui/lib/cn";

const HomePageMap = () => {
  const mapRef = useRef<MapView>(null);
  const { location } = useLocationStore();
  const { orderData } = useActiveOrder();

  const orderCoords: Coordinates | null = useMemo(() => {
    return orderData?.address.coordinates
      ? {
          lat: orderData.address.coordinates.latitude,
          lng: orderData.address.coordinates.longitude,
        }
      : null;
  }, [orderData]);

  const userCoords: Coordinates = useMemo(() => {
    return {
      lat: location?.latitude ?? 28.35,
      lng: location?.longitude ?? 78.57,
    };
  }, [location]);

  const fitMapToCoordinates = useCallback(() => {
    if (mapRef.current && orderCoords && userCoords.lat && userCoords.lng) {
      const coordinates = [
        {
          latitude: userCoords.lat,
          longitude: userCoords.lng,
        },
        {
          latitude: orderCoords.lat,
          longitude: orderCoords.lng,
        },
      ];

      mapRef.current.fitToCoordinates(coordinates, {
        edgePadding: {
          top: 50,
          right: 50,
          bottom: 50,
          left: 50,
        },
        animated: true,
      });
    }
  }, [orderCoords, userCoords]);

  useEffect(() => {
    if (orderCoords && userCoords.lat && userCoords.lng) {
      setTimeout(() => {
        fitMapToCoordinates();
      }, 500);
    }
  }, [orderCoords, userCoords, fitMapToCoordinates]);

  const centerCoordinate = orderCoords ?? userCoords;
  const zoomLevel = orderCoords ? 12 : 15;

  return (
    <View className={cn("h-fit min-h-[200px]", orderCoords && "min-h-[500px]")}>
      <MapView
        ref={mapRef}
        provider={PROVIDER_GOOGLE}
        style={{ height: orderCoords ? 500 : 200, width: "100%" }}
        showsUserLocation={true}
        showsMyLocationButton={true}
        camera={{
          center: {
            latitude: centerCoordinate.lat,
            longitude: centerCoordinate.lng,
          },
          zoom: zoomLevel,
          pitch: 0,
          heading: 0,
          altitude: 0,
        }}
        onMapReady={() => {
          if (orderCoords) {
            fitMapToCoordinates();
          }
        }}
      >
        <Marker
          coordinate={{
            latitude: userCoords.lat,
            longitude: userCoords.lng,
          }}
          title="Your Location"
          description="This is your current location"
          tracksViewChanges={false}
        >
          <Image source={TruckIcon} className="size-10" />
        </Marker>
        {orderCoords && (
          <Marker
            coordinate={{
              latitude: orderCoords.lat,
              longitude: orderCoords.lng,
            }}
            title="Active Order Location"
            description="This is the location of your active order"
            tracksViewChanges={false}
          >
            <Image source={OrderIcon} className="size-8" />
          </Marker>
        )}
      </MapView>
    </View>
  );
};

export default HomePageMap;

// Old Implementation for HomePageMap with directions and polyline
{
  /*
    import useActiveOrder from "@/hooks/use-active-order";
import type { Coordinates, PolylinePoint } from "@/types/types";
import { fetchDirections } from "@/utils/functions";
import { useLocationStore } from "@/utils/store";
import OrderIcon from "@assets/icons/order-location.png";
import TruckIcon from "@assets/icons/truck.png";
import React, {
    useCallback,
    useEffect,
    useMemo,
    useRef,
    useState,
} from "react";
import { Image, View } from "react-native";
import MapView, { Marker, Polyline, PROVIDER_GOOGLE } from "react-native-maps";

import { cn } from "@acme/ui/lib/cn";

const HomePageMap = () => {
  const mapRef = useRef<MapView>(null);
  const { location } = useLocationStore();
  const { orderData } = useActiveOrder();
  const [polylineCoords, setPolylineCoords] = useState<PolylinePoint[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const orderCoords: Coordinates | null = useMemo(() => {
    return orderData?.address.coordinates
      ? {
          lat: orderData.address.coordinates.latitude,
          lng: orderData.address.coordinates.longitude,
        }
      : null;
  }, [orderData]);

  const userCoords: Coordinates = useMemo(() => {
    return {
      lat: location?.latitude ?? 28.35,
      lng: location?.longitude ?? 78.57,
    };
  }, [location]);

  const handleFetchDirections = useCallback(
    async (origin: Coordinates, destination: Coordinates): Promise<void> => {
      if (isLoading) return;

      setIsLoading(true);
      try {
        const points = await fetchDirections(origin, destination);
        setPolylineCoords(points);
      } catch (error) {
        console.error("Error in handleFetchDirections:", error);
        setPolylineCoords([]);
      } finally {
        setIsLoading(false);
      }
    },
    [isLoading],
  );

  const fitMapToCoordinates = useCallback(() => {
    if (polylineCoords.length === 0) return;

    if (mapRef.current && orderCoords && userCoords.lat && userCoords.lng) {
      const coordinates = [
        {
          latitude: userCoords.lat,
          longitude: userCoords.lng,
        },
        {
          latitude: orderCoords.lat,
          longitude: orderCoords.lng,
        },
      ];

      mapRef.current.fitToCoordinates(coordinates, {
        edgePadding: {
          top: 50,
          right: 50,
          bottom: 50,
          left: 50,
        },
        animated: true,
      });
    }
  }, [orderCoords, userCoords]);

  useEffect(() => {
    if (orderCoords && userCoords.lat && userCoords.lng) {
      void handleFetchDirections(userCoords, orderCoords);
      // Fit bounds after a short delay to ensure map is ready
      setTimeout(() => {
        fitMapToCoordinates();
      }, 500);
    } else {
      setPolylineCoords([]);
    }
  }, [orderCoords, userCoords, handleFetchDirections, fitMapToCoordinates]);

  const centerCoordinate = orderCoords ?? userCoords;
  const zoomLevel = orderCoords ? 12 : 15;

  return (
    <View className={cn("h-fit min-h-[200px]", orderCoords && "min-h-[500px]")}>
      <MapView
        ref={mapRef}
        provider={PROVIDER_GOOGLE}
        style={{ height: orderCoords ? 500 : 200, width: "100%" }}
        showsUserLocation={true}
        showsMyLocationButton={true}
        camera={{
          center: {
            latitude: centerCoordinate.lat,
            longitude: centerCoordinate.lng,
          },
          zoom: zoomLevel,
          pitch: 0,
          heading: 0,
          altitude: 0,
        }}
        onMapReady={() => {
          if (orderCoords) {
            fitMapToCoordinates();
          }
        }}
      >
        <Marker
          coordinate={{
            latitude: userCoords.lat,
            longitude: userCoords.lng,
          }}
          title="Your Location"
          description="This is your current location"
          tracksViewChanges={false}
        >
          <Image source={TruckIcon} className="size-10" />
        </Marker>
        {orderCoords && (
          <Marker
            coordinate={{
              latitude: orderCoords.lat,
              longitude: orderCoords.lng,
            }}
            title="Active Order Location"
            description="This is the location of your active order"
            tracksViewChanges={false}
          >
            <Image source={OrderIcon} className="size-8" />
          </Marker>
        )}
        {polylineCoords.length > 0 && (
          <Polyline
            coordinates={polylineCoords}
            strokeColor="#007AFF"
            strokeWidth={3}
            lineJoin="round"
            lineCap="round"
          />
        )}
      </MapView>
    </View>
  );
};

export default HomePageMap;

    */
}
