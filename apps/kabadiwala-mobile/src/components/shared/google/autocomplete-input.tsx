import type { DetectedLocation, GoogleLocationResult } from "@/types/types";
import type { TextInputProps, TextStyle, ViewStyle } from "react-native";
import React, { forwardRef, useImperativeHandle } from "react";
import {
  ActivityIndicator,
  I18nManager,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import { useGoogleAutocomplete } from "@/hooks/google-autocomplete";
import AntDesign from "@expo/vector-icons/AntDesign";
import Ionicons from "@expo/vector-icons/Ionicons";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";

import { cn } from "@acme/ui/lib/cn";

export interface AutocompleteInputRef {
  clear: () => void;
  setValue: (value: string) => void;
  getCurrentLocation: () => Promise<DetectedLocation>;
}

export interface AutocompleteInputProps
  extends Omit<TextInputProps, "value" | "onChangeText"> {
  /** Google API Key (optional) */
  apiKey?: string;

  /** Initial value for the input */
  value?: string;

  /** Label shown above the input */
  label?: string;

  /** Placeholder text for the input */
  placeholder?: string;

  /** Additional styling for the container */
  containerStyle?: ViewStyle;

  /** Additional styling for the input */
  inputStyle?: TextStyle;

  /** Additional styling for the suggestions container */
  suggestionsContainerStyle?: ViewStyle;

  /** Additional styling for each suggestion item */
  suggestionItemStyle?: ViewStyle;

  /** Error message */
  error?: string;

  /** Callback when a location is selected from suggestions */
  onLocationSelect?: (location: DetectedLocation) => void;

  /** Callback when current location is detected */
  onCurrentLocationSelect?: (location: DetectedLocation) => void;

  /** Show/hide auto-detect location button */
  showAutoDetect?: boolean;

  /** Custom text for auto-detect button */
  autoDetectText?: string;

  /** Maximum number of suggestions to show */
  maxSuggestions?: number;

  /** Custom render function for suggestion items */
  renderSuggestion?: (
    suggestion: GoogleLocationResult,
    index: number,
  ) => React.ReactNode;

  /** Options for Google Places API */
  googleOptions?: {
    language?: string;
    queryTypes?:
      | "address"
      | "geocode"
      | "(cities)"
      | "establishment"
      | "geocode|establishment";
    debounce?: number;
    components?: string;
    radius?: string;
    lat?: number;
    lng?: number;
    strictBounds?: boolean;
    proxyUrl?: string;
  };
}

const AutocompleteInput = forwardRef<
  AutocompleteInputRef,
  AutocompleteInputProps
>(
  (
    {
      apiKey = "AIzaSyAORVJvt3XFhBe2BqBD34ZZZbbPyG3aSLA",
      value = "",
      label,
      placeholder = "Search for a location...",
      containerStyle,
      inputStyle,
      suggestionsContainerStyle,
      suggestionItemStyle,
      error,
      onLocationSelect,
      onCurrentLocationSelect,
      showAutoDetect = true,
      autoDetectText = "Autodetect my location",
      maxSuggestions = 3,
      renderSuggestion,
      googleOptions = {},
      ...textInputProps
    },
    ref,
  ) => {
    const {
      locationResults,
      setTerm,
      term,
      clearSearch,
      getLocationDetails,
      getCurrentLocation,
      isSearching,
      isLoadingLocation,
    } = useGoogleAutocomplete(apiKey, {
      language: googleOptions.language ?? "en",
      debounce: googleOptions.debounce ?? 300,
      queryTypes: googleOptions.queryTypes ?? "address",
      components: googleOptions.components,
      radius: googleOptions.radius,
      lat: googleOptions.lat,
      lng: googleOptions.lng,
      strictBounds: googleOptions.strictBounds,
      proxyUrl: googleOptions.proxyUrl,
    });

    const [isFocused, setIsFocused] = React.useState(false);

    // Set initial value if provided
    React.useEffect(() => {
      if (value && value !== term) {
        setTerm(value);
      }
    }, [value]);

    // Expose methods via ref
    useImperativeHandle(ref, () => ({
      clear: () => {
        setTerm("");
        clearSearch();
      },
      setValue: (value: string) => {
        setTerm(value);
      },
      getCurrentLocation: async () => {
        return await handleDetectLocation();
      },
    }));

    const handleSelectLocation = async (location: GoogleLocationResult) => {
      try {
        const detailedLocation = await getLocationDetails(location.place_id);

        if (onLocationSelect) {
          onLocationSelect(detailedLocation);
        }

        clearSearch();
      } catch (error) {
        console.error("Error selecting location:", error);
      }
    };

    const handleDetectLocation = async (): Promise<DetectedLocation> => {
      try {
        const userLocation = await getCurrentLocation();

        if (onCurrentLocationSelect) {
          onCurrentLocationSelect(userLocation);
        }

        return userLocation;
      } catch (error) {
        console.error("Error detecting location:", error);
        throw error;
      }
    };

    const renderDefaultSuggestion = (
      suggestion: GoogleLocationResult,
      index: number,
    ) => (
      <TouchableOpacity
        key={suggestion.place_id || index}
        className={cn(
          "flex-row items-center border-b border-gray-100 p-3",
          index === locationResults.length - 1 ? "border-b-0" : "",
        )}
        style={suggestionItemStyle}
        onPress={() => handleSelectLocation(suggestion)}
        activeOpacity={0.7}
      >
        <MaterialIcons
          name="location-on"
          size={20}
          color="#6B7280"
          className="mr-2"
        />
        <View className="flex-1">
          <Text className="text-sm font-medium text-gray-800">
            {suggestion.structured_formatting.main_text}
          </Text>
          <Text className="text-xs text-gray-500">
            {suggestion.structured_formatting.secondary_text}
          </Text>
        </View>
      </TouchableOpacity>
    );

    return (
      <View className="mb-4" style={containerStyle}>
        {label && (
          <Text className="font-jakarta-medium text-text-600 mb-2 text-base font-medium">
            {label}
          </Text>
        )}

        <View
          className={cn(
            "mt-0 flex-row items-center rounded-lg border-[1.2px] border-black-100 bg-black-0",
            isFocused && "border-[1.2px] border-teal-750 bg-white",
            error && "border-red-600",
          )}
        >
          <TextInput
            className="flex-1 px-4 py-[14px] text-base leading-5 text-black-700"
            style={[
              { writingDirection: I18nManager.isRTL ? "rtl" : "ltr" },
              { textAlign: I18nManager.isRTL ? "right" : "left" },
              inputStyle,
            ]}
            value={term}
            onChangeText={setTerm}
            placeholder={placeholder}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            {...textInputProps}
          />
          {isSearching ? (
            <View className="pr-4">
              <ActivityIndicator size="small" color="#3B82F6" />
            </View>
          ) : term.length > 0 ? (
            <TouchableOpacity
              className="pr-4"
              onPress={() => {
                setTerm("");
                clearSearch();
              }}
            >
              <AntDesign name="close" size={16} color="#9CA3AF" />
            </TouchableOpacity>
          ) : null}
        </View>

        {error && (
          <Text className="mt-2 text-sm leading-5 text-red-600">{error}</Text>
        )}

        {locationResults.length > 0 && (
          <View
            className="mt-2 overflow-hidden rounded-xl border border-gray-200 bg-white shadow-sm"
            style={suggestionsContainerStyle}
          >
            {locationResults
              .slice(0, maxSuggestions)
              .map((suggestion, index) =>
                renderSuggestion
                  ? renderSuggestion(suggestion, index)
                  : renderDefaultSuggestion(suggestion, index),
              )}
          </View>
        )}

        {showAutoDetect && (
          <TouchableOpacity
            className="mt-4 flex-row items-center"
            onPress={handleDetectLocation}
            disabled={isLoadingLocation}
            activeOpacity={0.7}
          >
            <Ionicons
              name="location"
              size={16}
              color="#3B82F6"
              className="mr-2"
            />
            <Text className="flex-1 text-sm font-medium text-black-700">
              {isLoadingLocation ? "Detecting location..." : autoDetectText}
            </Text>
            {isLoadingLocation && (
              <ActivityIndicator size="small" color="#3B82F6" />
            )}
          </TouchableOpacity>
        )}
      </View>
    );
  },
);

export default AutocompleteInput;
