import React, { useCallback, useEffect, useRef } from "react";
import { Image, Text, TouchableOpacity, View } from "react-native";
import MapView, { MarkerAnimated, PROVIDER_GOOGLE } from "react-native-maps";
import useActiveOrder from "@/hooks/use-active-order";
import OrderIcon from "@assets/icons/order-location.png";

import Error from "../error";
import SkeletonLoading from "../skeleton-loading";

const ActiveOrderMap = () => {
  const { orderData, isPending, isError, refetchActiveOrder } =
    useActiveOrder();
  const mapRef = useRef<MapView>(null);

  const coords = {
    lat: orderData?.address.coordinates?.latitude,
    lng: orderData?.address.coordinates?.longitude,
  };

  const handleRefreshLocation = useCallback(() => {
    if (coords.lat && coords.lng && mapRef.current) {
      mapRef.current.animateToRegion(
        {
          latitude: coords.lat,
          longitude: coords.lng,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        },
        1000,
      );
    }
  }, [coords.lat, coords.lng]);

  useEffect(() => {
    handleRefreshLocation();
  }, [handleRefreshLocation]);

  if (isPending) {
    return (
      <View className="h-fit min-h-[200px]">
        <SkeletonLoading type="default" count={4} />
      </View>
    );
  }

  if (isError || !coords.lat || !coords.lng) {
    return (
      <View className="h-fit min-h-[200px]">
        <Error
          title="Failed to load map"
          message="Please try again later."
          onRetry={refetchActiveOrder}
        />
      </View>
    );
  }

  return (
    <View className="relative h-fit min-h-[200px]">
      <MapView
        ref={mapRef}
        key={`map-${coords.lat}-${coords.lng}`}
        provider={PROVIDER_GOOGLE}
        style={{ height: 200, width: "100%" }}
        showsUserLocation={true}
        showsMyLocationButton={true}
        initialCamera={{
          center: {
            latitude: coords.lat,
            longitude: coords.lng,
          },
          zoom: 15,
          pitch: 0,
          heading: 0,
          altitude: 0,
        }}
      >
        <MarkerAnimated
          coordinate={{
            latitude: coords.lat,
            longitude: coords.lng,
          }}
        >
          <Image source={OrderIcon} style={{ width: 24, height: 24 }} />
        </MarkerAnimated>
      </MapView>

      <TouchableOpacity
        onPress={handleRefreshLocation}
        className="absolute bottom-2 right-2 rounded-lg bg-teal-850 px-2 py-1 text-white"
      >
        <Text className="text-xs font-medium text-white">Re-Locate</Text>
      </TouchableOpacity>
    </View>
  );
};

export default ActiveOrderMap;
