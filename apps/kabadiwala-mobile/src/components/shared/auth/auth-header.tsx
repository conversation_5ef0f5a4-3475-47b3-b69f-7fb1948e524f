import { Image, Text, View } from "react-native";
import { But<PERSON> } from "@/components/ui/button";
import { handleBack } from "@/utils/functions";
import Logo from "@assets/logo/scraplo.png";
import Ionicons from "@expo/vector-icons/Ionicons";

import { cn } from "@acme/ui/lib/cn";

interface AuthHeaderProps {
  showChangeNumberText?: boolean;
  showBackButton?: boolean;
}

const AuthHeader = ({
  showBackButton = true,
  showChangeNumberText,
}: AuthHeaderProps) => {
  return (
    <View
      className={cn(
        "h-fit flex-row items-center justify-between bg-white",
        !showBackButton && "justify-center",
      )}
    >
      {showBackButton && (
        <Button className="bg-black-50" onPress={handleBack}>
          <Ionicons name="arrow-back" size={20} color="black" />
          {showChangeNumberText && <Text>Change Number</Text>}
        </Button>
      )}

      <Image source={Logo} alt="logo" className="aspect-[87/46] w-[87px]" />
    </View>
  );
};

export default AuthHeader;
