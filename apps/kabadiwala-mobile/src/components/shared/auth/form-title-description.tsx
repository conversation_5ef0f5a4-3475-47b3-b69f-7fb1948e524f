import { Text, View } from "react-native";

interface FormTitleDescriptionProps {
  title: string;
  description: string;
}

const FormTitleDescription = ({
  title,
  description,
}: FormTitleDescriptionProps) => {
  return (
    <View className="flex flex-col gap-3">
      <Text className="font-jakarta text-[22px] font-bold leading-[34px] -tracking-[0.165px] text-teal-950">
        {title}
      </Text>
      <Text className="text-[15px] leading-6 text-black-600">
        {description}
      </Text>
    </View>
  );
};

export default FormTitleDescription;
