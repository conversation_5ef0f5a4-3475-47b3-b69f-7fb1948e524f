import type { Submit<PERSON><PERSON><PERSON> } from "react-hook-form";
import type { z } from "zod";
import React from "react";
import { Text, View } from "react-native";
import { showMessage } from "react-native-flash-message";
import { Link, router } from "expo-router";
import { But<PERSON> } from "@/components/ui/button";
import { ControlledInput } from "@/components/ui/input";
import { trpc } from "@/utils/api";
import AntDesign from "@expo/vector-icons/AntDesign";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import { useForm } from "react-hook-form";

import { KabadiwalaLoginSchema } from "@acme/validators/kabadiwala";

type LoginFormSchema = z.infer<typeof KabadiwalaLoginSchema>;

const LoginForm: React.FC = () => {
  const form = useForm<LoginFormSchema>({
    resolver: zodResolver(KabadiwalaLoginSchema),
    defaultValues: {
      phoneNumber: "",
    },
  });

  const { mutate: requestOtp, isPending } = useMutation(
    trpc.auth.requestOtp.mutationOptions(),
  );

  const onSubmit: SubmitHandler<LoginFormSchema> = (data) => {
    requestOtp(data, {
      onSuccess: (opts) => {
        showMessage({
          message: opts.message,
          description: opts.subMessage,
          type: "success",
        });
        router.push({
          pathname: "/(without-layout)/auth/otp-verification",
          params: { phoneNumber: data.phoneNumber },
        });
      },
      onError: (error) => {
        console.error("Error requesting OTP:", error.message);
        showMessage({
          message: error.message || "Something went wrong",
          type: "danger",
        });
      },
    });
  };

  return (
    <View>
      <ControlledInput
        name="phoneNumber"
        control={form.control}
        label="Your Phone Number"
        placeholder="eg: 9999999999"
        keyboardType="phone-pad"
      />

      <Button
        onPress={form.handleSubmit(onSubmit)}
        className="mt-10 w-full"
        disabled={isPending}
        variant={isPending ? "disabled" : "default"}
      >
        {isPending && (
          <AntDesign
            name="loading1"
            size={16}
            color="black"
            className="mr-2 animate-spin"
          />
        )}
        <Text className="mr-2">Send Code</Text>
        <AntDesign name="arrowright" size={20} color="black" />
      </Button>

      <View className="flex-row items-center justify-center pt-[18px]">
        <Text className="font-jakarta text-sm leading-5 text-gray-600">
          Don&apos;t have an account ?
        </Text>
        <Link href="/(without-layout)/auth/sign-up" dismissTo>
          <Text className="text-gray-850 text-sm font-bold"> Sign up</Text>
        </Link>
      </View>
    </View>
  );
};

export default LoginForm;
