import React from "react";
import { Pressable, Text, View } from "react-native";
import Timer from "@/components/ui/timer";

interface ResendButtonProps {
  otpExpiresAt?: Date;
  otpExpired?: boolean;
  handleResendOtp: () => void;
  isLoading?: boolean;
  onExpire?: () => void;
}

const ResendButton = ({
  otpExpiresAt,
  otpExpired,
  handleResendOtp,
  isLoading = false,
  onExpire,
}: ResendButtonProps) => {
  return (
    <Pressable
      onPress={handleResendOtp}
      disabled={isLoading}
      className="mt-4 flex-row"
    >
      <View className="flex w-fit flex-row items-center gap-1 rounded-2xl bg-[#EAFAEF]/60 px-2.5 py-1">
        <Text className="flex-row text-xs font-semibold text-teal-850 underline">
          {isLoading
            ? "Sending..."
            : otpExpired || !otpExpiresAt
              ? "Request New OTP"
              : "Resend code"}
        </Text>
        {!otpExpired && !isLoading && otpExpiresAt && (
          <>
            <Text className="text-xs text-teal-850">, otp expires in</Text>
            <Timer
              expiryDate={otpExpiresAt}
              onExpire={() => Promise.resolve(onExpire?.())}
            />
          </>
        )}
      </View>
    </Pressable>
  );
};

export default ResendButton;
