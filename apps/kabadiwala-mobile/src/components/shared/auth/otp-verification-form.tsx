import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "react-hook-form";
import type { z } from "zod";
import React, { useState } from "react";
import { Pressable, Text, View } from "react-native";
import { showMessage } from "react-native-flash-message";
import { OtpInput } from "react-native-otp-entry";
import { Link, router } from "expo-router";
import { Button } from "@/components/ui/button";
import { trpc } from "@/utils/api";
import { authClient } from "@/utils/auth";
import { useFirstTimeStore } from "@/utils/store";
import AntDesign from "@expo/vector-icons/AntDesign";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import { useForm } from "react-hook-form";

import { OTP_EXPIRATION_TIME_IN_SECONDS } from "@acme/kabadiwala-auth/constants";
import { KabadiwalaOtpSchema } from "@acme/validators/kabadiwala";

import ResendButton from "./resend-button";

type OtpFormSchema = z.infer<typeof KabadiwalaOtpSchema>;

interface OtpVerificationFormProps {
  phoneNumber: string;
}

const OtpVerificationForm = ({ phoneNumber }: OtpVerificationFormProps) => {
  const { setNotFirstTime } = useFirstTimeStore();

  const [requestResendOtp, setRequestResendOtp] = useState(false);
  const [isOtpExpired, setIsOtpExpired] = useState(false);
  const [otpExpiryDate, setOtpExpiryDate] = useState(
    () => new Date(Date.now() + OTP_EXPIRATION_TIME_IN_SECONDS * 1000),
  );

  const form = useForm<OtpFormSchema>({
    resolver: zodResolver(KabadiwalaOtpSchema),
    defaultValues: {
      otp: "",
      phoneNumber: phoneNumber,
    },
  });

  const resetOtpTimer = () => {
    setIsOtpExpired(false);
    setOtpExpiryDate(
      new Date(Date.now() + OTP_EXPIRATION_TIME_IN_SECONDS * 1000),
    );
  };

  const { mutate: verifyOtp, isPending } = useMutation(
    trpc.auth.verifyOtp.mutationOptions(),
  );

  const resendOtpMutation = useMutation(
    trpc.auth.resendOtp.mutationOptions({
      onSuccess: () => {
        setRequestResendOtp(true);
        showMessage({
          message: "OTP resent successfully",
          description: "Please check your SMS",
          type: "success",
        });
      },
      onError: (error) => {
        // Handle max retry case - request new OTP
        if (error.message === "OTP retry count maxed out") {
          showMessage({
            message: "Max retries reached",
            description: "Requesting new OTP...",
            type: "info",
          });
          requestNewOtpMutation.mutate({ phoneNumber });
        } else {
          showMessage({
            message: "Failed to resend OTP",
            description: error.message,
            type: "danger",
          });
        }
      },
    }),
  );

  const requestNewOtpMutation = useMutation(
    trpc.auth.requestOtp.mutationOptions({
      onSuccess: () => {
        setRequestResendOtp(false);
        form.reset(); // Clear the OTP input
        form.setValue("phoneNumber", phoneNumber); // Keep phone number
        showMessage({
          message: "New OTP sent",
          description: "Please check your SMS",
          type: "success",
        });
        resetOtpTimer();
      },
      onError: (error) => {
        showMessage({
          message: "Failed to request new OTP",
          description: error.message,
          type: "danger",
        });
      },
    }),
  );

  const handleResendOtp = () => {
    // If OTP is expired, directly request new OTP
    if (isOtpExpired) {
      showMessage({
        message: "OTP expired",
        description: "Requesting new OTP...",
        type: "info",
      });
      requestNewOtpMutation.mutate({ phoneNumber });
    } else {
      // OTP is still valid, try to resend existing one (retry logic handles max attempts)
      resendOtpMutation.mutate({ phoneNumber });
    }
  };
  const onSubmit: SubmitHandler<OtpFormSchema> = async (data) => {
    const otp = form.getValues("otp");
    if (!otp || otp.length !== 6) {
      showMessage({
        message: "Invalid OTP",
        description: "OTP must be a 6-digit number",
        type: "danger",
      });
      form.setError("otp", {
        type: "manual",
        message: "Please enter a valid 6-digit OTP",
      });
      return;
    }
    await authClient.phoneNumber.verify(
      { phoneNumber, code: otp },
      {
        onSuccess: ({ response }) => {
          setNotFirstTime();
          showMessage({
            message: "OTP Verified",
            description: "You are now logged in",
            type: "success",
          });
          router.replace("/(without-layout)/success");
        },
        onError: (error) => {
          showMessage({
            message: "OTP Verification Failed",
            description: error.error.message,
            type: "danger",
          });
          form.setError("otp", {
            type: "manual",
            message: error.error.message,
          });
        },
      },
    );
  };

  return (
    <View>
      <View>
        <Text className="font-jakarta-medium mb-2 text-[13px]">Enter OTP</Text>
        <OtpInput
          autoFocus={true}
          numberOfDigits={6}
          focusColor={"#007F80"}
          hideStick={true}
          placeholder="000000"
          blurOnFilled={true}
          type="numeric"
          focusStickBlinkingDuration={500}
          textInputProps={{
            accessibilityLabel: "One-Time Password",
          }}
          onTextChange={(text) => form.setValue("otp", text)}
          theme={{
            pinCodeContainerStyle: {
              borderRadius: 8,
              borderColor: form.formState.errors.otp ? "#FF0000" : "#E6E6E6",
              backgroundColor: "#F8F8F8",
              borderWidth: 1.2,
              width: 48,
              height: 56,
            },
            pinCodeTextStyle: {
              color: "#333333",
              fontSize: 16,
              lineHeight: 20,
            },
            placeholderTextStyle: {
              color: "#B3B3B3",
              fontSize: 16,
            },
          }}
        />
        {form.formState.errors.otp && (
          <Text className="font-jakarta-regular mt-2 text-xs text-red-500">
            {form.formState.errors.otp.message}
          </Text>
        )}
        <ResendButton
          otpExpired={isOtpExpired}
          otpExpiresAt={otpExpiryDate}
          handleResendOtp={handleResendOtp}
          isLoading={
            resendOtpMutation.isPending || requestNewOtpMutation.isPending
          }
          onExpire={() => setIsOtpExpired(true)}
        />
        {/* Wrong Number link - shown only after resending */}
        {requestResendOtp && (
          <Pressable
            onPress={() => router.back()}
            className="mt-2 flex-row justify-center"
          >
            <Text className="text-xs font-medium text-black-800">
              Wrong Number?
            </Text>
          </Pressable>
        )}
      </View>
      <Button
        onPress={form.handleSubmit(onSubmit, (error) => {
          console.log("submit handler error---", error);
        })}
        className="mt-10 w-full"
        variant={isPending ? "disabled" : "default"}
        disabled={isPending}
      >
        {isPending && (
          <AntDesign
            name="loading1"
            size={16}
            color="black"
            className="mr-2 animate-spin"
          />
        )}
        <Text>Confirm Code</Text>
      </Button>
      <View className="flex-row items-center justify-center pt-[18px]">
        <Text className="font-jakarta-regular text-sm leading-5 text-gray-600">
          Didn't get the code?{" "}
        </Text>
        <Link href="/(without-layout)/auth/login" dismissTo>
          <Text className="text-gray-850 font-jakarta-bold text-sm font-bold">
            Check SMS or try resending
          </Text>
        </Link>
      </View>
    </View>
  );
};

export default OtpVerificationForm;
