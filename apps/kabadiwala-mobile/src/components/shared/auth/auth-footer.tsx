import { Text, View } from "react-native";

interface AuthFooterProps {
  hideFooter?: boolean;
}

const AuthFooter = ({ hideFooter }: AuthFooterProps) => {
  if (hideFooter) {
    return null;
  }

  return (
    <View className="flex h-fit flex-row flex-wrap items-center justify-center bg-white text-center text-xs leading-5 text-black-600">
      <Text> By proceeding, you agree to our </Text>
      <Text className="font-bold text-teal-750">Terms of Service</Text>
      <Text> and </Text>
      <Text className="font-bold text-teal-750">Privacy Policy</Text>
    </View>
  );
};

export default AuthFooter;
