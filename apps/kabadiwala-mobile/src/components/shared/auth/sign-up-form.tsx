import type { Submit<PERSON><PERSON><PERSON> } from "react-hook-form";
import type { z } from "zod";
import React, { useState } from "react";
import { Text, View } from "react-native";
import { showMessage } from "react-native-flash-message";
import { Link, router } from "expo-router";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { ControlledInput } from "@/components/ui/input";
import { authClient } from "@/utils/auth";
import AntDesign from "@expo/vector-icons/AntDesign";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";

import { KabadiwalaSignupSchema } from "@acme/validators/kabadiwala";

type SignUpFormSchema = z.infer<typeof KabadiwalaSignupSchema>;

const SignUpForm: React.FC = () => {
  const [isPending, setIsPending] = useState(false);
  const form = useForm<SignUpFormSchema>({
    resolver: zodResolver(KabadiwalaSignupSchema),
    defaultValues: {
      phoneNumber: "",
      whatsappConsent: false,
    },
  });

  const onSubmit: SubmitHandler<SignUpFormSchema> = (data) => {
    setIsPending(true);
    authClient.phoneNumber
      .sendOtp(
        {
          phoneNumber: data.phoneNumber,
        },
        {
          onSuccess: (opts) => {
            showMessage({
              message: "OTP Sent",
              description: "Please check your phone for the OTP.",
              type: "success",
            });
            router.push({
              pathname: "/(without-layout)/auth/otp-verification",
              params: { phoneNumber: data.phoneNumber },
            });
          },
          onError: (error) => {
            console.error("Error requesting OTP:", error.error.message);
            showMessage({
              message: "Error",
              description: error.error.message,
              type: "danger",
            });
          },
        },
      )
      .catch((error) => {
        showMessage({
          message: "Error",
          description: "Something went wrong",
          type: "danger",
        });
      });
    setIsPending(false);
  };

  return (
    <View>
      <ControlledInput
        name="phoneNumber"
        control={form.control}
        label="Your Phone Number"
        placeholder="eg: 9999999999"
        keyboardType="phone-pad"
      />
      <View className="flex flex-row gap-2">
        <Checkbox
          accessibilityLabel="Remember me"
          checked={form.watch("whatsappConsent")}
          onChange={(value) => {
            form.setValue("whatsappConsent", value);
          }}
        />
        <Text>
          Receive updates and info via{" "}
          <Text className="font-semibold">WhatsApp</Text>
        </Text>
      </View>

      <Button
        onPress={form.handleSubmit(onSubmit)}
        className="mt-10 w-full"
        disabled={isPending}
        variant={isPending ? "disabled" : "default"}
      >
        {isPending && (
          <AntDesign
            name="loading1"
            size={16}
            color="black"
            className="mr-2 animate-spin"
          />
        )}
        <Text className="mr-2">Send Code</Text>
        <AntDesign name="arrowright" size={20} color="black" />
      </Button>

      <View className="flex-row items-center justify-center pt-[18px]">
        <Text className="font-jakarta text-sm leading-5 text-gray-600">
          Already have an account ?
        </Text>
        <Link href="/(without-layout)/auth/login" dismissTo>
          <Text className="text-gray-850 text-sm font-bold"> Log in</Text>
        </Link>
      </View>
    </View>
  );
};

export default SignUpForm;
