import React, { useCallback, useEffect } from "react";
import { useLocation } from "@/hooks/use-location";
import { GET_USER_LIVE_LOCATION_INTERVAL } from "@/lib/constant";
import { trpc } from "@/utils/api";
import { useMutation } from "@tanstack/react-query";
import { useDebounce } from "use-debounce";

interface LocationUpdatorProps {
  children: React.ReactNode;
}

const LocationUpdator = ({ children }: LocationUpdatorProps) => {
  const { getCurrentLocation } = useLocation();

  const { mutate: updateLiveLocation } = useMutation(
    trpc.user.updateLiveLocationWithOrWithoutOrderId.mutationOptions(),
  );

  const updateLocation = useCallback(async () => {
    try {
      const detectedLocation = await getCurrentLocation();
      if (detectedLocation) {
        updateLiveLocation({
          coordinates: {
            latitude: detectedLocation.latitude,
            longitude: detectedLocation.longitude,
          },
        });
      }
    } catch (error) {
      console.error("Failed to update location:", error);
    }
  }, [getCurrentLocation, updateLiveLocation]);

  const [debouncedUpdateLocation] = useDebounce(updateLocation, 500);

  useEffect(() => {
    // this is for initial location update
    // no need to worry about multiple calls for this function becoz it will only run once then set-interval calls it-self again and again without disturbing the initial call
    void debouncedUpdateLocation();

    const intervalId = setInterval(() => {
      void debouncedUpdateLocation();
    }, GET_USER_LIVE_LOCATION_INTERVAL);

    return () => clearInterval(intervalId);
  }, [debouncedUpdateLocation]);

  return <>{children}</>;
};

export default LocationUpdator;
