import type {
  Call,
  StreamVideoClient,
} from "@stream-io/video-react-native-sdk";
import React, { createContext, useContext, useEffect, useState } from "react";
import { useLocalSearchParams } from "expo-router";
import useActiveOrder from "@/hooks/use-active-order";
import { getStreamClient } from "@/lib/stream";
import { trpc } from "@/utils/api";
import { authClient } from "@/utils/auth";
import { skipToken, useQuery } from "@tanstack/react-query";

interface VideoContextType {
  call: Call | null;
  streamClient: StreamVideoClient | null;
  loading: boolean;
  error: string | null;
  retryCall: () => void;
}

const VideoContext = createContext<VideoContextType | undefined>(undefined);

export const useVideo = () => {
  const context = useContext(VideoContext);
  if (!context) {
    throw new Error("useVideo must be used within a VideoProvider");
  }
  return context;
};

const VideoProvider = ({ children }: { children: React.ReactNode }) => {
  const { orderData } = useActiveOrder();
  const { recipientId } = useLocalSearchParams();
  const { data: session, isPending } = authClient.useSession();
  const [call, setCall] = useState<Call | null>(null);
  const [streamClient, setStreamClient] = useState<StreamVideoClient | null>(
    null,
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const callerId = session?.user.id;
  const orderId = orderData?.id;
  const recipientIdString =
    typeof recipientId === "string" ? recipientId : null;

  const { data: callData, isPending: isPendingCall } = useQuery(
    trpc.streamCall.createCall.queryOptions(
      callerId && recipientIdString && orderId
        ? {
            callerId: callerId,
            recipientId: recipientIdString,
            orderId: orderId,
          }
        : skipToken,
    ),
  );

  useEffect(() => {
    if (!session || isPending) return;

    try {
      if (typeof recipientId !== "string") {
        return;
      }

      const client = getStreamClient({
        user: {
          id: session.user.id,
          name: session.user.name || "Unknown",
        },
        token: session.user.streamToken || "",
      });

      if (!callData?.callId) {
        return;
      }

      setStreamClient(client);

      const call = client.call("default", callData.callId, {
        reuseInstance: false,
      });

      const setupCall = async () => {
        try {
          await call.join({
            ring: true,
            video: false,
            members_limit: 2,
          });
          setCall(call);
          setError(null);
        } catch (err) {
          console.error("Call setup failed:", err);
          setError("Failed to join call. Please try again.");
        } finally {
          setLoading(false);
        }
      };

      void setupCall();

      return () => {
        if (call) {
          call.leave().catch(console.warn);
        }
      };
    } catch (err) {
      console.error("Setup error:", err);
      setError("Failed to initialize call");
      setLoading(false);
    }
  }, [session, isPending, recipientId, callData?.callId]);

  const retryCall = () => {
    setLoading(true);
    setError(null);
  };

  return (
    <VideoContext.Provider
      value={{
        call,
        streamClient,
        loading,
        error,
        retryCall,
      }}
    >
      {children}
    </VideoContext.Provider>
  );
};

export default VideoProvider;
