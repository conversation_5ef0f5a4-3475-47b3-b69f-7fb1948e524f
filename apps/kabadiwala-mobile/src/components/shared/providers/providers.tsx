import React from "react";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { KeyboardProvider } from "react-native-keyboard-controller";
import { ToastProvider } from "@/components/ui/toast-provider";
import { Env } from "@/lib/env";
import { queryClient } from "@/utils/api";
import { authClient } from "@/utils/auth";
import { BottomSheetModalProvider } from "@gorhom/bottom-sheet";
import {
  StreamVideo,
  StreamVideoClient,
} from "@stream-io/video-react-native-sdk";
import { QueryClientProvider } from "@tanstack/react-query";
import { PostHogProvider } from "posthog-react-native";

import RingingCalls from "../stream/ringing-calls";

interface ProvidersProps {
  children: React.ReactNode;
}

const Providers = ({ children }: ProvidersProps) => {
  const { data: session } = authClient.useSession();

  if (!session)
    return (
      <PostHogProvider
        apiKey="phc_NnFHWJxTqm6KKEXTnqYVmqqglqlVnMLf4ORMKnv0woI"
        options={{
          host: "https://eu.i.posthog.com",
        }}
      >
        <QueryClientProvider client={queryClient}>
          <ToastProvider>
            <GestureHandlerRootView style={{ flex: 1 }}>
              <KeyboardProvider enabled>
                <BottomSheetModalProvider>{children}</BottomSheetModalProvider>
              </KeyboardProvider>
            </GestureHandlerRootView>
          </ToastProvider>
        </QueryClientProvider>
      </PostHogProvider>
    );

  const user = {
    id: session.user.id,
    name: session.user.name,
  };

  const client = StreamVideoClient.getOrCreateInstance({
    apiKey: Env.GET_STREAM_API_KEY,
    tokenProvider: async () => Promise.resolve(session.user.streamToken),
    user,
  });

  return (
    <PostHogProvider
      apiKey="phc_NnFHWJxTqm6KKEXTnqYVmqqglqlVnMLf4ORMKnv0woI"
      options={{
        host: "https://eu.i.posthog.com",
      }}
    >
      <QueryClientProvider client={queryClient}>
        <ToastProvider>
          <GestureHandlerRootView style={{ flex: 1 }}>
            <KeyboardProvider enabled>
              <BottomSheetModalProvider>
                <StreamVideo client={client}>
                  {children}
                  <RingingCalls />
                </StreamVideo>
              </BottomSheetModalProvider>
            </KeyboardProvider>
          </GestureHandlerRootView>
        </ToastProvider>
      </QueryClientProvider>
    </PostHogProvider>
  );
};

export default Providers;
