import { showMessage } from "react-native-flash-message";
import useOnDuty from "@/hooks/use-on-duty";
import { trpc } from "@/utils/api";
import AntDesign from "@expo/vector-icons/AntDesign";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

import { Switch } from "../ui/checkbox";

const DutySwitch = () => {
  const queryClient = useQueryClient();

  const { isOnDuty } = useOnDuty();

  const { data: workHoursData } = useQuery(
    trpc.user.getWorkHoursSettings.queryOptions(),
  );

  const { mutate: toggleDuty, isPending } = useMutation(
    trpc.user.toggleDuty.mutationOptions({
      onSuccess: async (opts) => {
        await queryClient.invalidateQueries(
          trpc.user.getDutyStatus.queryFilter(),
        );
        showMessage({
          message: opts.message,
          type: "success",
        });
      },
      onError: (error) => {
        showMessage({
          message: error.message,
          type: "danger",
        });
      },
    }),
  );

  const handleChange = () => {
    if (workHoursData?.workHoursMode === "AUTOMATIC") {
      showMessage({
        message: "Switch to manual mode to control duty status manually",
        type: "danger",
      });
      return;
    }
    toggleDuty({ onDuty: !isOnDuty });
  };

  const isDisabled = workHoursData?.workHoursMode === "AUTOMATIC";

  return (
    <>
      {isPending ? (
        <AntDesign
          name="loading1"
          size={18}
          color="black"
          className="animate-spin"
        />
      ) : (
        <Switch
          checked={isOnDuty}
          onChange={handleChange}
          disabled={isDisabled}
          accessibilityLabel="Toggle switch"
        />
      )}
    </>
  );
};

export default DutySwitch;
