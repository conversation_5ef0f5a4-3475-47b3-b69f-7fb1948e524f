import type { BottomTabBarProps } from "@react-navigation/bottom-tabs";
import type { SvgProps } from "react-native-svg";
import React from "react";
import { Text, TouchableOpacity, View } from "react-native";
import HomeIcon from "@assets/icons/home.svg";
import OrdersIcon from "@assets/icons/orders.svg";

interface CustomTabButtonProps {
  label: string;
  icon: React.FC<SvgProps>; // Changed to React.FC for functional component
  isFocused: boolean;
  onPress: () => void;
}
const CustomTabButton = ({
  label,
  icon: Icon, // Renaming icon to Icon to use it as a component
  isFocused,
  onPress,
}: CustomTabButtonProps) => {
  return (
    <TouchableOpacity
      activeOpacity={0.6}
      onPress={onPress}
      className="items-center gap-2 self-center py-[14px]"
    >
      <View
        className="rounded-3xl px-4 py-[6px]"
        style={{ backgroundColor: isFocused ? "#FBD41D" : "white" }}
      >
        <Icon />
      </View>
      <Text className="font-jakarta text-[13px] font-semibold leading-[18px] tracking-[0.13px] text-black-900">
        {label}
      </Text>
    </TouchableOpacity>
  );
};

const BottomTabBar = ({
  state,
  descriptors,
  navigation,
}: BottomTabBarProps) => {
  return (
    <View className="-pb-safe-offset-4 px-safe-offset-7 h-fit flex-row items-center justify-around gap-2 border-t border-black-100 bg-white">
      {state.routes.map((route, index) => {
        const options = descriptors[route.key]?.options;

        const label = options?.title ?? route.name;
        const isFocused = state.index === index;
        const onPress = () => {
          const event = navigation.emit({
            type: "tabPress",
            target: route.key,
            canPreventDefault: true,
          });

          if (!isFocused && !event.defaultPrevented) {
            navigation.navigate(route.name);
          }
        };

        let Icon: React.FC<SvgProps> = HomeIcon; // Default icon
        if (route.name === "home/index") {
          Icon = HomeIcon;
        } else if (route.name === "orders/index") {
          Icon = OrdersIcon;
        }

        return (
          <CustomTabButton
            key={route.key}
            label={label}
            icon={Icon}
            isFocused={isFocused}
            onPress={onPress}
          />
        );
      })}
    </View>
  );
};
export default BottomTabBar;
