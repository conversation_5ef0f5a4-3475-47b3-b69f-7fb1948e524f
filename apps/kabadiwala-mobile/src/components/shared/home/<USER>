import type { DrawerNavigationProp } from "@react-navigation/drawer";
import type { ParamListBase } from "@react-navigation/native";
import { Image, Pressable, Text, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import useOnDuty from "@/hooks/use-on-duty";
import useProfile from "@/hooks/use-profile";
import UserIcon from "@assets/icons/user.png";
import { AntDesign } from "@expo/vector-icons";
import { DrawerActions } from "@react-navigation/native";

import DutySwitch from "../duty-switch";

const HomeScreenHeader = ({
  navigation,
}: {
  navigation: DrawerNavigationProp<ParamListBase, string, undefined>;
}) => {
  const { data: user, isPending, isError } = useProfile();
  const { isOnDuty } = useOnDuty();

  const toggleDrawer = () => {
    navigation.dispatch(DrawerActions.toggleDrawer());
  };

  if (isPending) {
    return (
      <SafeAreaView className="-pb-safe-offset-20 bg-white">
        <View>
          <View className="flex-row items-center justify-between px-4 py-2">
            <View className="h-16 w-16 rounded-full border-2 border-yellow-500" />
            <View className="h-10 w-24 rounded-full bg-gray-200" />
          </View>
          <View className="h-10 w-full bg-gray-200" />
        </View>
      </SafeAreaView>
    );
  }

  if (isError) {
    return (
      <SafeAreaView className="-pb-safe-offset-20 bg-white">
        <View className="flex-row items-center justify-center px-4 py-4">
          <Text className="font-jakarta-semibold text-center font-semibold text-red-500">
            Failed to load profile
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="-pb-safe-offset-20 bg-white">
      <View className="flex-row items-center justify-between px-4 py-2">
        {/* profile image - now a Pressable to open drawer */}
        <Pressable
          className="h-fit w-fit justify-center"
          onPress={toggleDrawer}
        >
          <View className="relative h-16 w-16 items-center justify-center rounded-full border-2 border-yellow-500 p-4">
            {user.image ? (
              <Image
                source={{ uri: user.image }}
                className="h-12 w-12 rounded-full object-cover"
              />
            ) : (
              <Image
                source={UserIcon}
                alt="not found"
                className="h-12 w-12 rounded-full object-cover"
              />
            )}
          </View>
          <View className="absolute -bottom-1 left-4 h-fit w-fit flex-row items-center justify-center gap-1 rounded-3xl bg-black-900 px-[6px] py-[3px]">
            <AntDesign name="star" size={8} color="yellow" />
            <Text className="font-inter-regular text-[10px] font-semibold leading-[10px] tracking-[0.1px] text-white">
              {Number(user.averageRating ?? 0).toFixed(1)}
            </Text>
          </View>
        </Pressable>

        <View
          className={`flex-row items-center justify-center gap-3 rounded-3xl border px-4 py-3 ${isOnDuty ? "border-[#009951] bg-[#EFFFF1]" : "border-[rgba(0,0,0,0.20)] bg-black-0"}`}
        >
          <Text className="font-inter-medium text-[13px] font-medium leading-[18px] text-teal-900">
            {isOnDuty ? " On Duty" : "Off Duty"}
          </Text>
          <DutySwitch />
        </View>
      </View>
      <Text className="bg-black-0 px-6 py-3 text-center text-[10px] leading-4">
        {isOnDuty
          ? "You are currently Online. soon you will receive pickup orders!"
          : "You are currently Offline. slide on off duty to receive orders!"}
      </Text>
    </SafeAreaView>
  );
};
export default HomeScreenHeader;
