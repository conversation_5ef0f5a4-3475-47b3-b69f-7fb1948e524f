import React from "react";
import { Text, View } from "react-native";
import { trpc } from "@/utils/api";
import { useQuery } from "@tanstack/react-query";

const TodaysEarning = () => {
  const { data } = useQuery(trpc.wallet.getTodayEarnings.queryOptions());

  return (
    <View className="flex-row items-center justify-between gap-2 self-stretch bg-teal-950 px-6 py-3">
      <Text className="text-xs font-medium text-black-150">
        Today's Earning
      </Text>
      <Text className="font-jakarta-regular text-sm font-extrabold text-yellow-500">
        ₹{data?.todayEarnings ?? "0"}
      </Text>
    </View>
  );
};

export default TodaysEarning;
