import { View } from "react-native";
import { Text } from "react-native-gesture-handler";
import { Image } from "expo-image";
import { AntDesign } from "@expo/vector-icons";

const ProfileButton = () => {
  return (
    <View>
      <View>
        <Image source={require("@assets/logo/scraplo.svg")} />
      </View>
      <View>
        <AntDesign name="star" size={24} color="black" />
        <Text className="font-jakarta text-[13px] font-semibold leading-[18px] tracking-[0.13px] text-black-900">
          4.5
        </Text>
      </View>
    </View>
  );
};
