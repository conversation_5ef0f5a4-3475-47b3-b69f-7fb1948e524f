import React from "react";
import { Text, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { router } from "expo-router";
import { Button } from "@/components/ui/button";
import { Ionicons } from "@expo/vector-icons";

interface PaymentHeaderProps {
  title: string;
}

const PaymentHeader = ({ title }: PaymentHeaderProps) => {
  return (
    <SafeAreaView className="-pb-safe-offset-20 flex-row items-center justify-between bg-white">
      {/* Header with back button */}
      <View className="flex-row items-center justify-between px-5 py-3">
        <View className="w-fit flex-1 flex-row items-center gap-2">
          <Button
            variant="loading"
            className="bg-black-0"
            onPress={() => router.dismissTo("/")}
          >
            <Ionicons name="chevron-back" size={24} color="#000" />
          </Button>
          <View className="">
            <Text className="font-jakarta-semibold text-start text-lg text-teal-800">
              {title}
            </Text>
          </View>
        </View>
        <Button className="h-fit w-fit bg-[#0033330A]">
          <Text className="">Help</Text>
        </Button>
      </View>
    </SafeAreaView>
  );
};

export default PaymentHeader;
