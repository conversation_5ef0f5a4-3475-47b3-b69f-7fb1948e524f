import React, { useEffect, useState } from "react";
import { Text } from "react-native";

interface TimerProps {
  targetDate: Date;
  onExpire?: () => void;
  className?: string;
}

export const Timer: React.FC<TimerProps> = ({
  targetDate,
  onExpire,
  className,
}) => {
  const [timeLeft, setTimeLeft] = useState<{
    minutes: number;
    seconds: number;
  }>({ minutes: 0, seconds: 0 });
  const [isExpired, setIsExpired] = useState(false);

  useEffect(() => {
    const calculateTimeLeft = () => {
      const now = new Date().getTime();
      const target = targetDate.getTime();
      const difference = target - now;

      if (difference > 0) {
        const minutes = Math.floor(
          (difference % (1000 * 60 * 60)) / (1000 * 60),
        );
        const seconds = Math.floor((difference % (1000 * 60)) / 1000);

        setTimeLeft({ minutes, seconds });
        setIsExpired(false);
      } else {
        setTimeLeft({ minutes: 0, seconds: 0 });
        if (!isExpired) {
          setIsExpired(true);
          onExpire?.();
        }
      }
    };

    // Calculate immediately
    calculateTimeLeft();

    // Set up interval to update every second
    const interval = setInterval(calculateTimeLeft, 1000);

    // Cleanup interval on unmount
    return () => clearInterval(interval);
  }, [targetDate, onExpire, isExpired]);

  return (
    <Text className={`w-fit ${className ?? ""}`}>
      {timeLeft.minutes} min : {timeLeft.seconds.toString().padStart(2, "0")}{" "}
      sec
    </Text>
  );
};
