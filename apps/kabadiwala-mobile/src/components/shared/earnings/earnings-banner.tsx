import React from "react";
import { ImageBackground, Text, View } from "react-native";
import { trpc } from "@/utils/api";
import EarningsBackground from "@assets/images/wallet-background.png";
import { useQuery } from "@tanstack/react-query";

import Error from "../error";
import SkeletonLoading from "../skeleton-loading";

const TotalEarningsBanner = () => {
  const { data, isPending, isError, refetch } = useQuery(
    trpc.wallet.getTotalEarning.queryOptions(),
  );

  if (isPending) {
    return (
      <View className="">
        <SkeletonLoading type="default" />
      </View>
    );
  }

  if (isError) {
    return (
      <View className="">
        <Error message="Failed to load earnings" onRetry={refetch} />
      </View>
    );
  }

  return (
    <View>
      <ImageBackground
        source={EarningsBackground}
        className="flex h-[135px] flex-col items-center justify-center gap-2 rounded-xl px-6 py-5"
        imageStyle={{ borderRadius: 12 }}
        resizeMode="cover"
      >
        <Text className="text-sm font-medium leading-5 text-white">
          Total Earnings
        </Text>
        <Text className="text-[40px] font-extrabold leading-10 text-white">
          ₹{data?.totalEarnings}
        </Text>
      </ImageBackground>
    </View>
  );
};

export default TotalEarningsBanner;
