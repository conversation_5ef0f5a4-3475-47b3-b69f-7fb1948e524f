import React from "react";
import { Text, TouchableOpacity, View } from "react-native";
import { Path, Svg } from "react-native-svg";
import { getVehicleIcon } from "@/utils/functions";
import Feather from "@expo/vector-icons/Feather";

import { cn } from "@acme/ui/cn";

interface VehicleCardProps {
  vehicleName: string;
  vehicleNumber: string;
  vehicleType: string;
  isDefault?: boolean;
  onSetDefault?: () => void;
  onOptionsPress?: () => void;
  onDeletePress?: () => void;
}

export default function VehicleCard({
  vehicleName = "Vehicle 1",
  vehicleNumber = "TN88F4089",
  vehicleType = "Tampo",
  isDefault = false,
  onSetDefault,
  onOptionsPress,
  onDeletePress,
}: VehicleCardProps) {
  return (
    <View
      className={cn(
        "w-full flex-col items-start gap-y-4 rounded-xl px-4 py-[14px]",
        isDefault ? "bg-yellow-50" : "bg-black-0",
      )}
    >
      <View className="w-full flex-row items-center justify-between">
        {isDefault ? (
          <View className="rounded-md bg-[#EBFFEE] px-2 py-1">
            <Text className="font-inter-medium text-[9px] leading-3 tracking-[-0.045px] text-teal-900">
              Default
            </Text>
          </View>
        ) : (
          <TouchableOpacity
            className="rounded-md border border-black-100 bg-teal-600 px-4 py-1.5"
            onPress={onSetDefault}
          >
            <Text className="font-inter-medium text-[11px] leading-4 tracking-[-0.055px] text-black-900">
              Set as Default
            </Text>
          </TouchableOpacity>
        )}

        <View className="flex-row gap-4">
          {onDeletePress && (
            <TouchableOpacity onPress={onDeletePress}>
              <Feather name="trash-2" size={20} color="#EF4444" />
            </TouchableOpacity>
          )}
          <TouchableOpacity
            className="h-7 w-7 items-center justify-center rounded p-1"
            onPress={onOptionsPress}
          >
            <View className="h-[18px] w-[18px] flex-col">
              {/* Dots SVG */}
              <Svg
                width="4"
                height="4"
                viewBox="0 0 4 4"
                style={{
                  position: "absolute",
                  top: 7,
                  right: 7,
                  bottom: 7,
                  left: 7,
                }}
                fill="none"
              >
                <Path
                  d="M2 0.3125C1.06962 0.3125 0.3125 1.06962 0.3125 2C0.3125 2.93038 1.06962 3.6875 2 3.6875C2.93038 3.6875 3.6875 2.93038 3.6875 2C3.6875 1.06962 2.93038 0.3125 2 0.3125Z"
                  fill="#0A0A0A"
                />
              </Svg>
              <Svg
                width="5"
                height="4"
                viewBox="0 0 5 4"
                style={{
                  position: "absolute",
                  top: 7,
                  right: 13,
                  bottom: 7,
                  left: 2,
                }}
                fill="none"
              >
                <Path
                  d="M2.375 0.3125C1.44462 0.3125 0.6875 1.06962 0.6875 2C0.6875 2.93038 1.44462 3.6875 2.375 3.6875C3.30538 3.6875 4.0625 2.93038 4.0625 2C4.0625 1.06962 3.30538 0.3125 2.375 0.3125Z"
                  fill="#0A0A0A"
                />
              </Svg>
              <Svg
                width="5"
                height="4"
                viewBox="0 0 5 4"
                style={{
                  position: "absolute",
                  top: 7,
                  right: 2,
                  bottom: 7,
                  left: 13,
                }}
                fill="none"
              >
                <Path
                  d="M2.625 0.3125C1.69462 0.3125 0.9375 1.06962 0.9375 2C0.9375 2.93038 1.69462 3.6875 2.625 3.6875C3.55538 3.6875 4.3125 2.93038 4.3125 2C4.3125 1.06962 3.55538 0.3125 2.625 0.3125Z"
                  fill="#0A0A0A"
                />
              </Svg>
            </View>
          </TouchableOpacity>
        </View>
      </View>

      <View className="mt-3 flex-col items-start gap-1">
        <Text className="font-jakarta-semibold text-[10px] leading-[13px] text-black-500">
          {vehicleName}
        </Text>
        <View className="w-full flex-row items-center justify-between">
          <Text className="font-inter-medium text-sm leading-5 tracking-[0.14px] text-teal-900">
            {vehicleNumber}
          </Text>
          <View
            className={cn(
              "flex-row items-center justify-center rounded-full border bg-[#FBFBFB] px-2 py-1",
              isDefault ? "border-teal-650" : "border-black-700",
            )}
          >
            <View className="mr-1 h-3.5 w-3.5 items-center justify-center">
              {getVehicleIcon({ vehicleType, isDefault })}
            </View>
            <Text
              className={cn(
                "font-inter-medium text-[10px] capitalize leading-3",
                isDefault ? "text-teal-850" : "text-black-700",
              )}
            >
              {vehicleType.toLowerCase().replaceAll("_", " ")}
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
}
