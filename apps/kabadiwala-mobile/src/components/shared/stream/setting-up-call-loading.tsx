import React from "react";
import { ActivityIndicator, Text, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Ionicons } from "@expo/vector-icons";

const SettingUpCallLoading = () => {
  return (
    <SafeAreaView className="flex-1 bg-teal-950">
      <View className="flex-1 items-center justify-center px-6">
        <View className="items-center space-y-6">
          {/* Phone icon with pulse animation */}
          <View className="rounded-full bg-teal-700 p-6">
            <Ionicons name="call" size={48} color="#00FFFF" />
          </View>

          {/* Loading spinner */}
          <ActivityIndicator size="large" color="#00FFFF" className="my-4" />

          {/* Loading text */}
          <View className="items-center space-y-2">
            <Text className="font-jakarta-semibold text-xl text-white">
              Setting up your call
            </Text>
            <Text className="font-jakarta-regular text-center leading-6 text-black-250">
              Please wait while we connect you with the seller. This should only
              take a moment.
            </Text>
          </View>

          {/* Connection indicators */}
          <View className="mt-4 flex-row space-x-2">
            <View className="h-2 w-2 rounded-full bg-teal-500" />
            <View className="h-2 w-2 rounded-full bg-teal-600" />
            <View className="h-2 w-2 rounded-full bg-teal-700" />
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default SettingUpCallLoading;
