import React from "react";
import { Text, TouchableOpacity, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { router } from "expo-router";
import { Ionicons } from "@expo/vector-icons";

interface SettingUpCallErrorProps {
  error: string;
  onRetry: () => void;
}

const SettingUpCallError = ({ error, onRetry }: SettingUpCallErrorProps) => {
  return (
    <SafeAreaView className="flex-1 bg-teal-950">
      <View className="flex-1 items-center justify-center px-6">
        <View className="items-center space-y-6">
          {/* Error icon */}
          <View className="mb-6 rounded-full border-2 border-red-500/30 bg-red-500/20 p-6">
            <Ionicons name="call-outline" size={48} color="#EF4444" />
          </View>

          {/* Error text */}
          <View className="items-center">
            <Text className="font-jakarta-bold text-xl text-white">
              Call Connection Failed
            </Text>
            <Text className="font-jakarta-medium text-center text-base text-red-400">
              {error}
            </Text>
            <Text className="font-jakarta-regular mt-2 text-center leading-6 text-black-250">
              We're having trouble connecting your call. Please check your
              internet connection and try again.
            </Text>
          </View>

          {/* Action buttons */}
          <View className="mt-6 w-full max-w-xs flex-col gap-4 space-y-3">
            <TouchableOpacity
              onPress={onRetry}
              className="flex-row items-center justify-center space-x-2 rounded-xl bg-teal-600 px-6 py-4"
              activeOpacity={0.8}
            >
              <Ionicons name="refresh" size={20} color="white" />
              <Text className="font-jakarta-semibold text-base text-white">
                Retry Call
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => router.back()}
              className="flex-row items-center justify-center space-x-2 rounded-xl bg-black-700 px-6 py-4"
              activeOpacity={0.8}
            >
              <Ionicons name="arrow-back" size={20} color="#CCCCCC" />
              <Text className="font-jakarta-medium text-base text-black-200">
                Go Back
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default SettingUpCallError;
