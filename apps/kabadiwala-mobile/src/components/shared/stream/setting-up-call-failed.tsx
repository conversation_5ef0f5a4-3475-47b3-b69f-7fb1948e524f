import React from "react";
import { Text, TouchableOpacity, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { router } from "expo-router";
import { Ionicons } from "@expo/vector-icons";

interface SettingUpCallFailedProps {
  onRetry: () => void;
}

const SettingUpCallFailed = ({ onRetry }: SettingUpCallFailedProps) => {
  return (
    <SafeAreaView className="flex-1 bg-teal-950">
      <View className="flex-1 items-center justify-center px-6">
        <View className="items-center space-y-6">
          {/* Warning icon */}
          <View className="my-4 rounded-full border-2 border-yellow-500/30 bg-yellow-500/20 p-6">
            <Ionicons name="warning" size={48} color="#FBCF04" />
          </View>

          {/* Warning text */}
          <View className="items-center space-y-3">
            <Text className="font-jakarta-bold text-xl text-white">
              Call Not Available
            </Text>
            <Text className="font-jakarta-regular text-center leading-6 text-black-250">
              We couldn't initialize the call session. Please try again or
              contact support if the problem persists.
            </Text>
          </View>

          {/* Action buttons */}
          <View className="mt-6 w-full max-w-xs flex-col gap-4 space-y-3">
            <TouchableOpacity
              onPress={onRetry}
              className="flex-row items-center justify-center space-x-2 rounded-xl bg-teal-600 px-6 py-4"
              activeOpacity={0.8}
            >
              <Ionicons name="refresh" size={20} color="white" />
              <Text className="font-jakarta-semibold text-base text-white">
                Try Again
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => router.back()}
              className="flex-row items-center justify-center space-x-2 rounded-xl bg-black-700 px-6 py-4"
              activeOpacity={0.8}
            >
              <Ionicons name="arrow-back" size={20} color="#CCCCCC" />
              <Text className="font-jakarta-medium text-base text-black-200">
                Go Back
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default SettingUpCallFailed;
