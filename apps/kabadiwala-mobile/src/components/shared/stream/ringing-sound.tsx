import { useEffect } from "react";
import InCallManager from "react-native-incall-manager";
import {
  CallingState,
  useCall,
  useCallStateHooks,
} from "@stream-io/video-react-native-sdk";

const RingingSound = () => {
  const call = useCall();
  const isCallCreatedByMe = call?.isCreatedByMe;
  const { useCallCallingState } = useCallStateHooks();
  const callingState = useCallCallingState();

  useEffect(() => {
    if (callingState !== CallingState.RINGING) return;
    if (isCallCreatedByMe) {
      // play outgoing call sound
      InCallManager.start({ media: "video", ringback: "_BUNDLE_" }); // or _DEFAULT_ or _DTMF_
      return () => InCallManager.stopRingback();
    } else {
      // play incoming call sound
      InCallManager.startRingtone("_BUNDLE_", 1, "ringtone", 200); // or _DEFAULT_ or system filename with extension
      return () => InCallManager.stopRingtone();
    }
  }, [callingState, isCallCreatedByMe]);
  // renderless component
  return null;
};

export default RingingSound;
