import type { BottomSheetModal } from "@gorhom/bottom-sheet";
import type { FieldValues } from "react-hook-form";
import type { PressableProps } from "react-native";
import type { SvgProps } from "react-native-svg";
import * as React from "react";
import { Pressable, View } from "react-native";
import { ScrollView } from "react-native-gesture-handler";
import Svg, { Path } from "react-native-svg";
import Ionicons from "@expo/vector-icons/Ionicons";
import { useController } from "react-hook-form";
import { tv } from "tailwind-variants";

import type { InputControllerType } from "./input";
import { Modal, useModal } from "./modal";
import { Text } from "./text";

const selectTv = tv({
  slots: {
    container: "mb-4",
    label: "mb-2 font-jakarta-medium text-base font-medium text-text-600",
    input:
      "mt-0 flex-row items-center rounded-lg border-[1.2px] border-black-100 bg-black-0 p-3",
    inputValue: "text-base text-black-700",
  },

  variants: {
    focused: {
      true: {
        input: "bg-white border-[1.2px] border-teal-750",
      },
      false: {},
    },
    error: {
      true: {
        input: "border-red-600",
        label: "font-medium text-red-600",
        inputValue: "text-red-600",
      },
    },
    disabled: {
      true: {
        input: "bg-neutral-200 opacity-50",
      },
    },
  },
  defaultVariants: {
    focused: false,
    error: false,
    disabled: false,
  },
});

export interface OptionType {
  label: string;
  value: string | number;
  icon?: React.ReactNode;
}

interface OptionsProps {
  options: OptionType[];
  onSelect: (option: OptionType) => void;
  value?: string | number;
  testID?: string;
  icon?: React.ReactNode;
}

export const Options = React.forwardRef<BottomSheetModal, OptionsProps>(
  ({ options, onSelect, value, testID }, ref) => {
    const height = options.length * 30;
    const snapPoints = React.useMemo(() => [height], [height]);

    return (
      <Modal
        ref={ref}
        snapPoints={snapPoints}
        backgroundStyle={{
          backgroundColor: "white",
        }}
      >
        <ScrollView className="flex-1" contentContainerClassName="pb-20">
          {options.map((item) => (
            <Option
              key={`select-item-${item.value}`}
              label={item.label}
              icon={item.icon}
              selected={value === item.value}
              onPress={() => onSelect(item)}
              testID={testID ? `${testID}-item-${item.value}` : undefined}
            />
          ))}
        </ScrollView>
      </Modal>
    );
  },
);

const Option = React.memo(
  ({
    label,
    icon,
    selected = false,
    ...props
  }: PressableProps & {
    selected?: boolean;
    label: string;
    icon?: React.ReactNode;
  }) => {
    return (
      <Pressable
        className="flex-row items-center gap-3 border-b border-black-100 bg-white px-3 py-4"
        {...props}
      >
        {icon}
        <Text className="flex-1 text-base capitalize text-black-700">
          {label}
        </Text>
        {selected && <Check />}
      </Pressable>
    );
  },
);

export interface SelectProps {
  value?: string | number;
  label?: string;
  disabled?: boolean;
  error?: string;
  options?: OptionType[];
  onSelect?: (value: string | number) => void;
  placeholder?: string;
  testID?: string;
}

interface ControlledSelectProps<T extends FieldValues>
  extends SelectProps,
    InputControllerType<T> {}

export const Select = (props: SelectProps) => {
  const {
    label,
    value,
    error,
    options = [],
    placeholder = "select...",
    disabled = false,
    onSelect,
    testID,
  } = props;
  const modal = useModal();
  const [isFocussed, setIsFocussed] = React.useState(false);

  const onSelectOption = React.useCallback(
    (option: OptionType) => {
      onSelect?.(option.value);
      modal.dismiss();
      setIsFocussed(false);
    },
    [modal, onSelect],
  );

  const styles = React.useMemo(
    () =>
      selectTv({
        error: Boolean(error),
        focused: isFocussed,
        disabled,
      }),
    [error, isFocussed, disabled],
  );

  const textValue = React.useMemo(
    () =>
      value !== undefined
        ? (options.find((t) => t.value === value)?.label ?? placeholder)
        : placeholder,
    [value, options, placeholder],
  );

  return (
    <>
      <View className={styles.container()}>
        {label && (
          <Text
            testID={testID ? `${testID}-label` : undefined}
            className={styles.label()}
          >
            {label}
          </Text>
        )}
        <Pressable
          className={styles.input()}
          disabled={disabled}
          onPress={() => {
            console.log("Select pressed");

            setIsFocussed(true);
            modal.present();
          }}
          testID={testID ? `${testID}-trigger` : undefined}
        >
          <View className="flex-1">
            <Text className={styles.inputValue()}>{textValue}</Text>
          </View>
          <Ionicons
            name="chevron-down"
            size={24}
            color={isFocussed ? "#C58E00" : "#926C57"}
          />
        </Pressable>
        {error && (
          <Text
            testID={testID ? `${testID}-error` : undefined}
            className="mt-2 text-sm leading-5 text-red-600"
          >
            {error}
          </Text>
        )}
      </View>
      <Options
        testID={testID}
        ref={modal.ref}
        options={options}
        onSelect={onSelectOption}
      />
    </>
  );
};

// only used with react-hook-form
export function ControlledSelect<T extends FieldValues>(
  props: ControlledSelectProps<T>,
) {
  const { name, control, rules, onSelect: onNSelect, ...selectProps } = props;

  const { field, fieldState } = useController({ control, name, rules });
  const onSelect = React.useCallback(
    (value: string | number) => {
      console.log("onSelect called with value:", value);

      field.onChange(value);
      onNSelect?.(value);
    },
    [field, onNSelect],
  );
  return (
    <Select
      onSelect={onSelect}
      value={field.value}
      error={fieldState.error?.message}
      {...selectProps}
    />
  );
}

const Check = ({ ...props }: SvgProps) => (
  <Svg
    width={25}
    height={24}
    fill="none"
    viewBox="0 0 25 24"
    {...props}
    className="stroke-black dark:stroke-white"
  >
    <Path
      d="m20.256 6.75-10.5 10.5L4.506 12"
      strokeWidth={2.438}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
);
