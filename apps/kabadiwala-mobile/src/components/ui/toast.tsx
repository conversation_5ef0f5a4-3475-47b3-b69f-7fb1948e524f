import React, { useEffect } from "react";
import { Pressable, View } from "react-native";
import Svg, { Path } from "react-native-svg";
import { <PERSON><PERSON><PERSON>ie<PERSON> } from "moti";
import { tv } from "tailwind-variants";

import { Text } from "./text";

type ToastVariant = "success" | "info" | "warning" | "error";
type ToastPosition = "top" | "bottom" | "left" | "right";
interface ToastAction {
  label: string;
  onPress: () => void;
}

export interface ToastProps {
  title: string;
  message?: string;
  variant?: ToastVariant;
  actions?: ToastAction[];
  onClose?: () => void;
  duration?: number;
  position?: ToastPosition;
  visible?: boolean;
}

const toast = tv({
  slots: {
    container:
      "flex flex-row items-center justify-between p-3 rounded-lg border border-solid",
    leftContent: "flex flex-row items-center gap-2",
    iconContainer: "flex items-center justify-center h-7 w-7",
    content: "flex-1 justify-center",
    title: "font-extrabold text-xs",
    message: "text-xs font-medium",
    actions: "flex flex-row mt-2 gap-x-4",
    action: "py-1 px-2 rounded",
    actionLabel: "font-semibold text-xs",
    closeButton: "h-6 w-6 flex items-center justify-center pr-8",
  },
  variants: {
    variant: {
      success: {
        container: "bg-[#EBFFF4] border-[#00CC66]",
        title: "text-[#003D1F]",
        message: "text-[#003D1F]",
        action: "bg-[#C8FFE1]",
        actionLabel: "text-[#003D1F]",
      },
      info: {
        container: "bg-[#FFFAE6] border-[#FBD41D]",
        title: "text-[#322901]",
        message: "text-[#322901]",
        action: "bg-[#FFF2B3]",
        actionLabel: "text-[#322901]",
      },
      warning: {
        container: "bg-[#FFF5EB] border-[#FF8A00]",
        title: "text-[#332000]",
        message: "text-[#332000]",
        action: "bg-[#FFDBB3]",
        actionLabel: "text-[#332000]",
      },
      error: {
        container: "bg-[#FFEBEB] border-[#FF0000]",
        title: "text-[#330000]",
        message: "text-[#330000]",
        action: "bg-[#FFB3B3]",
        actionLabel: "text-[#330000]",
      },
    },
    position: {
      top: {
        container: "absolute top-8 left-4 right-4",
      },
      bottom: {
        container: "absolute bottom-8 left-4 right-4",
      },
      left: {
        container: "absolute top-1/4 left-4 max-w-[70%]",
      },
      right: {
        container: "absolute top-1/4 right-4 max-w-[70%]",
      },
    },
  },
  defaultVariants: {
    variant: "success",
    position: "top",
  },
});

// New SVG icons to match the provided design
const variantIcons = {
  success: (
    <Svg width="17" height="17" viewBox="0 0 17 17" fill="none">
      <Path
        d="M8.5 5.16667V8.5M8.5 11.8333H8.50833M16 8.5C16 12.6421 12.6421 16 8.5 16C4.35786 16 1 12.6421 1 8.5C1 4.35786 4.35786 1 8.5 1C12.6421 1 16 4.35786 16 8.5Z"
        stroke="#00994D"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  ),
  info: (
    <Svg width="17" height="17" viewBox="0 0 17 17" fill="none">
      <Path
        d="M8.5 5.16667V8.5M8.5 11.8333H8.50833M16 8.5C16 12.6421 12.6421 16 8.5 16C4.35786 16 1 12.6421 1 8.5C1 4.35786 4.35786 1 8.5 1C12.6421 1 16 4.35786 16 8.5Z"
        stroke="#967C03"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  ),
  warning: (
    <Svg width="17" height="17" viewBox="0 0 17 17" fill="none">
      <Path
        d="M8.5 5.16667V8.5M8.5 11.8333H8.50833M16 8.5C16 12.6421 12.6421 16 8.5 16C4.35786 16 1 12.6421 1 8.5C1 4.35786 4.35786 1 8.5 1C12.6421 1 16 4.35786 16 8.5Z"
        stroke="#CC6E00"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  ),
  error: (
    <Svg width="17" height="17" viewBox="0 0 17 17" fill="none">
      <Path
        d="M8.5 5.16667V8.5M8.5 11.8333H8.50833M16 8.5C16 12.6421 12.6421 16 8.5 16C4.35786 16 1 12.6421 1 8.5C1 4.35786 4.35786 1 8.5 1C12.6421 1 16 4.35786 16 8.5Z"
        stroke="#CC0000"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  ),
};

const CloseIcon = ({ color }: { color: string }) => (
  <Svg width="10" height="10" viewBox="0 0 10 10" fill="none">
    <Path
      d="M1 9L9 1M1 1L9 9"
      stroke={color}
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </Svg>
);

const ActionIcon = ({ color }: { color: string }) => (
  <Svg width="12" height="12" viewBox="0 0 24 24" fill="none">
    <Path
      d="M17 3H7c-1.1 0-2 .9-2 2v16l7-3 7 3V5c0-1.1-.9-2-2-2z"
      stroke={color}
      strokeWidth="1.5"
      fill="none"
    />
  </Svg>
);

export const Toast = ({
  title,
  message,
  variant = "success",
  actions = [],
  onClose,
  position = "top",
  visible = true,
  duration,
}: ToastProps) => {
  const styles = toast({ variant, position });

  // Auto-dismiss logic if duration is provided
  useEffect(() => {
    if (visible && onClose && duration) {
      const timer = setTimeout(() => {
        onClose();
      }, duration);
      return () => clearTimeout(timer);
    }
    return undefined;
  }, [visible, onClose, duration]);

  if (!visible) return null;

  // Determine icon and text colors based on variant
  const getColorForVariant = () => {
    switch (variant) {
      case "success":
        return "#00994D";
      case "info":
        return "#967C03";
      case "warning":
        return "#CC6E00";
      case "error":
        return "#CC0000";
      default:
        return "#00994D";
    }
  };

  return (
    <MotiView
      from={{
        opacity: 0,
        translateY: position === "bottom" ? 20 : -20,
        scale: 0.95,
      }}
      animate={{ opacity: 1, translateY: 0, scale: 1 }}
      exit={{
        opacity: 0,
        translateY: position === "bottom" ? 20 : -20,
        scale: 0.95,
      }}
      transition={{ type: "spring", damping: 18, stiffness: 250 }}
      className={styles.container()}
    >
      <View className={styles.leftContent()}>
        <View className={styles.iconContainer()}>{variantIcons[variant]}</View>
        <View className={styles.content()}>
          <Text className={`${styles.title()} ${message ? "mb-0.5" : ""}`}>
            {title}
          </Text>
          {message && <Text className={styles.message()}>{message}</Text>}
          {actions.length > 0 && (
            <View className={styles.actions()}>
              {actions.map((action, index) => (
                <Pressable
                  key={index}
                  className={styles.action()}
                  onPress={action.onPress}
                  style={({ pressed }) => [{ opacity: pressed ? 0.7 : 1 }]}
                >
                  <Text className={styles.actionLabel()}>
                    <ActionIcon color={getColorForVariant()} />
                    {" " + action.label}
                  </Text>
                </Pressable>
              ))}
            </View>
          )}
        </View>
      </View>
      {onClose && (
        <Pressable
          hitSlop={10}
          className={styles.closeButton()}
          onPress={onClose}
          style={({ pressed }) => [{ opacity: pressed ? 0.6 : 1 }]}
        >
          <CloseIcon color={getColorForVariant()} />
        </Pressable>
      )}
    </MotiView>
  );
};
