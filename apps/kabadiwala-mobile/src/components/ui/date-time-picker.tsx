import type { DateTimePickerEvent } from "@react-native-community/datetimepicker";
import type {
  Control,
  FieldValues,
  Path,
  RegisterOptions,
} from "react-hook-form";
import React, { useState } from "react";
import { Platform, Pressable } from "react-native";
import DateTimePicker from "@react-native-community/datetimepicker";
import { useController } from "react-hook-form";

import { Input } from "./input";

type DateTimeMode = "date" | "time";

interface ControlledDateTimePickerProps<T extends FieldValues> {
  name: Path<T>;
  control: Control<T>;
  label?: string;
  mode?: DateTimeMode;
  rules?: Omit<
    RegisterOptions<T>,
    "disabled" | "valueAsNumber" | "valueAsDate" | "setValueAs"
  >;
  minDate?: Date;
  maxDate?: Date;
}

export function ControlledDateTimePicker<T extends FieldValues>({
  name,
  control,
  label,
  mode = "date",
  rules,
  minDate,
  maxDate,
  ...props
}: ControlledDateTimePickerProps<T>) {
  const { field, fieldState } = useController({ control, name, rules });
  const [showPicker, setShowPicker] = useState(false);

  // Format value for display
  const getDisplayValue = (value: Date | undefined) => {
    if (!value) return "";
    if (mode === "date") return value.toLocaleDateString();
    if (mode === "time") return value.toLocaleTimeString();
    return "";
  };

  // Handler for DateTimePicker change
  const handleChange = (_event: DateTimePickerEvent, selectedDate?: Date) => {
    setShowPicker(false);
    if (selectedDate) {
      field.onChange(selectedDate);
    }
  };

  if (Platform.OS === "ios") {
    return (
      <Pressable
        onPress={() => setShowPicker(true)}
        className="border-customborder dark:bg-bgtextInput mt-2 items-start rounded-xl border"
      >
        <DateTimePicker
          value={field.value || new Date()}
          mode={mode}
          display="default"
          onChange={handleChange}
          minimumDate={minDate ? new Date() : undefined}
          maximumDate={maxDate ? new Date() : undefined}
          className="bg-transparent"
        />
      </Pressable>
    );
  }

  return (
    <>
      <Input
        label={label}
        value={getDisplayValue(field.value)}
        // readOnly={true}
        onPress={() => {
          console.log("clicked");
          setShowPicker(true);
        }}
        error={fieldState.error?.message}
        {...props}
      />
      {showPicker && (
        <DateTimePicker
          value={field.value || new Date()}
          mode={mode}
          display="default"
          onChange={handleChange}
          minimumDate={minDate ? new Date() : undefined}
          maximumDate={maxDate ? new Date() : undefined}
        />
      )}
    </>
  );
}

// export const CustomDateTimePicker = ({
//   value,
//   mode,
//   onChange,
// }: {
//   value: Date;
//   mode: "date" | "time";
//   onChange: (date: DateTimePickerEvent, selectedTime: Date | undefined) => void;
// }) => {
//   const [showPicker, setShowPicker] = useState(false);

//   const togglePicker = () => {
//     setShowPicker(!showPicker);
//   };

//   return (
//     <>
//       {Platform.OS === "ios" ? (
//         <Pressable
//           onPress={() => setShowPicker(true)}
//           className="border-customborder dark:bg-bgtextInput mt-2 items-start rounded-xl border"
//         >
//           <DateTimePicker
//             value={value}
//             mode={mode}
//             display="default"
//             onChange={onChange}
//             minimumDate={mode === "date" ? new Date() : undefined}
//           />
//         </Pressable>
//       ) : (
//         <>
//           <Input
//             editable={false}
//             onPress={togglePicker}
//             // value={value}
//             // onChangeText={(value) => {
//             //   onChange(value);
//             // }}
//             placeholder={mode === "date" ? "Select Date" : "Select Time"}
//           />
//           {showPicker && (
//             <DateTimePicker
//               value={value}
//               mode={mode}
//               display="default"
//               onChange={onChange}
//               minimumDate={mode === "date" ? new Date() : undefined}
//             />
//           )}
//         </>
//       )}
//     </>
//   );
// };

// export default CustomDateTimePicker;
