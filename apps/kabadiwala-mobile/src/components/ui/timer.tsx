import { memo, useEffect, useState } from "react";
import { Text, View } from "react-native";

interface TimerProps {
  expiryDate?: Date;
  onExpire?: () => Promise<void>;
}

const Timer = memo(function Timer({ expiryDate, onExpire }: TimerProps) {
  const [timeLeft, setTimeLeft] = useState<number>(0);

  useEffect(() => {
    const calculateTimeLeft = () => {
      const difference = (expiryDate?.getTime() ?? 0) - new Date().getTime();
      return difference > 0 ? Math.floor(difference / 1000) : 0;
    };

    setTimeLeft(calculateTimeLeft());

    const timer = setInterval(() => {
      const remaining = calculateTimeLeft();
      setTimeLeft(remaining);

      if (remaining <= 0) {
        clearInterval(timer);
        void onExpire?.();
      }
    }, 1000);

    return () => clearInterval(timer);
  }, [expiryDate, onExpire]);

  const getTimeDisplay = () => {
    if (timeLeft >= 60) {
      // More than or equal to 1 minute
      const minutes = Math.floor(timeLeft / 60);
      const seconds = timeLeft % 60;
      return {
        value: `${minutes}:${String(seconds).padStart(2, "0")}`,
        unit: "min",
      };
    } else {
      // Less than 1 minute
      return {
        value: String(timeLeft),
        unit: "sec",
      };
    }
  };

  const timeDisplay = getTimeDisplay();

  return (
    <>
      {timeLeft > 0 && (
        <View className="flex flex-row items-center gap-1">
          <Text className="text-xs text-teal-850">{timeDisplay.value}</Text>
          <Text className="text-xs text-teal-850">{timeDisplay.unit}</Text>
        </View>
      )}
    </>
  );
});

export default Timer;
