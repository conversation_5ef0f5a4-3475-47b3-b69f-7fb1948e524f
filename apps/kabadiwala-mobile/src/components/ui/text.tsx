import type { TextProps } from "react-native";
import React from "react";
import { Text as NNText, StyleSheet } from "react-native";

import { cn } from "@acme/ui/lib/cn";

interface Props extends TextProps {
  className?: string;
}

export const Text = ({ className = "", style, children, ...props }: Props) => {
  const textStyle = React.useMemo(
    () => cn("font-inter text-base font-normal", className),
    [className],
  );

  const nStyle = React.useMemo(() => StyleSheet.flatten([style]), [style]);
  return (
    <NNText className={textStyle} style={nStyle} {...props}>
      {children}
    </NNText>
  );
};
