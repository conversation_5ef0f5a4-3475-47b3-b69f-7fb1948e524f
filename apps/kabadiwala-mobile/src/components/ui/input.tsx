import type {
  Control,
  FieldValues,
  Path,
  RegisterOptions,
} from "react-hook-form";
import type { TextInputProps } from "react-native";
import * as React from "react";
import {
  I18nManager,
  TextInput as NTextInput,
  StyleSheet,
  View,
} from "react-native";
import { Image as NImage } from "expo-image";
import { useController } from "react-hook-form";
import { tv } from "tailwind-variants";

import { Text } from "./text";

const inputTv = tv({
  slots: {
    container: "mb-4",
    label: "mb-2 font-jakarta-medium text-base font-medium text-text-600",
    input:
      "flex-1 px-4 py-[14px] text-base text-black-700 text-center leading-5 ",
    textContainer:
      "mt-0 flex-row items-center rounded-lg border-[1.2px] border-black-100 bg-black-0",
    iconStyle: "ml-5 h-6 w-5",
  },

  variants: {
    focused: {
      true: {
        textContainer: "bg-white border-[1.2px] border-teal-750",
      },
      false: {},
    },
    error: {
      true: {
        textContainer: "border-red-600",
        label: " font-medium text-red-600",
      },
    },
    disabled: {
      true: {
        textContainer: "bg-neutral-200 opacity-50",
      },
    },
  },
  defaultVariants: {
    focused: false,
    error: false,
    disabled: false,
  },
});

export interface NInputProps extends TextInputProps {
  label?: string;
  disabled?: boolean;
  error?: string;
  icon?: number;
  imagesource?: string;
}

type TRule<T extends FieldValues> =
  | Omit<
      RegisterOptions<T>,
      "disabled" | "valueAsNumber" | "valueAsDate" | "setValueAs"
    >
  | undefined;

export type RuleType<T extends FieldValues> = { [name in keyof T]: TRule<T> };
export interface InputControllerType<T extends FieldValues> {
  name: Path<T>;
  control: Control<T>;
  rules?: RuleType<T>;
}

interface ControlledInputProps<T extends FieldValues>
  extends NInputProps,
    InputControllerType<T> {
  transformToUppercase?: boolean;
}

export const Input = React.forwardRef<NTextInput, NInputProps>((props, ref) => {
  const {
    label,
    error,
    icon,
    testID,
    autoCapitalize,
    imagesource,
    ...inputProps
  } = props;
  const [isFocussed, setIsFocussed] = React.useState(false);
  const onBlur = React.useCallback(() => setIsFocussed(false), []);
  const onFocus = React.useCallback(() => setIsFocussed(true), []);

  const styles = React.useMemo(
    () =>
      inputTv({
        error: Boolean(error),
        focused: isFocussed,
        disabled: Boolean(props.disabled),
      }),
    [error, isFocussed, props.disabled],
  );

  return (
    <View className={styles.container()}>
      {label && (
        <Text
          testID={testID ? `${testID}-label` : undefined}
          className={styles.label()}
        >
          {label}
        </Text>
      )}
      <View className={styles.textContainer()}>
        {icon === 1 ? (
          <NImage
            source={imagesource}
            className={styles.iconStyle()}
            contentFit="contain"
            tintColor={isFocussed ? "#C58E00" : "#926C57"}
          />
        ) : null}
        <NTextInput
          testID={testID}
          ref={ref}
          className={styles.input()}
          onBlur={onBlur}
          onFocus={onFocus}
          autoCapitalize={autoCapitalize}
          {...inputProps}
          style={StyleSheet.flatten([
            { writingDirection: I18nManager.isRTL ? "rtl" : "ltr" },
            { textAlign: I18nManager.isRTL ? "right" : "left" },
            inputProps.style,
          ])}
        />
      </View>
      {error && (
        <Text
          testID={testID ? `${testID}-error` : undefined}
          className="mt-2 text-sm leading-5 text-red-600"
        >
          {error}
        </Text>
      )}
    </View>
  );
});

// only used with react-hook-form
export function ControlledInput<T extends FieldValues>(
  props: ControlledInputProps<T>,
) {
  const { name, control, rules, transformToUppercase, ...inputProps } = props;

  const { field, fieldState } = useController({ control, name, rules });
  return (
    <Input
      ref={field.ref}
      autoCapitalize={inputProps.autoCapitalize}
      onChangeText={(text) =>
        transformToUppercase
          ? field.onChange(text.toUpperCase())
          : field.onChange(text)
      }
      value={(field.value as string) || ""}
      {...inputProps}
      error={fieldState.error?.message}
    />
  );
}
