import type { KeyboardEvent } from "react-native";
import React, {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useRef,
  useState,
} from "react";
import { Keyboard, View } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { registerToastFunction } from "@/utils/functions";

import type { ToastProps } from "./toast";
import { Toast } from "./toast";

interface ToastContextType {
  toast: (props: Omit<ToastProps, "visible" | "onClose">) => string;
  update: (id: string, props: Partial<ToastProps>) => void;
  dismiss: (id: string) => void;
  dismissAll: () => void;
}

type ToastItemType = ToastProps & { id: string; timer?: NodeJS.Timeout };

const ToastContext = createContext<ToastContextType | undefined>(undefined);

export const useToast = () => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error("useToast must be used within a ToastProvider");
  }
  return context;
};

export const ToastProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [toasts, setToasts] = useState<ToastItemType[]>([]);
  const [keyboardVisible, setKeyboardVisible] = useState(false);
  const [keyboardHeight, setKeyboardHeight] = useState(0);
  //   const { top, bottom } = useSafeAreaInsets();
  const toastCount = useRef(0);

  useEffect(() => {
    const keyboardWillShow = (e: KeyboardEvent) => {
      setKeyboardVisible(true);
      setKeyboardHeight(e.endCoordinates.height);
    };

    const keyboardWillHide = () => {
      setKeyboardVisible(false);
      setKeyboardHeight(0);
    };

    const keyboardDidShow = Keyboard.addListener(
      "keyboardWillShow",
      keyboardWillShow,
    );
    const keyboardDidHide = Keyboard.addListener(
      "keyboardWillHide",
      keyboardWillHide,
    );

    return () => {
      keyboardDidShow.remove();
      keyboardDidHide.remove();
    };
  }, []);

  const toast = useCallback(
    (props: Omit<ToastProps, "visible" | "onClose">) => {
      const id = `toast-${Date.now()}-${toastCount.current++}`;
      const duration = props.duration ?? 5000; // Default 5 seconds

      // Create the toast
      setToasts((current) => [...current, { id, ...props, visible: true }]);

      // Set up auto-dismiss timer
      if (duration > 0) {
        const timer = setTimeout(() => {
          dismiss(id);
        }, duration);

        // Update toast with timer reference
        setToasts((current) =>
          current.map((t) => (t.id === id ? { ...t, timer } : t)),
        );
      }

      return id;
    },
    [],
  );

  const update = useCallback((id: string, props: Partial<ToastProps>) => {
    setToasts((current) =>
      current.map((t) => (t.id === id ? { ...t, ...props } : t)),
    );
  }, []);

  const dismiss = useCallback((id: string) => {
    setToasts((current) => {
      const toastToRemove = current.find((t) => t.id === id);

      // Clear the timeout if it exists
      if (toastToRemove?.timer) {
        clearTimeout(toastToRemove.timer);
      }

      return current.filter((t) => t.id !== id);
    });
  }, []);

  const dismissAll = useCallback(() => {
    setToasts((current) => {
      // Clear all timeouts
      current.forEach((t) => {
        if (t.timer) clearTimeout(t.timer);
      });

      return [];
    });
  }, []);

  // Register the toast function for the global utility
  useEffect(() => {
    registerToastFunction(toast);
  }, [toast]);

  // Adjust toast positions for keyboard
  const getAdjustedPosition = (position: ToastProps["position"] = "top") => {
    if (position === "bottom" && keyboardVisible) {
      return "bottom";
    }
    return position;
  };
  const { top: topInsert, bottom: bottomInsert } = useSafeAreaInsets();
  return (
    <ToastContext.Provider value={{ toast, update, dismiss, dismissAll }}>
      {children}
      <View
        style={{
          position: "absolute",
          top: topInsert + 10,
          bottom: bottomInsert,
          left: 0,
          right: 0,
          paddingTop: topInsert,
          paddingBottom: keyboardVisible ? keyboardHeight : bottomInsert,
          pointerEvents: "box-none",
        }}
      >
        {toasts.map((t) => (
          <Toast
            key={t.id}
            {...t}
            position={getAdjustedPosition(t.position)}
            onClose={() => dismiss(t.id)}
          />
        ))}
      </View>
    </ToastContext.Provider>
  );
};
