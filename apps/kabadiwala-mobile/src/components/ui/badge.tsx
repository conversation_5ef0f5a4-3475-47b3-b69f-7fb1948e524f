import type { ViewProps } from "react-native";
import type { VariantProps } from "tailwind-variants";
import React from "react";
import { Text, View } from "react-native";
import { tv } from "tailwind-variants";

const badge = tv({
  slots: {
    container:
      "flex-row items-center justify-center rounded-2xl px-3 py-1 shrink-0 w-fit h-fit",
    label: "font-inter-semibold text-xs",
  },

  variants: {
    variant: {
      default: {
        container: "bg-teal-550 border-[1px] border-transparent",
        label: "font-jakarta-semibold text-xs text-black-900",
      },
      secondary: {
        container: "bg-yellow-100 border-[1px] border-transparent",
        label: "font-jakarta-semibold text-xs text-yellow-700",
      },
      outline: {
        container: "border-[1px] border-black-250 bg-transparent",
        label: "font-jakarta-semibold text-xs text-black-700",
      },
      destructive: {
        container: "bg-red-500 border-[1px] border-transparent",
        label: "font-jakarta-semibold text-xs text-white",
      },
      success: {
        container: "bg-green-500 border-[1px] border-transparent",
        label: "font-jakarta-semibold text-xs text-white",
      },
    },
    size: {
      default: {
        container: "px-2 py-1 rounded-md",
        label: "text-xs",
      },
      sm: {
        container: "px-1.5 py-0.5 rounded",
        label: "text-xs",
      },
      lg: {
        container: "px-3 py-1.5 rounded-lg",
        label: "text-sm",
      },
    },
  },
  defaultVariants: {
    variant: "default",
    size: "default",
  },
});

type BadgeVariants = VariantProps<typeof badge>;
interface Props extends BadgeVariants, Omit<ViewProps, "children"> {
  label?: string;
  className?: string;
  textClassName?: string;
  children?: React.ReactNode;
}

export const Badge = React.forwardRef<View, Props>(
  (
    {
      label,
      children,
      variant = "default",
      size = "default",
      className = "",
      textClassName = "",
      ...props
    },
    ref,
  ) => {
    const styles = React.useMemo(
      () => badge({ variant, size }),
      [variant, size],
    );

    return (
      <View className={styles.container({ className })} {...props} ref={ref}>
        {label && (
          <Text className={styles.label({ className: textClassName })}>
            {label}
          </Text>
        )}
        {children}
      </View>
    );
  },
);
