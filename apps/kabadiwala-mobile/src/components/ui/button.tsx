import type { PressableProps, View } from "react-native";
import type { VariantProps } from "tailwind-variants";
import React from "react";
import { ActivityIndicator, Pressable, Text } from "react-native";
import { tv } from "tailwind-variants";

const button = tv({
  slots: {
    container: " flex-row items-center justify-center rounded-md px-4",
    label: "font-inter-semibold text-base",
    indicator: "h-6 text-white",
  },

  variants: {
    variant: {
      default: {
        container: "bg-teal-550 rounded-lg border-[1.2px] border-transparent",
        label: "font-jakarta-semibold text-sm tracking-[0.14px] text-black-900",
        indicator: "text-black-900",
      },
      disabled: {
        container: "bg-black-100",
        label: "font-jakarta-semibold text-sm tracking-[0.14px] text-black-600",
        indicator: "text-black-600",
      },
      loading: {
        container: "bg-black-250",
        label: "font-jakarta-semibold text-sm tracking-[0.14px] text-black-700",
        indicator: "text-black-700",
      },
      outline: {
        container: "border-[1.2px] border-black-250 bg-transparent",
        label: "font-jakarta-semibold text-sm tracking-[0.14px] text-black-700",
        indicator: "text-black-700",
      },
      ghost: {
        container: "bg-transparent border-0",
        label: "font-jakarta-semibold text-sm tracking-[0.14px] text-black-700",
        indicator: "text-black-700",
      },
      destructive: {
        container: "bg-red-500 border-[1.2px] border-transparent",
        label: "font-jakarta-semibold text-sm tracking-[0.14px] text-white",
        indicator: "text-white",
      },
    },
    size: {
      default: {
        container: "rounded-lg px-3 py-[14px]",
        label: "text-base",
      },
      sm: {
        container: "h-8 rounded-md px-3",
        label: "text-xs",
        indicator: "h-4",
      },
      lg: {
        container: "h-10 rounded-md px-8",
        label: "text-base",
        indicator: "h-5",
      },
      icon: {
        container: "h-9 w-9",
      },
    },
    disabled: {
      true: {
        container: "bg-black-100 opacity-50",
        label: "text-black-600",
        indicator: "text-black-600",
      },
    },
    fullWidth: {
      true: {
        container: "w-90%",
      },
      false: {
        container: "self-center",
      },
    },
  },
  defaultVariants: {
    variant: "default",
    disabled: false,
    fullWidth: true,
    size: "default",
  },
});

type ButtonVariants = VariantProps<typeof button>;
interface Props extends ButtonVariants, Omit<PressableProps, "disabled"> {
  label?: string;
  loading?: boolean;
  className?: string;
  textClassName?: string;
}

export const Button = React.forwardRef<View, Props>(
  (
    {
      label: text,
      loading = false,
      variant = "default",
      disabled = false,
      size = "default",
      className = "",
      testID,
      textClassName = "",
      ...props
    },
    ref,
  ) => {
    const styles = React.useMemo(
      () => button({ variant, disabled, size }),
      [variant, disabled, size],
    );

    return (
      <Pressable
        disabled={disabled || loading}
        className={styles.container({ className })}
        {...props}
        ref={ref}
        testID={testID}
      >
        {props.children ?? (
          <>
            {loading ? (
              <ActivityIndicator
                size="small"
                className={styles.indicator()}
                testID={testID ? `${testID}-activity-indicator` : undefined}
              />
            ) : (
              <Text
                testID={testID ? `${testID}-label` : undefined}
                className={styles.label({ className: textClassName })}
              >
                {text}
              </Text>
            )}
          </>
        )}
      </Pressable>
    );
  },
);
