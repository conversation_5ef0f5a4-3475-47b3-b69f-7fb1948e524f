import type { Route } from "expo-router";

import type { RouterOutputs } from "@acme/kabadiwala-api";

export interface DrawerNav {
  sectionTitle: string;
  sectionsLinks: {
    name: string;
    link: Route;
    linkIcon?: React.ReactNode;
    onPress?: () => Promise<void>;
  }[];
}

export interface Query {
  language: string;
  key: string;
  types:
    | "address"
    | "geocode"
    | "(cities)"
    | "establishment"
    | "geocode|establishment";
  components?: string;
  radius?: string;
  lat?: number;
  lng?: number;
  strictBounds?: boolean;
}

export interface GoogleLocationDetailResult {
  adr_address: string;
  formatted_address: string;
  icon: string;
  id: string;
  name: string;
  place_id: string;
  scope: string;
  reference: string;
  url: string;
  utc_offset: number;
  vicinity: string;
  types: string[];
  geometry: {
    location: {
      lat: number;
      lng: number;
    };
    viewport: Record<
      string,
      {
        lat: number;
        lng: number;
      }
    >;
  };
  address_components: {
    long_name: string;
    short_name: string;
    types: string[];
  }[];
}

export interface GoogleLocationResult {
  description: string;
  id: string;
  matched_substrings: {
    length: number;
    offset: number;
  }[];
  place_id: string;
  reference: string;
  structured_formatting: {
    main_text: string;
    secondary_text: string;
    main_text_matched_substrings: {
      length: number;
    }[];
  };
  terms: {
    offset: number;
    value: string;
  }[];
  types: string[];
}

export interface NormalizeQuery {
  language: string;
  key: string;
  types:
    | "address"
    | "geocode"
    | "(cities)"
    | "establishment"
    | "geocode|establishment";
  components?: string;
  radius?: string;
  location?: string;
  strictBounds?: boolean;
}

export interface GeographicPoint {
  lat: number;
  lng: number;
}

export interface GeographicBounds {
  northeast: GeographicPoint;
  southwest: GeographicPoint;
}

export interface AddressComponent {
  short_name: string;
  long_name: string;
  types: string[];
  postcode_localities?: string[];
}

export interface Address {
  display: string;
  street: string | null;
  city: string | null;
  state: string | null;
  country: string | null;
  postalCode: string | null;
}

export interface DetectedLocation {
  latitude: number;
  longitude: number;
  address: Address | null;
}

export interface SearchBounds {
  point1?: GeographicPoint;
  point2?: GeographicPoint;
  point3?: GeographicPoint;
  point4?: GeographicPoint;
}

// Order Modal Types
export interface OrderData {
  orderId: string;
  location: string;
  distanceAway: string;
  estimatedScrapValue: number;
  walletBalance: number;
  requiredBalance: number;
  expiresInTime: Date;
  vehicleType: "truck" | "bike" | "car";
}

export interface HandleComponentProps {
  expiresInTime: Date;
}

export type OrderModalOfferDetailsProps = Omit<
  OrderData,
  "orderId" | "expiresInTime"
>;

export type Order = RouterOutputs["order"]["getActiveOrder"];
export type IncomingOrder = RouterOutputs["order"]["getNearbyOrder"];
export type Category =
  RouterOutputs["category"]["getTopLevelCategories"][number];

// Schedule Hours Types
export interface TimeSlot {
  startTime: string;
  endTime: string;
}

export interface DaySchedule {
  isAvailable: boolean;
  timeSlots: TimeSlot[];
}

export interface ScheduleHoursFormType {
  sunday: DaySchedule;
  monday: DaySchedule;
  tuesday: DaySchedule;
  wednesday: DaySchedule;
  thursday: DaySchedule;
  friday: DaySchedule;
  saturday: DaySchedule;
}

export interface ShowPickerState {
  show: boolean;
  day: keyof ScheduleHoursFormType;
  index: number;
  field: "startTime" | "endTime";
}

// Map-related types
export interface Coordinates {
  lat: number;
  lng: number;
}

export interface PolylinePoint {
  latitude: number;
  longitude: number;
}

export interface DirectionsResponse {
  routes: {
    overview_polyline: {
      points: string;
    };
    legs: {
      distance: {
        text: string;
        value: number;
      };
      duration: {
        text: string;
        value: number;
      };
    }[];
  }[];
  status: string;
  error_message?: string;
}

export type NearbyOrder = RouterOutputs["order"]["getNearbyOrder"];
