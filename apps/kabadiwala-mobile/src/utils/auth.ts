import * as SecureStore from "expo-secure-store";
import { expoClient } from "@better-auth/expo/client";
import {
  customSessionClient,
  phoneNumberClient,
} from "better-auth/client/plugins";
import { createAuthClient } from "better-auth/react";

import type { auth } from "@acme/kabadiwala-auth";

import { getBaseUrl } from "./base-url";

export const authClient = createAuthClient({
  baseURL: getBaseUrl(),
  plugins: [
    phoneNumberClient(),
    expoClient({
      scheme: "expo",
      storagePrefix: "expo",
      storage: SecureStore,
    }),
    customSessionClient<typeof auth>(),
  ],
});
