import { router } from "expo-router";
import {
  FontAwesome5,
  Ionicons,
  MaterialCommunityIcons,
  MaterialIcons,
} from "@expo/vector-icons";
import axios from "axios";

import type { ToastProps } from "../components/ui/toast";
import type {
  Coordinates,
  DirectionsResponse,
  PolylinePoint,
  TimeSlot,
} from "../types/types";

export const handleBack = () => {
  if (router.canGoBack()) {
    router.back();
  } else return null;
};

let toastFunction:
  | ((props: Omit<ToastProps, "visible" | "onClose">) => void)
  | null = null;

export const registerToastFunction = (
  fn: (props: Omit<ToastProps, "visible" | "onClose">) => void,
) => {
  toastFunction = fn;
};

const showToast = (props: Omit<ToastProps, "visible" | "onClose">) => {
  if (!toastFunction) {
    console.warn(
      "Toast function not registered. Make sure ToastProvider is initialized.",
    );
    return;
  }

  return toastFunction(props);
};

// Convenient utility functions for common toast types
export const toast = {
  success: (title: string, message?: string, options?: Partial<ToastProps>) => {
    return showToast({
      title,
      message,
      variant: "success",
      duration: 3000,
      position: "top",
      ...options,
    });
  },

  info: (title: string, message?: string, options?: Partial<ToastProps>) => {
    return showToast({
      title,
      message,
      variant: "info",
      duration: 5000,
      position: "top",
      ...options,
    });
  },

  warning: (title: string, message?: string, options?: Partial<ToastProps>) => {
    return showToast({
      title,
      message,
      variant: "warning",
      duration: 5000,
      position: "top",
      ...options,
    });
  },

  error: (title: string, message?: string, options?: Partial<ToastProps>) => {
    return showToast({
      title,
      message,
      variant: "error",
      duration: 0, // Stays until manually closed
      position: "top",
      ...options,
    });
  },

  // Custom toast - can be used with any configuration
  custom: (props: Omit<ToastProps, "visible" | "onClose">) => {
    return showToast(props);
  },
};

export const calculateDistance = (
  lat1: number,
  lng1: number,
  lat2: number,
  lng2: number,
): number => {
  const R = 6371; // Radius of the Earth in kilometers
  const dLat = toRadians(lat2 - lat1);
  const dLng = toRadians(lng2 - lng1);

  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRadians(lat1)) *
      Math.cos(toRadians(lat2)) *
      Math.sin(dLng / 2) *
      Math.sin(dLng / 2);

  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distance = R * c;

  return Math.round(distance * 100) / 100; // Round to 2 decimal places
};

/**
 * Convert degrees to radians
 * @param degrees Degrees to convert
 * @returns Radians
 */
const toRadians = (degrees: number): number => {
  return degrees * (Math.PI / 180);
};

export const calculateDistanceToOrder = (
  userLocation: { latitude: number; longitude: number } | null,
  orderAddress:
    | { coordinates?: { latitude: number; longitude: number } | null }
    | null
    | undefined,
): number | null => {
  if (!userLocation || !orderAddress?.coordinates) {
    return null;
  }

  return calculateDistance(
    userLocation.latitude,
    userLocation.longitude,
    orderAddress.coordinates.latitude,
    orderAddress.coordinates.longitude,
  );
};

export const formatDistance = (distance: number | null): string => {
  if (distance === null) {
    return "Distance unknown";
  }

  if (distance < 1) {
    return `${Math.round(distance * 1000)} m away`;
  }

  return `${distance} km away`;
};

export const getVehicleIcon = ({
  isDefault,
  vehicleType,
  iconSize = 14,
}: {
  isDefault?: boolean;
  vehicleType?: string;
  iconSize?: number;
}) => {
  const iconColor = isDefault ? "#004C4D" : "#333333";
  const iconDimensions = iconSize;

  switch (vehicleType?.toLowerCase()) {
    case "bicycle":
      return (
        <Ionicons name="bicycle" size={iconDimensions} color={iconColor} />
      );
    case "cycle rickshaw":
      return (
        <MaterialCommunityIcons
          name="rickshaw"
          size={iconDimensions}
          color={iconColor}
        />
      );
    case "handcart":
    case "pushcart":
      return (
        <MaterialCommunityIcons
          name="cart"
          size={iconDimensions}
          color={iconColor}
        />
      );
    case "motorcycle with sidecar":
      return (
        <MaterialCommunityIcons
          name="motorbike"
          size={iconDimensions}
          color={iconColor}
        />
      );
    case "three-wheeler cargo vehicle":
      return (
        <MaterialCommunityIcons
          name="car-3-plus"
          size={iconDimensions}
          color={iconColor}
        />
      );
    case "mini truck":
    case "pickup truck":
      return (
        <FontAwesome5
          name="truck-pickup"
          size={iconDimensions}
          color={iconColor}
        />
      );
    case "flatbed truck":
    case "tipper truck":
    case "dump truck":
      return (
        <FontAwesome5
          name="truck-loading"
          size={iconDimensions}
          color={iconColor}
        />
      );
    case "grapple truck":
      return (
        <MaterialCommunityIcons
          name="truck-cargo-container"
          size={iconDimensions}
          color={iconColor}
        />
      );
    case "garbage truck":
      return (
        <FontAwesome5 name="trash" size={iconDimensions} color={iconColor} />
      );
    case "tow truck":
      return (
        <MaterialCommunityIcons
          name="tow-truck"
          size={iconDimensions}
          color={iconColor}
        />
      );
    case "box truck":
      return (
        <FontAwesome5
          name="truck-moving"
          size={iconDimensions}
          color={iconColor}
        />
      );
    case "van":
      return <Ionicons name="car" size={iconDimensions} color={iconColor} />;
    default:
      // Default to a generic vehicle icon
      return (
        <MaterialIcons
          name="local-shipping"
          size={iconSize}
          color={iconColor}
        />
      );
  }
};

/**
 * Convert time string to minutes since midnight
 */
export const timeToMinutes = (timeStr: string): number => {
  if (!timeStr) return 0;
  const normalizedTime = timeStr
    .trim()
    .toLowerCase()
    .replace(/(a\.?m\.?|p\.?m\.?)/, " $1")
    .replace(/\s+/g, " ")
    .trim();

  const [timePart, periodPart] = normalizedTime.split(" ");
  if (!timePart) return 0;

  const [hoursStr, minutesStr] = timePart.split(":");
  let hours = parseInt(hoursStr ?? "0", 10);
  const minutes = parseInt(minutesStr ?? "0", 10);

  if (periodPart?.startsWith("p") && hours < 12) {
    hours += 12;
  } else if (periodPart?.startsWith("a") && hours === 12) {
    hours = 0;
  }

  return hours * 60 + minutes;
};

/**
 * Convert minutes since midnight to time string
 */
export const minutesToTime = (minutes: number): string => {
  if (isNaN(minutes) || minutes < 0 || minutes >= 24 * 60) return "12:00 am";

  let hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  const period = hours >= 12 ? "pm" : "am";

  hours = hours % 12;
  if (hours === 0) hours = 12;

  return `${hours}:${mins.toString().padStart(2, "0")} ${period}`;
};

/**
 * Calculate time range from an array of time slots
 */
export const getTimeRangeFromSlots = (timeSlots: TimeSlot[]): string => {
  try {
    let earliestStart = Infinity;
    let latestEnd = -Infinity;

    const validTimeSlots = timeSlots.filter(
      (slot): slot is TimeSlot =>
        typeof slot === "object" &&
        typeof slot.startTime === "string" &&
        typeof slot.endTime === "string",
    );

    validTimeSlots.forEach((slot) => {
      const start = timeToMinutes(slot.startTime);
      const end = timeToMinutes(slot.endTime);

      if (!isNaN(start) && start < earliestStart) earliestStart = start;
      if (!isNaN(end) && end > latestEnd) latestEnd = end;
    });

    if (
      Number.isFinite(earliestStart) &&
      Number.isFinite(latestEnd) &&
      earliestStart !== Infinity &&
      latestEnd !== -Infinity
    ) {
      return `${minutesToTime(earliestStart)} - ${minutesToTime(latestEnd)}`;
    }
    return validTimeSlots[0]?.startTime ?? "Not Available";
  } catch (error) {
    console.error("Error calculating time range:", error);
    return "Not Available";
  }
};

// Map utility functions
const GOOGLE_MAPS_API_KEY = "AIzaSyB-rzgrUvIAHI0r0KbTCRbyiGGwBRFWFeU";

/**
 * Decode Google Polyline encoded string to coordinates
 * @param encoded The encoded polyline string
 * @returns Array of coordinate points
 */
export const decodePolyline = (encoded: string): PolylinePoint[] => {
  const points: PolylinePoint[] = [];
  let index = 0;
  const len = encoded.length;
  let lat = 0;
  let lng = 0;

  while (index < len) {
    let b: number;
    let shift = 0;
    let result = 0;

    do {
      b = encoded.charCodeAt(index++) - 63;
      result |= (b & 0x1f) << shift;
      shift += 5;
    } while (b >= 0x20);

    const dlat = (result & 1) !== 0 ? ~(result >> 1) : result >> 1;
    lat += dlat;

    shift = 0;
    result = 0;

    do {
      b = encoded.charCodeAt(index++) - 63;
      result |= (b & 0x1f) << shift;
      shift += 5;
    } while (b >= 0x20);

    const dlng = (result & 1) !== 0 ? ~(result >> 1) : result >> 1;
    lng += dlng;

    points.push({
      latitude: lat / 1e5,
      longitude: lng / 1e5,
    });
  }

  return points;
};

export const fetchDirections = async (
  origin: Coordinates,
  destination: Coordinates,
): Promise<PolylinePoint[]> => {
  try {
    const response = await axios.get<DirectionsResponse>(
      "https://maps.googleapis.com/maps/api/directions/json",
      {
        params: {
          origin: `${origin.lat},${origin.lng}`,
          destination: `${destination.lat},${destination.lng}`,
          key: GOOGLE_MAPS_API_KEY,
          mode: "driving",
        },
        timeout: 10000, // 10 second timeout
      },
    );

    const { data } = response;

    if (data.status !== "OK") {
      console.error("Directions API error:", data.error_message ?? data.status);
      return [];
    }

    if (data.routes.length > 0 && data.routes[0]?.overview_polyline.points) {
      const points = data.routes[0].overview_polyline.points;
      return decodePolyline(points);
    } else {
      console.warn("No routes found in directions response");
      return [];
    }
  } catch (error) {
    if (axios.isAxiosError(error)) {
      console.error("Directions API request failed:", {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
      });
    } else {
      console.error("Unexpected error fetching directions:", error);
    }
    return [];
  }
};
