import type { DetectedLocation } from "@/types/types";
import { create } from "zustand";
import { createJSONStorage, persist } from "zustand/middleware";

import { storage } from "./mmkv";

// Logger utility
const logger = {
  info: (component: string, message: string, data?: any) => {
    console.log(`[${component}] ${message}`, data ? data : "");
  },
  error: (component: string, message: string, error?: any) => {
    console.error(`[${component}] ${message}`, error ? error : "");
  },
};

interface FirstTimeState {
  isFirstTime: boolean;
  setNotFirstTime: () => void;
}

interface OnboardingState {
  isOnboardingCompleted: boolean;
  setOnboardingCompleted: () => void;
}

interface LocationStoreState {
  location: DetectedLocation | null;
  isLoading: boolean;
  error: string | null;
  setLocation: (location: DetectedLocation | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
  clearLocation: () => void;
  resetLocationState: () => void;
}

interface PermissionStoreState {
  microphoneAccess: boolean;
  setMicrophoneAccess: (access: boolean) => void;
}

// Custom storage adapter for zustand that works with MMKV
const mmkvStorage = {
  getItem: (name: string) => {
    logger.info("Storage", `Reading item: ${name}`);
    const value = storage.getString(name);
    logger.info(
      "Storage",
      `Read result for ${name}:`,
      value ? "Found" : "Not found",
    );
    return value ?? null;
  },
  setItem: (name: string, value: string) => {
    logger.info("Storage", `Setting item: ${name}`);
    storage.set(name, value);
    logger.info("Storage", `Item set: ${name}`);
  },
  removeItem: (name: string) => {
    logger.info("Storage", `Removing item: ${name}`);
    storage.delete(name);
    logger.info("Storage", `Item removed: ${name}`);
  },
};

// First Time User Store - simplified
export const useFirstTimeStore = create<FirstTimeState>()(
  persist(
    (set) => ({
      isFirstTime: true,
      setNotFirstTime: () => {
        logger.info("FirstTime", "Setting user as not first time");
        set({ isFirstTime: false });
        logger.info("FirstTime", "User marked as not first time");
      },
    }),
    {
      name: "first-time-status",
      storage: createJSONStorage(() => mmkvStorage),
      onRehydrateStorage: () => (state) => {
        logger.info("FirstTime", "Store rehydrated", {
          isFirstTime: state?.isFirstTime,
        });
      },
    },
  ),
);

export const useOnboardingStore = create<OnboardingState>()(
  persist(
    (set) => ({
      isOnboardingCompleted: false,
      setOnboardingCompleted: () => {
        logger.info("Onboarding", "Setting onboarding as completed");
        set({ isOnboardingCompleted: true });
        logger.info("Onboarding", "Onboarding marked as completed");
      },
    }),
    {
      name: "onboarding-status",
      storage: createJSONStorage(() => mmkvStorage),
      onRehydrateStorage: () => (state) => {
        logger.info("Onboarding", "Store rehydrated", {
          isOnboardingCompleted: state?.isOnboardingCompleted,
        });
      },
    },
  ),
);

export const useLocationStore = create<LocationStoreState>()((set) => ({
  location: null,
  isLoading: false,
  error: null,
  setLocation: (location) => {
    logger.info("LocationDataStore", "Setting location", { location });
    set({ location, error: null });
  },
  setLoading: (isLoading) => {
    logger.info("LocationDataStore", "Setting loading state", { isLoading });
    set({ isLoading });
  },
  setError: (error) => {
    logger.info("LocationDataStore", "Setting error", { error });
    set({ error, isLoading: false });
  },
  clearError: () => {
    logger.info("LocationDataStore", "Clearing error");
    set({ error: null });
  },
  clearLocation: () => {
    logger.info("LocationDataStore", "Clearing location");
    set({ location: null, error: null });
  },
  resetLocationState: () => {
    logger.info("LocationDataStore", "Resetting location state");
    set({ location: null, isLoading: false, error: null });
  },
}));

export const usePermissionStore = create<PermissionStoreState>()(
  persist(
    (set) => ({
      microphoneAccess: false,
      setMicrophoneAccess: (access: boolean) => {
        set({ microphoneAccess: access });
      },
    }),
    {
      name: "permission-storage",
      storage: createJSONStorage(() => mmkvStorage),
    },
  ),
);
