import { generateReactNativeHelpers } from "@uploadthing/expo";

import { authClient } from "./auth";
import { getBaseUrl } from "./base-url";

export const { useImageUploader, useDocumentUploader } =
  generateReactNativeHelpers({
    url: `${getBaseUrl()}/api/uploadthing`,
    fetch: (input, init) => {
      const cookies = authClient.getCookie();

      // @ts-expect-error: This is is just an ts error, this is for sure this ts error will not cause any production issues later. all i am doing here is just adding the Authorization header to the request. which fetch does not accpet.

      return fetch(input, {
        ...init,
        headers: {
          ...init?.headers,
          Cookie: cookies,
          "Content-Type": "multipart/form-data",
        },
      });
    },
  });
