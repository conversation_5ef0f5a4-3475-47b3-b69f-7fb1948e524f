import { trpc } from "@/utils/api";
import { useQuery, useQueryClient } from "@tanstack/react-query";

const useWallet = () => {
  const queryClient = useQueryClient();
  const walletQuery = useQuery(trpc.user.getWalletBalance.queryOptions());

  const handleRefreshWallet = async () => {
    await queryClient.invalidateQueries(
      trpc.user.getWalletBalance.queryOptions(),
    );
  };

  return {
    ...walletQuery,
    handleRefreshWallet,
    isRefreshingWallet: walletQuery.isFetching || walletQuery.isRefetching,
  };
};

export default useWallet;
