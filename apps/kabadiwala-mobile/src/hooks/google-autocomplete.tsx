import type { DetectedLocation, GoogleLocationResult } from "@/types/types";
import { useEffect, useState } from "react";
import { Platform } from "react-native";
import * as Location from "expo-location";
import { GoogleService } from "@/services/google";
import { useDebounce } from "use-debounce";

import { useIsMounted } from "./is-mounted";

const isWeb = Platform.OS === "web";

interface Options {
  /**
   * Minimun length of the input before start fetching - default: 2
   */
  minLength?: number;

  /**
   * Debounce request time in ms - default: 300
   */
  debounce?: number;

  /**
   * Debounce options
   */
  debounceOptions?: {
    maxWait?: number;
    leading?: boolean;
    trailing?: boolean;
    equalityFn?: (left: any, right: any) => boolean;
  };

  /**
   * Language for Google query - default: en
   */
  language?: string;

  /**
   * A grouping of places to which you would like to restrict your results
   */
  components?: string;

  /**
   * See https://developers.google.com/places/web-service/autocomplete#place_types = default: address
   */
  queryTypes?:
    | "address"
    | "geocode"
    | "(cities)"
    | "establishment"
    | "geocode|establishment";

  /**
   * The distance (in meters) within which to return place results.
   * Note that setting a radius biases results to the indicated area,
   * but may not fully restrict results to the specified area.
   */
  radius?: string;

  /**
   * The latitude to retrieve place information
   */
  lat?: number;

  /**
   * The longitude to retrieve place information
   */
  lng?: number;

  /**
   * Enable strict mode to return search result only in the area defined by radius, lat and lng
   */
  strictBounds?: boolean;

  /**
   * Proxy url if you want to use the web, this is needed cause of CORS issue
   */
  proxyUrl?: string;
}

export const useGoogleAutocomplete = (apiKey: string, opts: Options = {}) => {
  const {
    minLength = 2,
    debounce = 300,
    debounceOptions = {},
    language = "en",
    queryTypes = "address",
  } = opts;
  const isMounted = useIsMounted();
  const [isSearching, setIsSearching] = useState(false);
  const [isLoadingLocation, setIsLoadingLocation] = useState(false);
  const [term, setTerm] = useState("");
  const [debouncedTerm] = useDebounce(term, debounce, debounceOptions);
  const [locationResults, setLocationResults] = useState<
    GoogleLocationResult[]
  >([]);
  const [selectedLocation, setSelectedLocation] =
    useState<DetectedLocation | null>(null);
  const [searchError, setSearchError] = useState<Error | null>(null);

  const search = async (value: string) => {
    if (isWeb && !opts.proxyUrl) {
      throw new Error("A proxy url is needed for web");
    }

    setIsSearching(true);
    try {
      const results = await GoogleService.search(
        value,
        {
          key: apiKey,
          language,
          types: queryTypes,
          strictBounds: opts.strictBounds,
          lat: opts.lat,
          lng: opts.lng,
          radius: opts.radius,
          components: opts.components,
        },
        opts.proxyUrl,
      );

      setLocationResults(results.predictions);
    } catch (error) {
      if (error instanceof Error) {
        setSearchError(error);
      }
    } finally {
      setIsSearching(false);
    }
  };

  const searchDetails = async (placeId: string) => {
    return GoogleService.searchDetails(placeId, {
      key: apiKey,
      language,
      types: queryTypes,
      components: opts.components,
    });
  };

  /**
   * Gets detailed location information for a place ID in the same format as web
   * @param placeId Google Place ID
   * @returns Location details with formatted address information
   */
  const getLocationDetails = async (
    placeId: string,
  ): Promise<DetectedLocation> => {
    try {
      const location = await GoogleService.getLocationDetails(placeId, {
        key: apiKey,
        language,
        types: queryTypes,
        components: opts.components,
      });

      if (isMounted()) {
        setSelectedLocation(location);
        // Update the term to show the location display name
        if (location.address?.display) {
          setTerm(location.address.display);
        }
      }

      return location;
    } catch (error) {
      console.error("Error getting location details:", error);
      if (error instanceof Error) {
        setSearchError(error);
      }
      throw error;
    }
  };

  /**
   * Gets the user's current location using Expo's Location API
   * @returns Promise with the detected location
   */
  const getCurrentLocation = async (): Promise<DetectedLocation> => {
    setIsLoadingLocation(true);
    try {
      // Request permission to access location
      const { status } = await Location.requestForegroundPermissionsAsync();

      if (status !== "granted") {
        throw new Error("Permission to access location was denied");
      }

      // Get current position
      const position = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
      });

      const { latitude, longitude } = position.coords;

      // Get address from coordinates using the service we implemented
      const location = await GoogleService.getLocationFromCoordinates(
        latitude,
        longitude,
      );

      if (isMounted()) {
        setSelectedLocation(location);
        if (location.address?.display) {
          setTerm(location.address.display);
        }
      }

      setIsLoadingLocation(false);
      return location;
    } catch (error) {
      setIsLoadingLocation(false);
      console.error("Error getting current location:", error);
      if (error instanceof Error) {
        setSearchError(error);
      }
      throw error;
    }
  };

  const clearSearch = () => {
    if (isMounted()) {
      setLocationResults([]);
      setIsSearching(false);
      setSelectedLocation(null);
    }
  };

  useEffect(() => {
    if (debouncedTerm.length >= minLength) {
      search(debouncedTerm);
    }

    if (debouncedTerm.length < minLength) {
      setLocationResults([]);
    }
  }, [debouncedTerm]);

  return {
    locationResults,
    isSearching,
    isLoadingLocation,
    searchError,
    clearSearch,
    setTerm,
    term,
    searchDetails,
    getLocationDetails,
    getCurrentLocation,
    selectedLocation,
  };
};
