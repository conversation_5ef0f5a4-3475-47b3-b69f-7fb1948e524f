import type { DetectedLocation } from "@/types/types";
import { useCallback } from "react";
import * as Location from "expo-location";
import { GoogleService } from "@/services/google";
import { useLocationStore } from "@/utils/store";

interface UseLocationReturn {
  location: DetectedLocation | null;
  isLoading: boolean;
  error: string | null;
  getCurrentLocation: () => Promise<DetectedLocation | null>;
  requestPermission: () => Promise<boolean>;
  clearError: () => void;
}

export const useLocation = (): UseLocationReturn => {
  const {
    location,
    isLoading,
    error,
    setLocation,
    setLoading,
    setError,
    clearError,
  } = useLocationStore();

  const requestPermission = useCallback(async (): Promise<boolean> => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      return status === Location.PermissionStatus.GRANTED;
    } catch (err) {
      console.error("Error requesting location permission:", err);
      setError("Failed to request location permission");
      return false;
    }
  }, [setError]);

  const getCurrentLocation =
    useCallback(async (): Promise<DetectedLocation | null> => {
      setLoading(true);
      clearError();

      try {
        // Check if we have permission
        const { status } = await Location.getForegroundPermissionsAsync();

        if (status !== Location.PermissionStatus.GRANTED) {
          const permissionGranted = await requestPermission();
          if (!permissionGranted) {
            throw new Error(
              "Location permission is required to get your current location",
            );
          }
        }

        // Get current position with high accuracy
        const position = await Location.getCurrentPositionAsync({
          accuracy: Location.Accuracy.High,
        });

        const { latitude, longitude } = position.coords;

        // Get detailed address information using your existing GoogleService
        const detectedLocation = await GoogleService.getLocationFromCoordinates(
          latitude,
          longitude,
        );

        setLocation(detectedLocation);
        return detectedLocation;
      } catch (err) {
        console.error("Error getting current location:", err);
        let errorMessage = "Failed to get your current location";

        if (err instanceof Error) {
          if (err.message.includes("permission")) {
            errorMessage =
              "Location permission is required. Please enable location access in your device settings.";
          } else if (err.message.includes("timeout")) {
            errorMessage = "Location request timed out. Please try again.";
          } else if (err.message.includes("network")) {
            errorMessage =
              "Network error while getting location. Please check your internet connection.";
          } else {
            errorMessage = err.message;
          }
        }

        setError(errorMessage);
        return null;
      } finally {
        setLoading(false);
      }
    }, [requestPermission, setLoading, clearError, setLocation, setError]);

  return {
    location,
    isLoading,
    error,
    getCurrentLocation,
    requestPermission,
    clearError,
  };
};
