import { trpc } from "@/utils/api";
import { useQuery, useQueryClient } from "@tanstack/react-query";

const useActiveOrder = () => {
  const queryClient = useQueryClient();

  const {
    data: activeOrder,
    isPending,
    isLoading,
    isError,
    isRefetching: isRefetchingActiveOrder,
  } = useQuery(
    trpc.order.getActiveOrder.queryOptions(undefined, {
      gcTime: 0, // Don't cache the data in memory for long
    }),
  );

  const handleRefreshActiveOrder = async () => {
    await queryClient.invalidateQueries(
      trpc.order.getActiveOrder.queryOptions(),
    );
    await queryClient.invalidateQueries(
      trpc.order.getNearbyOrder.queryOptions(),
    );
  };

  const invalidateActiveOrder = async () => {
    // Force remove cached data and refetch
    queryClient.removeQueries(trpc.order.getActiveOrder.queryOptions());
    await queryClient.invalidateQueries(
      trpc.order.getActiveOrder.queryOptions(),
    );
    await queryClient.invalidateQueries(
      trpc.order.getNearbyOrder.queryOptions(),
    );
  };

  return {
    orderData: activeOrder,
    isPending: isPending,
    isError: isError,
    refetchActiveOrder: handleRefreshActiveOrder,
    invalidateActiveOrder,
    isRefetchingActiveOrder: isRefetchingActiveOrder || isPending || isLoading,
    haveActiveOrder: !!activeOrder,
  };
};

export default useActiveOrder;
