import { trpc } from "@/utils/api";
import { useQuery, useQueryClient } from "@tanstack/react-query";

const useProfile = () => {
  const queryClient = useQueryClient();
  const profileQuery = useQuery(trpc.user.getProfile.queryOptions());

  const handleProfileRefresh = async () => {
    await queryClient.invalidateQueries(trpc.user.getProfile.queryOptions());
  };

  return {
    refreshProfile: handleProfileRefresh,
    ...profileQuery,
  };
};

export default useProfile;
