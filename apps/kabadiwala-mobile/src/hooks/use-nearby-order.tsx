import { CHECK_FOR_ACTIVE_ORDERS_INTERVAL } from "@/lib/constant";
import { trpc } from "@/utils/api";
import { useQuery, useQueryClient } from "@tanstack/react-query";

import useActiveOrder from "./use-active-order";
import useOnDuty from "./use-on-duty";

const useNearbyOrder = () => {
  const { haveActiveOrder, isPending: isPendingActiveOrder } = useActiveOrder();
  const { isOnDuty } = useOnDuty();

  const defaultEnabled =
    haveActiveOrder === false &&
    isPendingActiveOrder === false &&
    isOnDuty === true;

  const queryClient = useQueryClient();
  const {
    data: nearbyOrderData,
    isRefetching,
    ...queryResult
  } = useQuery(
    trpc.order.getNearbyOrder.queryOptions(undefined, {
      refetchInterval: CHECK_FOR_ACTIVE_ORDERS_INTERVAL,
      enabled: defaultEnabled,
    }),
  );

  const nearbyOrder = haveActiveOrder ? null : nearbyOrderData;

  const handleNearbyOrderRefresh = async () => {
    await queryClient.invalidateQueries(
      trpc.order.getNearbyOrder.queryOptions(),
    );
  };

  return {
    nearbyOrder,
    refreshNearbyOrder: handleNearbyOrderRefresh,
    isRefreshingNearbyOrder: isRefetching,
    ...queryResult,
  };
};

export default useNearbyOrder;
