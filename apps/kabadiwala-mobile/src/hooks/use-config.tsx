import { useMemo } from "react";
import { trpc } from "@/utils/api";
import { useQuery } from "@tanstack/react-query";

const useConfig = () => {
  const { data } = useQuery(trpc.utils.getSystemConfigData.queryOptions());

  const config = useMemo(() => {
    const radiusToShowKabadiwalaNewOrder = data?.find(
      (config) => config.key === "RADIUS_TO_SHOW_KABADIWALA_NEW_ORDER",
    );

    const minWalletBalanceRequiredToAcceptOrder = data?.find(
      (config) =>
        config.key === "MINIMUM_WALLET_BALANCE_TO_ACCEPT_ORDER_BY_KABADIWALA",
    );

    const securityFeePercentage = data?.find(
      (config) => config.key === "SECURITY_FEE_PERCENTAGE",
    );

    const gstPercentage = data?.find(
      (config) => config.key === "GST_PERCENTAGE",
    );

    const contactEmail = data?.find((config) => config.key === "CONTACT_EMAIL");

    const contactPhone = data?.find((config) => config.key === "CONTACT_PHONE");

    const supportEmail = data?.find((config) => config.key === "SUPPORT_EMAIL");

    const supportPhone = data?.find((config) => config.key === "SUPPORT_PHONE");

    return {
      radiusToShowKabadiwalaNewOrder,
      minWalletBalanceRequiredToAcceptOrder,
      securityFeePercentage,
      gstPercentage,
      contactEmail,
      contactPhone,
      supportEmail,
      supportPhone,
    };
  }, [data]);

  return config;
};

export default useConfig;
