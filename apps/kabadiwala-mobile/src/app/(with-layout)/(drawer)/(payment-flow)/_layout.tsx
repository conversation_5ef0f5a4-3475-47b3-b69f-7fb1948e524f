import React from "react";
import { Stack } from "expo-router";
import OrderDetailsHeader from "@/components/shared/orders/order-details-header";
import PaymentHeader from "@/components/shared/payment/payment-header";

const PaymentFlowLayout = () => {
  return (
    <Stack>
      <Stack.Screen
        name="pay-for-order/index"
        options={{
          title: "Payment",
          headerShown: true,
          header: () => <OrderDetailsHeader title="Pay to Customer" />,
        }}
      />
      <Stack.Screen
        name="payment-confirmation/index"
        options={{
          title: "Payment Confirmation",
          headerShown: true,
          header: () => <PaymentHeader title="Payment Confirmation" />,
        }}
      />
    </Stack>
  );
};

export default PaymentFlowLayout;
