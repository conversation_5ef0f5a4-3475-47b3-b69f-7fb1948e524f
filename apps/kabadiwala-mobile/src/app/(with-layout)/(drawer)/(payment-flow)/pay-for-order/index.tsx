import React from "react";
import { Image, Text, View } from "react-native";
import { showMessage } from "react-native-flash-message";
import { ScrollView } from "react-native-gesture-handler";
import { SafeAreaView } from "react-native-safe-area-context";
import { router } from "expo-router";
import Error from "@/components/shared/error";
import SkeletonLoading from "@/components/shared/skeleton-loading";
import { Button } from "@/components/ui/button";
import useActiveOrder from "@/hooks/use-active-order";
import { trpc } from "@/utils/api";
import TrashIcon from "@assets/icons/trash.png";
import AntDesign from "@expo/vector-icons/AntDesign";
import {
  skipToken,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";

const PayForOrderIndex = () => {
  const { orderData, invalidateActiveOrder } = useActiveOrder();
  const queryClient = useQueryClient();

  const {
    data: finalAmountData,
    isPending,
    isError,
    refetch,
  } = useQuery(
    trpc.order.getFinalOrderTotalAmountWithBreakdown.queryOptions(
      orderData?.id
        ? {
            orderId: orderData.id,
          }
        : skipToken,
    ),
  );

  const { mutate: processPayment, isPending: isPendingPayment } = useMutation(
    trpc.order.processPayment.mutationOptions({
      onSuccess: async (opts) => {
        showMessage({
          message: opts.message,
          type: "success",
        });

        // Clear all order-related cache completely
        queryClient.removeQueries(trpc.order.getActiveOrder.queryOptions());
        queryClient.removeQueries(trpc.order.getNearbyOrder.queryOptions());
        queryClient.removeQueries(
          trpc.order.getOrderHistory.infiniteQueryOptions({ limit: 10 }),
        );

        // Force refetch with fresh data
        await invalidateActiveOrder();

        router.push({
          pathname: "/payment-confirmation",
          params: { transactionId: opts.transactionId },
        });
      },
      onError: (error) => {
        showMessage({
          message: error.message || "Failed to process payment",
          type: "danger",
        });
      },
    }),
  );

  if (isPending) {
    return (
      <View className="px-4 py-5">
        <SkeletonLoading />
      </View>
    );
  }

  if (isError) {
    return (
      <Error
        title="Failed to load payment details"
        message="Please try again later."
        onRetry={refetch}
      />
    );
  }

  const handleConfirmPayment = () => {
    const orderId = orderData?.id;

    if (!orderId) {
      showMessage({
        message: "Payment processing is not implemented yet.",
        type: "danger",
      });
      return;
    }

    processPayment({
      orderId: orderId,
    });
  };

  return (
    <SafeAreaView className="-pt-safe-offset-[110px] flex-1 bg-white px-5">
      <ScrollView
        className="flex-1"
        contentContainerClassName="pb-10"
        showsVerticalScrollIndicator={false}
      >
        <View className="gap-6">
          {/* Header */}
          <View className="flex-row items-center gap-3 rounded-[20px] border border-black-150 bg-white px-4 py-5">
            <View className="rounded-lg bg-black-50 p-2">
              <Image source={TrashIcon} className="h-6 w-6" />
            </View>
            <Text className="font-inter-semibold text-lg font-semibold leading-6 text-black-800">
              Payment Breakdown
            </Text>
          </View>

          {/* Items Breakdown */}
          <View className="rounded-[20px] border border-black-150 bg-white px-4 py-5">
            <Text className="font-inter-semibold mb-4 text-base font-semibold text-black-800">
              Items
            </Text>
            <View className="gap-3">
              {finalAmountData.items.map((item, index) => (
                <View key={index} className="rounded-lg bg-black-0 p-3">
                  <View className="flex-row items-center justify-between">
                    <View className="flex-1">
                      <Text className="font-inter-semibold font-semibold leading-5 text-black-700">
                        {item.name}
                      </Text>
                      <Text className="text-xs text-black-600">
                        ₹{item.rate} × {item.quantity}
                      </Text>
                    </View>
                    <Text className="font-inter-semibold font-semibold leading-5 text-black-800">
                      ₹{item.total}
                    </Text>
                  </View>
                </View>
              ))}
            </View>
          </View>

          {/* Amount Breakdown */}
          <View className="rounded-[20px] border border-black-150 bg-white px-4 py-5">
            <Text className="font-inter-semibold mb-4 text-base font-semibold text-black-800">
              Amount Breakdown
            </Text>

            <View className="gap-3">
              {/* Base Amount */}
              <View className="flex-row items-center justify-between">
                <Text className="font-inter-medium text-black-700">
                  Base Amount
                </Text>
                <Text className="font-inter-semibold font-semibold text-black-800">
                  ₹{finalAmountData.estimatedAmount}
                </Text>
              </View>

              {/* Pickup Charge (if below min order) */}
              {finalAmountData.pickupCharge > 0 && (
                <View className="flex-row items-center justify-between">
                  <Text className="font-inter-medium text-red-600">
                    - Pickup Charge
                  </Text>
                  <Text className="font-inter-semibold font-semibold text-red-600">
                    -₹{finalAmountData.pickupCharge}
                  </Text>
                </View>
              )}

              {/* GST on Pickup Charge */}
              {finalAmountData.gstAmount > 0 && (
                <View className="flex-row items-center justify-between">
                  <Text className="font-inter-medium text-red-600">
                    - GST ({finalAmountData.gstPercent}%)
                  </Text>
                  <Text className="font-inter-semibold font-semibold text-red-600">
                    -₹{finalAmountData.gstAmount}
                  </Text>
                </View>
              )}

              {/* Amount Without Service Fee */}
              {/* <View className="flex-row items-center justify-between">
                <Text className="font-inter-medium text-black-700">
                  Amount After Charges
                </Text>
                <Text className="font-inter-semibold font-semibold text-black-800">
                  ₹{finalAmountData.amountWithoutServiceFee}
                </Text>
              </View> */}

              {/* Security Fee */}
              <View className="flex-row items-center justify-between">
                <Text className="font-inter-medium">
                  Security Amount ({finalAmountData.securityFeePercentage}%)
                </Text>
                <Text className="font-inter-semibold font-semibold">
                  ₹{finalAmountData.securityFeeAmount}
                </Text>
              </View>

              {/* Divider */}
              <View className="my-2 h-[1px] bg-black-200" />

              {/* Final Amount to Pay Customer */}
              <View className="flex-row items-center justify-between">
                <Text className="font-inter-semibold font-semibold text-black-800">
                  Amount to Pay Customer
                </Text>
                <Text className="font-inter-semibold font-semibold text-teal-700">
                  ₹{finalAmountData.amountToPayCustomer}
                </Text>
              </View>
            </View>
          </View>

          {/* Wallet Information */}
          <View className="rounded-[20px] border border-black-150 bg-white px-4 py-5">
            <Text className="font-inter-semibold mb-4 text-base font-semibold text-black-800">
              Wallet Information
            </Text>

            <View className="gap-3">
              <View className="flex-row items-center justify-between">
                <Text className="font-inter-medium text-black-700">
                  Current Balance
                </Text>
                <Text className="font-inter-semibold font-semibold text-black-800">
                  ₹{finalAmountData.kabadiwalaWalletBalance}
                </Text>
              </View>

              <View className="flex-row items-center justify-between">
                <Text className="font-inter-medium text-black-700">
                  Balance After Payment
                </Text>
                <Text
                  className={`font-inter-semibold font-semibold ${
                    finalAmountData.balanceAfterPayment >= 0
                      ? "text-teal-700"
                      : "text-red-600"
                  }`}
                >
                  ₹{finalAmountData.balanceAfterPayment}
                </Text>
              </View>

              {/* Payment Status */}
              {/* <View
                className={`mt-3 rounded-lg p-3 ${
                  finalAmountData.canProceedWithPayment
                    ? "border border-teal-300 bg-teal-100"
                    : "border border-red-300 bg-red-100"
                }`}
              >
                <Text
                  className={`font-inter-semibold text-center font-semibold ${
                    finalAmountData.canProceedWithPayment
                      ? "text-teal-800"
                      : "text-red-800"
                  }`}
                >
                  {finalAmountData.canProceedWithPayment
                    ? "✓ Payment can proceed"
                    : "⚠ Wallet balance will be negative after payment"}
                </Text>
              </View> */}
            </View>
          </View>

          {/* Final Amount */}
          <View className="rounded-[20px] border border-teal-600 bg-teal-50 px-4 py-5">
            <View className="flex-row items-center justify-between">
              <Text className="font-inter-bold text-xl font-bold text-teal-850">
                Total Deduction Amount
              </Text>
              <Text className="font-jakarta-bold text-2xl font-bold text-teal-850">
                ₹{finalAmountData.totalAmount}
              </Text>
            </View>
          </View>

          <Button
            className="rounded-[20px] bg-teal-850"
            onPress={handleConfirmPayment}
            disabled={isPendingPayment}
          >
            {isPendingPayment ? (
              <AntDesign
                name="loading1"
                size={16}
                color="white"
                className="mr-3 animate-spin"
              />
            ) : null}
            <Text className="font-jakarta-semibold text-xl font-semibold text-teal-100">
              {isPendingPayment ? "Processing..." : "Confirm Payment"}
            </Text>
          </Button>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default PayForOrderIndex;
