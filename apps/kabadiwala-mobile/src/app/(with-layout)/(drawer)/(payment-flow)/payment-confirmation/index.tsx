import React from "react";
import { RefreshControl, Text, View } from "react-native";
import { ScrollView } from "react-native-gesture-handler";
import { useLocalSearchParams } from "expo-router";
import ContactSupportCard from "@/components/shared/contact-support-card";
import Error from "@/components/shared/error";
import NavigateToScraphubButton from "@/components/shared/orders/navigate-to-scraphub-button";
import RateSellerCard from "@/components/shared/orders/rate-seller-card";
import SkeletonLoading from "@/components/shared/skeleton-loading";
import { trpc } from "@/utils/api";
import { Ionicons } from "@expo/vector-icons";
import { skipToken, useQuery } from "@tanstack/react-query";

const PaymentConfirmation = () => {
  const { transactionId } = useLocalSearchParams();

  const {
    data: paymentDetails,
    isPending,
    isError,
    refetch,
    isFetching,
  } = useQuery(
    trpc.order.getPaymentDetailsById.queryOptions(
      transactionId && typeof transactionId === "string"
        ? { paymentId: transactionId }
        : skipToken,
    ),
  );

  if (isPending) {
    return (
      <View className="px-4 py-5">
        <SkeletonLoading />
      </View>
    );
  }

  if (isError) {
    return (
      <View className="px-4 py-5">
        <Error
          title="Error fetching payment details"
          message="Please try again later"
          onRetry={refetch}
        />
      </View>
    );
  }

  const getStatusConfig = (status: string) => {
    switch (status) {
      case "COMPLETED":
        return {
          bgColor: "bg-teal-50",
          borderColor: "border-teal-200",
          iconColor: "#00CCCC",
          iconName: "checkmark-circle" as const,
          title: "Payment Successful",
          titleColor: "text-teal-700",
        };
      case "FAILED":
        return {
          bgColor: "bg-red-50",
          borderColor: "border-red-200",
          iconColor: "#EF4444",
          iconName: "close-circle" as const,
          title: "Payment Failed",
          titleColor: "text-red-700",
        };
      case "PENDING":
      default:
        return {
          bgColor: "bg-yellow-50",
          borderColor: "border-yellow-200",
          iconColor: "#FDB400",
          iconName: "time" as const,
          title: "Payment Pending",
          titleColor: "text-yellow-700",
        };
    }
  };

  const statusConfig = getStatusConfig(paymentDetails.status);

  const formatAmount = (amount: number, currency: string) => {
    return new Intl.NumberFormat("en-IN", {
      style: "currency",
      currency: currency || "INR",
    }).format(amount);
  };

  return (
    <ScrollView
      className="flex-1 bg-black-0"
      refreshControl={
        <RefreshControl refreshing={isFetching} onRefresh={refetch} />
      }
    >
      <View className="px-4 py-6">
        {/* Status Card */}
        <View
          className={`${statusConfig.bgColor} ${statusConfig.borderColor} mb-6 items-center rounded-lg border p-6`}
        >
          <Ionicons
            name={statusConfig.iconName}
            size={64}
            color={statusConfig.iconColor}
          />
          <Text
            className={`${statusConfig.titleColor} font-jakarta-bold mb-2 mt-4 text-xl`}
          >
            {statusConfig.title}
          </Text>
          <Text className="font-inter-regular text-center text-base text-black-600">
            Your payment has been {paymentDetails.status.toLowerCase()}
          </Text>
        </View>

        {/* Payment Details Card */}
        <View className="mb-4 rounded-lg border border-black-150 bg-white p-6">
          <Text className="font-jakarta-bold mb-4 text-lg text-black-800">
            Payment Details
          </Text>

          {/* Amount */}
          <View className="flex-row items-center justify-between border-b border-black-100 py-3">
            <Text className="font-inter-medium text-base text-black-600">
              Amount
            </Text>
            <Text className="font-inter-bold text-lg text-black-800">
              {formatAmount(
                Number(paymentDetails.amount),
                paymentDetails.currency ?? "INR",
              )}
            </Text>
          </View>

          {/* Transaction ID */}
          <View className="flex-row items-center justify-between border-b border-black-100 py-3">
            <Text className="font-inter-medium text-base text-black-600">
              Transaction ID
            </Text>
            <Text className="font-inter-regular ml-4 flex-1 text-right text-sm text-black-800">
              {paymentDetails.id}
            </Text>
          </View>

          {/* Status */}
          <View className="flex-row items-center justify-between border-b border-black-100 py-3">
            <Text className="font-inter-medium text-base text-black-600">
              Status
            </Text>
            <View
              className={`${statusConfig.bgColor} ${statusConfig.borderColor} rounded-full border px-3 py-1`}
            >
              <Text
                className={`${statusConfig.titleColor} font-inter-semibold text-sm`}
              >
                {paymentDetails.status}
              </Text>
            </View>
          </View>

          {/* Transaction For */}
          <View className="flex-row items-center justify-between py-3">
            <Text className="font-inter-medium text-base text-black-600">
              Transaction For
            </Text>
            <Text className="font-inter-regular text-base capitalize text-black-800">
              {paymentDetails.transactionFor.replace("_", " ")}
            </Text>
          </View>
        </View>

        <NavigateToScraphubButton />

        {paymentDetails.orderId && paymentDetails.order?.sellerId ? (
          <RateSellerCard
            orderId={paymentDetails.orderId}
            sellerId={paymentDetails.order.sellerId}
          />
        ) : null}

        {/* Additional Info Card */}
        <ContactSupportCard orderId={paymentDetails.orderId ?? undefined} />

        <View className="h-[50px]" />
      </View>
    </ScrollView>
  );
};

export default PaymentConfirmation;
