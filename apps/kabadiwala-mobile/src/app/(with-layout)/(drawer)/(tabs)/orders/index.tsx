import React from "react";
import { Text, View } from "react-native";
import { FlatList, RefreshControl } from "react-native-gesture-handler";
import { SafeAreaView } from "react-native-safe-area-context";
import Error from "@/components/shared/error";
import OrderCard from "@/components/shared/orders/order-card";
import SkeletonLoading from "@/components/shared/skeleton-loading";
import { trpc } from "@/utils/api";
import { useInfiniteQuery } from "@tanstack/react-query";

const OrderIndex = () => {
  const { data, isPending, isError, fetchNextPage, isFetchingNextPage } =
    useInfiniteQuery({
      ...trpc.order.getOrderHistory.infiniteQueryOptions({
        limit: 10,
      }),
      initialPageParam: null,
      getNextPageParam: (lastPage) => lastPage.nextCursor ?? null,
    });

  const allOrders = data?.pages
    ? data.pages.flatMap((page) => page.orders)
    : [];

  const handleLoadMore = async () => {
    if (isFetchingNextPage || !data?.pages) return;

    await fetchNextPage();
  };

  const renderFooter = () => {
    if (isFetchingNextPage) {
      return (
        <View className="py-4">
          <SkeletonLoading type="details" />
        </View>
      );
    }
    return null;
  };

  if (isPending) {
    return (
      <View className="mt-5 flex-1">
        <SkeletonLoading type="details" />
      </View>
    );
  }

  if (isError) {
    return (
      <View className="mt-5 flex-1">
        <Error
          title="Failed to load order history"
          message="Please try again later."
          onRetry={handleLoadMore}
        />
      </View>
    );
  }

  return (
    <SafeAreaView className="-py-safe-offset-40 flex-1 px-4">
      {allOrders.length === 0 ? (
        <View className="flex-1 items-center justify-center">
          <Text className="text-center text-lg font-medium text-black-700">
            No previous order history
          </Text>
          <Text className="mt-2 text-center text-sm text-black-600">
            Your completed or active orders will appear here
          </Text>
        </View>
      ) : (
        <FlatList
          data={allOrders}
          renderItem={({ item }) => <OrderCard orderItem={item} />}
          keyExtractor={(item) => item.id}
          refreshControl={
            <RefreshControl
              refreshing={isPending}
              onRefresh={handleLoadMore}
              colors={["grey"]}
              progressBackgroundColor={"black"}
            />
          }
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.5}
          ListFooterComponent={renderFooter}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ flexGrow: 1 }}
        />
      )}
    </SafeAreaView>
  );
};

export default OrderIndex;
