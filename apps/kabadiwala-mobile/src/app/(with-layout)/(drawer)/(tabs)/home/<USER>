import type { RelativePathString } from "expo-router";
import type { ImageSourcePropType } from "react-native";
import { Alert, Image, Text, View } from "react-native";
import { showMessage } from "react-native-flash-message";
import { RefreshControl, ScrollView } from "react-native-gesture-handler";
import { SafeAreaView } from "react-native-safe-area-context";
import { Link, router } from "expo-router";
import HomePageMap from "@/components/shared/google/home-page-map";
import TodaysEarning from "@/components/shared/home/<USER>";
import ActiveOrderStrip from "@/components/shared/orders/active-order-strip";
import { IncommingOrderPickupRequestModal } from "@/components/shared/orders/incomming-order-request-modal";
import AddMoneyModal from "@/components/shared/wallet/add-money-modal";
import WalletBalanceDisplayCard from "@/components/shared/wallet/wallet-balance-display-card";
import { Modal, useModal } from "@/components/ui/modal";
import useActiveOrder from "@/hooks/use-active-order";
import useNearbyOrder from "@/hooks/use-nearby-order";
import useOnDuty from "@/hooks/use-on-duty";
import { trpc } from "@/utils/api";
import CalendarIcon from "@assets/icons/calendar.png";
import SupportIcon from "@assets/icons/customer-support.png";
import HistoryIcon from "@assets/icons/history.png";
import ThumbsUpIcon from "@assets/icons/thumbs-up.png";
import { useMutation, useQueryClient } from "@tanstack/react-query";

const HomeIndex = () => {
  const { isOnDuty } = useOnDuty();
  const {
    ref: walletModalRef,
    present: presentWalletModal,
    dismiss: dismissWalletModal,
  } = useModal();

  const { refetchActiveOrder, isRefetchingActiveOrder } = useActiveOrder();

  const { refreshNearbyOrder, isRefreshingNearbyOrder } = useNearbyOrder();

  const handleRefreshControl = async () => {
    if (isRefetchingActiveOrder || isRefreshingNearbyOrder) {
      return;
    }

    await Promise.allSettled([refetchActiveOrder(), refreshNearbyOrder()]);
  };

  return (
    <SafeAreaView className="-py-safe-offset-40 flex-1">
      <TodaysEarning />

      <ScrollView
        className="flex-1 bg-white"
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={isRefetchingActiveOrder || isRefreshingNearbyOrder}
            onRefresh={handleRefreshControl}
            colors={["grey"]}
            progressBackgroundColor={"black"}
          />
        }
      >
        <View className="flex-1 gap-4">
          <HomePageMap />
          <ActiveOrderStrip />

          <WalletSection onTopUpPress={presentWalletModal} />
          <QuickLinksSection />
        </View>

        <View className="h-[40px]" />
      </ScrollView>

      <OrderHandlingSection isOnDuty={isOnDuty} />

      <Modal
        ref={walletModalRef}
        children={<AddMoneyModal onDismiss={dismissWalletModal} />}
        onDismiss={dismissWalletModal}
      />
    </SafeAreaView>
  );
};

export default HomeIndex;

const WalletSection = ({ onTopUpPress }: { onTopUpPress: () => void }) => (
  <View className="px-4">
    <WalletBalanceDisplayCard onTopUpPress={onTopUpPress} />
  </View>
);

const QuickLinksSection = () => (
  <View className="flex-col gap-6 px-4">
    <Text className="font-jakarta-semibold text-lg font-semibold leading-7 text-black-600">
      Quick Links
    </Text>

    <View className="flex-row flex-wrap gap-3">
      <View className="w-[48%]">
        <QuickLinkCard
          icon={ThumbsUpIcon}
          title="Notifications"
          description="Check all notifications"
          link="/notifications"
        />
      </View>
      <View className="w-[48%]">
        <QuickLinkCard
          icon={HistoryIcon}
          title="Order History"
          description="View order history"
          link="/orders"
        />
      </View>
      <View className="w-[48%]">
        <QuickLinkCard
          icon={CalendarIcon}
          title="Work Hours"
          description="Set your availability"
          link="/general-settings/schedule-hours"
        />
      </View>
      <View className="w-[48%]">
        <QuickLinkCard
          icon={SupportIcon}
          title="Help & Support"
          description="Get help with your orders"
          link="/contact-support"
        />
      </View>
    </View>
  </View>
);

const QuickLinkCard = ({
  icon,
  title,
  description,
  link,
}: {
  icon: ImageSourcePropType;
  title: string;
  description: string;
  link: RelativePathString;
}) => {
  return (
    <Link href={link} className="">
      <View className="w-full flex-1 flex-col gap-5 rounded-[20px] bg-black-50 px-6 py-5">
        <Image source={icon} className="h-6 w-6" />
        <View className="flex-col gap-1.5">
          <Text className="font-jakarta-bold text-[15px] font-bold leading-6 text-black-800">
            {title}
          </Text>
          <Text className="font-inter leading-5 text-black-600">
            {description}
          </Text>
        </View>
      </View>
    </Link>
  );
};

const OrderHandlingSection = ({ isOnDuty }: { isOnDuty: boolean }) => {
  const { present, dismiss, ref } = useModal();
  const queryClient = useQueryClient();
  const { refetchActiveOrder, haveActiveOrder } = useActiveOrder();
  const { nearbyOrder, refreshNearbyOrder } = useNearbyOrder();

  const { mutate: acceptOrder } = useMutation(
    trpc.order.acceptOrder.mutationOptions({
      onSuccess: async (opts) => {
        showMessage({
          message: "Order accepted successfully",
          type: "success",
        });
        await Promise.allSettled([refreshNearbyOrder(), refetchActiveOrder()]);

        router.push("/congratulations");
        dismiss();
      },
      onError: (error) => {
        showMessage({
          message: error.message,
          type: "danger",
        });
      },
    }),
  );

  const { mutate: rejectOrder } = useMutation(
    trpc.order.rejectOrder.mutationOptions({
      onSuccess: async (opts) => {
        await queryClient.invalidateQueries(
          trpc.order.getNearbyOrder.queryOptions(),
        );
        await refetchActiveOrder();
        showMessage({
          message: opts.message,
          type: "success",
        });
        dismiss();
      },
    }),
  );

  const handleOrderAccept = () => {
    if (!nearbyOrder) {
      console.error("No nearby order found");
      return;
    }
    acceptOrder({ orderId: nearbyOrder.id });
  };

  const handleOrderRejectPress = () => {
    if (!nearbyOrder) {
      console.error("No nearby order found to reject");
      return;
    }

    Alert.alert("Reject Order", "Are you sure you want to reject this order?", [
      { text: "Cancel", style: "cancel" },
      {
        text: "Reject",
        onPress: () => rejectOrder({ orderId: nearbyOrder.id }),
      },
    ]);
  };

  // Show the incoming order pickup request modal
  // Only show if we have a nearby order, we're on duty, and we don't already have an active order
  if (nearbyOrder && isOnDuty && !haveActiveOrder) {
    present();
  } else if (!nearbyOrder || haveActiveOrder) {
    // Make sure we dismiss the modal if we no longer have a nearby order or got an active order
    dismiss();
  }

  return nearbyOrder && isOnDuty && !haveActiveOrder ? (
    <IncommingOrderPickupRequestModal
      ref={ref}
      incomingOrder={nearbyOrder}
      onDismiss={dismiss}
      onReject={handleOrderRejectPress}
      onAccept={handleOrderAccept}
    />
  ) : null;
};
