import React from "react";
import { View } from "react-native";
import { Tabs } from "expo-router";
import BottomTabBar from "@/components/shared/home/<USER>";

const TabsLayout = () => {
  return (
    <Tabs
      tabBar={(props) => <BottomTabBar {...props} />}
      screenLayout={(props) => (
        <View className="flex-1 bg-white">{props.children}</View>
      )}
    >
      <Tabs.Screen
        name="home/index"
        options={{ title: "Home", headerShown: false }}
      />
      <Tabs.Screen
        name="orders/index"
        options={{ title: "Orders", headerShown: false }}
      />
    </Tabs>
  );
};

export default TabsLayout;
