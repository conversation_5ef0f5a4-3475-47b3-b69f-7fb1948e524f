import { View } from "react-native";
import { Stack } from "expo-router";

const ProfileLayout = () => {
  return (
    <Stack
      screenLayout={(props) => (
        <View className="flex-1 bg-red-400">{props.children}</View>
      )}
    >
      <Stack.Screen
        name="general-settings"
        options={{
          title: "General Settings",
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="terms"
        options={{
          title: "Terms and Conditions",
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="support"
        options={{
          title: "Support",
          headerShown: false,
        }}
      />
    </Stack>
  );
};
export default ProfileLayout;
