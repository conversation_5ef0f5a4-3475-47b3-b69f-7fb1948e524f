import { Stack } from "expo-router";
import SectionHeader from "@/components/shared/profile/section-header";

const TermsLayout = () => {
  return (
    <Stack
      screenOptions={{
        header: (props) => <SectionHeader props={props} />,
      }}
    >
      <Stack.Screen
        name="faq/index"
        options={{
          title: "Terms and Conditions",
          headerShown: true,
        }}
      />
      <Stack.Screen
        name="help/index"
        options={{
          title: "Rate Us",
          headerShown: true,
        }}
      />
      <Stack.Screen
        name="rate-us/index"
        options={{
          title: "Rate Us",
          headerShown: true,
        }}
      />
    </Stack>
  );
};
export default TermsLayout;
