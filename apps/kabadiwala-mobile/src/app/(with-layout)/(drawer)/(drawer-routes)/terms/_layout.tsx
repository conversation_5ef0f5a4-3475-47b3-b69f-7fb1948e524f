import { Stack } from "expo-router";
import SectionHeader from "@/components/shared/profile/section-header";

const TermsLayout = () => {
  return (
    <Stack
      screenOptions={{
        header: (props) => <SectionHeader props={props} />,
      }}
    >
      <Stack.Screen
        name="terms-and-conditions/index"
        options={{
          title: "Terms and Conditions",
          headerShown: true,
        }}
      />
      <Stack.Screen
        name="privacy-policy/index"
        options={{
          title: "Privacy Policy",
          headerShown: true,
        }}
      />
    </Stack>
  );
};
export default TermsLayout;
