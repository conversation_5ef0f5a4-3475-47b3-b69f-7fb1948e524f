import type {
  DaySchedule,
  ScheduleHoursFormType,
  ShowPickerState,
  TimeSlot,
} from "@/types/types";
import type { DateTimePickerEvent } from "@react-native-community/datetimepicker";
import React, { useEffect, useState } from "react";
import { Al<PERSON>, ScrollView, Text, TouchableOpacity, View } from "react-native";
import { showMessage } from "react-native-flash-message";
import { RefreshControl } from "react-native-gesture-handler";
import { SafeAreaView } from "react-native-safe-area-context";
import { router, Stack } from "expo-router";
import SkeletonLoading from "@/components/shared/skeleton-loading";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/checkbox";
import { trpc } from "@/utils/api";
import { getTimeRangeFromSlots } from "@/utils/functions";
import { Ionicons } from "@expo/vector-icons";
import { zodResolver } from "@hookform/resolvers/zod";
import DateTimePicker from "@react-native-community/datetimepicker";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { Controller, useForm } from "react-hook-form";

import { ScheduleHoursSchema } from "@acme/validators/kabadiwala";

/**
 * Days of week constant with typesafe id
 */
const DAYS_OF_WEEK: { id: keyof ScheduleHoursFormType; label: string }[] = [
  { id: "sunday", label: "SUN" },
  { id: "monday", label: "MON" },
  { id: "tuesday", label: "TUE" },
  { id: "wednesday", label: "WED" },
  { id: "thursday", label: "THU" },
  { id: "friday", label: "FRI" },
  { id: "saturday", label: "SAT" },
];

/**
 * Default time slot values
 */
const DEFAULT_TIME_SLOT: TimeSlot = {
  startTime: "10:00 am",
  endTime: "6:00 pm",
};

const ScheduleHours = () => {
  const [showPicker, setShowPicker] = useState<ShowPickerState | null>(null);
  const [selectedMode, setSelectedMode] = useState<"AUTOMATIC" | "MANUAL">(
    "MANUAL",
  );
  const [applyToAll, setApplyToAll] = useState(false);

  const queryClient = useQueryClient();

  const {
    data: workHoursData,
    isLoading,
    refetch,
    isFetching,
  } = useQuery(trpc.user.getWorkHoursSettings.queryOptions());

  const { mutate: updateWorkHours, isPending: isUpdating } = useMutation(
    trpc.user.updateWorkHoursSettings.mutationOptions({
      onSuccess: async () => {
        await queryClient.invalidateQueries(
          trpc.user.getWorkHoursSettings.queryFilter(),
        );
        await queryClient.invalidateQueries(
          trpc.user.getDutyStatus.queryFilter(),
        );
        showMessage({
          message: "Work hours settings updated successfully",
          type: "success",
        });
        router.back();
      },
      onError: (error) => {
        showMessage({
          message: error.message,
          type: "danger",
        });
      },
    }),
  );

  const { control, handleSubmit, setValue, watch, reset } =
    useForm<ScheduleHoursFormType>({
      resolver: zodResolver(ScheduleHoursSchema),
      defaultValues: {
        sunday: { isAvailable: false, timeSlots: [] },
        monday: { isAvailable: false, timeSlots: [] },
        tuesday: { isAvailable: false, timeSlots: [] },
        wednesday: { isAvailable: false, timeSlots: [] },
        thursday: { isAvailable: false, timeSlots: [] },
        friday: { isAvailable: false, timeSlots: [] },
        saturday: { isAvailable: false, timeSlots: [] },
      },
    });

  useEffect(() => {
    if (workHoursData) {
      setSelectedMode(workHoursData.workHoursMode);

      if (workHoursData.scheduleHours) {
        reset(workHoursData.scheduleHours);
      }
    }
  }, [workHoursData, reset]);

  const onSubmit = (data: ScheduleHoursFormType): void => {
    if (selectedMode === "MANUAL") {
      // For manual mode, just update the mode without schedule
      updateWorkHours({
        mode: "MANUAL",
      });
      return;
    }

    // For automatic mode, validate that at least one day has time slots
    const hasValidSchedule = Object.values(data).some(
      (day: DaySchedule) => day.isAvailable && day.timeSlots.length > 0,
    );

    if (!hasValidSchedule) {
      showMessage({
        message: "Invalid Schedule",
        description:
          "Please set at least one day with working hours for automatic mode.",
        type: "danger",
      });
      return;
    }

    updateWorkHours({
      mode: "AUTOMATIC",
      scheduleHours: data,
    });
  };

  const handleModeChange = (mode: "AUTOMATIC" | "MANUAL") => {
    if (mode === "MANUAL" && selectedMode === "AUTOMATIC") {
      Alert.alert(
        "Switch to Manual Mode",
        "Switching to manual mode will allow you to control your duty status manually. Your schedule will be saved but not used for automatic duty toggling.",
        [
          { text: "Cancel", style: "cancel" },
          { text: "Switch", onPress: () => setSelectedMode(mode) },
        ],
      );
    } else {
      setSelectedMode(mode);
    }
  };

  const handleAddTimeSlot = (day: keyof ScheduleHoursFormType): void => {
    const currentTimeSlots = watch(`${day}.timeSlots`);
    setValue(`${day}.timeSlots`, [
      ...currentTimeSlots,
      { ...DEFAULT_TIME_SLOT },
    ]);
  };

  const handleRemoveTimeSlot = (
    day: keyof ScheduleHoursFormType,
    index: number,
  ): void => {
    const currentTimeSlots = watch(`${day}.timeSlots`);
    setValue(
      `${day}.timeSlots`,
      currentTimeSlots.filter((_, i) => i !== index),
    );
  };

  const handleToggleAvailability = (
    day: keyof ScheduleHoursFormType,
    value: boolean,
  ): void => {
    setValue(`${day}.isAvailable`, value);

    // If turning on availability and no time slots, add default one
    if (value) {
      const timeSlots = watch(`${day}.timeSlots`);
      if (timeSlots.length === 0) {
        setValue(`${day}.timeSlots`, [{ ...DEFAULT_TIME_SLOT }]);
      }
    }
  };

  const handleApplyToAll = () => {
    if (!applyToAll) {
      // Apply the first available day's schedule to all days
      const firstAvailableDay = DAYS_OF_WEEK.find((day) =>
        watch(`${day.id}.isAvailable`),
      );

      if (firstAvailableDay) {
        const scheduleToApply = watch(firstAvailableDay.id);
        DAYS_OF_WEEK.forEach((day) => {
          setValue(day.id, { ...scheduleToApply });
        });
      } else {
        // If no day is available, set a default schedule for all
        DAYS_OF_WEEK.forEach((day) => {
          setValue(`${day.id}`, {
            isAvailable: true,
            timeSlots: [{ ...DEFAULT_TIME_SLOT }],
          });
        });
      }
    }
    setApplyToAll(!applyToAll);
  };

  const showTimePicker = (
    day: keyof ScheduleHoursFormType,
    index: number,
    field: "startTime" | "endTime",
  ): void => {
    setShowPicker({ show: true, day, index, field });
  };

  const onTimeChange = (
    event: DateTimePickerEvent,
    selectedDate?: Date,
  ): void => {
    if (!showPicker || !selectedDate || event.type === "dismissed") {
      setShowPicker(null);
      return;
    }

    const hours = selectedDate.getHours();
    const minutes = selectedDate.getMinutes();
    let period = "am";
    let hour = hours;

    if (hours >= 12) {
      period = "pm";
      hour = hours === 12 ? 12 : hours - 12;
    } else {
      hour = hours === 0 ? 12 : hours;
    }

    const timeString = `${hour}:${minutes.toString().padStart(2, "0")} ${period}`;

    const { day, index, field } = showPicker;
    const timeSlots = [...watch(`${day}.timeSlots`)];

    const slotToUpdate = timeSlots[index];
    if (slotToUpdate) {
      timeSlots[index] = {
        ...slotToUpdate,
        [field]: timeString,
      };
      setValue(`${day}.timeSlots`, timeSlots);

      // If apply to all is enabled, update all days
      if (applyToAll) {
        DAYS_OF_WEEK.forEach((dayItem) => {
          const currentDaySlots = [...watch(`${dayItem.id}.timeSlots`)];
          if (currentDaySlots[index]) {
            currentDaySlots[index] = {
              ...currentDaySlots[index],
              [field]: timeString,
            };
            setValue(`${dayItem.id}.timeSlots`, currentDaySlots);
          }
        });
      }
    }

    setShowPicker(null);
  };

  const renderTimeSlots = (
    day: keyof ScheduleHoursFormType,
  ): React.ReactNode => {
    const daySchedule = watch(day);
    const isAvailable = daySchedule.isAvailable;
    const timeSlots = watch(`${day}.timeSlots`);

    if (!isAvailable) return null;

    return (
      <View className="mt-2">
        {timeSlots.map((slot, index) => (
          <View key={index} className="mb-4 flex-row items-center">
            <TouchableOpacity
              onPress={() => showTimePicker(day, index, "startTime")}
              className="flex-1 rounded-md border border-gray-300 p-3"
            >
              <Text className="text-center">
                {slot.startTime || "Start Time"}
              </Text>
            </TouchableOpacity>

            <Text className="mx-2">-</Text>

            <TouchableOpacity
              onPress={() => showTimePicker(day, index, "endTime")}
              className="flex-1 rounded-md border border-gray-300 p-3"
            >
              <Text className="text-center">{slot.endTime || "End Time"}</Text>
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => handleRemoveTimeSlot(day, index)}
              className="ml-2 p-2"
            >
              <Ionicons name="close" size={20} color="red" />
            </TouchableOpacity>
          </View>
        ))}

        {showPicker?.show && (
          <DateTimePicker
            testID="timePicker"
            value={new Date()}
            mode="time"
            is24Hour={false}
            display="default"
            onChange={onTimeChange}
          />
        )}
      </View>
    );
  };

  const renderSchedulePreview = () => {
    return (
      <View className="mt-6 gap-5 rounded-[20px] bg-yellow-0 p-4">
        <Text className="mb-2 font-medium">Your Current Schedule Preview</Text>
        <View>
          {DAYS_OF_WEEK.map((day) => {
            const isAvailable = watch(`${day.id}.isAvailable`);
            const timeSlots = watch(`${day.id}.timeSlots`);

            return (
              <View
                key={`preview-${day.id}`}
                className="mb-2 flex-row justify-between"
              >
                <Text>
                  {day.id.charAt(0).toUpperCase() +
                    day.id.slice(1).toLowerCase()}
                </Text>
                <Text>
                  {isAvailable &&
                  Array.isArray(timeSlots) &&
                  timeSlots.length > 0
                    ? getTimeRangeFromSlots(timeSlots)
                    : "Not Available"}
                </Text>
              </View>
            );
          })}
        </View>
      </View>
    );
  };

  if (isLoading) {
    return (
      <SafeAreaView className="-pt-safe-40 flex-1 bg-white">
        <SkeletonLoading type="list" />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="-pt-safe-40 flex-1 bg-white">
      <ScrollView
        className="flex-1"
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={isFetching} onRefresh={refetch} />
        }
      >
        <Stack.Screen
          options={{
            title: "Work Hours",
            headerRight: () => (
              <TouchableOpacity
                onPress={() => router.push("/support/help")}
                className="mr-4"
              >
                <Text className="font-medium text-emerald-600">Help</Text>
              </TouchableOpacity>
            ),
          }}
        />
        <View className="p-4">
          <Text className="mb-6 text-gray-600">
            Set your availability for pickups
          </Text>

          {/* Mode Selection */}
          <View className="mb-6">
            <Text className="mb-4 text-lg font-semibold">
              Choose Work Hours Mode
            </Text>

            {/* Manual Mode */}
            <TouchableOpacity
              onPress={() => handleModeChange("MANUAL")}
              className={`mb-4 rounded-lg border-2 p-4 ${
                selectedMode === "MANUAL"
                  ? "border-emerald-500 bg-emerald-50"
                  : "border-gray-300"
              }`}
            >
              <Text className="mb-2 text-lg font-medium">Manual Mode</Text>
              <Text className="text-gray-600">
                You can toggle your duty status on/off manually whenever you
                want to receive orders.
              </Text>
            </TouchableOpacity>

            {/* Automatic Mode */}
            <TouchableOpacity
              onPress={() => handleModeChange("AUTOMATIC")}
              className={`mb-4 rounded-lg border-2 p-4 ${
                selectedMode === "AUTOMATIC"
                  ? "border-emerald-500 bg-emerald-50"
                  : "border-gray-300"
              }`}
            >
              <Text className="mb-2 text-lg font-medium">Automatic Mode</Text>
              <Text className="text-gray-600">
                Set a weekly schedule and your duty status will automatically
                turn on/off based on your working hours.
              </Text>
            </TouchableOpacity>
          </View>

          {/* Manual Mode Info */}
          {selectedMode === "MANUAL" && (
            <View className="rounded-lg bg-blue-50 p-4">
              <View className="mb-2 flex-row items-center">
                <Ionicons name="information-circle" size={24} color="#3b82f6" />
                <Text className="ml-2 text-lg font-medium text-blue-700">
                  Manual Mode Selected
                </Text>
              </View>
              <Text className="text-blue-600">
                You are in manual mode. You can toggle your duty status on/off
                using the duty switch in the app whenever you want to receive
                pickup requests.
              </Text>
            </View>
          )}

          {/* Automatic Mode Schedule */}
          {selectedMode === "AUTOMATIC" && (
            <>
              {/* Apply to All Toggle */}
              <View className="mb-6 flex-row items-center">
                <TouchableOpacity
                  onPress={handleApplyToAll}
                  className="flex-row items-center"
                >
                  <View
                    className={`mr-2 h-5 w-5 rounded border border-gray-300 ${
                      applyToAll
                        ? "border-emerald-600 bg-emerald-600"
                        : "bg-white"
                    }`}
                  >
                    {applyToAll && (
                      <Ionicons name="checkmark" size={16} color="white" />
                    )}
                  </View>
                  <Text>Apply these work hours for all week days</Text>
                </TouchableOpacity>
              </View>

              {/* Daily Schedule */}
              {DAYS_OF_WEEK.map((day) => (
                <View key={day.id} className="mb-4">
                  <View className="flex-row items-center justify-between">
                    <View className="flex-row items-center">
                      <Controller
                        control={control}
                        name={`${day.id}.isAvailable`}
                        render={({ field: { value } }) => (
                          <View className="mr-4">
                            <Switch
                              checked={value}
                              onChange={(checked) =>
                                handleToggleAvailability(day.id, checked)
                              }
                              accessibilityLabel={`Toggle availability for ${day.label}`}
                            />
                          </View>
                        )}
                      />
                      <Text className="font-medium">{day.label}</Text>
                    </View>
                    <View className="flex-row items-center">
                      <Text className="mr-2 text-gray-500">
                        {watch(`${day.id}.isAvailable`)
                          ? "Available"
                          : "Not Available"}
                      </Text>
                      {watch(`${day.id}.isAvailable`) && (
                        <TouchableOpacity
                          onPress={() => handleAddTimeSlot(day.id)}
                          className="p-1"
                        >
                          <Ionicons name="add" size={24} color="black" />
                        </TouchableOpacity>
                      )}
                    </View>
                  </View>
                  {renderTimeSlots(day.id)}
                </View>
              ))}

              <View className="mt-6 rounded-[8px] bg-[#F3FFFF] p-3">
                <Text className="text-black-700">
                  Setting your work hours helps us send you pickup requests only
                  when you're available and ready to work.
                </Text>
              </View>

              {renderSchedulePreview()}
            </>
          )}

          <View className="mb-4 mt-8">
            <Button
              onPress={handleSubmit(onSubmit)}
              disabled={isUpdating}
              className="bg-emerald-500 py-3"
            >
              <Text className="text-center font-medium text-white">
                {isUpdating ? "Saving..." : "Save"}
              </Text>
            </Button>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default ScheduleHours;
