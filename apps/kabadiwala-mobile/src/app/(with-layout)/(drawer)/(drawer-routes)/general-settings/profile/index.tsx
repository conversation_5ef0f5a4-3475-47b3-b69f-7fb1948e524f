import type { AutocompleteInputRef } from "@/components/shared/google/autocomplete-input";
import type { DetectedLocation } from "@/types/types";
import type { z } from "zod";
import React, { useRef } from "react";
import { ScrollView, Text, View } from "react-native";
import { showMessage } from "react-native-flash-message";
import { Image } from "expo-image";
import Error from "@/components/shared/error";
import AutocompleteInput from "@/components/shared/google/autocomplete-input";
import SkeletonLoading from "@/components/shared/skeleton-loading";
import { Button } from "@/components/ui/button";
import { ControlledInput } from "@/components/ui/input";
import { ImagePlaceholder } from "@/lib/constant";
import { trpc } from "@/utils/api";
import { Ionicons } from "@expo/vector-icons";
import MaterialIcons from "@expo/vector-icons/MaterialIcons";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useForm } from "react-hook-form";

import { EditProfileSchema } from "@acme/validators/kabadiwala";

type ProfileFormData = z.infer<typeof EditProfileSchema>;

const Profile = () => {
  const queryClient = useQueryClient();

  const {
    data,
    isPending: isFetchingProfile,
    isError,
  } = useQuery(trpc.user.getProfile.queryOptions());

  const { mutate: updateProfile, isPending: isUpdatingProfile } = useMutation(
    trpc.user.updateProfile.mutationOptions({
      onSuccess: async (opts) => {
        showMessage({
          message: opts.message || "Profile updated successfully",
          type: "success",
        });
        await queryClient.invalidateQueries(trpc.user.getProfile.queryFilter());
      },
      onError: (error) => {
        showMessage({
          message: error.message || "Failed to update profile",
          type: "danger",
        });
      },
    }),
  );

  const addressData = data?.addresses[0];
  const autocompleteRef = useRef<AutocompleteInputRef>(null);

  const form = useForm<ProfileFormData>({
    resolver: zodResolver(EditProfileSchema),
    defaultValues: {
      imageUrl: data?.image ?? "",
      imageFileKey: data?.imageFileKey ?? "",
      fullName: data?.name ?? "",
      phoneNumber: data?.phoneNumber ?? "",
      email: data?.email ?? "",
      address: {
        name: addressData?.name ?? "",
        city: addressData?.city ?? "",
        state: addressData?.state ?? "",
        postalCode: addressData?.postalCode ?? "",
        country: addressData?.country ?? "",
        landmark: addressData?.landmark ?? "",
        display: addressData?.display ?? "",
        localAddress: addressData?.localAddress ?? "",
        addressType: addressData?.addressType ?? "HOME",
      },
    },
  });

  const profileImage = form.watch("imageUrl");
  const addressError = form.formState.errors.address?.display?.message;

  const handleLocationSelect = async (location: DetectedLocation) => {
    form.setValue("address", {
      name: "Selected Location",
      street: location.address?.street ?? "",
      city: location.address?.city ?? "",
      state: location.address?.state ?? "",
      country: location.address?.country ?? "",
      postalCode: location.address?.postalCode ?? "",
      coordinates: {
        latitude: location.latitude,
        longitude: location.longitude,
      },
      addressType: "OTHER",
      localAddress: location.address?.display ?? "",
      display: location.address?.display ?? "",
      landmark: location.address?.display ?? "",
    });

    // Trigger validation
    await form.trigger("address");
  };

  const onSubmit = (data: ProfileFormData) => {
    updateProfile(data);
  };

  React.useEffect(() => {
    form.reset({
      imageUrl: data?.image ?? "",
      imageFileKey: data?.imageFileKey ?? "",
      fullName: data?.name ?? "",
      phoneNumber: data?.phoneNumber ?? "",
      email: data?.email ?? "",
      address: {
        name: addressData?.name ?? "",
        city: addressData?.city ?? "",
        state: addressData?.state ?? "",
        postalCode: addressData?.postalCode ?? "",
        country: addressData?.country ?? "",
        landmark: addressData?.landmark ?? "",
        display: addressData?.display ?? "",
        coordinates: addressData?.coordinates ?? undefined,
        localAddress: addressData?.localAddress ?? undefined,
        addressType: addressData?.addressType ?? "OTHER",
      },
    });
  }, [data, addressData, form]);

  if (isFetchingProfile) {
    return <SkeletonLoading type="form" />;
  }

  if (isError) {
    return <Error />;
  }

  return (
    <ScrollView className="flex-1">
      <View className="flex-1 py-6">
        {/* Profile Form */}
        <View className="flex-col gap-4">
          {/* Profile Photo */}
          <View className="flex flex-row items-center gap-4">
            <View className="relative">
              <Image
                source={profileImage}
                contentFit="cover"
                style={{
                  width: 96,
                  height: 96,
                  borderRadius: 48,
                  borderWidth: 2,
                  borderColor: "#00CC66",
                }}
                placeholder={ImagePlaceholder}
                placeholderContentFit="cover"
                className="rounded-full"
              />
              <View className="absolute bottom-0 right-0 h-6 w-6 items-center justify-center rounded-full bg-green-500">
                <Ionicons name="checkmark" size={16} color="white" />
              </View>
            </View>

            <View className="flex-1">
              <Text className="text-base font-semibold">
                Uploaded Profile Photo
              </Text>
              <Text className="text-sm text-gray-500">
                This is your uploaded photo to verified by admin, reach support
                to change profile photo
              </Text>
            </View>
          </View>

          {/* Form Fields */}
          <ControlledInput
            name="fullName"
            label="Your Full Name"
            control={form.control}
            placeholder="eg: Anil Kumar"
          />

          {/* Phone Number Field (with verified badge) */}
          <View className="h-fit flex-1 flex-col">
            <View className="flex-row items-center justify-between">
              <Text className="font-jakarta-medium mb-2 text-[13px] font-medium text-black-800">
                Phone Number
              </Text>
              {data.phoneNumberVerified && (
                <View className="flex-row items-center">
                  <MaterialIcons name="verified" size={16} color="green" />
                  <Text className="ml-1 text-green-600">Verified</Text>
                </View>
              )}
            </View>
            <ControlledInput
              name="phoneNumber"
              control={form.control}
              placeholder="eg: +91 9829345903"
              inputMode="numeric"
              editable={false}
            />
          </View>

          <View className="h-fit flex-1 flex-col">
            <View className="flex-row items-center justify-between">
              <Text className="font-jakarta-medium mb-2 text-[13px] font-medium text-black-800">
                Email (optional)
              </Text>
              {data.emailVerified && (
                <View className="flex-row items-center">
                  <MaterialIcons name="verified" size={16} color="green" />
                  <Text className="ml-1 text-green-600">Verified</Text>
                </View>
              )}
            </View>

            <ControlledInput
              name="email"
              control={form.control}
              placeholder="eg: <EMAIL>"
            />
          </View>

          {/* Properly integrated AutocompleteInput */}
          <AutocompleteInput
            ref={autocompleteRef}
            value={addressData?.display ?? ""}
            label="Address"
            placeholder="eg: Gurgaon sector 38, Haryana"
            onLocationSelect={handleLocationSelect}
            onCurrentLocationSelect={handleLocationSelect}
            showAutoDetect={true}
            autoDetectText="Autodetect my location"
            error={addressError}
            maxSuggestions={3}
          />

          {/* Update Button */}
          <Button
            onPress={form.handleSubmit(onSubmit, (err) => console.error(err))}
            disabled={isUpdatingProfile}
            variant={isUpdatingProfile ? "disabled" : "default"}
            label="Update Profile"
          />
        </View>
      </View>
    </ScrollView>
  );
};

export default Profile;
