import React, { useState } from "react";
import { Al<PERSON>, Pressable, ScrollView, Text, View } from "react-native";
import { showMessage } from "react-native-flash-message";
import ImageModal from "@/components/shared/image-modal";
import SkeletonLoading from "@/components/shared/skeleton-loading";
import { Input } from "@/components/ui/input";
import { useModal } from "@/components/ui/modal";
import { trpc } from "@/utils/api";
import { useImageUploader } from "@/utils/uploadthing";
import AntDesign from "@expo/vector-icons/AntDesign";
import Feather from "@expo/vector-icons/Feather";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

const Documents = () => {
  const [previewImage, setPreviewImage] = useState<{
    url: string;
    title: string;
  } | null>(null);

  const {
    present: presentImagePreview,
    dismiss: dismissImagePreview,
    ref: imagePreviewRef,
  } = useModal();

  const { data: documentData, isPending: isFetchingDocuments } = useQuery(
    trpc.user.getDocumentDetails.queryOptions(),
  );

  const { mutate: deleteFile, isPending: isDeletingFile } = useMutation(
    trpc.utils.deleteUploadthingFileUsingFileKey.mutationOptions(),
  );

  const openImagePreview = (imageUrl: string, title: string) => {
    setPreviewImage({ url: imageUrl, title });
    presentImagePreview();
  };

  if (isFetchingDocuments) {
    return <SkeletonLoading type="details" />;
  }

  return (
    <ScrollView className="flex-1">
      <View className="mt-4 flex-col gap-5">
        <DocumentField
          fieldName="frontOfAdharCardImageUrl"
          label="Front of Aadhar Card"
          fileUrl={documentData?.frontAdharCard?.mediaUrl}
          documentData={documentData?.frontAdharCard}
          onPreview={openImagePreview}
          deleteFile={deleteFile}
          isDeletingFile={isDeletingFile}
          fileKey={documentData?.frontAdharCard?.fileKey}
        />

        <DocumentField
          fieldName="backOfAdharCardImageUrl"
          label="Back of Aadhar Card"
          fileUrl={documentData?.backAdharCard?.mediaUrl}
          documentData={documentData?.backAdharCard}
          onPreview={openImagePreview}
          deleteFile={deleteFile}
          isDeletingFile={isDeletingFile}
          fileKey={documentData?.backAdharCard?.fileKey}
        />

        <DocumentField
          fieldName="policeVerificationDocumentUrl"
          label="Police Verification"
          fileUrl={documentData?.policeVerificationDocument?.mediaUrl}
          documentData={documentData?.policeVerificationDocument}
          onPreview={openImagePreview}
          deleteFile={deleteFile}
          isDeletingFile={isDeletingFile}
          helperText="Upload a copy of police verification document that is not older than 6 months"
          fileKey={documentData?.policeVerificationDocument?.fileKey}
        />
      </View>

      {/* Image Preview Modal */}
      {previewImage && (
        <ImageModal
          imageUrl={previewImage.url}
          ref={imagePreviewRef}
          dismiss={dismissImagePreview}
          title={previewImage.title}
        />
      )}
    </ScrollView>
  );
};

export default Documents;

interface DocumentFieldProps {
  fieldName: string;
  label: string;
  fileUrl: string | undefined;
  fileKey?: string | null;
  documentData?: {
    id?: string;
    mediaUrl?: string;
    fileName?: string;
    fileKey?: string | null;
  } | null;
  onPreview: (imageUrl: string, title: string) => void;
  deleteFile: (params: { fileKey: string }) => void;
  isDeletingFile: boolean;
  helperText?: string;
}

const DocumentField = ({
  fieldName,
  label,
  fileUrl,
  documentData,
  onPreview,
  deleteFile,
  isDeletingFile,
  helperText,
}: DocumentFieldProps) => {
  const { openImagePicker, isUploading } = useImageUploader("imageUploader", {
    onClientUploadComplete: (res) => {
      const imageUrl = res[0]?.ufsUrl;
      const imageKey = res[0]?.key;

      if (!imageUrl || !imageKey || !documentData?.id) {
        showMessage({
          message: "Image upload failed. Please try again.",
          type: "danger",
        });
        return;
      }

      updateDocuments({
        mediaId: documentData.id,
        fileUrl: imageUrl,
        fileKey: imageKey,
      });
    },
    onUploadError: () => {
      showMessage({
        message: `${label} upload failed. Please try again.`,
        type: "danger",
      });
    },
  });

  const queryClient = useQueryClient();
  const { mutate: updateDocuments } = useMutation(
    trpc.user.updateDocuments.mutationOptions({
      onSuccess: async (opts) => {
        await queryClient.invalidateQueries(
          trpc.user.getDocumentDetails.queryOptions(),
        );
        showMessage({
          message: opts.message || `${label} updated successfully`,
          type: "success",
        });
      },
      onError: (error) => {
        showMessage({
          message: `Failed to update ${label}. ${error.message}`,
          type: "danger",
        });
      },
    }),
  );

  const handleImageUpload = async () => {
    // Delete previous image if exists
    if (documentData?.fileKey) {
      deleteFile({ fileKey: documentData.fileKey });
    }

    await openImagePicker({
      source: "library",
      onInsufficientPermissions: () => {
        Alert.alert(
          "No Permissions",
          "You need to grant permission to your Photos to use this",
          [{ text: "Dismiss" }, { text: "Open Settings" }],
        );
      },
    });
  };

  return (
    <View>
      <DocumentUploadField
        fieldName={fieldName}
        label={label}
        fileUrl={fileUrl}
        fileName={documentData?.fileName}
        onUpload={handleImageUpload}
        isUploading={isUploading}
        helperText={helperText}
      />

      {fileUrl && (
        <View className="flex flex-row items-center justify-between">
          <Pressable
            onPress={() => onPreview(fileUrl, label)}
            className="flex-row items-center"
          >
            <Text className="font-inter-regular mt-1 text-xs text-black-400">
              Tap to preview the uploaded document
            </Text>
          </Pressable>

          <Pressable
            onPress={handleImageUpload}
            disabled={isUploading || isDeletingFile}
          >
            <Text className="font-inter-regular mt-1 text-xs text-black-700">
              {isUploading || isDeletingFile ? "Updating..." : "Change"}
            </Text>
          </Pressable>
        </View>
      )}
    </View>
  );
};

interface DocumentUploadFieldProps {
  fieldName: string;
  label: string;
  fileUrl: string | undefined;
  fileName: string | null | undefined;
  onUpload: () => Promise<void>;
  isUploading: boolean;
  helperText?: string;
}

const DocumentUploadField = ({
  label,
  fileUrl,
  fileName,
  onUpload,
  isUploading,
  helperText,
}: DocumentUploadFieldProps) => {
  if (fileUrl) {
    return (
      <View>
        <View className="flex-row items-center justify-between">
          <Text className="font-jakarta-medium text-text-600 mb-2 text-base font-medium">
            {label}
          </Text>
          <Text className="font-inter-semibold flex flex-row items-center gap-1 text-[10px] font-semibold text-teal-750">
            <Feather name="check" size={12} color="#007F80" />
            Uploaded
          </Text>
        </View>
        <Input
          value={fileName ? `${fileName} uploaded` : "Document uploaded"}
          disabled
        />
      </View>
    );
  }

  return (
    <View className="mb-4">
      <Pressable onPress={onUpload} disabled={isUploading}>
        <Text className="font-jakarta-medium text-text-600 mb-2 text-base font-medium">
          {label}
        </Text>
        <View className="mb-1.5 flex h-[120px] items-center justify-center rounded-lg border-[1.2px] border-black-100 bg-black-0">
          {isUploading ? (
            <AntDesign
              name="loading1"
              size={24}
              color="black"
              className="animate-spin"
            />
          ) : (
            <Text className="font-inter-medium text-black-400">Upload</Text>
          )}
        </View>
        {helperText && (
          <Text className="font-inter-regular mt-1 text-xs text-black-400">
            {isUploading ? "Uploading..." : helperText}
          </Text>
        )}
      </Pressable>
    </View>
  );
};
