import type { z } from "zod";
import React, { useState } from "react";
import { ScrollView, Text, View } from "react-native";
import { showMessage } from "react-native-flash-message";
import { KeyboardAwareScrollView } from "react-native-keyboard-controller";
import Error from "@/components/shared/error";
import { ModalHeader } from "@/components/shared/modal-header";
import SkeletonLoading from "@/components/shared/skeleton-loading";
import VehicleCard from "@/components/shared/vehicle-card";
import { Button } from "@/components/ui/button";
import { ControlledInput } from "@/components/ui/input";
import { Modal, useModal } from "@/components/ui/modal";
import { ControlledSelect } from "@/components/ui/select";
import { trpc } from "@/utils/api";
import { getVehicleIcon } from "@/utils/functions";
import AntDesign from "@expo/vector-icons/AntDesign";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useForm } from "react-hook-form";

import { VehicleSchema } from "@acme/validators/kabadiwala";

const VehicleIndex = () => {
  const { ref, present, dismiss } = useModal();
  const [selectedVehicle, setSelectedVehicle] = useState<{
    id?: string;
    vehicleType: string;
    vehicleNumber: string | null;
  } | null>(null);

  const [isAddingNew, setIsAddingNew] = useState(false);

  const { data, isPending, isError } = useQuery(
    trpc.user.getVehicles.queryOptions(),
  );

  const queryClient = useQueryClient();

  const { mutate: deleteVehicle } = useMutation(
    trpc.user.deleteVehicle.mutationOptions({
      onSuccess: async (opts) => {
        showMessage({
          message: opts.message,
          type: "success",
        });
        await queryClient.invalidateQueries(
          trpc.user.getVehicles.queryOptions(),
        );
      },
      onError: (error) => {
        showMessage({
          message: error.message,
          type: "danger",
        });
      },
    }),
  );

  const { mutate: setDefaultVehicle } = useMutation(
    trpc.user.setDefaultVehicle.mutationOptions({
      onSuccess: async (opts) => {
        showMessage({
          message: opts.message,
          type: "success",
        });
        await queryClient.invalidateQueries(
          trpc.user.getVehicles.queryOptions(),
        );
      },
      onError: (error) => {
        showMessage({
          message: error.message,
          type: "danger",
        });
      },
    }),
  );

  const handleDeleteVehicle = (id: string) => {
    deleteVehicle({ id });
  };

  const handleSetDefault = (id: string) => {
    setDefaultVehicle({ id });
  };

  if (isPending) {
    return <SkeletonLoading type="details" />;
  }

  if (isError) {
    return (
      <Error
        title="Failed to load vehicles"
        message="Please try again later."
      />
    );
  }

  const handleEditVehicle = (vehicle: {
    id: string;
    vehicleType: string;
    vehicleNumber: string | null;
  }) => {
    setSelectedVehicle(vehicle);
    setIsAddingNew(false);
    present();
  };

  const handleAddNewVehicle = () => {
    setSelectedVehicle(null);
    setIsAddingNew(true);
    present();
  };

  return (
    <ScrollView
      className="relative flex-1 bg-white py-4"
      contentContainerClassName="h-full"
    >
      <View className="mb-4 flex-row items-center justify-between">
        <Text className="font-inter-medium text-xs font-medium leading-5 text-black-600">
          {data.vehicles.length}{" "}
          {data.vehicles.length === 1 ? "Vehicle" : "Vehicles"} added
        </Text>
      </View>

      {data.vehicles.length === 0 ? (
        <View className="items-center justify-center py-10">
          <Text className="text-center text-gray-500">
            No vehicles added yet
          </Text>
          <Button className="mt-4" onPress={handleAddNewVehicle}>
            <Text>Add Your First Vehicle</Text>
          </Button>
        </View>
      ) : (
        data.vehicles.map((vehicle) => (
          <View key={vehicle.id} className="mb-4">
            <VehicleCard
              vehicleName={`Vehicle #${vehicle.id.substring(0, 4)}`}
              isDefault={!!vehicle.isActive}
              vehicleNumber={vehicle.vehicleNumber ?? "Not specified"}
              vehicleType={vehicle.vehicleType}
              onOptionsPress={() => handleEditVehicle(vehicle)}
              onDeletePress={() => handleDeleteVehicle(vehicle.id)}
              onSetDefault={() => handleSetDefault(vehicle.id)}
            />
          </View>
        ))
      )}

      <View className="absolute bottom-10 left-0 right-0">
        <Button
          onPress={handleAddNewVehicle}
          className="flex items-center justify-center gap-2 border border-yellow-750 bg-yellow-150"
        >
          <AntDesign name="plus" size={16} color="black" />
          <Text>Add Vehicle</Text>
        </Button>
      </View>

      <Modal
        ref={ref}
        handleComponent={null}
        children={
          <ModalContent
            onDismiss={dismiss}
            vehicleId={selectedVehicle?.id}
            vehicleType={selectedVehicle?.vehicleType}
            vehicleNumber={selectedVehicle?.vehicleNumber}
            isAddingNew={isAddingNew}
          />
        }
      />
    </ScrollView>
  );
};

export default VehicleIndex;

interface ModalContentProps {
  vehicleId?: string;
  vehicleType?: string | null;
  vehicleNumber?: string | null;
  onDismiss: () => void;
  isAddingNew?: boolean;
}

const VehicleTypeFieldValues = VehicleSchema.shape.vehicleType.options;

const getVehicleTypeOptions = () => {
  return VehicleTypeFieldValues.map((item) => {
    return {
      label: item,
      value: item,
      icon: getVehicleIcon({ vehicleType: item, iconSize: 20 }),
    };
  });
};

const ModalContent = ({
  vehicleId,
  vehicleType,
  vehicleNumber,
  onDismiss,
  isAddingNew = false,
}: ModalContentProps) => {
  const form = useForm<z.infer<typeof VehicleSchema>>({
    resolver: zodResolver(VehicleSchema),
    defaultValues: {
      vehicleNumber: vehicleNumber ?? undefined,
      vehicleType: vehicleType as (typeof VehicleTypeFieldValues)[number],
    },
  });

  const queryClient = useQueryClient();

  const { mutate: createVehicle, isPending: isCreating } = useMutation(
    trpc.user.createVehicle.mutationOptions({
      onSuccess: async (opts) => {
        showMessage({
          message: opts.message,
          type: "success",
        });
        await queryClient.invalidateQueries(
          trpc.user.getVehicles.queryOptions(),
        );
        onDismiss();
      },
      onError: (error) => {
        showMessage({
          message: error.message,
          type: "danger",
        });
      },
    }),
  );

  const { mutate: updateVehicle, isPending: isUpdating } = useMutation(
    trpc.user.updateVehicle.mutationOptions({
      onSuccess: async (opts) => {
        showMessage({
          message: opts.message,
          type: "success",
        });
        await queryClient.invalidateQueries(
          trpc.user.getVehicles.queryOptions(),
        );
        onDismiss();
      },
      onError: (error) => {
        showMessage({
          message: error.message,
          type: "danger",
        });
      },
    }),
  );

  const onSubmit = (data: z.infer<typeof VehicleSchema>) => {
    if (isAddingNew) {
      createVehicle(data);
    } else if (vehicleId) {
      updateVehicle({
        ...data,
        id: vehicleId,
      });
    }
  };

  const isPending = isCreating || isUpdating;
  const actionText = isAddingNew ? "Add Vehicle" : "Update Vehicle";

  return (
    <KeyboardAwareScrollView className="flex-1 bg-white">
      <ModalHeader
        title={isAddingNew ? "Add New Vehicle" : "Vehicle Details"}
        subTitle={
          isAddingNew
            ? "Enter your vehicle information"
            : "Manage your vehicle information"
        }
        onDismiss={onDismiss}
      />

      <View className="p-4">
        <View className="flex flex-col gap-6">
          <ControlledSelect
            name="vehicleType"
            label="Vehicle Type"
            control={form.control}
            options={getVehicleTypeOptions()}
          />

          <ControlledInput
            name="vehicleNumber"
            label="Your Vehicle Number"
            control={form.control}
            placeholder="eg: AB12CD34"
          />
        </View>

        <Button
          onPress={form.handleSubmit(onSubmit)}
          className="mt-6 w-full"
          disabled={isPending}
          variant={isPending ? "disabled" : "default"}
        >
          {isPending && (
            <AntDesign
              name="loading1"
              size={16}
              color="black"
              className="mr-2 animate-spin"
            />
          )}
          <Text className="mr-2">{actionText}</Text>
        </Button>
      </View>
    </KeyboardAwareScrollView>
  );
};
