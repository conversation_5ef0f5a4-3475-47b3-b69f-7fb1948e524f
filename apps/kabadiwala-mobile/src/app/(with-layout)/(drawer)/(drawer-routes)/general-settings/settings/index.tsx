import { useCallback, useEffect, useState } from "react";
import { Text, View } from "react-native";
import { showMessage } from "react-native-flash-message";
import { AudioModule, PermissionStatus } from "expo-audio";
import Error from "@/components/shared/error";
import SkeletonLoading from "@/components/shared/skeleton-loading";
import { Switch } from "@/components/ui/checkbox";
import { trpc } from "@/utils/api";
import { usePermissionStore } from "@/utils/store";
import { Ionicons } from "@expo/vector-icons";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

interface SettingItemProps {
  iconName: keyof typeof Ionicons.glyphMap;
  title: string;
  description: string;
  checked?: boolean;
  onToggle?: (checked: boolean) => void;
}

const SettingsIndex = () => {
  const { microphoneAccess, setMicrophoneAccess } = usePermissionStore();
  const [isLoading, setIsLoading] = useState(false);

  const { data, isPending, isError } = useQuery(
    trpc.user.getSettingsInformation.queryOptions(),
  );

  const queryClient = useQueryClient();
  const { mutate: updateSettings } = useMutation(
    trpc.user.updateSettings.mutationOptions({
      onSuccess: async (opts) => {
        showMessage({
          message: opts.message,
          type: "success",
        });
        await queryClient.invalidateQueries(
          trpc.user.getSettingsInformation.queryOptions(),
        );
      },
      onError: (error) => {
        showMessage({
          message: error.message,
          type: "danger",
        });
      },
    }),
  );

  const handleGeneralNotificationToggle = () => {
    if (data?.generalNotificationPermission === null) return;
    updateSettings({
      generalNotificationPermission: data?.generalNotificationPermission
        ? false
        : true,
    });
  };

  const handleChatNotificationToggle = () => {
    if (data?.chatNotificationPermission === null) return;
    updateSettings({
      chatNotificationPermission: data?.chatNotificationPermission
        ? false
        : true,
    });
  };

  const checkMicrophonePermission = useCallback(async () => {
    try {
      const { status } = await AudioModule.getRecordingPermissionsAsync();
      const hasPermission = status === PermissionStatus.GRANTED;
      setMicrophoneAccess(hasPermission);
      return hasPermission;
    } catch (error) {
      console.error("Error checking microphone permission:", error);
      return false;
    }
  }, [setMicrophoneAccess]);

  const requestMicrophonePermission = async () => {
    try {
      const { status } = await AudioModule.requestRecordingPermissionsAsync();
      const hasPermission = status === PermissionStatus.GRANTED;
      setMicrophoneAccess(hasPermission);

      if (hasPermission) {
        showMessage({
          message: "Microphone access granted",
          type: "success",
        });
      } else {
        showMessage({
          message: "Microphone access denied",
          type: "danger",
        });
      }

      return hasPermission;
    } catch (error) {
      console.error("Error requesting microphone permission:", error);
      showMessage({
        message: "Failed to request microphone permission",
        type: "danger",
      });
      return false;
    }
  };

  const handleMicrophoneToggle = async (checked: boolean) => {
    setIsLoading(true);
    if (checked) {
      await requestMicrophonePermission();
    } else {
      setMicrophoneAccess(false);
      showMessage({
        message: "Microphone access disabled",
        type: "info",
      });
    }
    setIsLoading(false);
  };

  useEffect(() => {
    void checkMicrophonePermission();
  }, [checkMicrophonePermission]);

  if (isPending) {
    return <SkeletonLoading type="form" />;
  }

  if (isError) {
    return (
      <Error
        title="Failed to load settings"
        message="Please try again later."
      />
    );
  }

  return (
    <View className="flex-1 bg-white">
      {/* General Settings Section */}
      <View className="py-5">
        <Text className="font-jakarta-semibold mb-3 text-[13px] leading-6 text-black-500">
          General Settings
        </Text>

        <SettingItem
          iconName="notifications-outline"
          title="Notification"
          description="Toggle on to mute the notification, you will not receive any notification related to app"
          checked={data.generalNotificationPermission ?? false}
          onToggle={handleGeneralNotificationToggle}
        />
      </View>

      {/* Divider */}
      <View className="mx-6 h-[1px] bg-black-100" />

      {/* Chat Settings Section */}
      <View className="py-5">
        <Text className="font-jakarta-semibold mb-3 text-[13px] leading-6 text-black-500">
          Chat Settings
        </Text>

        <View className="space-y-6">
          <SettingItem
            iconName="notifications-outline"
            title="Notification"
            description="Toggle on to mute the notification, you will not receive any notification related to app"
            checked={data.chatNotificationPermission ?? false}
            onToggle={handleChatNotificationToggle}
          />

          <View className="mt-6">
            <SettingItem
              iconName="mic-outline"
              title="Microphone access"
              description="Allow app to access microphone for voice features"
              checked={microphoneAccess}
              onToggle={handleMicrophoneToggle}
              isLoading={isLoading}
            />
          </View>
        </View>
      </View>
    </View>
  );
};

const SettingItem = ({
  iconName,
  title,
  description,
  checked = false,
  onToggle,
  isLoading,
}: SettingItemProps & { isLoading?: boolean }) => {
  const handleToggle = (newChecked: boolean) => {
    onToggle?.(newChecked);
  };

  return (
    <View className="flex-row items-center justify-between">
      <View className="flex-1 flex-row items-start gap-3 pr-4">
        <View className="pt-1">
          <Ionicons name={iconName} size={24} color="#333333" />
        </View>
        <View className="flex-1">
          <Text className="font-inter-medium mb-0.5 text-[13px] leading-[18px] text-black-800">
            {title}
          </Text>
          <Text className="font-inter-regular text-[11px] leading-[18px] text-black-500">
            {description}
          </Text>
        </View>
      </View>
      <View>
        <Switch
          checked={checked}
          onChange={handleToggle}
          accessibilityLabel={`Toggle ${title}`}
          disabled={isLoading}
        />
      </View>
    </View>
  );
};

export default SettingsIndex;
