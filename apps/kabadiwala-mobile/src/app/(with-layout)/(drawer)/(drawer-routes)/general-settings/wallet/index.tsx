import React from "react";
import { View } from "react-native";
import AddMoneyModal from "@/components/shared/wallet/add-money-modal";
import WalletTransactionHistory from "@/components/shared/wallet/transaction-history";
import WalletBalanceDisplayCard from "@/components/shared/wallet/wallet-balance-display-card";
import { Modal, useModal } from "@/components/ui/modal";

const Walletbutton = () => {
  const { ref, present, dismiss } = useModal();

  const handleTopUpPress = () => {
    present();
  };

  return (
    <View className="pb-safe mt-4 flex-1">
      <WalletBalanceDisplayCard onTopUpPress={handleTopUpPress} />
      <WalletTransactionHistory />

      <Modal
        ref={ref}
        children={<AddMoneyModal onDismiss={dismiss} />}
        onDismiss={dismiss}
      />
    </View>
  );
};

export default Walletbutton;
