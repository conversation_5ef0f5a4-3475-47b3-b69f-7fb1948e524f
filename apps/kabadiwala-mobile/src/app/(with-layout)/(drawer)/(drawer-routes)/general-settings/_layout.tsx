import React from "react";
import { View } from "react-native";
import { Stack } from "expo-router";
import SectionHeader from "@/components/shared/profile/section-header";

const GeneralSettingsLayout = () => {
  return (
    <Stack
      screenLayout={(props) => (
        <View className="flex-1 bg-white px-6">{props.children}</View>
      )}
      screenOptions={{
        header: (props) => <SectionHeader props={props} />,
      }}
    >
      <Stack.Screen
        name="profile/index"
        options={{
          title: "Your Profile",
        }}
      />
      <Stack.Screen
        name="settings/index"
        options={{
          title: "Settings",
        }}
      />
      <Stack.Screen
        name="earnings/index"
        options={{
          title: "Earnings",
        }}
      />
      <Stack.Screen
        name="schedule-hours/index"
        options={{
          title: "Schedule Hours",
        }}
      />
      <Stack.Screen
        name="transactions/index"
        options={{
          title: "Transactions",
        }}
      />
      <Stack.Screen
        name="vehicle/index"
        options={{
          title: "Vehicle",
        }}
      />
      <Stack.Screen
        name="wallet/index"
        options={{
          title: "Wallet",
        }}
      />
      <Stack.Screen
        name="documents/index"
        options={{
          title: "Documents",
        }}
      />
    </Stack>
  );
};
export default GeneralSettingsLayout;
