import type { DrawerContentComponentProps } from "@react-navigation/drawer";
import type { Route } from "expo-router";
import { View } from "react-native";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { router, usePathname } from "expo-router";
import { Drawer } from "expo-router/drawer";
import HomeScreenHeader from "@/components/shared/home/<USER>";
import DrawerHeader from "@/components/shared/profile/drawer-header";
import SectionGroup from "@/components/shared/profile/section-group";
import SectionItem from "@/components/shared/profile/section-item";
import { DRAWER_NAVIGATION_URLS } from "@/lib/constant";
import { authClient } from "@/utils/auth";
import { Ionicons, Octicons } from "@expo/vector-icons";
import { DrawerContentScrollView } from "@react-navigation/drawer";

export default function DrawerLayout() {
  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <Drawer
        drawerContent={({ navigation }) => (
          <DrawerLinks navigation={navigation} />
        )}
        screenOptions={{
          drawerType: "front",
          swipeEnabled: false,
        }}
      >
        <Drawer.Screen
          name="(tabs)"
          options={{
            title: "Home",
            header: ({ navigation }) => (
              <HomeScreenHeader navigation={navigation} />
            ),
          }}
        />
        <Drawer.Screen
          name="(drawer-routes)"
          options={{
            title: "Profile",
            headerShown: false,
            // Make profile the only item visible in drawer
            drawerLabelStyle: { fontSize: 16 },
          }}
        />
        <Drawer.Screen
          name="(order-flow)"
          options={{
            title: "Settings",
            headerShown: false,
            // Make profile the only item visible in drawer
            drawerLabelStyle: { fontSize: 16 },
          }}
        />
        <Drawer.Screen
          name="(payment-flow)"
          options={{
            title: "Payment",
            headerShown: false,
            // Make profile the only item visible in drawer
            drawerLabelStyle: { fontSize: 16 },
          }}
        />
        <Drawer.Screen
          name="(support)"
          options={{
            title: "Support",
            headerShown: false,
            // Make profile the only item visible in drawer
            drawerLabelStyle: { fontSize: 16 },
          }}
        />
        <Drawer.Screen
          name="(miscellaneous)"
          options={{
            headerShown: false,
            drawerLabelStyle: { fontSize: 16 },
          }}
        />
      </Drawer>
    </GestureHandlerRootView>
  );
}

function DrawerLinks({
  navigation,
}: {
  navigation: DrawerContentComponentProps["navigation"];
}) {
  const pathName = usePathname();

  const handleLogout = async () => {
    await authClient.signOut();
  };

  const handleNavigation = (route: Route) => {
    if (pathName === route) return;

    router.replace(route);
    navigation.closeDrawer();
  };

  return (
    <DrawerContentScrollView
      contentContainerStyle={{
        paddingStart: 0,
        paddingEnd: 0,
      }}
    >
      <DrawerHeader />
      <View className="flex-1 px-4 pt-4">
        {DRAWER_NAVIGATION_URLS.map((sections, index) => (
          <SectionGroup
            key={index}
            title={sections.sectionTitle}
            className="mb-4"
            titleClassName="text-[11px] leading-[14px] font-medium font-jakarta-medium text-black-500"
          >
            {sections.sectionsLinks.map((item, index) => (
              <SectionItem
                key={index}
                title={item.name}
                icon={item.linkIcon}
                onPress={() => item.onPress?.() ?? handleNavigation(item.link)}
                className={pathName === item.link ? "bg-teal-600" : ""}
              />
            ))}
          </SectionGroup>
        ))}
        <View className="gap-3">
          <SectionItem
            title="Logout"
            variant="custom"
            icon={<Ionicons name="power" size={16} color="#333333" />}
            className="bg-black-0"
            showArrow={false}
            onPress={handleLogout}
          />
          <SectionItem
            title="Delete Account"
            icon={<Octicons name="trash" size={16} color="#900B09" />}
            variant="dangerous"
            showArrow={false}
          />
        </View>
      </View>
    </DrawerContentScrollView>
  );
}
