import type { RelativePathString } from "expo-router";
import React from "react";
import {
  ActivityIndicator,
  FlatList,
  RefreshControl,
  Text,
  View,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { router } from "expo-router";
import Error from "@/components/shared/error";
import NotificationCard from "@/components/shared/notifications/notification-card";
import { Button } from "@/components/ui/button";
import { trpc } from "@/utils/api";
import AntDesign from "@expo/vector-icons/AntDesign";
import { useQuery } from "@tanstack/react-query";

interface Notification {
  id: string;
  title: string;
  content: string;
  metadata: Record<string, unknown>;
  createdAt: Date;
}

const NotificationIndex = () => {
  const {
    data: notifications,
    isPending,
    isError,
    refetch,
    isRefetching,
  } = useQuery(trpc.notification.getAll.queryOptions());

  const handleNotificationPress = (notification: Notification) => {
    const link = notification.metadata.link as
      | RelativePathString
      | undefined
      | null;

    if (link) {
      router.push(link);
    }
  };

  const renderNotification = ({ item }: { item: Notification }) => (
    <NotificationCard
      notification={item}
      onPress={() => handleNotificationPress(item)}
    />
  );

  const renderEmpty = () => (
    <View className="flex-1 items-center justify-center px-6 py-12">
      <View className="items-center gap-3">
        <View className="h-16 w-16 items-center justify-center rounded-full bg-black-100">
          <Text className="text-2xl">🔔</Text>
        </View>
        <Text className="font-jakarta-semibold text-lg text-black-700">
          No notifications yet
        </Text>
        <Text className="text-center font-inter text-black-500">
          You'll see important updates and alerts here when they arrive.
        </Text>
      </View>
    </View>
  );

  const renderHeader = () => (
    <View className="flex-row items-center gap-1">
      <Button onPress={() => router.back()} className="ml-4">
        <AntDesign name="left" size={14} color="black" />
      </Button>
      <View className="px-4 pb-4 pt-6">
        <Text className="font-jakarta-bold text-2xl text-black-800">
          Notifications
        </Text>
        <Text className="mt-1 font-inter text-black-500">
          Stay updated with your latest alerts
        </Text>
      </View>
    </View>
  );

  if (isPending) {
    return (
      <SafeAreaView className="flex-1 bg-white">
        {renderHeader()}
        <View className="flex-1 items-center justify-center">
          <ActivityIndicator size="large" color="#00CCCC" />
          <Text className="mt-3 font-inter text-black-500">
            Loading notifications...
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  if (isError) {
    return (
      <SafeAreaView className="flex-1 bg-white">
        {renderHeader()}
        <Error
          title="Failed to fetch notifications"
          message="Please try again later."
          onRetry={refetch}
        />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-white">
      <FlatList
        // @ts-expect-error: Typescript dont know about the metadata type
        data={notifications}
        renderItem={renderNotification}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={renderEmpty}
        contentContainerStyle={{ flexGrow: 1 }}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={isRefetching}
            onRefresh={refetch}
            colors={["#00CCCC"]}
            progressBackgroundColor="white"
          />
        }
        ItemSeparatorComponent={() => <View className="h-3" />}
        className="flex-1"
      />
    </SafeAreaView>
  );
};

export default NotificationIndex;
