import { useState } from "react";
import { Alert, Text, View } from "react-native";
import { showMessage } from "react-native-flash-message";
import { ScrollView } from "react-native-gesture-handler";
import { SafeAreaView } from "react-native-safe-area-context";
import { router } from "expo-router";
import ContactSupportCard from "@/components/shared/contact-support-card";
import ActiveOrderMap from "@/components/shared/google/active-order-map";
import { ChatModal } from "@/components/shared/orders/order-chat-modal";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { useModal } from "@/components/ui/modal";
import useActiveOrder from "@/hooks/use-active-order";
import { trpc } from "@/utils/api";
import { calculateDistanceToOrder, formatDistance } from "@/utils/functions";
import { useLocationStore } from "@/utils/store";
import { Feather, Ionicons } from "@expo/vector-icons";
import { skipToken, useQuery } from "@tanstack/react-query";

const OrderDetails = () => {
  const { location } = useLocationStore();
  const { orderData } = useActiveOrder();
  const [isChatModalOpen, setIsChatModalOpen] = useState(false);

  const { data: estimatedTotalAmount } = useQuery(
    trpc.order.getEstimatedOrderTotalAmount.queryOptions(
      orderData?.id ? { orderId: orderData.id } : skipToken,
    ),
  );

  const distance = calculateDistanceToOrder(location, orderData?.address);
  const formattedDistance = formatDistance(distance);

  const handleCancelOrder = () => {
    Alert.alert(
      "Order Cancelled",
      "The order has been cancelled successfully.",
      [
        {
          text: "OK",
          onPress: () => router.replace("/orders"),
        },
      ],
    );
  };

  const handleStartPickup = () => {
    if (orderData?.pickupOtpStatus === "VERIFIED") {
      router.push("/collect-items-price");
    } else {
      router.push("/arrival-verification");
    }
  };

  const handleCallCustomer = () => {
    if (!orderData?.sellerId) {
      showMessage({
        message: "No seller ID found for this order.",
        type: "danger",
      });
      return;
    }

    router.push({
      pathname: "/phone-call",
      params: {
        recipientId: orderData.sellerId,
      },
    });
  };

  const { ref, present, dismiss } = useModal();

  const handlePresentModal = () => {
    setIsChatModalOpen(true);
    present();
  };

  const handleDismissModal = () => {
    setIsChatModalOpen(false);
    dismiss();
  };

  return (
    <SafeAreaView className="-pt-safe-offset-[110px] flex-1 bg-white px-5">
      <ScrollView
        className="flex w-full flex-1 flex-col gap-5"
        showsVerticalScrollIndicator={false}
        contentContainerClassName="gap-5 flex w-full"
      >
        {/* Customer Location Info */}
        <View className="py-3">
          <View className="flex-row items-start gap-2">
            <View className="items-center justify-center rounded-xl bg-teal-50 p-2">
              <Ionicons name="location-outline" size={22} color="" />
            </View>
            <View className="flex-1">
              <Text className="font-jakarta-bold text-lg font-bold">
                Customer Location
              </Text>
              <Text className="font-jakarta-regular text-ellipsis text-gray-600">
                {orderData?.address.display}
              </Text>
            </View>
          </View>
        </View>

        {/* Map View */}
        <View className="flex gap-[14px]">
          <ActiveOrderMap />

          {/* Distance info */}
          <View className="flex h-fit w-full flex-row items-center justify-start gap-3">
            <Badge
              label={formattedDistance}
              variant="secondary"
              className="rounded-full"
            />
            {/* <Badge
              label="ETA: 15 min : 40 sec"
              variant="secondary"
              className="rounded-full"
            /> */}
          </View>

          {/* Location status */}
          {/* when distance is under 1km then this below message will be visible */}
          {distance && distance < 1 && (
            <View className="mb-4 flex-row items-center">
              <Ionicons
                name="checkmark-circle-outline"
                size={18}
                color="#10b981"
              />

              <Text className="ml-1 text-sm text-green-600">
                You are near the location
              </Text>
            </View>
          )}
          <View className="flex gap-3">
            <Button
              label={
                orderData?.pickupOtpStatus === "VERIFIED" &&
                orderData?.kabadiwalaSelfiImageFileKey &&
                orderData?.kabadiwalaSelfiImageUrl
                  ? "View More Details"
                  : "Start Pickup / Arrived at Location"
              }
              onPress={handleStartPickup}
              className="w-full rounded-md bg-teal-600"
            />
          </View>
        </View>

        {/* Talk to customer section */}
        <View className="gap-5 rounded-[20px] border border-black-50 px-4 py-5">
          <View className="w-full flex-row items-start justify-start gap-2">
            <Ionicons
              name="information-circle-outline"
              size={22}
              color="#006666"
            />
            <View className="w-full flex-1 items-start justify-start">
              <Text className="font-inter-semibold text-teal-800">
                Talk to Customer
              </Text>
              <Text className="text-wrap font-inter text-xs leading-5 text-black-500">
                Message or call the customer if you anticipate being late{" "}
              </Text>
            </View>
          </View>

          <View className="flex w-full flex-row items-center justify-between gap-3">
            <Button
              onPress={handlePresentModal}
              className="w-full flex-1 flex-row items-center justify-start rounded-full border border-yellow-600 bg-yellow-0 px-4 py-3"
            >
              <Ionicons name="chatbubble-outline" size={18} color="#374151" />
              <Text className="ml-2 text-gray-500">Hello Customer...</Text>
            </Button>

            <Button
              onPress={handleCallCustomer}
              className="min-h-fit min-w-fit flex-shrink-0 shrink-0 items-center justify-center rounded-full border border-yellow-600 bg-yellow-0 p-4"
            >
              <Ionicons name="call-outline" size={20} color="#374151" />
            </Button>
          </View>
        </View>

        {/* Pickup Details */}
        <View className="flex gap-5 rounded-[20px] bg-black-0 px-4 py-5">
          <View className="flex flex-row items-center gap-3">
            <Badge className="bg-teal-50 p-2">
              <Feather name="package" size={24} color="#374151" />
            </Badge>
            <Text className="font-inter-semibold text-teal-800">
              Pickup Details
            </Text>
          </View>

          <View className="flex gap-[14px]">
            <View className="mb-1 flex-row justify-between">
              <Text className="text-xs text-black-600">Estimated Value</Text>
              <Text className="font-inter-semibold text-sm text-teal-700">
                ₹{orderData?.totalAmount ?? estimatedTotalAmount ?? "pending"}
              </Text>
            </View>

            <View className="mb-1 flex-row justify-between">
              <Text className="text-xs text-black-600">Category</Text>
              <Text className="font-inter-medium line-clamp-1 max-w-[60%] text-ellipsis text-sm text-black-800">
                {orderData?.items
                  .map(
                    (item) => item.category.parent?.name ?? item.category.name,
                  )
                  .join(", ") ?? "Unknown"}
              </Text>
            </View>

            <View className="mb-3 flex-row items-center">
              <Ionicons name="cube-outline" size={18} color="#666666" />
              <Text className="font-inter-medium ml-2 text-sm text-black-700">
                {orderData?.items.length}{" "}
                {orderData?.items.length === 1 ? "item" : "items"}
              </Text>
            </View>
          </View>
        </View>

        {/* Customer Details */}
        <View className="flex gap-5 rounded-[20px] bg-black-0 px-4 py-5">
          <View className="flex flex-row items-center gap-3">
            <Badge className="bg-teal-50 p-2">
              <Ionicons name="person-outline" size={24} color="#374151" />
            </Badge>
            <Text className="font-inter-semibold text-teal-800">
              Customer Details
            </Text>
          </View>

          <View className="flex gap-[14px]">
            <View className="mb-1 flex-row justify-between">
              <Text className="text-xs text-black-600">Name</Text>
              <Text className="font-inter-semibold text-sm text-black-800">
                {orderData?.seller.fullName}
              </Text>
            </View>

            <View className="mb-1 flex-row justify-between">
              <Text className="text-xs text-black-600">Landmark</Text>
              <Text className="font-inter-semibold text-sm text-black-800">
                {orderData?.address.landmark ?? "Not provided"}
              </Text>
            </View>

            <View className="mb-1 flex-row justify-between">
              <Text className="text-xs text-black-600">Time Window</Text>
              <Text className="font-inter-semibold text-sm text-black-800">
                Today, 2-4 PM
              </Text>
            </View>
          </View>
        </View>

        {/* Cancel Order */}
        <Button
          onPress={handleCancelOrder}
          className="items-center rounded-xl bg-red-50 py-3"
        >
          <Text className="font-medium text-red-600">Cancel Order</Text>
        </Button>

        {/* Need Support Section */}
        <ContactSupportCard orderId={orderData?.id} />
      </ScrollView>

      <ChatModal
        ref={ref}
        dismiss={handleDismissModal}
        onCallPress={handleCallCustomer}
        isOpen={isChatModalOpen}
      />
    </SafeAreaView>
  );
};

export default OrderDetails;
