import React, { useState } from "react";
import { View } from "react-native";
import { useLocalSearchParams } from "expo-router";
import AddItemToOrderModalContent from "@/components/shared/orders/add-item-to-order-modal-content";
import BrowseCategoryCard from "@/components/shared/orders/browse-category-card";
import { Modal, useModal } from "@/components/ui/modal";
import { trpc } from "@/utils/api";
import { useQuery } from "@tanstack/react-query";

const CategoryIndividualScreen = () => {
  const { id } = useLocalSearchParams();
  const [itemPressedId, setItemPressedId] = useState<string | null>(null);

  const { ref, present, dismiss } = useModal();

  const { data } = useQuery(
    trpc.category.getSubCategoriesWithOrWithoutParent.queryOptions(
      {
        id: id as string,
        withParent: true,
      },
      {
        enabled: !!id,
      },
    ),
  );

  const parent = data?.parent;
  const subCategories = data?.subCategories;

  const handleAddUpdateInOrder = (id: string) => {
    setItemPressedId(id);
    present();
  };

  if (!subCategories || subCategories.length === 0) {
    if (!parent) return null;

    return (
      <View className="flex-1 bg-white px-4">
        <BrowseCategoryCard
          category={parent}
          parentName={parent.name}
          variant="add-to-order"
          onAddUpdateInOrderPress={() => handleAddUpdateInOrder(parent.id)}
        />

        <Modal
          ref={ref}
          children={
            <AddItemToOrderModalContent
              itemId={itemPressedId}
              onDismiss={() => dismiss()}
            />
          }
          enablePanDownToClose={false}
          snapPoints={["50%"]}
        />
      </View>
    );
  }

  return (
    <>
      <View className="flex-1 bg-white px-4">
        {subCategories.map((item) =>
          item.children.length > 0 ? (
            <BrowseCategoryCard
              key={item.id}
              category={item}
              parentName={parent?.name}
              variant="view-items"
            />
          ) : (
            <BrowseCategoryCard
              key={item.id}
              category={item}
              parentName={parent?.name}
              variant="add-to-order"
              onAddUpdateInOrderPress={() => handleAddUpdateInOrder(item.id)}
            />
          ),
        )}
      </View>

      <Modal
        ref={ref}
        children={
          <AddItemToOrderModalContent
            itemId={itemPressedId}
            onDismiss={() => dismiss()}
          />
        }
        enablePanDownToClose={false}
        snapPoints={["50%"]}
      />
    </>
  );
};

export default CategoryIndividualScreen;
