import type { Category } from "@/types/types";
import React, { useEffect, useState } from "react";
import { FlatList, Text, View } from "react-native";
import { RefreshControl, ScrollView } from "react-native-gesture-handler";
import { KeyboardAwareScrollView } from "react-native-keyboard-controller";
import Error from "@/components/shared/error";
import CategoryCard from "@/components/shared/orders/category-card";
import SkeletonLoading from "@/components/shared/skeleton-loading";
import { Input } from "@/components/ui/input";
import { trpc } from "@/utils/api";
import { useQuery } from "@tanstack/react-query";

const AddItemToOrderIndex = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  const {
    data: topLevelCategories,
    isPending,
    isRefetching,
    isError,
    refetch,
  } = useQuery(trpc.category.getTopLevelCategories.queryOptions({}));

  const {
    data: searchResults,
    isPending: isSearchPending,
    isError: isSearchError,
  } = useQuery({
    ...trpc.category.searchCategoryByName.queryOptions({
      name: debouncedSearchTerm,
      limit: 5,
    }),
    enabled: debouncedSearchTerm.length > 0,
  });

  if (isPending || isRefetching) {
    return (
      <View className="px-4 py-4">
        <SkeletonLoading type="card" />;
      </View>
    );
  }

  if (isError) {
    return (
      <Error
        title="Error loading categories"
        message="Please try again later."
        onRetry={refetch}
      />
    );
  }

  const renderCategoryItem = ({ item }: { item: Category }) => (
    <CategoryCard category={item} />
  );

  return (
    <ScrollView
      className="flex-1"
      contentContainerStyle={{ flexGrow: 1, backgroundColor: "white" }}
      refreshControl={
        <RefreshControl refreshing={isRefetching} onRefresh={refetch} />
      }
    >
      <View className="p-4">
        <KeyboardAwareScrollView>
          <Input
            className="flex-1"
            placeholder="Search categories..."
            value={searchTerm}
            onChangeText={(e) => {
              setSearchTerm(e);
            }}
          />
        </KeyboardAwareScrollView>

        {debouncedSearchTerm.length > 0 && (
          <View className="mt-4">
            {isSearchPending ? (
              <View className="py-4">
                <SkeletonLoading type="card" />
              </View>
            ) : isSearchError ? (
              <View className="py-4">
                <Text className="text-center text-gray-500">
                  Error searching categories. Please try again.
                </Text>
              </View>
            ) : searchResults && searchResults.length > 0 ? (
              <View>
                <Text className="mb-3 text-lg font-semibold text-gray-800">
                  Search Results
                </Text>
                <View className="gap-3">
                  {searchResults.map((item) => (
                    <CategoryCard key={item.id} category={item} />
                  ))}
                </View>
              </View>
            ) : (
              <View className="py-4">
                <Text className="text-center text-gray-500">
                  No categories found for "{debouncedSearchTerm}"
                </Text>
              </View>
            )}
          </View>
        )}
      </View>

      {searchResults && searchResults.length > 0 ? (
        <View className="h-px bg-gray-200" />
      ) : null}

      <FlatList
        data={topLevelCategories}
        renderItem={renderCategoryItem}
        keyExtractor={(item) => item.id.toString()}
        className="flex-1 bg-white px-4 py-4"
        numColumns={2}
        columnWrapperStyle={{ gap: 12 }}
        showsVerticalScrollIndicator={false}
      />
    </ScrollView>
  );
};

export default AddItemToOrderIndex;
