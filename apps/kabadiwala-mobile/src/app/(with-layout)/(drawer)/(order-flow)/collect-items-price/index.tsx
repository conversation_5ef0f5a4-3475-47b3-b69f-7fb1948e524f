import React, { useState } from "react";
import { Image, Text, View } from "react-native";
import { showMessage } from "react-native-flash-message";
import { RefreshControl, ScrollView } from "react-native-gesture-handler";
import { SafeAreaView } from "react-native-safe-area-context";
import { useRouter } from "expo-router";
import ContactSupportCard from "@/components/shared/contact-support-card";
import AddItemToOrderModalContent from "@/components/shared/orders/add-item-to-order-modal-content";
import OrderItemCard from "@/components/shared/orders/order-item-card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Modal, useModal } from "@/components/ui/modal";
import useActiveOrder from "@/hooks/use-active-order";
import { trpc } from "@/utils/api";
import TrashIcon from "@assets/icons/trash.png";
import { Feather, Ionicons } from "@expo/vector-icons";
import AntDesign from "@expo/vector-icons/AntDesign";
import {
  skipToken,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";

const CollectItemsAndPrice = () => {
  const router = useRouter();
  const [selectedItemId, setSelectedItemId] = useState<string | null>(null);

  const { ref: updateItemModalRef, dismiss, present } = useModal();

  const { orderData, refetchActiveOrder, isRefetchingActiveOrder } =
    useActiveOrder();

  const queryClient = useQueryClient();
  const { data: estiMatedTotalAmount } = useQuery(
    trpc.order.getEstimatedOrderTotalAmount.queryOptions(
      orderData?.id
        ? {
            orderId: orderData.id,
          }
        : skipToken,
    ),
  );

  const { mutate: removeItemFromOrder } = useMutation(
    trpc.order.removeItemFromOrder.mutationOptions({
      onSuccess: async (opts) => {
        await queryClient.invalidateQueries(
          trpc.order.getActiveOrder.queryOptions(),
        );
        await queryClient.invalidateQueries(
          trpc.order.getEstimatedOrderTotalAmount.queryOptions(
            orderData?.id ? { orderId: orderData.id } : skipToken,
          ),
        );
        showMessage({
          message: opts.message,
          type: "success",
        });
      },
      onError: (error) => {
        showMessage({
          message: error.message || "Failed to remove item from order",
          type: "danger",
        });
      },
    }),
  );

  const { mutate: requestCustomerApproval, isPending: isRequestingApproval } =
    useMutation(
      trpc.order.requestCustomerApproval.mutationOptions({
        onSuccess: async (opts) => {
          await queryClient.invalidateQueries(
            trpc.order.getActiveOrder.queryOptions(),
          );
          showMessage({
            message: opts.message,
            type: "success",
          });
        },
        onError: (error) => {
          showMessage({
            message: error.message || "Failed to request customer approval",
            type: "danger",
          });
        },
      }),
    );

  const handleAddItemInOrderPress = () => {
    router.push("/add-item-to-order");
  };

  const handleRemoveItemFromOrder = (itemId: string) => {
    if (!orderData?.id) {
      showMessage({
        message: "No active order found",
        type: "danger",
      });
      return;
    }

    removeItemFromOrder({ categoryId: itemId, orderId: orderData.id });
  };

  const handleRequestCustomerApproval = () => {
    if (!orderData?.id) {
      showMessage({
        message: "No active order found",
        type: "danger",
      });
      return;
    }

    requestCustomerApproval({ orderId: orderData.id });
  };

  const handleEditItemInOrderPress = (itemId: string) => {
    setSelectedItemId(itemId);
    present();
  };

  const handleProceedToPayment = () => {
    if (!orderData?.id) {
      showMessage({
        message: "No active order found",
        type: "danger",
      });
      return;
    }

    router.push("/pay-for-order");
  };

  return (
    <>
      <SafeAreaView className="-pt-safe-offset-[110px] flex-1 bg-white px-5">
        <ScrollView
          className="flex w-full flex-1 flex-col gap-5"
          showsVerticalScrollIndicator={false}
          contentContainerClassName="gap-5 flex w-full mb-10"
          refreshControl={
            <RefreshControl
              refreshing={isRefetchingActiveOrder}
              onRefresh={refetchActiveOrder}
            />
          }
        >
          {/* Amount To Pay Card */}
          {orderData?.items && orderData.items.length > 0 ? (
            <View className="gap-6 rounded-[20px] border border-black-150 bg-white px-4 py-5">
              {/* title */}
              <View className="flex-row items-center gap-3">
                <View className="rounded-lg bg-black-50 p-2">
                  <Image source={TrashIcon} className="h-6 w-6" />
                </View>
                <Text className="font-inter-semibold font-semibold leading-6 text-black-800">
                  Amount to Pay
                </Text>
              </View>

              {/* items */}
              <View className="flex-col gap-1">
                {orderData.items.map((item) => (
                  <View className="rounded-lg bg-black-0 p-2">
                    <View className="flex-row items-center justify-between">
                      <View>
                        <Text className="font-inter-semibold font-semibold leading-5 text-black-700">
                          {item.category.name}
                        </Text>
                        <View>
                          {item.category.rateType === "PER_ITEM" ? (
                            <Text className="text-xs text-black-600">
                              Per Item : ₹ {item.category.rate}
                            </Text>
                          ) : (
                            <Text className="text-xs text-black-600">
                              Per Kg : ₹ {item.category.rate}
                            </Text>
                          )}
                          <Text className="text-xs text-black-600">
                            Quantity : {item.quantity}
                          </Text>
                        </View>
                      </View>
                      <Text className="font-inter-semibold font-semibold leading-5 text-black-800">
                        ₹ {Number(item.category.rate) * Number(item.quantity)}
                      </Text>
                    </View>
                  </View>
                ))}
              </View>

              {/* horizontal line */}
              <View className="h-[1px] bg-black-800" />

              {/* estimated amount */}
              <View className="flex-col gap-1">
                <View className="flex-row items-center justify-between">
                  <Text className="font-inter-semibold text-xl font-semibold leading-6">
                    Estimated Amount
                  </Text>
                  <Text className="font-jakarta-bold text-xl font-bold leading-9 text-teal-850">
                    ₹ {estiMatedTotalAmount}
                  </Text>
                </View>

                {/* total amount */}
                {/* {orderData?.totalAmount ? (
                <View className="flex-row items-center justify-between">
                  <Text className="font-inter-semibold text-2xl font-semibold leading-6">
                    Total Amount
                  </Text>
                  <Text className="font-jakarta-bold text-xl font-bold leading-9 text-teal-850">
                    ₹ {orderData.totalAmount}
                  </Text>
                </View>
              ) : null} */}
              </View>

              <View className="flex-col gap-2">
                {/* Status note */}
                {orderData.orderApprovalStatus ===
                  "REQUESTED_RE_REVIEW_BY_SELLER" && (
                  <View className="rounded-lg bg-yellow-50 p-3">
                    <Text className="text-center text-sm text-yellow-800">
                      💡 Customer wants you to review the items and pricing
                      again
                    </Text>
                  </View>
                )}

                {orderData.orderApprovalStatus ===
                  "REQUEST_REJECTED_BY_SELLER" && (
                  <View className="rounded-lg bg-red-50 p-3">
                    <Text className="text-center text-sm text-red-800">
                      ❌ Customer has rejected this order. You may need to
                      adjust items or pricing.
                    </Text>
                  </View>
                )}

                {orderData.orderApprovalStatus ===
                  "REQUEST_APPROVED_BY_SELLER" && (
                  <View className="rounded-lg bg-green-50 p-3">
                    <Text className="text-center text-sm text-green-800">
                      ✅ Great! Customer has approved the order. You can proceed
                      with payment.
                    </Text>
                  </View>
                )}

                {/* confirm payment button */}
                {orderData.orderApprovalStatus ===
                "REQUEST_APPROVED_BY_SELLER" ? (
                  <Button
                    className="bg-teal-800"
                    onPress={handleProceedToPayment}
                  >
                    <AntDesign
                      name="checkcircleo"
                      className="mr-2"
                      size={18}
                      color="white"
                    />
                    <Text className="text-teal-50">Proceed For Payment</Text>
                  </Button>
                ) : (
                  <Button
                    className="bg-teal-800"
                    onPress={handleRequestCustomerApproval}
                    disabled={
                      isRequestingApproval ||
                      orderData.orderApprovalStatus ===
                        "REQUESTED_CONFIRMATION_BY_KABADIWALA" ||
                      orderData.orderApprovalStatus ===
                        "REQUEST_REJECTED_BY_SELLER"
                    }
                  >
                    {isRequestingApproval ? (
                      <AntDesign
                        name="loading1"
                        size={18}
                        color="white"
                        className="mr-2 animate-spin"
                      />
                    ) : null}

                    {/* Handle different approval states */}
                    {orderData.orderApprovalStatus ===
                    "REQUESTED_CONFIRMATION_BY_KABADIWALA" ? (
                      <>
                        <AntDesign
                          name="clockcircleo"
                          className="mr-2"
                          size={18}
                          color="white"
                        />
                        <Text className="text-teal-50">
                          Waiting for Customer Response
                        </Text>
                      </>
                    ) : orderData.orderApprovalStatus ===
                      "REQUEST_REJECTED_BY_SELLER" ? (
                      <>
                        <AntDesign
                          name="closecircleo"
                          className="mr-2"
                          size={18}
                          color="white"
                        />
                        <Text className="text-teal-50">
                          Rejected by Customer
                        </Text>
                      </>
                    ) : (
                      <Text className="text-teal-50">
                        Request Customer Approval
                      </Text>
                    )}
                  </Button>
                )}

                <Button
                  className="flex-row items-center justify-center gap-2 bg-yellow-150"
                  onPress={handleAddItemInOrderPress}
                  disabled={
                    orderData.orderApprovalStatus ===
                      "REQUESTED_CONFIRMATION_BY_KABADIWALA" ||
                    orderData.orderApprovalStatus ===
                      "REQUEST_APPROVED_BY_SELLER" ||
                    orderData.orderApprovalStatus ===
                      "REQUEST_REJECTED_BY_SELLER"
                  }
                >
                  <AntDesign name="plus" size={18} color="black" />
                  <Text className="">Add Item In Order</Text>
                </Button>
              </View>
            </View>
          ) : null}

          {orderData?.items.length === 0 ? (
            <View className="flex flex-col gap-8">
              <Text className="font-jakarta-semibold self-center text-center text-xl font-semibold">
                “Please add scrap item to continue with pickup”
              </Text>

              <Button
                className="flex-row items-center justify-center gap-2 bg-yellow-150"
                onPress={handleAddItemInOrderPress}
              >
                <AntDesign name="plus" size={20} color="black" />
                <Text className="">Add Item</Text>
              </Button>
            </View>
          ) : (
            <>
              {orderData?.items.map((item) => (
                <OrderItemCard
                  name={item.category.name}
                  image={item.category.image}
                  rate={Number(item.category.rate)}
                  rateType={item.category.rateType}
                  showDeleteIcon
                  showEditIcon
                  onDeletePress={() =>
                    handleRemoveItemFromOrder(item.categoryId)
                  }
                  onEditPress={() =>
                    handleEditItemInOrderPress(item.categoryId)
                  }
                  disabledDeleteIcon={
                    orderData.orderApprovalStatus ===
                      "REQUESTED_CONFIRMATION_BY_KABADIWALA" ||
                    orderData.orderApprovalStatus ===
                      "REQUEST_APPROVED_BY_SELLER" ||
                    orderData.orderApprovalStatus ===
                      "REQUEST_REJECTED_BY_SELLER"
                  }
                  disabledEditIcon={
                    orderData.orderApprovalStatus ===
                      "REQUESTED_CONFIRMATION_BY_KABADIWALA" ||
                    orderData.orderApprovalStatus ===
                      "REQUEST_APPROVED_BY_SELLER" ||
                    orderData.orderApprovalStatus ===
                      "REQUEST_REJECTED_BY_SELLER"
                  }
                />
              ))}
            </>
          )}

          {/* Pickup Details */}
          <View className="flex gap-5 rounded-[20px] bg-black-0 px-4 py-5">
            <View className="flex flex-row items-center gap-3">
              <Badge className="bg-teal-50 p-2">
                <Feather name="trash-2" size={24} color="#374151" />
              </Badge>
              <Text className="font-inter-semibold text-teal-800">
                Pickup Details
              </Text>
            </View>

            <View className="flex gap-[14px]">
              <View className="mb-1 flex-row justify-between">
                <Text className="text-xs text-black-600">Estimated Value</Text>
                <Text className="font-inter-semibold text-sm text-teal-700">
                  ₹{estiMatedTotalAmount}
                </Text>
              </View>

              <View className="mb-1 flex-row justify-between">
                <Text className="text-xs text-black-600">Category</Text>
                <Text className="font-inter-medium line-clamp-1 max-w-[60%] text-ellipsis text-sm text-black-800">
                  {orderData?.items
                    .map(
                      (item) =>
                        item.category.parent?.name ?? item.category.name,
                    )
                    .join(", ") ?? "Unknown"}
                </Text>
              </View>

              <View className="mb-1 flex-row justify-between gap-5">
                <Text className="text-xs text-black-600">Items</Text>
                <Text className="font-inter-medium line-clamp-1 max-w-[60%] text-ellipsis text-wrap text-sm text-black-800">
                  {orderData?.items
                    .map((item) => item.category.name)
                    .join(", ") ?? "No items"}
                </Text>
              </View>
            </View>
          </View>

          {/* Customer Details */}
          <View className="flex gap-5 rounded-[20px] bg-black-0 px-4 py-5">
            <View className="flex flex-row items-center gap-3">
              <Badge className="bg-teal-50 p-2">
                <Ionicons name="person-outline" size={24} color="#374151" />
              </Badge>
              <Text className="font-inter-semibold text-teal-800">
                Customer Details
              </Text>
            </View>

            <View className="flex gap-[14px]">
              <View className="mb-1 flex-row justify-between">
                <Text className="text-xs text-black-600">Name</Text>
                <Text className="font-inter-semibold text-sm text-black-800">
                  {orderData?.seller.fullName}
                </Text>
              </View>

              <View className="mb-1 flex-row justify-between">
                <Text className="text-xs text-black-600">Landmark</Text>
                <Text className="font-inter-semibold text-sm text-black-800">
                  {orderData?.address.landmark ?? "Not provided"}
                </Text>
              </View>

              <View className="mb-1 flex-row justify-between">
                <Text className="text-xs text-black-600">Time Window</Text>
                <Text className="font-inter-semibold text-sm text-black-800">
                  Today, 2-4 PM
                </Text>
              </View>
            </View>
          </View>

          {/* Need Support Section */}
          <ContactSupportCard orderId={orderData?.id} />
        </ScrollView>
      </SafeAreaView>

      <Modal
        ref={updateItemModalRef}
        children={
          <AddItemToOrderModalContent
            itemId={selectedItemId}
            onDismiss={() => dismiss()}
          />
        }
      />
    </>
  );
};

export default CollectItemsAndPrice;
