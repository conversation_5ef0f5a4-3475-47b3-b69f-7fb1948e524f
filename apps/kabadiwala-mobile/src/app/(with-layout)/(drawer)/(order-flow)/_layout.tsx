import { Stack } from "expo-router";
import OrderDetailsHeader from "@/components/shared/orders/order-details-header";

const OrderStartedLayout = () => {
  return (
    <Stack>
      <Stack.Screen
        name="congratulations/index"
        options={{
          title: "Order Confirmation",
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="order-details/index"
        options={{
          title: "Order Details",
          headerShown: true,
          header: () => <OrderDetailsHeader title="Order Details" />,
        }}
      />
      <Stack.Screen
        name="arrival-verification/index"
        options={{
          title: "Arrival Verification",
          headerShown: true,
          header: () => <OrderDetailsHeader title="Arrival Verification" />,
        }}
      />
      <Stack.Screen
        name="collect-items-price/index"
        options={{
          title: "Collect Items and Price",
          headerShown: true,
          header: () => <OrderDetailsHeader title="Collect Items and Price" />,
        }}
      />
      <Stack.Screen
        name="add-item-to-order/index"
        options={{
          title: "Add Item to Order",
          headerShown: true,
          header: () => <OrderDetailsHeader title="Add Item to Order" />,
        }}
      />
      <Stack.Screen
        name="add-item-to-order/[id]"
        options={{
          title: "Add Item to Order",
          headerShown: true,
          header: () => <OrderDetailsHeader title="Add Item to Order" />,
        }}
      />
      <Stack.Screen
        name="phone-call"
        options={{
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="order-history-details/index"
        options={{
          headerShown: false,
        }}
      />
    </Stack>
  );
};
export default OrderStartedLayout;
