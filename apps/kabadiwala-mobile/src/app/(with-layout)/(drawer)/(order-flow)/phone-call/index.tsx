import React from "react";
import { SafeAreaView } from "react-native-safe-area-context";
import { router } from "expo-router";
import { useVideo } from "@/components/shared/providers/video-provider";
import SettingUpCallError from "@/components/shared/stream/setting-up-call-error";
import SettingUpCallFailed from "@/components/shared/stream/setting-up-call-failed";
import SettingUpCallLoading from "@/components/shared/stream/setting-up-call-loading";
import {
  CallContent,
  StreamCall,
  StreamVideo,
} from "@stream-io/video-react-native-sdk";

const PhoneCallIndex = () => {
  const { call, streamClient, loading, error, retryCall } = useVideo();

  if (loading) {
    return <SettingUpCallLoading />;
  }

  if (error) {
    return <SettingUpCallError error={error} onRetry={retryCall} />;
  }

  if (!call || !streamClient) {
    return <SettingUpCallFailed onRetry={retryCall} />;
  }

  const handleHangupCall = async () => {
    await call.leave();
    router.back();
  };

  return (
    <SafeAreaView className="flex-1">
      <StreamVideo client={streamClient}>
        <StreamCall call={call}>
          <CallContent onHangupCallHandler={handleHangupCall} />
        </StreamCall>
      </StreamVideo>
    </SafeAreaView>
  );
};

export default PhoneCallIndex;
