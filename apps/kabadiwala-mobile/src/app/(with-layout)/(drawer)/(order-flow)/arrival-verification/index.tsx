import type { BottomSheetBackdropProps } from "@gorhom/bottom-sheet";
import type { SubmitHandler } from "react-hook-form";
import type { z } from "zod";
import React from "react";
import { Text, View } from "react-native";
import { showMessage } from "react-native-flash-message";
import { KeyboardAwareScrollView } from "react-native-keyboard-controller";
import { OtpInput } from "react-native-otp-entry";
import { SafeAreaView } from "react-native-safe-area-context";
import { Image } from "expo-image";
import ArrivalSelfieVerificationModalContent from "@/components/shared/orders/arrival-selfie-verification-modal-content";
import { Button } from "@/components/ui/button";
import { Modal, useModal } from "@/components/ui/modal";
import useActiveOrder from "@/hooks/use-active-order";
import { trpc } from "@/utils/api";
import LocationImage from "@assets/images/location.png";
import AntDesign from "@expo/vector-icons/AntDesign";
import { BottomSheetBackdrop } from "@gorhom/bottom-sheet";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import { useForm } from "react-hook-form";

import { KabadiwalaArrivalVerificationSchema } from "@acme/validators/kabadiwala";

const ArrivalVerificationIndex = () => {
  const { orderData } = useActiveOrder();
  const form = useForm<z.infer<typeof KabadiwalaArrivalVerificationSchema>>({
    resolver: zodResolver(KabadiwalaArrivalVerificationSchema),
    defaultValues: {
      otp: "",
    },
  });

  const { ref, present, dismiss } = useModal();
  const renderBackdrop = React.useCallback(
    (props: BottomSheetBackdropProps) => (
      <BottomSheetBackdrop
        {...props}
        pressBehavior="none" // Prevents closing on outside tap
      />
    ),
    [],
  );

  const { mutate: verifyOrderOtp, isPending } = useMutation(
    trpc.order.verifyOrderOtp.mutationOptions({
      onSuccess: (opts) => {
        showMessage({
          message: opts.message,
          description: opts.subMessage,
          type: "success",
        });
        present();
      },
      onError: (error) => {
        showMessage({
          message: "Error",
          description: error.message,
          type: "danger",
        });
      },
    }),
  );

  const onSubmit: SubmitHandler<
    z.infer<typeof KabadiwalaArrivalVerificationSchema>
  > = (data) => {
    if (!orderData?.id) {
      showMessage({
        message: "Error",
        description: "Order ID is missing.",
        type: "danger",
      });
      return;
    }

    verifyOrderOtp({ ...data, orderId: orderData.id });
  };

  return (
    <>
      <SafeAreaView className="-pt-safe-offset-[110px] flex-1 bg-white px-5">
        <View className="flex flex-1">
          <KeyboardAwareScrollView contentContainerClassName="flex-1 justify-evenly">
            {/* Location Image & Text */}
            <View className="mx-auto flex max-w-[287px] flex-col items-center justify-center gap-6">
              <Image
                source={LocationImage}
                contentFit="cover"
                style={{ width: 80, height: 80 }}
              />

              <View className="flex flex-col gap-1">
                <Text className="font-inter-bold text-center text-xl font-bold leading-[30px] text-[#02542D]">
                  You've Arrived!
                </Text>
                <Text className="line-clamp-1 text-center text-base leading-6 text-black-700">
                  {orderData?.address.display}
                </Text>
              </View>
            </View>

            {/* OTP Form */}
            <View>
              <View>
                <Text className="font-jakarta-medium mb-2 text-[13px]">
                  Customer's Pickup Code (OTP)
                </Text>
                <OtpInput
                  autoFocus={true}
                  numberOfDigits={6}
                  focusColor={"#007F80"}
                  hideStick={true}
                  placeholder="000000"
                  blurOnFilled={true}
                  type="numeric"
                  focusStickBlinkingDuration={500}
                  textInputProps={{
                    accessibilityLabel: "One-Time Password",
                  }}
                  onTextChange={(text) => form.setValue("otp", text)}
                  theme={{
                    pinCodeContainerStyle: {
                      borderRadius: 8,
                      borderColor: form.formState.errors.otp
                        ? "#FF0000"
                        : "#E6E6E6",
                      backgroundColor: "#F8F8F8",
                      borderWidth: 1.2,
                      width: 48,
                      height: 56,
                    },
                    pinCodeTextStyle: {
                      color: "#333333",
                      fontSize: 16,
                      lineHeight: 20,
                    },
                    placeholderTextStyle: {
                      color: "#B3B3B3",
                      fontSize: 16,
                    },
                  }}
                />
                {form.formState.errors.otp && (
                  <Text className="font-jakarta-regular mt-2 text-xs text-red-500">
                    {form.formState.errors.otp.message}
                  </Text>
                )}

                <Text className="font-inter-regular mt-[14px] text-xs leading-4 text-black-500">
                  The customer has a 6-digit code in their app for this pickup.
                  Enter it here.
                </Text>
              </View>

              <Button
                onPress={form.handleSubmit(onSubmit, (error) => {
                  console.log("submit handler error---", error);
                })}
                className="mt-10 w-full"
                variant={isPending ? "disabled" : "default"}
                disabled={isPending}
              >
                {isPending && (
                  <AntDesign
                    name="loading1"
                    size={16}
                    color="black"
                    className="mr-2 animate-spin"
                  />
                )}
                <Text>Verify</Text>
              </Button>
            </View>

            {/* Concern Message */}
            <View className="mt-2 flex flex-row items-center gap-2 rounded-xl bg-[#FFF8F8] p-3">
              <AntDesign name="infocirlceo" size={20} color="#900B09" />
              <Text className="font-inter-regular text-[11px] leading-[18px] text-[#900B09]">
                Entering the correct code confirms the customer agrees with the
                scrap pickup.
              </Text>
            </View>
          </KeyboardAwareScrollView>
        </View>
      </SafeAreaView>

      <Modal
        ref={ref}
        children={<ArrivalSelfieVerificationModalContent onDismiss={dismiss} />}
        backdropComponent={renderBackdrop}
        enablePanDownToClose={false}
        snapPoints={["80%"]}
      />
    </>
  );
};

export default ArrivalVerificationIndex;
