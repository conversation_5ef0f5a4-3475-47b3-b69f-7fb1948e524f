import { Text, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { router } from "expo-router";
import { Button } from "@/components/ui/button";
import useActiveOrder from "@/hooks/use-active-order";
import { Ionicons } from "@expo/vector-icons";

const CongratulationsScreen = () => {
  const { orderData } = useActiveOrder();

  const handleStartPickup = () => {
    router.replace("/orders");
  };

  return (
    <SafeAreaView className="flex-1 bg-white">
      <View className="flex-1 items-center justify-center px-6">
        {/* Success animation/icon */}
        <View className="mb-8 items-center justify-center rounded-full bg-teal-50 p-8">
          <Ionicons name="checkmark-circle" size={80} color="#006666" />
        </View>

        {/* Congratulations text */}
        <Text className="font-jakarta-bold mb-3 text-center text-3xl text-teal-800">
          Order Confirmed!
        </Text>

        <Text className="font-jakarta-regular mb-8 text-center text-base text-black-600">
          You've successfully accepted the pickup request
        </Text>

        {/* Order card */}
        <View className="mb-10 w-full overflow-hidden rounded-2xl border border-black-50 bg-black-0">
          <View className="border-b border-black-50 bg-teal-50 px-5 py-3">
            <Text className="font-inter-semibold text-sm text-teal-800">
              Order #{orderData?.id}
            </Text>
          </View>

          <View className="p-5">
            <View className="mb-3 flex-row items-center">
              <Ionicons name="location-outline" size={18} color="#666666" />
              <Text className="font-inter-medium ml-2 flex-1 text-sm text-black-700">
                {orderData?.address.display.slice(0, 40)}
              </Text>
            </View>

            <View className="mb-3 flex-row items-center">
              <Ionicons name="cube-outline" size={18} color="#666666" />
              <Text className="font-inter-medium ml-2 text-sm text-black-700">
                {orderData?.items.length}{" "}
                {orderData?.items.length === 1 ? "item" : "items"}
              </Text>
            </View>
          </View>
        </View>

        {/* Start button */}
        <Button
          label="Start Pickup"
          onPress={handleStartPickup}
          className="w-full rounded-xl bg-teal-850 py-4 shadow-sm"
        >
          <Text className="font-jakarta-semibold text-lg text-white">
            Continue to Pickup
          </Text>
          <Ionicons
            name="arrow-forward"
            size={20}
            color="white"
            className="ml-2"
          />
        </Button>
      </View>
    </SafeAreaView>
  );
};

export default CongratulationsScreen;
