import React from "react";
import { ScrollView, Text, TouchableOpacity, View } from "react-native";
import { showMessage } from "react-native-flash-message";
import { SafeAreaView } from "react-native-safe-area-context";
import { router, useLocalSearchParams } from "expo-router";
import * as WebBrowser from "expo-web-browser";
import ContactSupportCard from "@/components/shared/contact-support-card";
import NavigateToScraphubButton from "@/components/shared/orders/navigate-to-scraphub-button";
import RateSellerCard from "@/components/shared/orders/rate-seller-card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { trpc } from "@/utils/api";
import {
  Feather,
  FontAwesome,
  Ionicons,
  MaterialIcons,
} from "@expo/vector-icons";
import AntDesign from "@expo/vector-icons/AntDesign";
import { skipToken, useMutation, useQuery } from "@tanstack/react-query";
import { format } from "date-fns";

const OrderHistoryDetails = () => {
  const { orderId } = useLocalSearchParams();
  const {
    data: orderData,
    isPending,
    isError,
  } = useQuery(
    trpc.order.getOrderById.queryOptions(
      orderId && typeof orderId === "string" ? { orderId } : skipToken,
    ),
  );

  const { mutate: viewBill } = useMutation(
    trpc.payment.viewBill.mutationOptions({
      onSuccess: async (opts) => {
        await WebBrowser.openBrowserAsync(opts.billUrl);
      },
      onError: (error) => {
        showMessage({
          message: error.message || "Failed to view bill",
          type: "danger",
        });
      },
    }),
  );

  if (isPending) {
    return (
      <SafeAreaView className="flex-1 items-center justify-center bg-white">
        <Text className="text-black-700">Loading order details...</Text>
      </SafeAreaView>
    );
  }

  if (isError) {
    return (
      <SafeAreaView className="flex-1 items-center justify-center bg-white">
        <Text className="text-red-600">Failed to load order details</Text>
      </SafeAreaView>
    );
  }

  const formatOrderDate = (date: Date | string | null | undefined) => {
    if (!date) return "N/A";
    return format(new Date(date), "dd MMM yyyy");
  };

  const formatOrderTime = (date: Date | string | null | undefined) => {
    if (!date) return "";
    return format(new Date(date), "h:mm a");
  };

  const createdDate = formatOrderDate(orderData.createdAt);
  const createdTime = formatOrderTime(orderData.createdAt);
  const completedDate = formatOrderDate(orderData.completedAt);
  const completedTime = formatOrderTime(orderData.completedAt);

  const handleInvoiceView = () => {
    viewBill({
      orderId: orderData.id,
    });
  };

  return (
    <SafeAreaView className="flex-1 bg-white px-5">
      <ScrollView
        className="flex w-full flex-1 flex-col gap-5"
        showsVerticalScrollIndicator={false}
        contentContainerClassName="gap-5 flex w-full pb-8"
      >
        {/* Order Header */}
        <View className="mt-4 flex-1 flex-row items-center gap-2">
          <Button onPress={() => router.back()}>
            <AntDesign name="left" size={12} color="black" />
          </Button>
          <View className="flex flex-1 flex-row items-center justify-between">
            <View className="flex">
              <Text className="font-inter-semibold text-lg text-black-800">
                Order #{orderData.id.slice(-10).toUpperCase()}
              </Text>
              <Text className="text-sm text-black-500">
                {createdDate} {createdTime}
              </Text>
            </View>
            <OrderStatusBadge status={orderData.status} />
          </View>
        </View>

        {/* Rate Seller */}
        <RateSellerCard orderId={orderData.id} sellerId={orderData.seller.id} />

        {/* navigate to scraphub */}
        <NavigateToScraphubButton />

        {/* Order Timeline */}
        <View className="rounded-[20px] border border-black-50 p-4">
          <View className="mb-4 flex flex-row items-center gap-3">
            <Badge className="bg-teal-50 p-2">
              <MaterialIcons name="timeline" size={20} color="#006666" />
            </Badge>
            <Text className="font-inter-semibold text-base text-teal-800">
              Order Timeline
            </Text>
          </View>

          <View className="flex gap-3">
            <View className="flex-row items-center">
              <View className="h-6 w-6 items-center justify-center rounded-full bg-teal-600">
                <Text className="text-xs text-white">1</Text>
              </View>
              <View className="ml-3">
                <Text className="font-inter-medium text-sm text-black-700">
                  Order Placed
                </Text>
                <Text className="text-xs text-black-500">
                  {createdDate} {createdTime}
                </Text>
              </View>
            </View>

            <View className="ml-3 h-12 w-0.5 bg-black-100" />

            <View className="flex-row items-center">
              <View className="h-6 w-6 items-center justify-center rounded-full bg-teal-600">
                <Text className="text-xs text-white">2</Text>
              </View>
              <View className="ml-3">
                <Text className="font-inter-medium text-sm text-black-700">
                  {orderData.status === "COMPLETED"
                    ? "Order Completed"
                    : orderData.status === "CANCELLED"
                      ? "Order Cancelled"
                      : "Order Processed"}
                </Text>
                <Text className="text-xs text-black-500">
                  {orderData.status === "COMPLETED"
                    ? `${completedDate} ${completedTime}`
                    : "N/A"}
                </Text>
              </View>
            </View>
          </View>
        </View>

        {/* Customer Location Info */}
        <View className="rounded-[20px] border border-black-50 p-4">
          <View className="mb-3 flex-row items-start gap-2">
            <View className="items-center justify-center rounded-xl bg-teal-50 p-2">
              <Ionicons name="location-outline" size={22} color="#006666" />
            </View>
            <View className="flex-1">
              <Text className="font-inter-semibold text-base text-black-800">
                Pickup Location
              </Text>
              <Text className="text-sm text-black-600">
                {orderData.address.display}
              </Text>
              {orderData.address.landmark && (
                <Text className="mt-1 text-sm text-black-500">
                  Landmark: {orderData.address.landmark}
                </Text>
              )}
            </View>
          </View>
        </View>

        {/* Payment Details */}
        <View className="rounded-[20px] border border-black-50 p-4">
          <View className="mb-4 flex flex-row items-center gap-3">
            <Badge className="bg-teal-50 p-2">
              <Ionicons name="wallet-outline" size={20} color="#006666" />
            </Badge>
            <Text className="font-inter-semibold text-base text-teal-800">
              Payment Details
            </Text>
          </View>

          <View className="flex gap-3">
            <View className="flex-row justify-between">
              <Text className="text-sm text-black-600">Total Amount</Text>
              <Text className="font-inter-semibold text-sm text-black-800">
                ₹
                {orderData.totalAmount ??
                  orderData.amountToPayCustomer ??
                  "N/A"}
              </Text>
            </View>

            <View className="flex-row justify-between">
              <Text className="text-sm text-black-600">Service Charge</Text>
              <Text className="font-inter-medium text-sm text-black-800">
                ₹{orderData.securityFeeAmount ?? "0"} (
                {orderData.securityFeePercentage ?? "0"}%)
              </Text>
            </View>

            <View className="flex-row justify-between">
              <Text className="text-sm text-black-600">GST</Text>
              <Text className="font-inter-medium text-sm text-black-800">
                ₹{orderData.gstAmount ?? "0"} ({orderData.gstPercentage ?? "0"}
                %)
              </Text>
            </View>

            <View className="my-1 h-[1px] bg-black-100" />

            <View className="flex-row justify-between">
              <Text className="font-inter-semibold text-sm text-black-700">
                Amount Paid to Customer
              </Text>
              <Text className="font-inter-bold text-sm text-teal-700">
                ₹{orderData.amountToPayCustomer ?? "N/A"}
              </Text>
            </View>

            <View className="flex-row justify-between">
              <Text className="text-sm text-black-600">Payment Status</Text>
              <Text
                className={`font-inter-semibold text-sm ${
                  orderData.paymentStatus === "COMPLETED"
                    ? "text-green-600"
                    : orderData.paymentStatus === "FAILED"
                      ? "text-red-600"
                      : "text-yellow-600"
                }`}
              >
                {orderData.paymentStatus}
              </Text>
            </View>

            {orderData.paymentCompletedAt && (
              <View className="flex-row justify-between">
                <Text className="text-sm text-black-600">Payment Date</Text>
                <Text className="font-inter-medium text-sm text-black-800">
                  {format(
                    new Date(orderData.paymentCompletedAt),
                    "dd MMM yyyy",
                  )}
                </Text>
              </View>
            )}
          </View>
        </View>

        {/* Invoice Button */}
        <TouchableOpacity
          className="rounded-[20px] border border-black-50 bg-teal-50 p-4"
          onPress={handleInvoiceView}
        >
          <View className="flex-row items-center justify-between">
            <View className="flex-row items-center gap-3">
              <Badge className="bg-white p-2">
                <FontAwesome name="file-text-o" size={20} color="#006666" />
              </Badge>
              <Text className="font-inter-semibold text-base text-teal-800">
                View Invoice
              </Text>
            </View>
            <Feather name="chevron-right" size={20} color="#006666" />
          </View>
        </TouchableOpacity>

        {/* Pickup Details */}
        <View className="rounded-[20px] border border-black-50 p-4">
          <View className="mb-4 flex flex-row items-center gap-3">
            <Badge className="bg-teal-50 p-2">
              <Feather name="trash-2" size={20} color="#006666" />
            </Badge>
            <Text className="font-inter-semibold text-base text-teal-800">
              Items Collected
            </Text>
          </View>

          <View className="flex gap-3">
            {orderData.items.length > 0 ? (
              orderData.items.map((item, index) => (
                <View
                  key={index}
                  className="flex-row justify-between border-b border-black-50 pb-2"
                >
                  <View className="flex-1">
                    <Text className="font-inter-medium text-sm text-black-800">
                      {item.category.name}
                    </Text>
                    <Text className="text-xs text-black-500">
                      {item.category.parent?.name ?? "General"}
                    </Text>
                  </View>
                  <View className="flex items-end">
                    <Text className="font-inter-medium text-sm text-black-700">
                      {item.quantity}{" "}
                      {Number(item.quantity) > 1 ? "units" : "unit"}
                    </Text>
                  </View>
                </View>
              ))
            ) : (
              <Text className="text-sm text-black-500">
                No items recorded for this order
              </Text>
            )}
          </View>
        </View>

        {/* Customer Details */}
        <View className="rounded-[20px] border border-black-50 p-4">
          <View className="mb-4 flex flex-row items-center gap-3">
            <Badge className="bg-teal-50 p-2">
              <Ionicons name="person-outline" size={20} color="#006666" />
            </Badge>
            <Text className="font-inter-semibold text-base text-teal-800">
              Customer Details
            </Text>
          </View>

          <View className="flex gap-3">
            <View className="flex-row justify-between">
              <Text className="text-sm text-black-600">Name</Text>
              <Text className="font-inter-medium text-sm text-black-800">
                {orderData.seller.fullName}
              </Text>
            </View>

            <View className="flex-row justify-between">
              <Text className="text-sm text-black-600">Phone</Text>
              <Text className="font-inter-medium text-sm text-black-800">
                {orderData.seller.phoneNumber ?? "N/A"}
              </Text>
            </View>
          </View>
        </View>

        {/* Need Support Section */}
        <ContactSupportCard orderId={orderData.id} />
      </ScrollView>
    </SafeAreaView>
  );
};

export default OrderHistoryDetails;

const OrderStatusBadge = ({ status }: { status: string | null }) => {
  let color = "";
  let bgColor = "";

  switch (status) {
    case "COMPLETED":
      color = "text-green-700";
      bgColor = "bg-green-50";
      break;
    case "CANCELLED":
      color = "text-red-700";
      bgColor = "bg-red-50";
      break;
    default:
      color = "text-yellow-700";
      bgColor = "bg-yellow-50";
  }

  return (
    <View className={`rounded-full px-3 py-1 ${bgColor}`}>
      <Text className={`font-inter-medium text-xs ${color}`}>{status}</Text>
    </View>
  );
};
