import React, { useState } from "react";
import { Text, TextInput, TouchableOpacity, View } from "react-native";
import { KeyboardAwareScrollView } from "react-native-keyboard-controller";
import { SafeAreaView } from "react-native-safe-area-context";
import { router, useLocalSearchParams } from "expo-router";
import Error from "@/components/shared/error";
import RatingBottomSheet from "@/components/shared/rating-bottom-sheet";
import SkeletonLoading from "@/components/shared/skeleton-loading";
import { Badge } from "@/components/ui/badge";
import { GET_NEW_MESSAGES_IN_SUPPORT_CHAT } from "@/lib/constant";
import { trpc } from "@/utils/api";
import { Feather, Ionicons } from "@expo/vector-icons";
import { FlashList } from "@shopify/flash-list";
import { useMutation, useQuery } from "@tanstack/react-query";

const ContactSupportIndex = () => {
  const { orderId } = useLocalSearchParams();
  const [input, setInput] = useState("");
  const [showRating, setShowRating] = useState(false);

  const finalOrderId = typeof orderId === "string" ? orderId : undefined;

  const {
    data: conversation,
    isPending,
    isError,
    refetch,
  } = useQuery(
    trpc.support.getConversation.queryOptions(
      { orderId: finalOrderId },
      {
        refetchInterval: GET_NEW_MESSAGES_IN_SUPPORT_CHAT,
      },
    ),
  );

  const { mutate: sendMessage } = useMutation(
    trpc.support.sendMessage.mutationOptions({
      onSuccess: async () => {
        await refetch();
      },
    }),
  );

  const { mutate: closeSupportConversation, isPending: isClosing } =
    useMutation(
      trpc.support.closeSupportConversation.mutationOptions({
        onSuccess: async () => {
          await refetch();
          setShowRating(true);
        },
      }),
    );

  const { mutate: rateSupportConversation, isPending: isRating } = useMutation(
    trpc.support.rateSupportConversation.mutationOptions({
      onSuccess: async () => {
        await refetch();
        setShowRating(false);
        router.back();
      },
    }),
  );

  const handleSend = () => {
    if (!input.trim()) return;

    sendMessage({
      orderId: finalOrderId,
      message: input.trim(),
    });

    setInput("");
  };

  const handleCloseChat = () => {
    if (conversation?.id) {
      closeSupportConversation({ conversationId: conversation.id });
    }
  };

  const handleRatingSubmit = (rating: number | null, reviewText?: string) => {
    if (conversation?.id) {
      rateSupportConversation({
        conversationId: conversation.id,
        rating,
        reviewText,
      });
    }
  };

  const handleGoBack = () => {
    router.back();
  };

  // Show rating UI if conversation is closed and not rated
  const shouldShowRating =
    showRating ||
    (conversation && !conversation.isOpen && !conversation.isRated);

  return (
    <SafeAreaView className="flex-1 bg-white">
      {/* Header */}
      <View className="flex w-full flex-row items-center justify-between border-b border-gray-100 px-5 py-4">
        <View className="flex flex-row items-center">
          <TouchableOpacity onPress={handleGoBack} className="pr-4">
            <Feather name="arrow-left" size={24} color="#374151" />
          </TouchableOpacity>
          <Text className="font-inter-semibold text-lg text-black-800">
            {finalOrderId ? "Order Support" : "Contact Support"}
          </Text>
        </View>

        {/* Close Chat Button */}
        {conversation && conversation.isOpen && !conversation.isRated && (
          <TouchableOpacity
            onPress={handleCloseChat}
            disabled={isClosing}
            className={`rounded-full px-4 py-2 ${
              isClosing ? "bg-gray-200" : "bg-red-500"
            }`}
          >
            <Text
              className={`font-inter-medium text-sm ${
                isClosing ? "text-gray-500" : "text-white"
              }`}
            >
              {isClosing ? "Closing..." : "Close Chat"}
            </Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Order Info (if applicable) */}
      {finalOrderId && (
        <View className="border-b border-gray-100 px-5 py-3">
          <View className="flex flex-row items-center">
            <Badge className="mr-3 bg-teal-50 p-2">
              <Feather name="package" size={18} color="#374151" />
            </Badge>
            <View>
              <Text className="font-inter-medium text-sm text-black-800">
                Order ID: {finalOrderId}
              </Text>
              <Text className="font-inter text-xs text-black-500">
                Get help with this specific order
              </Text>
            </View>
          </View>
        </View>
      )}

      <View className="flex-1">
        {isPending ? (
          <View className="flex-1 items-center justify-center px-4 py-2">
            <SkeletonLoading type="list" containerClassName="w-full" />
          </View>
        ) : isError ? (
          <View className="flex-1 items-center justify-center">
            <Error
              title="Error"
              message="Failed to load conversation"
              onRetry={refetch}
            />
          </View>
        ) : (
          <KeyboardAwareScrollView
            contentContainerStyle={{
              flexGrow: 1,
              padding: 16,
            }}
            keyboardShouldPersistTaps="handled"
          >
            {/* Welcome message if no messages */}
            {(!conversation || conversation.messages.length === 0) && (
              <View className="items-center justify-center py-10">
                <View className="mb-4 rounded-full bg-yellow-50 p-4">
                  <Ionicons
                    name="chatbubble-outline"
                    size={32}
                    color="#006666"
                  />
                </View>
                <Text className="font-inter-semibold mb-2 text-center text-lg text-black-800">
                  {finalOrderId ? "Order Support" : "How can we help you?"}
                </Text>
                <Text className="mx-4 mb-8 text-center font-inter text-sm text-black-500">
                  {finalOrderId
                    ? "Send us a message regarding your order and our support team will assist you."
                    : "Our team is here to help! Send your question and we'll get back to you soon."}
                </Text>
              </View>
            )}

            {/* Messages */}
            {conversation && conversation.messages.length > 0 && (
              <FlashList
                data={conversation.messages}
                renderItem={({ item }) => (
                  <View
                    style={{
                      flexDirection: "row",
                      justifyContent:
                        item.senderType === "KABADIWALA"
                          ? "flex-end"
                          : "flex-start",
                      alignItems: "flex-end",
                      marginVertical: 4,
                    }}
                  >
                    <View
                      style={{
                        backgroundColor:
                          item.senderType === "KABADIWALA"
                            ? "#00d3d6"
                            : "#fdf8e7",
                        borderRadius: 24,
                        paddingVertical: 10,
                        paddingHorizontal: 18,
                        maxWidth: "75%",
                      }}
                    >
                      <Text
                        style={{
                          color: "#222",
                          fontSize: 16,
                        }}
                      >
                        {item.content}
                      </Text>
                      <Text
                        style={{
                          color: "#666",
                          fontSize: 12,
                          marginTop: 4,
                        }}
                      >
                        {new Date(item.createdAt).toLocaleTimeString([], {
                          hour: "2-digit",
                          minute: "2-digit",
                        })}
                      </Text>
                    </View>
                  </View>
                )}
                keyExtractor={(item) => item.id}
                showsVerticalScrollIndicator={false}
                contentContainerStyle={{ paddingBottom: 12 }}
                estimatedItemSize={50}
              />
            )}
          </KeyboardAwareScrollView>
        )}
      </View>

      {/* Message input */}
      <View className="border-t border-gray-100 bg-white p-4">
        <View className="flex-row items-center">
          <TextInput
            value={input}
            onChangeText={setInput}
            placeholder={
              finalOrderId ? "Ask about your order..." : "Type your message..."
            }
            className="mr-2 flex-1 rounded-3xl bg-gray-50 px-4 py-3 text-black-800"
            multiline
            maxLength={500}
          />
          <TouchableOpacity
            onPress={handleSend}
            disabled={!input.trim()}
            className={`rounded-full p-3 ${
              !input.trim() ? "bg-gray-100" : "bg-teal-600"
            }`}
          >
            <Ionicons
              name="send"
              size={20}
              color={!input.trim() ? "#9CA3AF" : "#FFFFFF"}
            />
          </TouchableOpacity>
        </View>
      </View>

      <RatingBottomSheet
        isVisible={!!shouldShowRating}
        onClose={() => setShowRating(false)}
        onSubmit={handleRatingSubmit}
        isLoading={isRating}
      />
    </SafeAreaView>
  );
};

export default ContactSupportIndex;
