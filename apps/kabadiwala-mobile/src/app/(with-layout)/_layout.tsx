import { View } from "react-native";
import { Stack } from "expo-router";
import GlobalActiveOrderModal from "@/components/shared/orders/global-active-order-modal";
import LocationUpdator from "@/components/shared/providers/location-updater";

export default function WithLayout() {
  return (
    <>
      <LocationUpdator>
        <Stack
          screenOptions={{ headerShown: false }}
          screenLayout={(props) => (
            <View className="flex-1 bg-red-400">{props.children}</View>
          )}
        >
          <Stack.Screen name="(drawer)" />
        </Stack>
        <GlobalActiveOrderModal />
      </LocationUpdator>
    </>
  );
}
