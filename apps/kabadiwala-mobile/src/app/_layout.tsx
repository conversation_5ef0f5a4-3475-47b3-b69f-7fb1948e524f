import { useEffect } from "react";
import { PermissionsAndroid, Platform } from "react-native";
import { SystemBars } from "react-native-edge-to-edge";
import FlashMessage from "react-native-flash-message";
import { Stack } from "expo-router";
import * as SplashScreen from "expo-splash-screen";
import Providers from "@/components/shared/providers/providers";
import { authClient } from "@/utils/auth";
import {
  Inter_400Regular,
  Inter_500Medium,
  Inter_600SemiBold,
  Inter_700Bold,
  Inter_800ExtraBold,
  useFonts,
} from "@expo-google-fonts/inter";
import {
  PlusJakartaSans_400Regular,
  PlusJakartaSans_500Medium,
  PlusJakartaSans_600SemiBold,
  PlusJakartaSans_700Bold,
  PlusJakartaSans_800ExtraBold,
} from "@expo-google-fonts/plus-jakarta-sans";

import "../styles.css";

export const unstable_settings = {
  initialRouteName: "index",
};

void SplashScreen.preventAutoHideAsync();
SplashScreen.setOptions({
  duration: 500,
  fade: true,
});

export default function RootLayout() {
  const { data } = authClient.useSession();
  const [fontsLoaded] = useFonts({
    Inter_400Regular,
    Inter_500Medium,
    Inter_600SemiBold,
    Inter_700Bold,
    Inter_800ExtraBold,
    PlusJakartaSans_400Regular,
    PlusJakartaSans_500Medium,
    PlusJakartaSans_600SemiBold,
    PlusJakartaSans_700Bold,
    PlusJakartaSans_800ExtraBold,
  });

  if (!fontsLoaded) {
    void SplashScreen.hideAsync();
  }

  // Initialize OneSignal in useEffect to ensure it runs only once
  useEffect(() => {
    // // Enable verbose logging for debugging (remove in production)
    // OneSignal.Debug.setLogLevel(LogLevel.Verbose);
    // // Initialize with your OneSignal App ID
    // OneSignal.initialize("************************************");
    // // Use this method to prompt for push notifications.
    // if (data?.session) {
    //   OneSignal.login(data.session.userId);
    // } else {
    //   OneSignal.logout();
    // }
    // // TODO: We recommend removing this method after testing and instead use In-App Messages to prompt for notification permission.
    // OneSignal.Notifications.requestPermission(true).catch((error) => {
    //   console.error("OneSignal permission request error", error);
    // });
    if (Platform.OS === "android") {
      PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS,
      )
        .then((response) => {
          console.log("GetStream permission granted", response);
        })
        .catch((error) => {
          console.error("GetStream permission request error", error);
        });
    }
    // OneSignal.Notifications.getPermissionAsync()
    //   .then((response) => {
    //     console.log("OneSignal permission granted", response);
    //   })
    //   .catch((error) => {
    //     console.error("OneSignal permission request error", error);
    //   });
  }, []); // Ensure this only runs once on app mount

  return (
    <Providers>
      <Stack screenOptions={{ headerShown: false }}>
        <Stack.Screen name="index" />
        <Stack.Screen name="(without-layout)" />
        <Stack.Protected guard={!!data?.session}>
          <Stack.Screen name="(with-layout)" />
        </Stack.Protected>
      </Stack>
      <FlashMessage position="top" />
      <SystemBars />
    </Providers>
  );
}
