import React, { useEffect, useRef } from "react";
import { Animated, View } from "react-native";
import { showMessage } from "react-native-flash-message";
import { SafeAreaView } from "react-native-safe-area-context";
import { router } from "expo-router";
import { trpc } from "@/utils/api";
import { authClient } from "@/utils/auth";
import AppIcon from "@assets/app-icon.png";
import { useQuery } from "@tanstack/react-query";

const RootIndex = () => {
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const {
    data: session,
    isPending: isSessionPending,
    error: sessionError,
  } = authClient.useSession();
  const {
    data: appStartData,
    isLoading,
    isError,
  } = useQuery(
    trpc.utils.getAppStartRequiredData.queryOptions(undefined, {
      enabled: session?.user ? true : false,
    }),
  );

  useEffect(() => {
    // Start pulse animation
    const pulseAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.3,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 0.8,
          duration: 800,
          useNativeDriver: true,
        }),
      ]),
    );

    pulseAnimation.start();

    return () => pulseAnimation.stop();
  }, []);

  useEffect(() => {
    if (isSessionPending || isLoading) return;

    if (!session?.user) {
      router.replace("/(without-layout)");
      return;
    }

    if (isError || !appStartData) {
      router.replace("/(without-layout)");
      showMessage({
        message: "Something went wrong. Please try again later.",
        type: "danger",
      });
      return;
    }

    if (!appStartData.onBoardingCompleted) {
      router.replace(`/onboarding/${appStartData.lastStepOnBoardingLeftAt}`);
      return;
    }

    if (appStartData.isBlocked) {
      void authClient.signOut();
      showMessage({
        message: "Your account is blocked. Please contact support.",
        type: "danger",
      });
      return;
    }

    if (!appStartData.accountActive) {
      router.replace("/under-verification");
      return;
    }

    // All conditions passed, redirect to home
    router.replace("/home");
  }, [session?.user, appStartData, isSessionPending, isLoading, isError]);

  return (
    <SafeAreaView className="flex-1">
      <View className="flex-1 items-center justify-center bg-white px-8">
        <Animated.Image
          source={AppIcon}
          className="object-contain"
          style={{
            width: 200,
            height: 200,
            transform: [{ scale: pulseAnim }],
          }}
        />
      </View>
    </SafeAreaView>
  );
};

export default RootIndex;
