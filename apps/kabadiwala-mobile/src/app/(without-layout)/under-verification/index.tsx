import React from "react";
import { Text, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { MaterialIcons } from "@expo/vector-icons";

const UnderVerificationIndex = () => {
  return (
    <SafeAreaView className="flex-1 bg-black-00">
      <View className="flex-1 items-center justify-center px-8">
        {/* Icon Container */}
        <View className="mb-8 rounded-full bg-teal-50 p-8">
          <View className="rounded-full bg-teal-600 p-6">
            <MaterialIcons name="verified-user" size={64} color="#00FFFF" />
          </View>
        </View>

        {/* Title */}
        <Text className="font-inter-bold mb-4 text-center text-3xl text-black-900">
          Profile Under Review
        </Text>

        {/* Subtitle */}
        <Text className="font-inter-semibold mb-6 text-center text-lg text-teal-700">
          Verification in Progress
        </Text>

        {/* Description */}
        <Text className="font-inter-regular mb-8 text-center text-base leading-6 text-black-600">
          Your profile is currently under review by our admin team. Once your
          account is verified, you will be able to access all application
          features.
        </Text>

        {/* Status Indicator */}
        <View className="flex-row items-center rounded-xl border border-yellow-200 bg-yellow-50 px-6 py-4">
          <View className="mr-3 rounded-full bg-yellow-500 p-2">
            <MaterialIcons name="hourglass-empty" size={20} color="#FBCF04" />
          </View>
          <View className="flex-1">
            <Text className="font-inter-semibold text-sm text-yellow-800">
              Estimated Review Time
            </Text>
            <Text className="font-inter-regular text-xs text-yellow-700">
              24-48 hours
            </Text>
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default UnderVerificationIndex;
