import { Image, Text, View } from "react-native";
import { Link, useLocalSearchParams } from "expo-router";
import { But<PERSON> } from "@/components/ui/button";
import { HOW_IT_WORKS_STEPS } from "@/lib/constant";
import { handleBack } from "@/utils/functions";
import Logo from "@assets/logo/scraplo.png";
import Ionicons from "@expo/vector-icons/Ionicons";

const HowItWorksIndex = () => {
  const { step } = useLocalSearchParams();
  const stepData = HOW_IT_WORKS_STEPS[step as keyof typeof HOW_IT_WORKS_STEPS];

  return (
    <View className="flex-1 flex-col gap-5 bg-white">
      <View className="px-6">
        <Image source={Logo} className="h-[57px] w-[107px]" />
      </View>

      <View className="h-[300px] flex-row items-center justify-center">
        <Image
          source={stepData.image}
          alt={stepData.imageAlt}
          className="h-full w-full"
        />
      </View>

      <View className="flex flex-1 flex-col justify-end gap-10 bg-[#FFFAE6B2] px-6 pb-10">
        <View>
          <Text>Step Indicator</Text>
        </View>

        <View className="flex flex-col gap-6">
          <Text className="font-jakarta-extrabold text-xl font-extrabold leading-8 text-black-900">
            {stepData.title}
          </Text>
          <View>
            <Text className="font-inter-bold pb-2 font-bold text-teal-900">
              {stepData.subTitle}
            </Text>
            <Text className="font-inter-normal text-sm leading-[22px] -tracking-[0.14px] text-black-700">
              {stepData.description}
            </Text>

            {stepData.subDescription && (
              <Text className="font-inter-semibold pt-4 text-xs font-semibold -tracking-[0.12px] text-teal-800">
                {stepData.subDescription}
              </Text>
            )}
          </View>
        </View>

        <View className="flex w-full flex-row gap-6">
          <Button className="bg-black-50" onPress={handleBack}>
            <Ionicons name="arrow-back" size={20} color="black" />
          </Button>

          <Link href={stepData.buttonLink} asChild>
            <Button className="flex-1">
              <Text>{stepData.buttonText}</Text>
            </Button>
          </Link>
        </View>
      </View>
    </View>
  );
};

export default HowItWorksIndex;
