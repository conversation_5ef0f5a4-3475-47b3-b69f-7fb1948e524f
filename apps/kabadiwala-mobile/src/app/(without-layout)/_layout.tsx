import React from "react";
import { View } from "react-native";
import { Stack } from "expo-router";

const WithoutLayout = () => {
  return (
    <Stack
      screenLayout={(props) => (
        <View className="flex-1 bg-white">{props.children}</View>
      )}
      screenOptions={{ headerShown: false }}
    >
      <Stack.Screen name="index" />
      <Stack.Screen name="auth" />
      <Stack.Screen name="how-it-works" />
      <Stack.Screen name="success" />
      <Stack.Screen name="under-verification/index" />
    </Stack>
  );
};

export default WithoutLayout;
