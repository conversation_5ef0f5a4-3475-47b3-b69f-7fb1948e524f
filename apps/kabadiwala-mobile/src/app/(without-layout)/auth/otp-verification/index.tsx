import React from "react";
import { View } from "react-native";
import { KeyboardAwareScrollView } from "react-native-keyboard-controller";
import { Redirect, useLocalSearchParams } from "expo-router";
import FormTitleDescription from "@/components/shared/auth/form-title-description";
import OtpVerificationForm from "@/components/shared/auth/otp-verification-form";

const OtpVerificationIndex = () => {
  const { phoneNumber } = useLocalSearchParams();

  if (!phoneNumber) {
    return <Redirect href="/(without-layout)/auth/login" />;
  }

  return (
    <View className="flex-1 bg-white">
      <KeyboardAwareScrollView
        className="flex-1"
        contentContainerClassName="flex-grow justify-center gap-[70px]"
        showsVerticalScrollIndicator={false}
      >
        <FormTitleDescription
          title="Enter Sent Code"
          description={`We have sent a 6-digit code on ${phoneNumber.toString()} to verify`}
        />

        <OtpVerificationForm phoneNumber={phoneNumber.toString()} />
      </KeyboardAwareScrollView>
    </View>
  );
};

export default OtpVerificationIndex;
