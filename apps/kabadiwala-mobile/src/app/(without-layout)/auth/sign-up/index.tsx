import React from "react";
import { View } from "react-native";
import { KeyboardAwareScrollView } from "react-native-keyboard-controller";
import FormTitleDescription from "@/components/shared/auth/form-title-description";
import SignUpForm from "@/components/shared/auth/sign-up-form";

const SignUpIndex = () => {
  return (
    <View className="flex-1 bg-white">
      <KeyboardAwareScrollView
        className="flex-1"
        contentContainerClassName="flex-grow justify-center gap-[70px]"
        showsVerticalScrollIndicator={false}
      >
        {/* title & description */}
        <FormTitleDescription
          title="Get started with Scraplo"
          description="Enter your registered phone number below to continue with scraplo"
        />

        <SignUpForm />
      </KeyboardAwareScrollView>
    </View>
  );
};

export default SignUpIndex;
