import React from "react";
import { SafeAreaView } from "react-native-safe-area-context";
import { Stack } from "expo-router";
import AuthFooter from "@/components/shared/auth/auth-footer";
import AuthHeader from "@/components/shared/auth/auth-header";

const SignUpLayout = () => {
  return (
    <SafeAreaView className="flex-1 justify-between bg-white px-6 pb-4">
      <AuthHeader />
      <Stack>
        <Stack.Screen name="index" options={{ headerShown: false }} />
      </Stack>
      <AuthFooter />
    </SafeAreaView>
  );
};

export default SignUpLayout;
