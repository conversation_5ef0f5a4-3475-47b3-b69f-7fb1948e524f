import React from "react";
import { View } from "react-native";
import { KeyboardAwareScrollView } from "react-native-keyboard-controller";
import FormTitleDescription from "@/components/shared/auth/form-title-description";
import LoginForm from "@/components/shared/auth/login-form";

const LoginScreen = () => {
  return (
    <View className="flex-1 bg-white">
      <KeyboardAwareScrollView
        className="flex-1"
        contentContainerClassName="flex-grow justify-center gap-[70px]"
        showsVerticalScrollIndicator={false}
      >
        <FormTitleDescription
          title="Welcome, Back"
          description="Enter your registered phone number below to continue with scraplo"
        />

        <LoginForm />
      </KeyboardAwareScrollView>
    </View>
  );
};

export default LoginScreen;
