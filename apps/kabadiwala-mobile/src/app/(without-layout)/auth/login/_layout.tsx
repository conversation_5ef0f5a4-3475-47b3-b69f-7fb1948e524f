import React from "react";
import { SafeAreaView } from "react-native-safe-area-context";
import { Stack } from "expo-router";
import AuthHeader from "@/components/shared/auth/auth-header";

const LoginLayout = () => {
  return (
    <SafeAreaView className="flex-1 justify-between bg-white px-6 pb-4">
      <AuthHeader />
      <Stack
        screenOptions={{
          headerShown: false,
        }}
      >
        <Stack.Screen name="index" />
      </Stack>
    </SafeAreaView>
  );
};

export default LoginLayout;
