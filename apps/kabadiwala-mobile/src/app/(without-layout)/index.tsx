import React from "react";
import { ImageBackground, Text, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Link } from "expo-router";
import { Button } from "@/components/ui/button";
import Logo from "@assets/logo/scraplo.png";

const WithoutLayoutIndex = () => {
  return (
    <SafeAreaView className="relative flex flex-1 flex-shrink-0 flex-col items-center gap-y-6 rounded-[32px] bg-white px-6 pb-10 pt-6">
      <View className="flex-basis-0 relative flex flex-shrink flex-grow flex-col items-start gap-y-0 self-stretch rounded-[10px] bg-black-0" />
      <View className="relative flex flex-shrink-0 flex-col items-start gap-y-[56px] self-stretch">
        <View className="relative flex flex-shrink-0 flex-col items-center gap-y-8 self-stretch">
          <ImageBackground
            className="relative h-[57px] w-[108px] flex-shrink-0"
            source={Logo}
          />
          <View className="relative flex flex-shrink-0 flex-col items-start gap-y-3 self-stretch">
            <Text className="font-jakarta-semibold relative flex-shrink-0 self-stretch text-center text-xl leading-8 -tracking-[0.15px] text-black-800">
              Welcome to Scraplo - Your Partner in Scrap Collection
            </Text>
            <Text className="font-inter-medium relative flex-shrink-0 self-stretch text-center text-sm leading-5 tracking-[0.42px] text-black-500">
              Earn consistently, work on your own terms
            </Text>
          </View>
        </View>
        <View className="flex flex-col gap-5 self-stretch">
          <Link href="/(without-layout)/auth/sign-up" asChild>
            <Button className="w-full">
              <Text className="font-jakarta-medium relative flex-shrink-0 text-left text-sm leading-5 tracking-[0.14px] text-black-900">
                Start Earning Now
              </Text>
            </Button>
          </Link>

          <Link href="/(without-layout)/auth/login" asChild>
            <Button className="font-jakarta-medium relative w-full flex-shrink-0 bg-yellow-150 text-left text-sm leading-5 tracking-[0.14px] text-black-900">
              <Text>Login</Text>
            </Button>
          </Link>

          <Link
            href={{
              pathname: "/how-it-works/[step]",
              params: { step: "1" },
            }}
            className="text-center"
          >
            <Text className="font-jakarta-semibold relative flex-shrink-0 text-[13px] leading-[18px] tracking-[0.12px] text-teal-850">
              Check how it works
            </Text>
          </Link>
        </View>
      </View>
    </SafeAreaView>
  );
};

export default WithoutLayoutIndex;
