import React, { useEffect } from "react";
import { Image, Text, View } from "react-native";
import { router } from "expo-router";
import { authClient } from "@/utils/auth";
import CheckImage from "@assets/icons/check.png";
import Logo from "@assets/logo/scraplo.png";

const OtpVerificationSuccessIndex = () => {
  const { data, error } = authClient.useSession();

  useEffect(() => {
    const redirectTimer = setTimeout(() => {
      if (data?.session) {
        if (!data.user.onboardingCompleted) {
          router.dismissTo("/onboarding");
        }

        router.dismissTo("/home");
      }
    }, 2000);

    return () => clearTimeout(redirectTimer);
  }, [data]);

  if (error) {
    return router.replace("/(without-layout)/auth/login");
  }

  return (
    <View className="relative flex flex-1 shrink-0 flex-col items-start justify-between gap-y-[100px] bg-white px-6 pb-10 pt-[30px]">
      <View className="relative flex shrink-0 items-center justify-between gap-x-[69px] self-stretch">
        <Image className="h-[46px] w-[87px] shrink-0" source={Logo} />
      </View>

      <View className="flex flex-1 shrink-0 flex-col items-center gap-y-[70px] self-stretch">
        <Image source={CheckImage} className="flex h-[194px] w-[194px]" />

        <View className="relative flex shrink-0 flex-col items-center gap-y-2 self-stretch">
          <Text className="font-jakarta-bold relative shrink-0 text-center text-[22px] leading-[34px] tracking-[-0.165px] text-teal-950">
            Verified phone number
          </Text>
          <Text className="font-inter-regular relative shrink-0 self-stretch text-center text-[15px] leading-6 text-black-600">
            Hey {data?.user.name}! Your number is all set. Let&apos;s keep
            moving forward with{" "}
            <Text className="font-inter-semibold text-center text-[15px] leading-6 text-black-600">
              Scraplo!
            </Text>
          </Text>
        </View>
      </View>
    </View>
  );
};

export default OtpVerificationSuccessIndex;
