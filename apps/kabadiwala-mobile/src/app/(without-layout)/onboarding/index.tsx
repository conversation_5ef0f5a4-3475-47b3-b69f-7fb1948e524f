import React from "react";
import { View } from "react-native";
import { Redirect } from "expo-router";
import { trpc } from "@/utils/api";
import AntDesign from "@expo/vector-icons/AntDesign";
import { useQuery } from "@tanstack/react-query";

const OnboardingIndex = () => {
  const { data, isPending, isError } = useQuery(
    trpc.onboarding.getOnboardingAndActiveAccDetails.queryOptions(),
  );

  if (isPending) {
    return (
      <View className="flex flex-1 items-center justify-center bg-white">
        <AntDesign
          name="loading1"
          size={24}
          color="black"
          className="animate-spin"
        />
      </View>
    );
  }

  if (!data || isError) {
    return <Redirect href="/auth/login" />;
  }

  if (data.onBoardingCompleted) {
    return <Redirect href="/success" />;
  }

  return <Redirect href={`/onboarding/${data.lastStepOnBoardingLeftAt}`} />;
};

export default OnboardingIndex;
