import React from "react";
import { SafeAreaView } from "react-native-safe-area-context";
import { Stack } from "expo-router";
import { authClient } from "@/utils/auth";

const OnboardingLayout = () => {
  const { data: sessionData } = authClient.useSession();

  return (
    <SafeAreaView className="flex-1 bg-white px-6">
      <Stack screenOptions={{ headerShown: false }}>
        <Stack.Protected guard={!!sessionData?.session}>
          <Stack.Screen name="index" />
          <Stack.Screen name="success/index" />
        </Stack.Protected>
      </Stack>
    </SafeAreaView>
  );
};

export default OnboardingLayout;
