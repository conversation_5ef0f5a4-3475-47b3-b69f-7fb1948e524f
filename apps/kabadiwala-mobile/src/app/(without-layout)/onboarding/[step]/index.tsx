import React, { useCallback, useEffect } from "react";
import { BackHandler, Text, View } from "react-native";
import { KeyboardAwareScrollView } from "react-native-keyboard-controller";
import { router, useLocalSearchParams } from "expo-router";
import FormTitleDescription from "@/components/shared/onboarding/form-title-description";
import { Button } from "@/components/ui/button";
import { ONBOARDING_STEPS_DATA } from "@/lib/constant";
import Ionicons from "@expo/vector-icons/Ionicons";

const OnboardingStepIndex = () => {
  const {
    step,
    fullName,
  }: { step: keyof typeof ONBOARDING_STEPS_DATA; fullName?: string } =
    useLocalSearchParams();

  const stepData = ONBOARDING_STEPS_DATA[step];

  const handleBack = useCallback(() => {
    const keys = Object.keys(ONBOARDING_STEPS_DATA);
    const index = keys.indexOf(step);
    const prevStep = keys[index - 1];
    prevStep && router.replace(`/onboarding/${prevStep}`);

    return true;
  }, [step]);

  useEffect(() => {
    const subscription = BackHandler.addEventListener(
      "hardwareBackPress",
      handleBack,
    );

    return () => {
      subscription.remove();
    };
  }, [handleBack]);

  return (
    <View className="flex-1 bg-white">
      <View className="flex-row items-center justify-between">
        <Button className="bg-black-50" onPress={handleBack}>
          <Ionicons name="arrow-back" size={20} color="black" />
        </Button>

        <Text className="">Step {step?.split("_")[1]}</Text>
      </View>

      <KeyboardAwareScrollView
        className="flex-1"
        contentContainerClassName="flex-grow justify-center gap-16"
        showsVerticalScrollIndicator={false}
      >
        <FormTitleDescription
          title={stepData.title}
          description={stepData.description}
          fullName={fullName}
        />

        {<stepData.form />}
      </KeyboardAwareScrollView>
    </View>
  );
};

export default OnboardingStepIndex;
