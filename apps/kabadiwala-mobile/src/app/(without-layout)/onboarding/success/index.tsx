import React from "react";
import { Image, Text, View } from "react-native";
import { router } from "expo-router";
import { Button } from "@/components/ui/button";
import { trpc } from "@/utils/api";
import CheckImage from "@assets/icons/check.png";
import Logo from "@assets/logo/scraplo.png";
import { useQuery } from "@tanstack/react-query";

const OnboardingSuccessIndex = () => {
  const { data, isError } = useQuery(
    trpc.onboarding.getOnboardingAndActiveAccDetails.queryOptions(),
  );

  const handleNext = () => {
    if (!data) return;

    if (data.onBoardingCompleted) {
      router.replace("/home");
    } else if (!data.isActive) {
      router.replace("/(without-layout)/under-verification");
    } else {
      router.replace("/(without-layout)/onboarding");
    }
  };

  if (isError) {
    return router.replace("/(without-layout)/auth/login");
  }

  return (
    <View className="relative flex flex-1 shrink-0 flex-col items-start justify-between gap-y-[100px] bg-white px-6 pb-10 pt-[30px]">
      <View className="relative flex shrink-0 items-center justify-between gap-x-[69px] self-stretch">
        <Image className="h-[46px] w-[87px] shrink-0" source={Logo} />
      </View>

      <View className="flex flex-1 shrink-0 flex-col items-center gap-y-[70px] self-stretch">
        <Image source={CheckImage} className="flex h-[194px] w-[194px]" />

        <View className="relative flex shrink-0 flex-col items-center gap-6 self-stretch">
          <View className="relative flex shrink-0 flex-col items-center gap-y-3 self-stretch">
            <View>
              <Text className="font-jakarta-bold relative shrink-0 text-center text-[22px] leading-[34px] tracking-[-0.165px] text-teal-950">
                Thanks!
              </Text>
              <Text className="font-jakarta-bold relative shrink-0 text-center text-[22px] leading-[34px] tracking-[-0.165px] text-teal-950">
                Your profile is under review
              </Text>
            </View>

            <View className="rounded-lg border border-black-50 bg-black-0 px-3 py-4">
              <Text className="font-inter-regular relative shrink-0 self-stretch text-center text-[15px] leading-6 text-black-600">
                We've received your details and documents. Our team is verifying
                everything to ensure a secure platform for everyone. This
                usually takes{" "}
                <Text className="font-inter-semibold text-center text-[15px] leading-6 text-black-600">
                  24-48 hours
                </Text>
              </Text>
            </View>
          </View>

          <Button
            className="w-full rounded-xl bg-teal-900"
            onPress={handleNext}
          >
            <Text className="text-black-0">Okay, Got It</Text>
          </Button>
        </View>
      </View>
    </View>
  );
};

export default OnboardingSuccessIndex;
