import { ExpoConfig } from "@expo/config-types";

import { ClientEnv, Env } from "./env";

export default ({ config }: { config: ExpoConfig }): ExpoConfig => ({
  ...config,
  name: Env.NAME,
  slug: "scraplo-kabadiwala",
  scheme: Env.SCHEME,
  version: Env.VERSION.toString(),
  orientation: "portrait",
  userInterfaceStyle: "light",
  icon: "./assets/app-icon.png",
  newArchEnabled: true,
  updates: {
    fallbackToCacheTimeout: 0,
  },
  assetBundlePatterns: ["**/*"],
  ios: {
    bundleIdentifier: Env.BUNDLE_ID,
    supportsTablet: false,
    icon: {
      light: "./assets/app-icon.png",
      dark: "./assets/app-icon.png",
      // tinted: "",
    },
    config: {
      googleMapsApiKey: "AIzaSyAORVJvt3XFhBe2BqBD34ZZZbbPyG3aSLA",
    },
    infoPlist: {
      UIBackgroundModes: ["remote-notification"],
      ITSAppUsesNonExemptEncryption: false,
    },
    googleServicesFile: "./GoogleService-Info.plist",
    entitlements: {
      "aps-environment": "production",
    },
  },
  android: {
    edgeToEdgeEnabled: true,
    package: Env.PACKAGE,
    adaptiveIcon: {
      foregroundImage: "./assets/app-icon.png",
      backgroundColor: "#FFFFFF",
    },
    config: {
      googleMaps: {
        apiKey: "AIzaSyAORVJvt3XFhBe2BqBD34ZZZbbPyG3aSLA",
      },
    },
    permissions: [
      "READ_EXTERNAL_STORAGE",
      "WRITE_EXTERNAL_STORAGE",
      "VIBRATE",
      "INTERNET",
    ],
    googleServicesFile: "./google-services.json",
  },
  // extra: {
  //   eas: {
  //     projectId: "your-eas-project-id",
  //   },
  // },
  experiments: {
    tsconfigPaths: true,
    typedRoutes: true,
  },
  plugins: [
    "expo-localization",
    "expo-router",
    "expo-secure-store",
    "expo-web-browser",
    "expo-video",
    [
      "expo-image-picker",
      {
        photosPermission: "Allow Scraplo to access your photos.",
        cameraPermission: "Allow Scraplo to access your camera.",
      },
    ],
    [
      "expo-splash-screen",
      {
        resizeMode: "cover",
        backgroundColor: "#fff",
        imageWidth: 200,
        image: "./assets/app-icon.png",
      },
    ],
    "expo-font",
    [
      "expo-location",
      {
        locationAlwaysAndWhenInUsePermission:
          "Allow Scraplo to use your location.",
        isAndroidBackgroundLocationEnabled: true,
        isIosBackgroundLocationEnabled: true,
      },
    ],
    [
      "react-native-maps",
      {
        iosGoogleMapsApiKey: "AIzaSyAORVJvt3XFhBe2BqBD34ZZZbbPyG3aSLA",
        androidGoogleMapsApiKey: "AIzaSyAORVJvt3XFhBe2BqBD34ZZZbbPyG3aSLA",
      },
    ],
    [
      "expo-audio",
      {
        microphonePermission: "Allow Scraplo to access your microphone.",
      },
    ],
    [
      "expo-web-browser",
      {
        experimentalLauncherActivity: true,
      },
    ],
    [
      "@stream-io/video-react-native-sdk",
      {
        ringingPushNotifications: {
          disableVideoIos: false,
          includesCallsInRecentsIos: false,
          showWhenLockedAndroid: true,
        },
        androidKeepCallAlive: true,
      },
    ],
    "@config-plugins/react-native-callkeep",
    [
      "@config-plugins/react-native-webrtc",
      {
        cameraPermission:
          "$(PRODUCT_NAME) requires camera access in order to capture and transmit video",
        microphonePermission:
          "$(PRODUCT_NAME) requires microphone access in order to capture and transmit audio",
      },
    ],
    "@react-native-firebase/app",
    "@react-native-firebase/messaging",
    [
      "expo-build-properties",
      {
        android: {
          minSdkVersion: 24,
          // targetSdkVersion: 36,
          extraMavenRepos: [
            "$rootDir/../../../../../node_modules/@notifee/react-native/android/libs",
          ],
        },
        ios: {
          useFrameworks: "static",
        },
      },
    ],
  ],
  extra: {
    ...ClientEnv,
    eas: {
      projectId: Env.EAS_PROJECT_ID,
    },
  },
});
