{"extends": ["@acme/tsconfig/base.json"], "compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["./src/*"], "@env": ["./src/lib/env.js"], "@assets/*": ["./assets/*"]}, "jsx": "react-native", "checkJs": false, "esModuleInterop": true, "moduleSuffixes": [".ios", ".android", ".native", ""]}, "include": ["src", "*.ts", "*.js", ".expo/types/**/*.ts", "expo-env.d.ts", "nativewind-env.d.ts", "declarations.d.ts"], "exclude": ["node_modules"]}