{"cli": {"version": ">= 4.1.2", "appVersionSource": "remote"}, "build": {"base": {"node": "22.12.0", "pnpm": "9.15.4", "ios": {"resourceClass": "m-medium"}}, "development": {"extends": "base", "developmentClient": true, "distribution": "internal", "android": {"image": "ubuntu-18.04-jdk-11-ndk-r19c"}}, "preview": {"extends": "base", "distribution": "internal", "ios": {"simulator": true}}, "production": {"extends": "base"}}, "submit": {"production": {}}}