{"name": "scraplo-kabadiwala-mobile", "version": "0.0.1", "private": true, "main": "index.ts", "scripts": {"android": "expo run:android", "clean": "git clean -xdf .cache .expo .turbo android ios node_modules", "dev": "expo start", "dev:android": "expo start --android", "dev:ios": "expo start --ios", "format": "prettier --check . --ignore-path ../../.gitignore --ignore-path .prettierignore", "ios": "expo run:ios", "lint": "eslint --fix", "prebuild": "expo prebuild", "typecheck": "tsc --noEmit", "build:development:ios": "APP_ENV=development EXPO_NO_DOTENV=1 eas build --profile development --platform ios", "build:development:android": "APP_ENV=development EXPO_NO_DOTENV=1 eas build --profile development --platform android ", "build:preview:ios": "APP_ENV=preview EXPO_NO_DOTENV=1 eas build --profile preview --platform ios", "build:preview:android": "APP_ENV=preview EXPO_NO_DOTENV=1 eas build --profile preview --platform android ", "build:production:ios": "APP_ENV=production EXPO_NO_DOTENV=1 eas build --profile production --platform ios", "build:production:android": "APP_ENV=production EXPO_NO_DOTENV=1 eas build --profile production --platform android ", "app-release": "SKIP_BRANCH_PROTECTION=true np --no-publish --no-cleanup --no-release-draft"}, "prettier": "@acme/prettier-config", "dependencies": {"@acme/ui": "workspace:*", "@acme/validators": "workspace:*", "@better-auth/expo": "1.2.10", "@config-plugins/react-native-callkeep": "^11.0.0", "@config-plugins/react-native-webrtc": "^12.0.0", "@expo-google-fonts/inter": "^0.3.0", "@expo-google-fonts/plus-jakarta-sans": "^0.3.0", "@expo/metro-config": "^0.20.3", "@expo/vector-icons": "^14.1.0", "@gorhom/bottom-sheet": "^5.1.4", "@notifee/react-native": "^9.1.8", "@react-native-async-storage/async-storage": "1.24.0", "@react-native-clipboard/clipboard": "^1.13.2", "@react-native-community/datetimepicker": "8.4.1", "@react-native-community/netinfo": "11.4.1", "@react-native-community/slider": "4.5.6", "@react-native-firebase/app": "^22.4.0", "@react-native-firebase/messaging": "^22.4.0", "@react-native/metro-babel-transformer": "^0.79.0", "@react-navigation/drawer": "^7.5.2", "@react-navigation/native-stack": "^7.3.10", "@shopify/flash-list": "1.7.6", "@stream-io/react-native-webrtc": "^125.3.1", "@stream-io/video-react-native-sdk": "^1.18.0", "@tanstack/react-query": "catalog:", "@trpc/client": "catalog:", "@trpc/server": "catalog:", "@trpc/tanstack-react-query": "catalog:", "@uploadthing/expo": "^7.2.4", "babel-plugin-module-resolver": "^5.0.2", "better-auth": "^1.2.10", "date-fns": "catalog:", "dayjs": "^1.11.13", "dotenv": "latest", "expo": "53.0.19", "expo-application": "~6.1.5", "expo-audio": "~0.4.8", "expo-blur": "~14.1.5", "expo-build-properties": "~0.14.8", "expo-constants": "17.1.7", "expo-dev-client": "5.2.4", "expo-device": "~7.1.4", "expo-document-picker": "~13.1.5", "expo-file-system": "~18.1.11", "expo-image": "~2.3.2", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "14.1.5", "expo-linking": "7.1.7", "expo-localization": "~16.1.6", "expo-location": "~18.1.6", "expo-navigation-bar": "^4.2.7", "expo-router": "5.1.3", "expo-secure-store": "14.2.3", "expo-splash-screen": "0.30.10", "expo-status-bar": "2.2.3", "expo-system-ui": "5.0.10", "expo-video": "~2.2.1", "expo-video-thumbnails": "~9.1.0", "expo-web-browser": "14.2.0", "moti": "^0.30.0", "nativewind": "~4.1.23", "posthog-react-native": "^4.1.2", "query-string": "7.1.3", "react": "catalog:react19", "react-dom": "catalog:react19", "react-hook-form": "^7.54.2", "react-native": "0.79.5", "react-native-callkeep": "^4.3.16", "react-native-css-interop": "~0.1.22", "react-native-device-info": "^14.0.4", "react-native-edge-to-edge": "^1.5.1", "react-native-flash-message": "^0.4.2", "react-native-gesture-handler": "~2.24.0", "react-native-incall-manager": "^4.2.1", "react-native-keyboard-controller": "^1.17.1", "react-native-maps": "1.23.8", "react-native-mmkv": "^3.3.0", "react-native-otp-entry": "^1.8.4", "react-native-razorpay": "^2.3.0", "react-native-reanimated": "~3.17.5", "react-native-safe-area-context": "~5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "15.11.2", "react-native-video": "^6.15.0", "react-native-voip-push-notification": "^3.3.3", "react-native-webview": "13.13.5", "superjson": "2.2.2", "tailwind-variants": "^1.0.0", "use-debounce": "^10.0.1", "zod": "catalog:", "zustand": "latest"}, "devDependencies": {"@acme/db": "workspace:*", "@acme/eslint-config": "workspace:*", "@acme/kabadiwala-api": "workspace:*", "@acme/kabadiwala-auth": "workspace:*", "@acme/prettier-config": "workspace:*", "@acme/tailwind-config": "workspace:*", "@acme/tsconfig": "workspace:*", "@babel/core": "^7.26.10", "@babel/preset-env": "^7.26.9", "@babel/runtime": "^7.26.10", "@expo/config-types": "^53.0.4", "@types/babel__core": "^7.20.5", "@types/react": "catalog:react19", "@types/react-native-razorpay": "^2.2.6", "eslint": "catalog:", "prettier": "catalog:", "react-native-svg-transformer": "^1.5.0", "tailwindcss": "^3.4.15", "typescript": "catalog:"}}