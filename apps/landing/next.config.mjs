/** @type {import('next').NextConfig} */
const nextConfig = {
  transpilePackages: ["@acme/ui", "@acme/db"],
  eslint: {
    ignoreDuringBuilds: true,
  },
  //   typescript: {
  //     ignoreBuildErrors: true,
  //   },
  //   images: {
  //     unoptimized: true,
  //   },
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "images.unsplash.com",
        port: "",
      },
      {
        hostname: "*.ufs.sh",
        protocol: "https",
      },
    ],
  },
};

export default nextConfig;
