import { Handshake, Recycle, Scale, User, Warehouse } from "lucide-react";

import { Badge } from "@acme/ui/components/ui/badge";

const GetStarted = () => {
  const circularEconomySteps = [
    {
      step: "01",
      icon: User,
      title: "Seller Initiates",
      description:
        "Use Scraplo Connect app to select scrap categories and schedule pickup",
      color: "bg-blue-600",
      details:
        "Choose from metal, paper/cardboard, plastic, or mixed waste. Get live pricing per kg.",
    },
    {
      step: "02",
      icon: Handshake,
      title: "Connect to Pickup Partner",
      description: "GPS-enabled matching with nearest certified pickup agent",
      color: "bg-teal-700",
      details:
        "App connects you to trained pickup partner with real-time tracking",
    },
    {
      step: "03",
      icon: Scale,
      title: "Weighing & Payment",
      description: "Standard scale weighing with instant payment to seller",
      color: "bg-orange-500",
      details:
        "Scrap weighed in front of you, payment processed through app immediately",
    },
    {
      step: "04",
      icon: Warehouse,
      title: "Hub Processing",
      description: "Scrap transported to hub for segregation and warehousing",
      color: "bg-purple-600",
      details: "Hub partners segregate materials and prepare for recycling",
    },
    {
      step: "05",
      icon: Handshake,
      title: "Connect to Recycler",
      description: "Hub connects with certified recycling partners",
      color: "bg-green-600",
      details: "Materials sent to certified recyclers for processing",
    },
    {
      step: "06",
      icon: Recycle,
      title: "End Use - Circular Economy",
      description: "Materials recycled into new products, completing the cycle",
      color: "bg-emerald-600",
      details:
        "Recycled materials become new products, reducing waste and environmental impact",
    },
  ];

  return (
    <div
      id="how-it-works"
      className="container mx-auto flex min-w-full flex-col gap-10 bg-[#fbfbfb] py-10 lg:py-12 xl:gap-20 xl:py-[70px]"
    >
      <div className="flex flex-col items-center justify-center gap-2 text-center">
        <Badge className="w-fit rounded-full border-[1.4px] border-teal-700 bg-transparent">
          DAY IN LIFE - CIRCULAR ECONOMY
        </Badge>
        <h2 className="font-jakarta text-2xl font-semibold leading-normal text-black-700 sm:text-3xl md:text-4xl md:leading-tight lg:text-[36px] lg:leading-[56px] xl:text-[40px]">
          Complete{" "}
          <span className="font-extrabold text-teal-800">6-Step Process</span>{" "}
          from Seller to Recycler
        </h2>
        <p className="mt-4 max-w-3xl text-lg text-gray-600">
          Our transparent, traceable network connects everyone in the scrap
          recycling chain, creating a true circular economy where nothing goes
          to waste.
        </p>
      </div>

      {/* Circular Economy Flow */}
      <div className="mx-auto grid max-w-7xl grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
        {circularEconomySteps.map((step, index) => (
          <div key={index} className="relative">
            {/* Connection Line */}
            {/* {index < circularEconomySteps.length - 1 && (
              <div className="absolute -right-4 top-8 z-0 hidden h-0.5 w-8 bg-gray-300 lg:block"></div>
            )} */}

            <div className="relative z-10 rounded-xl border border-gray-200 bg-white p-6 shadow-sm transition-shadow duration-300 hover:shadow-lg">
              {/* Step Number */}
              <div className="absolute -left-4 -top-4 flex h-8 w-8 items-center justify-center rounded-full bg-teal-800 text-sm font-bold text-white">
                {step.step}
              </div>

              {/* Icon */}
              <div
                className={`h-16 w-16 ${step.color} mx-auto mb-4 flex items-center justify-center rounded-xl`}
              >
                <step.icon color="white" size={32} />
              </div>

              {/* Content */}
              <div className="text-center">
                <h3 className="mb-3 font-jakarta text-xl font-bold text-gray-900">
                  {step.title}
                </h3>
                <p className="mb-3 font-medium text-gray-600">
                  {step.description}
                </p>
                <p className="text-sm leading-relaxed text-gray-500">
                  {step.details}
                </p>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Mobile App Integration Highlight */}
      {/* <div className="mx-auto max-w-4xl rounded-2xl bg-gradient-to-r from-teal-50 to-blue-50 p-8">
        <div className="flex flex-col items-center gap-6 md:flex-row">
          <div className="flex-1">
            <h3 className="mb-4 text-2xl font-bold text-gray-900">
              Powered by Scraplo Connect App
            </h3>
            <p className="mb-4 text-gray-600">
              Every step is facilitated through our mobile app, ensuring
              transparency, real-time tracking, and seamless transactions for
              all partners in the ecosystem.
            </p>
            <div className="flex flex-wrap gap-4">
              <div className="flex items-center gap-2">
                <Smartphone className="h-5 w-5 text-teal-800" />
                <span className="text-sm font-medium">GPS Tracking</span>
              </div>
              <div className="flex items-center gap-2">
                <Handshake className="h-5 w-5 text-teal-800" />
                <span className="text-sm font-medium">Instant Payments</span>
              </div>
              <div className="flex items-center gap-2">
                <Scale className="h-5 w-5 text-teal-800" />
                <span className="text-sm font-medium">Live Pricing</span>
              </div>
            </div>
          </div>
          <div className="flex flex-1 justify-center">
            <div className="flex h-32 w-32 items-center justify-center rounded-2xl bg-teal-800">
              <Smartphone className="h-16 w-16 text-white" />
            </div>
          </div>
        </div>
      </div> */}
    </div>
  );
};
export default GetStarted;
