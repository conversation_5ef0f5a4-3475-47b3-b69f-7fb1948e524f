"use client";

import { useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { Menu, X } from "lucide-react";

import { Button } from "@acme/ui/components/ui/button";
import {
  Sheet,
  SheetContent,
  SheetTrigger,
} from "@acme/ui/components/ui/sheet";

import { env } from "~/env";

const Navbar = () => {
  const router = useRouter();
  const [isOpen, setIsOpen] = useState(false);

  // Debug state changes
  //   console.log("🏠 Navbar render - isOpen:", isOpen);

  const handleScrollTo = (id: string) => {
    // console.log("🎯 handleScrollTo called with id:", id);
    // console.log("📍 Current scroll position before:", window.scrollY);

    // Close sheet first
    setIsOpen(false);
    // console.log("� Sheet closed immediately");

    // Wait for sheet to close completely, then scroll
    // setTimeout(() => {
    router.push(id);
    //   console.log("⏰ Starting scroll after sheet closed");
    // const element = document.querySelector(id);
    // //   console.log("🔍 Found element:", element);

    // if (element) {
    //   // // console.log(
    //   //   "📐 Element position:",
    //   //   element.getBoundingClientRect().top + window.scrollY,
    //   // );
    //   element.scrollIntoView({ behavior: "smooth" });
    //   // console.log("⏩ Started scrollIntoView");

    //   // Check scroll position after a delay
    //   setTimeout(() => {
    //     //   console.log(
    //     //     "📍 Scroll position after scrollIntoView:",
    //     //     window.scrollY,
    //     //   );
    //   }, 1000);
    // }
    // }, 300); // Wait 300ms for sheet to close
  };

  return (
    <nav className="container mx-auto flex min-w-full items-center justify-between py-4 shadow-sm">
      {/* Left side - Logo */}
      <div className="flex items-center">
        <Link href="/">
          <Image
            src="/logo.svg"
            alt="Logo"
            width={120}
            height={40}
            className="cursor-pointer"
          />
        </Link>
      </div>

      {/* Center - Desktop Navigation Links */}
      <div className="hidden items-center justify-center lg:flex">
        <div className="flex items-center gap-x-2 lg:gap-x-6">
          <button
            onClick={() => handleScrollTo("/#benefits")}
            className="cursor-pointer text-nowrap border-none bg-transparent py-[6px] font-semibold text-[#fbfbfb]"
          >
            Benefits
          </button>
          <button
            onClick={() => handleScrollTo("/#how-it-works")}
            className="cursor-pointer text-nowrap border-none bg-transparent py-[6px] font-semibold text-[#fbfbfb]"
          >
            How it Works
          </button>
          <button
            onClick={() =>
              handleScrollTo(env.NEXT_PUBLIC_SELLER_BETTER_AUTH_URL)
            }
            className="cursor-pointer text-nowrap border-none bg-transparent py-[6px] font-semibold text-[#fbfbfb]"
          >
            Check Rates
          </button>
          <button
            onClick={() => handleScrollTo("/#social-proof")}
            className="cursor-pointer text-nowrap border-none bg-transparent py-[6px] font-semibold text-[#fbfbfb]"
          >
            Social Proof
          </button>
          <button
            onClick={() => handleScrollTo("/#faq")}
            className="cursor-pointer text-nowrap border-none bg-transparent py-[6px] font-semibold text-[#fbfbfb]"
          >
            FAQ
          </button>
          <Link
            href="/about-us"
            className="text-nowrap py-[6px] font-semibold text-[#fbfbfb]"
          >
            About Us
          </Link>
          <div className="flex items-center">
            <Button
              variant={"outline"}
              className="border-[1.4px] border-teal-800 bg-white text-lg font-semibold text-teal-800"
            >
              Get Started
            </Button>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      <div className="lg:hidden">
        <Sheet open={isOpen} onOpenChange={setIsOpen}>
          <SheetTrigger asChild>
            <button className="flex size-10 items-center justify-center rounded-sm p-0 hover:bg-gray-50/10">
              <Menu className="size-10 text-white" />
              <span className="sr-only">Toggle menu</span>
            </button>
          </SheetTrigger>
          <SheetContent
            side="right"
            hideCloseButton
            className="w-[300px] bg-[#0D2321] sm:w-[400px]"
          >
            <button
              className="absolute right-2 top-2 flex size-16 items-center justify-center rounded-sm p-0 hover:bg-gray-50/10"
              onClick={() => setIsOpen(false)}
            >
              <X className="self-center text-white" />
              <span className="sr-only">Close menu</span>
            </button>
            <div className="mt-6 flex flex-col space-y-6">
              <button
                onClick={() => handleScrollTo("#benefits")}
                className="block w-full cursor-pointer rounded-md border-none bg-transparent px-4 py-3 text-left text-lg font-semibold text-[#fbfbfb]"
              >
                Benefits
              </button>
              <button
                onClick={() => handleScrollTo("#how-it-works")}
                className="block w-full cursor-pointer rounded-md border-none bg-transparent px-4 py-3 text-left text-lg font-semibold text-[#fbfbfb]"
              >
                How it Works
              </button>
              <button
                onClick={() =>
                  handleScrollTo(env.NEXT_PUBLIC_SELLER_BETTER_AUTH_URL)
                }
                className="block w-full cursor-pointer rounded-md border-none bg-transparent px-4 py-3 text-left text-lg font-semibold text-[#fbfbfb]"
              >
                Check Rates
              </button>
              <button
                onClick={() => handleScrollTo("#social-proof")}
                className="block w-full cursor-pointer rounded-md border-none bg-transparent px-4 py-3 text-left text-lg font-semibold text-[#fbfbfb]"
              >
                Social Proof
              </button>
              <button
                onClick={() => handleScrollTo("#faq")}
                className="block w-full cursor-pointer rounded-md border-none bg-transparent px-4 py-3 text-left text-lg font-semibold text-[#fbfbfb]"
              >
                FAQ
              </button>
              <div className="border-t pt-4">
                <Button
                  variant={"outline"}
                  className="w-full border-[1.4px] border-teal-800 bg-white text-lg font-semibold text-teal-800"
                >
                  Get Started
                </Button>
              </div>
            </div>
          </SheetContent>
        </Sheet>
      </div>
    </nav>
  );
};

export default Navbar;
