import Image from "next/image";
import { StarIcon } from "lucide-react";

import { Button } from "@acme/ui/components/ui/button";

const ReadyCTASection = () => {
  return (
    <div
      id="check-rates"
      className="container mx-auto flex min-w-full flex-col bg-black-50 py-10 lg:py-12 xl:gap-20"
    >
      {" "}
      <div className="flex flex-col justify-between gap-10 overflow-hidden rounded-[50px] bg-yellow-950 px-7 py-10 lg:flex-row xl:px-[100px] xl:py-[70px]">
        <div className="flex flex-col gap-8 sm:gap-12 md:gap-16 xl:gap-[56px]">
          <div className="flex flex-col gap-3 sm:gap-4 md:gap-5">
            <div className="flex flex-col gap-3">
              <div className="flex flex-row gap-3">
                {/* start */}
                <div className="flex flex-row gap-1">
                  {Array.from({ length: 5 }, (_, i) => (
                    <StarIcon
                      key={i}
                      className="size-6 text-yellow-500"
                      fill="#FBCF04"
                    />
                  ))}
                </div>
                <p className="text-white"> 4.9/5</p>
              </div>
              <div className="flex w-fit flex-row">
                <div className="size-[38px] rounded-full bg-teal-700 sm:size-9 lg:size-11"></div>
                <div className="-ml-1.5 size-[38px] rounded-full bg-yellow-450 sm:-ml-2 sm:size-9 lg:size-11"></div>
                <div className="-ml-1.5 size-[38px] rounded-full bg-teal-700 sm:size-9 lg:size-11"></div>
                <div className="-ml-1.5 size-[38px] rounded-full bg-yellow-450 sm:-ml-2 sm:size-9 lg:size-11"></div>
                <div className="-ml-1.5 size-[38px] rounded-full bg-teal-700 sm:size-9 lg:size-11"></div>
              </div>{" "}
            </div>
            <h1 className="font-jakarta text-2xl font-bold leading-8 text-black-50 sm:text-3xl sm:leading-10 md:text-4xl md:leading-[48px] xl:text-[48px] xl:leading-[64px]">
              Ready to clear the clutter and pocket the cash?
            </h1>{" "}
          </div>
          <div className="flex flex-col gap-4 sm:gap-6 md:flex-row md:gap-8">
            <Button className="bg-yellow-500">Start Earning from Scrap</Button>
            <Button className="bg-[#F2FFF5]">Join as Pickup Partner</Button>
          </div>
        </div>
        <div className="mx-auto -mb-[38px] rounded-b-md lg:mx-0 xl:-mb-[65px]">
          <Image
            src={"/ready-cta.webp"}
            alt="get ready"
            width={400}
            height={600}
          />
        </div>
      </div>
    </div>
  );
};
export default ReadyCTASection;
