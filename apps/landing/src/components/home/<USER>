"use client";

import type { question } from "@acme/db/schema";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@acme/ui/components/ui/accordion";
import { Badge } from "@acme/ui/components/ui/badge";
import { Button } from "@acme/ui/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  TabsList,
  TabsTrigger,
} from "@acme/ui/components/ui/tabs";
import { Editor } from "@acme/ui/editor";

///add admin faq here
//
interface FAQProps {
  sellerQuestions: (typeof question.$inferSelect)[];
  kabadiwalaQuestions: (typeof question.$inferSelect)[];
  sellerAnswers: Record<string, string[]>;
  kabadiwalaAnswers: Record<string, string[]>;
}
const FAQSection = ({
  kabadiwalaAnswers,
  kabadiwalaQuestions,
  sellerAnswers,
  sellerQuestions,
}: FAQProps) => {
  return (
    <div
      id="faq"
      className="container mx-auto min-w-full py-10 font-jakarta xl:py-[70px]"
    >
      <div className="flex flex-col gap-10 lg:flex-row lg:gap-[60px]">
        <div className="flex flex-col gap-10 lg:gap-[60px]">
          <div className="space-y-2">
            <Badge className="rounded-full border-teal-650 bg-transparent px-5 text-teal-850">
              FAQ
            </Badge>
            <h1 className="text-[40px] font-bold leading-[56px] text-black-700">
              Questions and Answers
            </h1>
            <p className="font-jakarta text-lg text-black-600">
              We have answers to your questions about services and approach.
            </p>
          </div>
          <div className="w-full space-y-6">
            <div className="flex w-full flex-col gap-2">
              <h4 className="text-2xl font-bold text-teal-800">
                Got more question?
              </h4>
              <p className="font-inter text-lg text-black-600">
                contact us for more information.
              </p>
            </div>
            <Button>Contact Us</Button>
          </div>
        </div>
        <div className="flex w-full flex-col gap-2">
          <Tabs defaultValue="seller" className="w-full">
            <TabsList className="mb-4 w-fit justify-center gap-4 rounded-2xl bg-transparent p-2">
              <TabsTrigger
                value="seller"
                className="rounded-xl border border-teal-700 px-6 py-2 font-jakarta text-lg font-bold text-teal-700 transition-all duration-200 data-[state=active]:border-teal-700 data-[state=active]:bg-teal-700 data-[state=active]:text-white data-[state=active]:shadow-lg md:text-xl"
              >
                Seller FAQs
              </TabsTrigger>
              <TabsTrigger
                value="kabadiwala"
                className="rounded-xl border border-teal-700 px-6 py-2 font-jakarta text-lg font-bold text-teal-700 transition-all duration-200 data-[state=active]:border-teal-700 data-[state=active]:bg-teal-700 data-[state=active]:text-white data-[state=active]:shadow-lg md:text-xl"
              >
                Kabadiwala FAQs
              </TabsTrigger>
            </TabsList>
            <TabsContent value="seller">
              <Accordion type="multiple" className="min-w-full gap-3">
                {sellerQuestions.map((item, index) => (
                  <AccordionItem
                    key={index}
                    value={index.toString()}
                    className="border-none p-3 data-[state=open]:rounded-3xl data-[state=open]:bg-[#FBFBFB]"
                  >
                    <AccordionTrigger className="text-lg font-bold text-teal-850">
                      {item.content}
                    </AccordionTrigger>
                    <AccordionContent className="textt-black-700 font-inter text-base">
                      {(sellerAnswers[item.id] ?? []).length ? (
                        (sellerAnswers[item.id] ?? []).map((ans, i) => (
                          <Editor
                            key={i}
                            editorSerializedState={ans}
                            editable={false}
                          />
                        ))
                      ) : (
                        <span>No answer available.</span>
                      )}
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            </TabsContent>
            <TabsContent value="kabadiwala">
              <Accordion type="multiple" className="min-w-full gap-3">
                {kabadiwalaQuestions.map((item, index) => (
                  <AccordionItem
                    key={index}
                    value={index.toString()}
                    className="border-none p-3 data-[state=open]:rounded-3xl data-[state=open]:bg-[#FBFBFB]"
                  >
                    <AccordionTrigger className="text-lg font-bold text-teal-850">
                      {item.content}
                    </AccordionTrigger>
                    <AccordionContent className="textt-black-700 font-inter text-base">
                      {(kabadiwalaAnswers[item.id] ?? []).length ? (
                        (kabadiwalaAnswers[item.id] ?? []).map((ans, i) => (
                          <Editor
                            key={i}
                            editorSerializedState={ans}
                            editable={false}
                          />
                        ))
                      ) : (
                        <span>No answer available.</span>
                      )}
                    </AccordionContent>
                  </AccordionItem>
                ))}
              </Accordion>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};
export default FAQSection;
