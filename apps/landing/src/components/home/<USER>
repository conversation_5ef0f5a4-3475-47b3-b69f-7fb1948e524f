import Image from "next/image";
import { CalendarClock, PercentCircle, Scale, Wallet } from "lucide-react";

import { Badge } from "@acme/ui/components/ui/badge";

import CrosshairIcon from "../icons/CrosshairIcon";
import NavigationIcon from "../icons/NavigationIcon";
import PayoutIcon from "../icons/PayoutIcon";
import ToggleIcon from "../icons/ToggleIcon";

const BenefitsSection = () => {
  return (
    <div
      id="benefits"
      className="container mx-auto flex min-w-full flex-col items-center justify-center gap-8 sm:gap-12 sm:py-12 xl:gap-[50px]"
    >
      <div className="text-center">
        <Badge className="rounded-full border-[1.4px] border-teal-700 bg-transparent">
          BENEFITS
        </Badge>
        <h1 className="text-2xl font-bold sm:text-3xl lg:text-4xl">
          Discover the Perks for Every User
        </h1>
      </div>{" "}
      <div className="flex w-full flex-col items-center gap-6 self-stretch sm:gap-8 lg:gap-10">
        {/* SELLER SECTION */}
        <div className="flex w-full flex-col items-center gap-4 lg:flex-row">
          <div className="flex flex-row items-center gap-4 lg:gap-6">
            <p className="h-full w-[1ch] text-wrap break-all text-4xl font-extrabold tracking-wider text-black-250 sm:text-5xl lg:h-full lg:text-[76px] lg:leading-[76px] lg:tracking-[0.767px] xl:pt-10">
              SELLER
            </p>
            <div className="flex justify-center">
              <Image
                src={"/seller-benifit.webp"}
                alt="Seller Benefit"
                width={400}
                height={300}
                className="h-auto w-48 sm:w-56 lg:max-h-[561px] lg:max-w-[277px]"
              />
            </div>
          </div>
          <div className="flex w-full flex-col gap-4 sm:gap-6 lg:gap-10 lg:pt-10">
            {" "}
            <div className="flex flex-row items-center gap-2 sm:items-center md:gap-0">
              <div className="flex h-fit w-fit items-center rounded-full bg-yellow-450 p-3 sm:p-4">
                <PercentCircle className="h-5 w-5 sm:h-6 sm:w-6" />
              </div>
              <div className="hidden flex-1 border-t-2 border-dashed border-gray-400 md:block"></div>
              <div className="rounded-xl bg-yellow-50 p-4 text-center text-lg font-semibold text-[#333333] sm:text-xl lg:text-2xl xl:p-6">
                Quick selling of scrap with best market rates
              </div>
            </div>{" "}
            <div className="flex flex-row items-center gap-2 sm:items-center md:gap-0">
              <div className="flex h-fit w-fit items-center rounded-full bg-yellow-450 p-3 sm:p-4">
                <CalendarClock className="h-5 w-5 sm:h-6 sm:w-6" />
              </div>
              <div className="hidden h-2 flex-1 border-t-2 border-dashed border-gray-400 md:block"></div>
              <div className="inline-block w-max max-w-full break-words rounded-xl bg-yellow-50 p-4 text-lg font-semibold text-[#333333] sm:text-xl lg:text-2xl xl:p-6">
                Under 60 second booking - no cart required
              </div>
            </div>{" "}
            <div className="flex flex-row items-center gap-2 sm:items-center md:gap-0">
              <div className="flex h-fit w-fit items-center rounded-full bg-yellow-450 p-3 sm:p-4">
                <Wallet className="h-5 w-5 sm:h-6 sm:w-6" />
              </div>
              <div className="hidden flex-1 border-t-2 border-dashed border-gray-400 md:block"></div>
              <div className="rounded-xl bg-yellow-50 p-4 text-center text-lg font-semibold text-[#333333] sm:text-xl lg:text-2xl xl:p-6">
                Onthe spot digital payment
              </div>
            </div>{" "}
            <div className="flex flex-row items-center gap-2 sm:items-center md:gap-0">
              <div className="flex h-fit w-fit items-center rounded-full bg-yellow-450 p-3 sm:p-4">
                <Scale className="h-5 w-5 sm:h-6 sm:w-6" />
              </div>
              <div className="hidden flex-1 border-t-2 border-dashed border-gray-400 md:block"></div>
              <div className="rounded-xl bg-yellow-50 p-4 text-center text-lg font-semibold text-[#333333] sm:text-xl lg:text-2xl xl:p-6">
                Transparent weighing right at your door
              </div>
            </div>{" "}
          </div>
        </div>

        {/* PARTNER SECTION */}
        <div className="flex w-full flex-col-reverse items-center gap-4 lg:flex-row">
          <div className="flex w-full flex-col gap-4 sm:gap-6 lg:gap-10 xl:pt-10">
            {" "}
            <div className="flex w-full flex-row items-center justify-end gap-2 sm:gap-4 md:justify-between md:gap-0 lg:gap-2">
              <div className="order-1 rounded-xl bg-yellow-50 p-4 text-center text-lg font-semibold text-[#333333] sm:text-xl lg:text-2xl xl:p-6">
                Guranteed daily orders in your 5 km radius
              </div>
              <div className="order-2 hidden flex-1 border-t-2 border-dashed border-gray-400 md:block"></div>{" "}
              <div className="order-3 flex h-fit w-fit items-center rounded-full bg-yellow-450 p-3 sm:p-4">
                <CrosshairIcon className="h-5 w-5 sm:h-6 sm:w-6" />
              </div>
            </div>{" "}
            <div className="flex w-full flex-row items-center justify-end gap-2 sm:gap-4 md:justify-between md:gap-0 lg:gap-2">
              <div className="order-1 rounded-xl bg-yellow-50 p-4 text-center text-lg font-semibold text-[#333333] sm:text-xl lg:text-2xl xl:p-6">
                In-app navigation & live pricing
              </div>
              <div className="order-2 hidden flex-1 border-t-2 border-dashed border-gray-400 md:block"></div>{" "}
              <div className="order-3 flex h-fit w-fit items-center rounded-full bg-yellow-450 p-3 sm:p-4">
                <NavigationIcon className="h-5 w-5 sm:h-6 sm:w-6" />
              </div>
            </div>{" "}
            <div className="flex w-full flex-row items-center justify-end gap-2 sm:gap-4 md:justify-between md:gap-0 lg:gap-2">
              <div className="order-1 rounded-xl bg-yellow-50 p-4 text-center text-lg font-semibold text-[#333333] sm:text-xl lg:text-2xl xl:p-6">
                Instant payouts after every trip
              </div>
              <div className="order-2 hidden flex-1 border-t-2 border-dashed border-gray-400 md:block"></div>{" "}
              <div className="order-3 flex h-fit w-fit items-center rounded-full bg-yellow-450 p-3 sm:p-4">
                <PayoutIcon className="h-5 w-5 sm:h-6 sm:w-6" />
              </div>
            </div>{" "}
            <div className="flex w-full flex-row items-center justify-end gap-2 sm:gap-4 md:justify-between md:gap-0 lg:gap-2">
              <div className="order-1 rounded-xl bg-yellow-50 p-4 text-center text-lg font-semibold text-[#333333] sm:text-xl lg:text-2xl xl:p-6">
                Flexible shifts - set your own hours{" "}
              </div>
              <div className="order-2 hidden flex-1 border-t-2 border-dashed border-gray-400 md:block"></div>{" "}
              <div className="order-3 flex h-fit w-fit items-center rounded-full bg-yellow-450 p-3 sm:p-4">
                <ToggleIcon className="h-5 w-5 sm:h-6 sm:w-6" />
              </div>
            </div>{" "}
          </div>
          <div className="flex flex-row items-center gap-4 lg:gap-6">
            <div className="flex justify-center lg:order-1">
              <Image
                src={"/partner-benifit.webp"}
                alt="Partner Benefit"
                width={400}
                height={300}
                className="h-auto w-48 sm:w-56 lg:max-h-[561px] lg:max-w-[277px]"
              />
            </div>
            <p className="h-full w-[1ch] break-all text-4xl font-extrabold tracking-wider text-black-250 sm:text-5xl lg:order-1 lg:text-6xl xl:text-wrap xl:pt-10 xl:text-[76px] xl:leading-[76px] xl:tracking-[0.767px]">
              PARTNER
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BenefitsSection;
