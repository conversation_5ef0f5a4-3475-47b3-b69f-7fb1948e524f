import { ArrowR<PERSON>, Verified } from "lucide-react";

import { Badge } from "@acme/ui/components/ui/badge";
import { Button } from "@acme/ui/components/ui/button";

const EarnMoreCTA = () => {
  return (
    <div
      style={{
        background:
          "linear-gradient(180deg, rgba(0, 0, 0, 0.00) 0.15%, rgba(0, 0, 0, 0.80) 126.58%), url(/money-cta.webp) lightgray 50% / cover no-repeat",
      }}
      className="container mx-auto min-w-full py-6 text-white md:py-10 xl:py-[70px]"
    >
      <div className="flex flex-col gap-6 rounded-3xl bg-[rgba(1,38,38,0.4)] p-6 backdrop-blur-[8px] md:gap-8 md:p-8 xl:gap-[50px] xl:p-9">
        <div className="flex flex-col gap-2 md:gap-4 lg:gap-6 xl:gap-8">
          <div className="flex flex-col gap-3">
            {" "}
            <Badge className="w-fit rounded-full border-[1.6px] border-teal-250 bg-transparent px-3 py-1 text-xs text-white md:px-4 md:text-sm xl:px-5 xl:text-base">
              For Pickup Partners: Your Side‑Hustle, Super‑Charged
            </Badge>{" "}
            <h2 className="md:leading-12 font-jakarta text-2xl font-bold leading-8 text-[#56A359] sm:text-3xl sm:leading-10 md:text-4xl xl:text-5xl xl:leading-[64px]">
              Earn More<span className="text-[#fbfbfb]">, Waste Less</span>
            </h2>
          </div>{" "}
          <div className="flex flex-col items-start justify-start gap-4 sm:flex-row sm:flex-wrap sm:items-center md:gap-6 xl:gap-8">
            <p className="flex flex-row items-center gap-2 text-sm md:text-base">
              <Verified className="h-4 w-4 md:h-5 md:w-5" />
              Accept Pick-up with one tap
            </p>
            <p className="flex flex-row items-center gap-2 text-sm md:text-base">
              {" "}
              <Verified className="h-4 w-4 md:h-5 md:w-5" />
              See distance, weight estimate & payout before you commit
            </p>
            <p className="flex flex-row items-center gap-2 text-sm md:text-base">
              {" "}
              <Verified className="h-4 w-4 md:h-5 md:w-5" />
              Built‑in navigation, digital scale sync, and instant wallet
              settlement
            </p>
          </div>
        </div>{" "}
        <div>
          <Button className="bg-yellow-450 text-sm md:text-base">
            List Scrap for Pickup{" "}
            <ArrowRight className="ml-1 h-4 w-4 md:h-5 md:w-5" />
          </Button>
        </div>
      </div>
    </div>
  );
};
export default EarnMoreCTA;
