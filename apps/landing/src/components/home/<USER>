import Image from "next/image";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, User } from "lucide-react";

import type { testimonials } from "@acme/db/schema";
import { db } from "@acme/db/client";
import { Badge } from "@acme/ui/components/ui/badge";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@acme/ui/components/ui/carousel";

type Testimonial = typeof testimonials.$inferSelect;

const FeedBackSection = async () => {
  const testimonials = await db.query.testimonials.findMany();

  return (
    <div
      id="social-proof"
      className="container mx-auto min-w-full bg-[#FFFBED] py-10 xl:py-[70px]"
    >
      <div className="flex flex-col gap-8">
        <div className="flex flex-col items-center justify-center gap-3">
          <Badge className="rounded-full border-[1.6px] border-teal-650 bg-transparent px-5 py-[6px] text-teal-850">
            Social Proof
          </Badge>

          <h1 className="font-jakarta text-[40px] font-bold leading-[56px] text-black-700">
            See What Our Partners & Customers Says
          </h1>
        </div>
        <Carousel>
          <CarouselContent className="gap-8">
            {testimonials.map((f, i) => (
              <CarouselItem key={i} className="md:basis-1/2 lg:basis-1/3">
                <FeedbackCard {...f} />
              </CarouselItem>
            ))}
          </CarouselContent>
          <div className="flex flex-row items-center justify-center gap-2 self-center pt-8">
            <div className="size-3 rounded-full bg-yellow-450" />{" "}
            <div className="size-3 rounded-full bg-yellow-450" />{" "}
            <div className="size-3 rounded-full bg-yellow-450" />
          </div>
        </Carousel>
      </div>
    </div>
  );
};
export default FeedBackSection;

const FeedbackCard = ({
  title,
  message,
  stars,
  userName,
  userImage,
  userDesignation,
  userLocation,
}: Testimonial) => {
  return (
    <div className="flex flex-col gap-8 rounded-[40px] bg-white p-10 font-inter">
      <div className="flex flex-row items-center justify-between">
        <Quote className="size[27px] rotate-180" />
        <div className="flex flex-row items-center justify-center gap-1">
          {Array.from({ length: Number(stars) }, (_, i) => (
            <StarIcon
              key={i}
              className="size-6 text-yellow-500"
              fill="#FBCF04"
            />
          ))}
        </div>
      </div>
      <div className="flex flex-col gap-2">
        <h5 className="font-inter text-xl font-semibold leading-8 text-black-700">
          " {title}
        </h5>
        <p className="text-[#fbfbfb]0 font-inter text-base">{message}</p>
      </div>
      <div className="flex flex-row items-start gap-4">
        <div className="rounded-full">
          {userImage ? (
            <Image
              src={userImage}
              className="size-[60px] rounded-full"
              alt="User Profile Image"
              height={60}
              width={60}
            />
          ) : (
            <User className="size-[60px] rounded-full" />
          )}
        </div>
        <div className="flex flex-col gap-2">
          <h5 className="text-xl font-semibold leading-8 text-black-700">
            {userName ?? "Anonymous"}
          </h5>
          <p className="text-[#fbfbfb]0 font-inter text-base leading-6">
            {userDesignation && userDesignation + ", "}
            {userLocation}
          </p>
        </div>
      </div>
    </div>
  );
};
