import Image from "next/image";
import Link from "next/link";
import { Facebook, Instagram, Linkedin } from "lucide-react";

import { env } from "~/env";

const FooterSection = () => {
  return (
    <div className="container mx-auto min-w-full py-10">
      <div className="flex flex-col gap-[30px]">
        <div className="flex flex-col items-center justify-center md:flex-row md:justify-between">
          <div className="flex flex-col items-center gap-6 md:items-start">
            <div className="flex items-center">
              <Link href="/">
                <Image
                  src="/logo.svg"
                  alt="Logo"
                  width={120}
                  height={40}
                  className="cursor-pointer"
                />
              </Link>
            </div>

            <p>ⓒ 2025 Scraplo Recycling Services</p>
          </div>
          <div className="flex flex-row gap-1">
            <Link
              className="rounded-full bg-teal-950 p-2 text-white"
              href={"https://www.facebook.com/"}
            >
              <Facebook />
            </Link>
            <Link
              className="rounded-full bg-teal-950 p-2 text-white"
              href={"https://www.linkedin.com/"}
            >
              <Linkedin />
            </Link>
            <Link
              className="rounded-full bg-teal-950 p-2 text-white"
              href={"https://www.instagram.com/"}
            >
              <Instagram />
            </Link>
            <Link
              className="rounded-full bg-teal-950 p-2 text-white"
              href={"https://www.telegram.com/"}
            >
              <svg
                fill="white"
                viewBox="0 0 32 32"
                height={24}
                width={24}
                xmlns="http://www.w3.org/2000/svg"
              >
                <g id="SVGRepo_bgCarrier" strokeWidth="0"></g>
                <g
                  id="SVGRepo_tracerCarrier"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                ></g>
                <g id="SVGRepo_iconCarrier">
                  <path d="M29.919 6.163l-4.225 19.925c-0.319 1.406-1.15 1.756-2.331 1.094l-6.438-4.744-3.106 2.988c-0.344 0.344-0.631 0.631-1.294 0.631l0.463-6.556 11.931-10.781c0.519-0.462-0.113-0.719-0.806-0.256l-14.75 9.288-6.35-1.988c-1.381-0.431-1.406-1.381 0.288-2.044l24.837-9.569c1.15-0.431 2.156 0.256 1.781 2.013z"></path>
                </g>
              </svg>
            </Link>
          </div>
        </div>
        <div className="flex flex-col items-center justify-center gap-1 pb-20 font-inter text-lg font-semibold text-black-600 md:flex-row">
          {/* <Link href={"#"}> Real-time Price Feed </Link>
          {" | "} */}
          <Link prefetch href="/terms-of-service">
            {" "}
            Terms & Conditions - Refund Policy
          </Link>
          <p className="hidden md:block"> {" | "}</p>
          <Link prefetch href={"/privacy-policy"}>
            {" "}
            Privacy Policy{" "}
          </Link>
          {/* <p className="hidden md:block"> {" | "}</p>
          <Link prefetch href={"/cancellation-policy"}>
            {" "}
            Cancellation Policy{" "}
          </Link>{" "} */}
          <p className="hidden md:block"> {" | "}</p>
          <Link href={env.NEXT_PUBLIC_KABADIWALA_BETTER_AUTH_URL}>
            {" "}
            Partner Login{" "}
          </Link>
          <p className="hidden md:block"> {" | "}</p>
          <Link href={"#"}> Support (24/7 WhatsApp) </Link>
          <p className="hidden md:block"> {" | "}</p>
          <Link prefetch href={"/about-us"}>
            {" "}
            About Us{" "}
          </Link>
        </div>
      </div>
    </div>
  );
};
export default FooterSection;
