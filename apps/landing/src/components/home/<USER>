"use client";

import { useRouter } from "next/navigation";
import { <PERSON>R<PERSON>, Bike, Recycle, User, Warehouse } from "lucide-react";

import { Badge } from "@acme/ui/components/ui/badge";
import { Button } from "@acme/ui/components/ui/button";

import { env } from "~/env";

const HeroSection = () => {
  const router = useRouter();
  return (
    <div className="mx-auto flex h-full w-full max-w-[1200px] flex-col items-center justify-center gap-8 px-4 sm:gap-12 md:gap-16 lg:gap-[60px]">
      <div className="flex flex-col items-center gap-6 sm:gap-8 lg:gap-10">
        <div className="flex flex-col items-center gap-4 text-center sm:gap-5">
          <Badge className="space-x-3 rounded-[24px] border border-teal-800 bg-white/10 px-3 py-1.5 font-jakarta font-bold sm:space-x-4 sm:rounded-[28px] sm:px-4 sm:py-2 lg:space-x-5 lg:rounded-[32px] lg:px-5 lg:py-2">
            <span className="text-teal-800">Circular</span>
            <span className="text-xs font-semibold text-black-100">
              Economy
            </span>
            <span className="text-black-0">Network</span>
          </Badge>
          <div className="flex flex-col items-center gap-3 text-center sm:gap-4">
            <h1 className="font-jakarta text-2xl font-semibold leading-tight text-[#fbfbfb] sm:text-3xl sm:leading-[50px] md:text-4xl md:leading-[60px] lg:text-[52px] lg:leading-[70px]">
              Complete Scrap
              <span className="font-bold text-teal-800">
                {` Ecosystem`}
              </span>{" "}
              <br></br>
              From Seller to Recycler
            </h1>
            <p className="max-w-[500px] font-inter text-sm text-black-300 sm:max-w-[600px] sm:text-base md:max-w-[650px] md:text-lg lg:max-w-[700px] lg:text-xl">
              Join our transparent, traceable network that connects everyone in
              the scrap recycling chain. Get cash for your scrap while
              contributing to a sustainable future.
            </p>
          </div>
        </div>

        {/* Ecosystem Cards Preview (like in EcosystemSection) */}
        <div className="mb-12 mt-6 grid w-full grid-cols-1 gap-6 lg:grid-cols-2">
          <div className="relative rounded-lg border border-gray-200 bg-white/90 p-5 text-center shadow-sm transition-shadow duration-300 hover:shadow-lg">
            <div className="mx-auto -mt-10 flex h-14 w-14 items-center justify-center rounded-full border-4 border-white bg-teal-800 shadow-lg">
              <User className="h-7 w-7 text-white" />
            </div>
            <h3 className="mb-2 mt-2 text-lg font-bold text-gray-900">
              Seller
            </h3>
            <p className="mb-4 text-sm text-gray-600">
              Sell your scrap easily from home or office.
            </p>
            <Button
              onClick={() =>
                router.push(env.NEXT_PUBLIC_SELLER_BETTER_AUTH_URL)
              }
              className="w-full"
            >
              Sell scrap now <ArrowRight />
            </Button>
          </div>
          <div className="relative rounded-lg border border-gray-200 bg-white/90 p-5 text-center shadow-sm transition-shadow duration-300 hover:shadow-lg">
            <div className="mx-auto -mt-10 flex h-14 w-14 items-center justify-center rounded-full border-4 border-white bg-teal-800 shadow-lg">
              <Bike className="h-7 w-7 text-white" />
            </div>
            <h3 className="mb-2 mt-2 text-lg font-bold text-gray-900">
              Pickup Partner
            </h3>
            <p className="mb-4 text-sm text-gray-600">
              Join as a pickup partner for doorstep collections.
            </p>
            <Button
              onClick={() =>
                router.push(env.NEXT_PUBLIC_KABADIWALA_BETTER_AUTH_URL)
              }
              className="w-full"
            >
              Become pickup partner <ArrowRight />
            </Button>
          </div>
          <div className="relative rounded-lg border border-gray-200 bg-white/90 p-5 text-center shadow-sm transition-shadow duration-300 hover:shadow-lg">
            <div className="mx-auto -mt-10 flex h-14 w-14 items-center justify-center rounded-full border-4 border-white bg-teal-800 shadow-lg">
              <Warehouse className="h-7 w-7 text-white" />
            </div>
            <h3 className="mb-2 mt-2 text-lg font-bold text-gray-900">
              Scrap Hub Partner
            </h3>
            <p className="mb-4 text-sm text-gray-600">
              Manage and process scrap as a hub partner.
            </p>
            <Button
              onClick={() =>
                router.push(env.NEXT_PUBLIC_SCRAPHUB_EMPLOYEE_BETTER_AUTH_URL)
              }
              className="w-full"
            >
              Become Hub Partner <ArrowRight />
            </Button>
          </div>
          <div className="relative rounded-lg border border-gray-200 bg-white/90 p-5 text-center shadow-sm transition-shadow duration-300 hover:shadow-lg">
            <div className="mx-auto -mt-10 flex h-14 w-14 items-center justify-center rounded-full border-4 border-white bg-teal-800 shadow-lg">
              <Recycle className="h-7 w-7 text-white" />
            </div>
            <h3 className="mb-2 mt-2 text-lg font-bold text-gray-900">
              Recycle Partner
            </h3>
            <p className="mb-4 text-sm text-gray-600">
              Become a certified recycling partner in our network.
            </p>
            <Button className="w-full border-teal-800 bg-white text-teal-800 hover:bg-teal-50 hover:text-teal-800">
              Become Recycle Partner <ArrowRight />
            </Button>
          </div>
        </div>
      </div>
      {/* <div className="relative flex w-full items-center justify-center">
        <Image
          src={"/hero-phone.webp"}
          alt="hero-phone"
          width={500}
          height={500}
          className="h-auto w-full max-w-[300px] object-cover sm:max-w-[400px] md:max-w-[600px] lg:max-w-[800px] xl:max-w-[978px]"
        />
        <div className="absolute -right-2 bottom-2 flex w-fit items-center gap-2 rounded-lg bg-white px-3 py-3 sm:-right-4 sm:bottom-3 sm:gap-3 sm:rounded-xl sm:px-4 sm:py-4 md:px-5 md:py-5 lg:bottom-4 lg:px-6 lg:py-6 xl:-right-[140px]">
          <div className="flex w-fit flex-row">
            <div className="size-8 rounded-full bg-teal-800 sm:size-9 lg:size-11"></div>
            <div className="-ml-1.5 size-8 rounded-full bg-teal-600 sm:-ml-2 sm:size-9 lg:size-11"></div>
          </div>
          <div className="flex flex-col items-center">
            <h4 className="text-lg font-semibold text-black-700 sm:text-xl lg:text-2xl">
              124,89K+
            </h4>
            <p className="text-sm text-black-400 sm:text-base lg:text-lg">
              Trusted Users
            </p>
          </div>
        </div>
      </div> */}
    </div>
  );
};
export default HeroSection;
