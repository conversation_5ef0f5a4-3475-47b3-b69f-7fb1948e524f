import { Bike, Recycle, User, Warehouse } from "lucide-react";

const ecosystemData = [
  {
    icon: User,
    title: "Seller",
    description:
      "Consumers & Businesses easily sell scrap from home or office.",
  },
  {
    icon: Bike,
    title: "Pickup Partner",
    description:
      "Our trained partners handle quick, GPS-enabled doorstep pickups.",
  },
  {
    icon: Warehouse,
    title: "Scrap Hub Partner",
    description:
      "Franchise partners segregate and warehouse scrap for processing.",
  },
  {
    icon: Recycle,
    title: "Recycle Partner",
    description:
      "Certified recyclers process materials, completing the circular journey.",
  },
];

export default function EcosystemSection() {
  return (
    <section className="bg-white px-4 py-16">
      <div className="mx-auto max-w-7xl">
        {/* Header */}
        <div className="mb-12 text-center">
          <h2 className="mb-4 text-4xl font-bold text-gray-900">
            The Scraplo Ecosystem
          </h2>
          <p className="mx-auto max-w-3xl text-lg text-gray-600">
            We've built a transparent, traceable network that connects everyone
            from the seller to the recycler, creating a true circular economy.
          </p>
        </div>

        {/* Ecosystem Cards */}
        <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
          {ecosystemData.map((item, index) => (
            <div
              key={index}
              className="relative rounded-lg border border-gray-200 bg-white p-6 text-center transition-shadow duration-300 hover:shadow-lg"
            >
              {/* Icon with teal background */}
              <div className="relative mb-4">
                <div className="mx-auto -mt-8 flex h-16 w-16 items-center justify-center rounded-full border-4 border-white bg-teal-800 shadow-lg">
                  <item.icon className="h-8 w-8 text-white" />
                </div>
              </div>

              {/* Content */}
              <h3 className="mb-3 text-xl font-bold text-gray-900">
                {item.title}
              </h3>
              <p className="leading-relaxed text-gray-600">
                {item.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
