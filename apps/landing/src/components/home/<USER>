import { and, eq } from "drizzle-orm";

import { db } from "@acme/db/client";
import { blogs } from "@acme/db/schema";
import { Badge } from "@acme/ui/components/ui/badge";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@acme/ui/components/ui/carousel";

import BlogCard from "../blog-card";

const BlogsSection = async () => {
  const blogList = await db.query.blogs.findMany({
    where: and(eq(blogs.status, "PUBLISHED"), eq(blogs.isActive, true)),
    orderBy: (blogs, { desc }) => [desc(blogs.publishedAt)],
    limit: 6,
  });

  if (blogList.length === 0) {
    return null;
  }

  return (
    <section className="container mx-auto py-6 md:py-10 xl:py-[70px]">
      <div className="mb-12 text-center">
        <Badge className="mb-4 rounded-full border border-teal-700 bg-transparent text-teal-700">
          LATEST INSIGHTS
        </Badge>
        <h2 className="mb-4 text-3xl font-bold text-gray-900 dark:text-white lg:text-4xl">
          Latest Blog Posts
        </h2>
        <p className="mx-auto max-w-2xl text-gray-600 dark:text-gray-300">
          Stay updated with our latest insights, tutorials, and industry trends
        </p>
      </div>

      <Carousel
        opts={{
          align: "start",
          loop: false,
        }}
        className="w-full"
      >
        <CarouselContent className="-ml-4">
          {blogList.map((blog) => (
            <CarouselItem
              key={blog.id}
              className="pl-4 md:basis-1/2 lg:basis-1/3 xl:basis-1/4"
            >
              <BlogCard blog={blog} />
            </CarouselItem>
          ))}
        </CarouselContent>
      </Carousel>
    </section>
  );
};

export default BlogsSection;
