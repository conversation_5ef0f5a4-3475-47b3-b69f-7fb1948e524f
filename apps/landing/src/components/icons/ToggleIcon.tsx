import React from "react";

interface IconProps {
  width?: number;
  height?: number;
  className?: string;
}

const ToggleIcon: React.FC<IconProps> = ({
  width = 32,
  height = 31,
  className,
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 32 31"
      fill="none"
      className={className}
    >
      <path
        d="M22.25 24.0886H9.75C4.925 24.0886 1 20.1624 1 15.3386C1 10.5149 4.925 6.58862 9.75 6.58862H22.25C27.075 6.58862 31 10.5149 31 15.3386C31 20.1624 27.075 24.0886 22.25 24.0886ZM9.75 7.83862C5.61375 7.83862 2.25 11.2024 2.25 15.3386C2.25 19.4749 5.61375 22.8386 9.75 22.8386H22.25C26.3862 22.8386 29.75 19.4749 29.75 15.3386C29.75 11.2024 26.3862 7.83862 22.25 7.83862H9.75Z"
        fill="#0A0A0A"
        stroke="black"
        strokeWidth="0.4"
      />
      <path
        d="M9.75 20.3386C6.9925 20.3386 4.75 18.0961 4.75 15.3386C4.75 12.5811 6.9925 10.3386 9.75 10.3386C12.5075 10.3386 14.75 12.5811 14.75 15.3386C14.75 18.0961 12.5075 20.3386 9.75 20.3386Z"
        fill="#0A0A0A"
      />
    </svg>
  );
};

export default ToggleIcon;
