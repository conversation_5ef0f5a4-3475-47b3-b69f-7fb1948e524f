import React from "react";

interface IconProps {
  width?: number;
  height?: number;
  className?: string;
}

const CrosshairIcon: React.FC<IconProps> = ({
  width = 30,
  height = 31,
  className,
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 30 31"
      fill="none"
      className={className}
    >
      <g clipPath="url(#clip0_2158_4732)">
        <path
          d="M15 30.3386C6.58875 30.3386 0 23.7499 0 15.3386C0 6.92737 6.58875 0.338623 15 0.338623C16.4303 0.338623 17.8465 0.536904 19.2092 0.9279C19.8313 1.10644 20.1909 1.75542 20.0125 2.37751C19.834 2.9996 19.185 3.35925 18.5629 3.18071C17.4103 2.85007 16.2116 2.68237 15 2.68237C7.90307 2.68237 2.34375 8.24169 2.34375 15.3386C2.34375 22.4356 7.90307 27.9949 15 27.9949C22.0969 27.9949 27.6562 22.4356 27.6562 15.3386C27.6562 14.127 27.4886 12.9283 27.1579 11.7758C26.9794 11.1537 27.339 10.5047 27.9611 10.3262C28.5831 10.1476 29.2322 10.5073 29.4107 11.1294C29.8017 12.4921 30 13.9083 30 15.3386C30 23.7499 23.4113 30.3386 15 30.3386Z"
          fill="#0A0A0A"
        />
        <path
          d="M27.4296 2.90902C26.0589 1.53827 23.8284 1.53833 22.4577 2.90902C21.3775 3.98925 21.1493 5.6031 21.7717 6.90962L16.5148 12.1665C16.0558 11.9465 15.5421 11.823 15 11.823C13.0615 11.823 11.4844 13.4001 11.4844 15.3386C11.4844 17.2771 13.0615 18.8542 15 18.8542C16.9385 18.8542 18.5156 17.2771 18.5156 15.3386C18.5156 14.7966 18.3921 14.2828 18.172 13.8239L23.4289 8.56695C24.7361 9.18962 26.3487 8.96181 27.4296 7.88081C28.8035 6.50697 28.8037 4.2831 27.4296 2.90902ZM15 16.5105C14.3538 16.5105 13.8281 15.9848 13.8281 15.3386C13.8281 14.6925 14.3538 14.1667 15 14.1667C15.6462 14.1667 16.1719 14.6925 16.1719 15.3386C16.1719 15.9848 15.6462 16.5105 15 16.5105ZM25.7724 6.22355C25.3154 6.68046 24.5721 6.68052 24.1151 6.22355C23.6582 5.76663 23.6582 5.02314 24.1151 4.56622C24.5719 4.10931 25.3154 4.10925 25.7724 4.56622C26.2303 5.02425 26.2304 5.76552 25.7724 6.22355Z"
          fill="#0A0A0A"
        />
      </g>
      <defs>
        <clipPath id="clip0_2158_4732">
          <rect
            width="30"
            height="30"
            fill="white"
            transform="translate(0 0.338623)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default CrosshairIcon;
