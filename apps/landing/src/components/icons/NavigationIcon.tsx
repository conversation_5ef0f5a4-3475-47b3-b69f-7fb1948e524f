import React from "react";

interface IconProps {
  width?: number;
  height?: number;
  className?: string;
}

const NavigationIcon: React.FC<IconProps> = ({
  width = 32,
  height = 31,
  className,
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 32 31"
      fill="none"
      className={className}
    >
      <path
        d="M5.40921 20.7728C5.29205 20.7129 5.15943 20.69 5.02896 20.7072C4.8985 20.7245 4.77636 20.781 4.67876 20.8692C4.58117 20.9575 4.51274 21.0734 4.48256 21.2015C4.45238 21.3296 4.46188 21.4638 4.50979 21.5864L5.42585 24.1105L4.50979 26.6346C4.47143 26.7329 4.45758 26.839 4.46944 26.9438C4.4813 27.0486 4.51852 27.149 4.57787 27.2362C4.63722 27.3234 4.71693 27.3948 4.81009 27.4443C4.90325 27.4938 5.00706 27.5199 5.11255 27.5202C5.21605 27.5201 5.31804 27.4953 5.41009 27.448L10.7895 24.6854C10.8948 24.6303 10.9829 24.5474 11.0443 24.4456C11.1057 24.3439 11.1379 24.2272 11.1375 24.1084C11.137 23.9896 11.104 23.8732 11.0419 23.7719C10.9798 23.6706 10.8911 23.5883 10.7854 23.534L5.41009 20.7733L5.40921 20.7728ZM6.70513 23.8748L6.26175 22.6528L9.10026 24.1105L6.26175 25.5682L6.70554 24.3453C6.76473 24.1941 6.76458 24.026 6.70513 23.8748Z"
        fill="#0A0A0A"
        stroke="#0A0A0A"
        strokeWidth="0.2"
      />
      <path
        d="M7.1568 17.9537C5.9391 17.9537 4.74874 18.3148 3.73625 18.9913C2.72376 19.6678 1.93462 20.6294 1.46862 21.7544C1.00261 22.8794 0.880681 24.1173 1.11824 25.3116C1.35579 26.5059 1.94216 27.603 2.80321 28.464C3.66425 29.3251 4.76128 29.9115 5.95559 30.149C7.14989 30.3866 8.38782 30.2647 9.51283 29.7987C10.6378 29.3327 11.5994 28.5436 12.2759 27.5311C12.9524 26.5186 13.3135 25.3282 13.3135 24.1105C13.3116 22.4782 12.6623 20.9134 11.5081 19.7592C10.354 18.605 8.78908 17.9557 7.1568 17.9537ZM7.1568 28.9844C6.19283 28.9844 5.2505 28.6985 4.44899 28.163C3.64747 27.6274 3.02277 26.8662 2.65387 25.9757C2.28497 25.0851 2.18844 24.1051 2.3765 23.1596C2.56456 22.2142 3.02875 21.3457 3.71038 20.6641C4.39201 19.9825 5.26046 19.5183 6.2059 19.3302C7.15135 19.1422 8.13133 19.2387 9.02192 19.6076C9.91251 19.9765 10.6737 20.6012 11.2093 21.4027C11.7448 22.2042 12.0306 23.1466 12.0306 24.1105C12.0292 25.4027 11.5152 26.6415 10.6015 27.5552C9.68779 28.4689 8.44896 28.9829 7.1568 28.9844Z"
        fill="#0A0A0A"
        stroke="#0A0A0A"
        strokeWidth="0.2"
      />
      <path
        d="M30.407 2.96789L30.4046 2.96326C29.5753 1.41597 27.9309 0.43769 26.1133 0.410034H26.1035H26.0938C24.2759 0.43769 22.6314 1.41597 21.7997 2.96789C19.7709 6.8281 23.4403 10.6847 25.412 12.7569L25.4267 12.7718C25.6333 12.9752 25.8594 13.0782 26.099 13.0782H26.1081C26.3476 13.0782 26.5737 12.9752 26.7803 12.7718L26.7949 12.7569C28.7661 10.6849 32.4356 6.8281 30.407 2.96789ZM26.1032 11.6211C23.7619 9.13341 21.5064 6.28324 22.9326 3.56929C23.5404 2.43509 24.7548 1.71685 26.1032 1.69294C27.4509 1.71679 28.6644 2.43421 29.2727 3.56718C30.6985 6.28464 28.4437 9.13423 26.1032 11.6211Z"
        fill="#0A0A0A"
        stroke="#0A0A0A"
        strokeWidth="0.2"
      />
      <path
        d="M26.1032 2.59888C25.5578 2.59888 25.0247 2.7606 24.5712 3.06361C24.1177 3.36661 23.7643 3.79728 23.5555 4.30115C23.3468 4.80503 23.2922 5.35947 23.3986 5.89438C23.505 6.4293 23.7677 6.92064 24.1533 7.30629C24.539 7.69194 25.0303 7.95457 25.5652 8.06097C26.1001 8.16737 26.6546 8.11276 27.1584 7.90405C27.6623 7.69534 28.093 7.3419 28.396 6.88842C28.699 6.43495 28.8607 5.90181 28.8607 5.35642C28.8599 4.62532 28.5691 3.92441 28.0521 3.40745C27.5352 2.89049 26.8343 2.5997 26.1032 2.59888ZM26.1032 6.83099C25.8115 6.831 25.5264 6.74452 25.2839 6.58249C25.0414 6.42046 24.8524 6.19016 24.7408 5.9207C24.6291 5.65124 24.5999 5.35474 24.6568 5.06869C24.7137 4.78263 24.8542 4.51987 25.0604 4.31364C25.2666 4.1074 25.5294 3.96696 25.8155 3.91006C26.1015 3.85316 26.398 3.88237 26.6675 3.99399C26.9369 4.10561 27.1672 4.29462 27.3293 4.53713C27.4913 4.77964 27.5778 5.06476 27.5778 5.35642C27.5773 5.74734 27.4218 6.12211 27.1453 6.39852C26.8689 6.67493 26.4941 6.83042 26.1032 6.83087V6.83099Z"
        fill="#0A0A0A"
        stroke="#0A0A0A"
        strokeWidth="0.2"
      />
      <path
        d="M21.5269 14.6695H10.4541C9.53186 14.6684 8.64767 14.3016 7.99552 13.6495C7.34337 12.9973 6.97653 12.1131 6.97548 11.1909V11.0333C6.97652 10.111 7.34335 9.22676 7.99549 8.5746C8.64764 7.92245 9.53185 7.5556 10.4541 7.55455H11.4782C11.563 7.55536 11.6471 7.53936 11.7256 7.50749C11.8041 7.47562 11.8756 7.4285 11.9358 7.36885C11.996 7.30921 12.0438 7.23822 12.0764 7.15999C12.109 7.08176 12.1258 6.99785 12.1258 6.91309C12.1258 6.82833 12.109 6.74442 12.0764 6.66619C12.0438 6.58796 11.996 6.51698 11.9358 6.45733C11.8756 6.39769 11.8041 6.35056 11.7256 6.31869C11.6471 6.28682 11.563 6.27083 11.4782 6.27164H10.4541C9.19171 6.27306 7.98141 6.77519 7.08875 7.66786C6.19609 8.56053 5.69398 9.77084 5.69257 11.0333V11.1909C5.69399 12.4533 6.19611 13.6636 7.08877 14.5562C7.98143 15.4489 9.19172 15.951 10.4541 15.9524H21.5269C22.4495 15.9524 23.3343 16.3189 23.9867 16.9713C24.639 17.6237 25.0055 18.5085 25.0055 19.4311C25.0055 20.3537 24.639 21.2385 23.9867 21.8909C23.3343 22.5432 22.4495 22.9097 21.5269 22.9097H15.6389C15.5542 22.9089 15.4701 22.9249 15.3916 22.9568C15.313 22.9887 15.2416 23.0358 15.1814 23.0954C15.1211 23.1551 15.0733 23.2261 15.0407 23.3043C15.0081 23.3825 14.9913 23.4664 14.9913 23.5512C14.9913 23.636 15.0081 23.7199 15.0407 23.7981C15.0733 23.8763 15.1211 23.9473 15.1814 24.007C15.2416 24.0666 15.313 24.1137 15.3916 24.1456C15.4701 24.1775 15.5542 24.1935 15.6389 24.1927H21.5269C22.7868 24.1882 23.9937 23.6846 24.8831 22.7921C25.7724 21.8996 26.2718 20.691 26.2718 19.4311C26.2718 18.1711 25.7724 16.9625 24.8831 16.07C23.9937 15.1775 22.7868 14.6739 21.5269 14.6695Z"
        fill="#0A0A0A"
        stroke="#0A0A0A"
        strokeWidth="0.2"
      />
      <path
        d="M13.8223 7.54565H15.7559C15.926 7.54565 16.0892 7.47806 16.2095 7.35773C16.3299 7.23741 16.3975 7.07422 16.3975 6.90405C16.3975 6.73389 16.3299 6.5707 16.2095 6.45037C16.0892 6.33005 15.926 6.26245 15.7559 6.26245H13.8223C13.6521 6.26245 13.4889 6.33005 13.3686 6.45037C13.2483 6.5707 13.1807 6.73389 13.1807 6.90405C13.1807 7.07422 13.2483 7.23741 13.3686 7.35773C13.4889 7.47806 13.6521 7.54565 13.8223 7.54565Z"
        fill="#0A0A0A"
        stroke="#0A0A0A"
        strokeWidth="0.4"
      />
      <path
        d="M18.0414 7.54565H19.975C20.1452 7.54565 20.3084 7.47806 20.4287 7.35773C20.549 7.23741 20.6166 7.07422 20.6166 6.90405C20.6166 6.73389 20.549 6.5707 20.4287 6.45037C20.3084 6.33005 20.1452 6.26245 19.975 6.26245H18.0414C17.8713 6.26245 17.7081 6.33005 17.5878 6.45037C17.4674 6.5707 17.3998 6.73389 17.3998 6.90405C17.3998 7.07422 17.4674 7.23741 17.5878 7.35773C17.7081 7.47806 17.8713 7.54565 18.0414 7.54565Z"
        fill="#0A0A0A"
        stroke="#0A0A0A"
        strokeWidth="0.4"
      />
    </svg>
  );
};

export default NavigationIcon;
