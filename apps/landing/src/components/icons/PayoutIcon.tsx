import React from "react";

interface IconProps {
  width?: number;
  height?: number;
  className?: string;
}

const PayoutIcon: React.FC<IconProps> = ({
  width = 30,
  height = 31,
  className,
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 30 31"
      fill="none"
      className={className}
    >
      <g clipPath="url(#clip0_2158_4751)">
        <path
          d="M17.1487 8.49465C16.2033 8.49465 16.2033 7.05677 17.1487 7.05677H24.8593C25.7402 7.05677 26.5406 7.41677 27.1203 7.99646C27.7 8.57615 28.0597 9.37659 28.0597 10.2575V23.236C29.0072 23.5457 29.6666 24.4411 29.6666 25.4477V27.4919C29.6666 28.7725 28.6216 29.8177 27.3411 29.8177H2.65893C1.38155 29.8177 0.333435 28.7693 0.333435 27.4919V25.4477C0.333435 24.4407 0.99285 23.5474 1.93998 23.2367V10.2291C1.93998 8.48261 3.36617 7.05677 5.11268 7.05677H12.3294C13.2748 7.05677 13.2748 8.49465 12.3294 8.49465H5.11268C4.16024 8.49465 3.37787 9.27667 3.37787 10.2291V23.1219H26.6221V10.2575C26.6221 9.77344 26.4234 9.33265 26.1038 9.01304C25.7838 8.69344 25.3434 8.49465 24.8593 8.49465H17.1487ZM18.9228 24.5598V26.6833C18.9228 27.0802 18.6007 27.4022 18.2039 27.4022H11.7961C11.3989 27.4022 11.0772 27.0802 11.0772 26.6833V24.5598H2.65893C2.16888 24.5598 1.77132 24.9577 1.77132 25.4477V27.4919C1.77132 27.9819 2.16888 28.3799 2.65893 28.3799H27.3411C27.8276 28.3799 28.2287 27.9784 28.2287 27.4919V25.4477C28.2287 24.9588 27.8301 24.5598 27.3411 24.5598H18.9228ZM12.5151 24.5598V25.9644H17.4849V24.5598H12.5151ZM20.4886 10.3599C20.6165 10.1621 20.8391 10.0314 21.0921 10.0314C21.3454 10.0314 21.5683 10.1625 21.6962 10.3606L24.4139 13.7615C24.6606 14.0701 24.6102 14.5205 24.3016 14.7671C23.993 15.0137 23.5426 14.9634 23.296 14.6547L21.811 12.7963V21.0377C21.811 21.9834 20.3731 21.9834 20.3731 21.0377V12.7959L18.8881 14.6547C18.6415 14.9634 18.1915 15.0137 17.8829 14.7671C17.5742 14.5205 17.5239 14.0701 17.7705 13.7615L20.4886 10.3599ZM14.2446 1.18617C14.5266 0.750695 15.1715 0.751049 15.4528 1.18653L18.1702 4.58742C18.757 5.3216 17.6391 6.21487 17.0527 5.48069L15.5676 3.62221V9.37624C15.5676 10.322 14.1298 10.322 14.1298 9.37624V3.62186L12.6448 5.48069C12.058 6.21487 10.9401 5.3216 11.5268 4.58742L14.2446 1.18617ZM14.1298 12.3062C14.1298 11.3605 15.5676 11.3605 15.5676 12.3062V14.8209C15.5676 15.7666 14.1298 15.7666 14.1298 14.8209V12.3062ZM9.72008 20.1175V20.3818C9.72008 21.3275 8.2822 21.3275 8.2822 20.3818V20.1175C7.12388 19.8404 6.17639 18.878 6.17639 17.6418C6.17639 16.6964 7.61428 16.6964 7.61428 17.6418C7.61428 18.3543 8.35909 18.7639 9.00114 18.7639C10.832 18.7639 10.8288 16.52 9.00149 16.52C5.71647 16.52 5.10524 12.2446 8.2822 11.4845V11.2198C8.2822 10.2745 9.72008 10.2745 9.72008 11.2198V11.4845C10.8788 11.7616 11.8262 12.7236 11.8262 13.9599C11.8262 14.9056 10.3884 14.9056 10.3884 13.9599C10.3884 13.2491 9.64674 12.8395 9.0061 12.8377C7.16817 12.8377 7.17491 15.0821 9.00149 15.0821C12.2858 15.0821 12.8977 19.3575 9.72008 20.1175Z"
          fill="#0A0A0A"
        />
      </g>
      <defs>
        <clipPath id="clip0_2158_4751">
          <rect
            width="30"
            height="30"
            fill="white"
            transform="translate(0 0.338623)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default PayoutIcon;
