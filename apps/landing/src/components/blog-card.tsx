import Image from "next/image";
import Link from "next/link";
import { ArrowUpRight, Calendar } from "lucide-react";

import type { blogs } from "@acme/db/schema";
import { Badge } from "@acme/ui/components/ui/badge";

interface BlogCardProps {
  blog: typeof blogs.$inferSelect;
}

const BlogCard = ({ blog }: BlogCardProps) => {
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat("en-US", {
      day: "numeric",
      month: "short",
      year: "numeric",
    }).format(date);
  };

  const tags = blog.tags ?? [];

  return (
    <Link href={`/blogs/${blog.slug}`} className="group block max-w-[360px]">
      <article className="h-full overflow-hidden rounded-2xl border border-gray-100 bg-white shadow-sm transition-all duration-200 hover:border-gray-200 hover:shadow-md">
        {/* Image */}
        <div className="aspect-video w-full overflow-hidden">
          <Image
            src={blog.banner}
            alt={blog.title}
            width={600}
            height={340}
            className="h-full w-full object-cover transition-transform duration-200 group-hover:scale-105"
          />
        </div>

        {/* Content */}
        <div className="flex flex-col justify-between p-6">
          <div>
            {/* Tags */}
            {tags.length > 0 && (
              <div className="mb-3 flex flex-wrap gap-2">
                {tags.slice(0, 3).map((tag) => (
                  <Badge
                    key={tag}
                    variant="outline"
                    className="rounded-full border-blue-200 bg-blue-50 px-3 py-1 text-xs font-medium text-blue-700"
                  >
                    {tag}
                  </Badge>
                ))}
              </div>
            )}
            {/* Title */}
            <h3 className="mb-3 line-clamp-4 h-[9ch] text-xl font-bold leading-tight text-gray-900 transition-colors group-hover:text-blue-600">
              {blog.title}
            </h3>

            {/* Excerpt */}
            {blog.excerpt && (
              <p className="mb-4 line-clamp-2 text-sm leading-relaxed text-gray-600">
                {blog.excerpt}
              </p>
            )}
          </div>

          {/* Footer with Date and Arrow */}
          <div className="flex items-center justify-between">
            {blog.publishedAt && (
              <div className="flex items-center text-xs text-gray-500">
                <Calendar className="mr-1 h-3 w-3" />
                <time dateTime={blog.publishedAt.toISOString()}>
                  {formatDate(blog.publishedAt)}
                </time>
              </div>
            )}
            <ArrowUpRight className="h-4 w-4 text-gray-400 transition-colors group-hover:text-blue-600" />
          </div>
        </div>
      </article>
    </Link>
  );
};

export default BlogCard;
