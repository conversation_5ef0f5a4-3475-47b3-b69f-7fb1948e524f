"use client";

import Image from "next/image";
import { Calendar, Clock, Tag } from "lucide-react";

import { Editor } from "@acme/ui/editor";

import { ShareButton } from "./share-button";

interface Blog {
  id: string;
  title: string;
  slug: string;
  excerpt: string | null;
  content: string;
  banner: string;
  status: "DRAFT" | "PUBLISHED" | "ARCHIVED" | null;
  isActive: boolean | null;
  isFeatured: boolean | null;
  tags: string[] | null;
  publishedAt: Date | null;
  createdAt: Date;
  updatedAt: Date | null;
}

interface BlogDetailProps {
  blog: Blog;
}

const BlogDetail = ({ blog }: BlogDetailProps) => {
  const formatDate = (date: Date | null) => {
    if (!date) return "";
    return new Intl.DateTimeFormat("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    }).format(date);
  };

  const estimatedReadTime = Math.ceil(blog.content.split(" ").length / 200);

  return (
    <>
      <article className="w-full">
        {/* Header Section */}
        <header className="mb-8">
          <div className="mb-4 flex items-center gap-2 text-sm text-gray-600">
            {blog.isFeatured && (
              <span className="rounded-full bg-yellow-100 px-2 py-1 text-xs font-medium text-yellow-800">
                Featured
              </span>
            )}
            <span className="flex items-center gap-1">
              <Calendar size={14} />
              {formatDate(blog.publishedAt)}
            </span>
            <span className="flex items-center gap-1">
              <Clock size={14} />
              {estimatedReadTime} min read
            </span>
          </div>

          <h1 className="mb-4 text-4xl font-bold leading-tight text-gray-900 md:text-5xl">
            {blog.title}
          </h1>

          {blog.excerpt && (
            <p className="mb-6 text-xl leading-relaxed text-gray-600">
              {blog.excerpt}
            </p>
          )}

          {/* Tags */}
          {blog.tags && blog.tags.length > 0 && (
            <div className="mb-6 flex flex-wrap gap-2">
              {blog.tags.map((tag, index) => (
                <span
                  key={index}
                  className="inline-flex items-center gap-1 rounded-full bg-blue-50 px-3 py-1 text-sm font-medium text-blue-700"
                >
                  <Tag size={12} />
                  {tag}
                </span>
              ))}
            </div>
          )}

          {/* Action Buttons */}
          <ShareButton blog={blog} />
        </header>

        {/* Banner Image */}
        <div className="relative mb-8 h-64 w-full overflow-hidden rounded-xl md:h-96">
          <Image
            src={blog.banner}
            alt={blog.title}
            fill
            className="object-cover"
            priority
          />
        </div>

        {/* Content */}
        <div className="prose prose-lg max-w-none">
          <Editor
            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
            editorSerializedState={JSON.parse(blog.content)}
            editable={false}
          />
        </div>
      </article>
    </>
  );
};

export default BlogDetail;
