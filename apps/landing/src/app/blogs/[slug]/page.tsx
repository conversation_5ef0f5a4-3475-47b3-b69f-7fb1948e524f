import type { Metadata } from "next";
import { notFound } from "next/navigation";

import { eq } from "@acme/db";
import { db } from "@acme/db/client";
import { blogs } from "@acme/db/schema";

import FooterSection from "~/components/home/<USER>";
import Navbar from "~/components/home/<USER>";
import { env } from "~/env";
import BlogDetail from "./blog-detail";
import RelatedBlogs from "./related-blogs";

interface BlogDetailPageProps {
  params: Promise<{ slug: string }>;
}

export const revalidate = 600; // 10 minutes

export async function generateMetadata({
  params,
}: BlogDetailPageProps): Promise<Metadata> {
  const slug = (await params).slug;
  const blog = await db.query.blogs.findFirst({
    where: eq(blogs.slug, slug),
  });

  if (!blog) {
    return {
      title: "Blog Not Found",
      description: "The requested blog post could not be found.",
    };
  }

  const baseUrl = env.NEXT_PUBLIC_LANDING_PAGE_BASE_URL;
  const blogUrl = `${baseUrl}/blogs/${blog.slug}`;

  return {
    title: blog.title,
    description:
      blog.excerpt ??
      `Read ${blog.title} - A comprehensive blog post covering important topics.`,
    keywords: blog.tags?.join(", ") ?? "",
    authors: [{ name: "Scraplo" }],
    creator: "Scraplo",
    publisher: "Scraplo",

    openGraph: {
      type: "article",
      url: blogUrl,
      title: blog.title,
      description:
        blog.excerpt ??
        `Read ${blog.title} - A comprehensive blog post covering important topics.`,
      images: [
        {
          url: blog.banner,
          width: 1200,
          height: 630,
          alt: blog.title,
        },
      ],
      publishedTime: blog.publishedAt?.toISOString(),
      modifiedTime: blog.updatedAt?.toISOString(),
      tags: blog.tags ?? [],
    },

    twitter: {
      card: "summary_large_image",
      title: blog.title,
      description:
        blog.excerpt ??
        `Read ${blog.title} - A comprehensive blog post covering important topics.`,
      images: [blog.banner],
      creator: "@scraplo",
    },

    alternates: {
      canonical: blogUrl,
    },
    robots: {
      index: blog.status === "PUBLISHED",
      follow: blog.status === "PUBLISHED",
    },
  };
}

export const generateStaticParams = async () => {
  const publishedBlogs = await db.query.blogs.findMany({
    where: eq(blogs.status, "PUBLISHED"),
  });
  return publishedBlogs.map((blog) => ({ slug: blog.slug }));
};

const BlogDetailPage = async ({ params }: BlogDetailPageProps) => {
  const slug = (await params).slug;
  const blog = await db.query.blogs.findFirst({
    where: eq(blogs.slug, slug),
  });

  if (!blog || blog.status !== "PUBLISHED" || !blog.isActive) {
    notFound();
  }

  return (
    <>
      <div className="h-full w-full bg-[url('/hero.webp')] bg-cover bg-center bg-no-repeat">
        <Navbar />
      </div>
      <div className="mx-auto flex w-full min-w-full flex-col px-4 py-8 lg:container lg:min-h-screen lg:flex-row lg:gap-8">
        <div className="w-full lg:w-2/3">
          <BlogDetail blog={blog} />
        </div>
        <div className="mt-8 w-full lg:mt-0 lg:w-1/3">
          <RelatedBlogs
            tags={blog.tags}
            currentBlogId={blog.id}
            currentBlogTitle={blog.title}
          />
        </div>
      </div>
      <FooterSection />
    </>
  );
};

export default BlogDetailPage;
