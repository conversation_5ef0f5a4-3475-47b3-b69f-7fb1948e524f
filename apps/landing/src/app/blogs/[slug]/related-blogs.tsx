// import { arrayOverlaps } from "drizzle-orm";

import { and, desc, eq, ne } from "drizzle-orm";

import { db } from "@acme/db/client";
import { blogs } from "@acme/db/schema";

import BlogCard from "~/components/blog-card";

const RelatedBlogs = async ({
  currentBlogId,
  // tags,
}: {
  tags: string[] | null;
  currentBlogId?: string;
  currentBlogTitle: string;
}) => {
  const relatedBlogs = await db.query.blogs.findMany({
    where: and(
      eq(blogs.status, "PUBLISHED"),
      eq(blogs.isActive, true),
      currentBlogId ? ne(blogs.id, currentBlogId) : undefined,
      // tags && tags.length > 0 ? arrayContains(blogs.tags, tags) : undefined,
    ),
    orderBy: desc(blogs.publishedAt),
    limit: 6,
  });

  return (
    <aside className="-px-[12px] h-full w-full gap-4">
      <h2 className="mb-4 text-center text-xl font-semibold text-gray-900 lg:text-right lg:text-3xl">
        Related Blogs
      </h2>
      <div className="hide-scrollbar flex h-full flex-row flex-wrap items-center justify-center gap-2 lg:max-h-[2200px] lg:flex-col lg:flex-nowrap lg:items-end lg:justify-normal lg:gap-4 lg:overflow-y-auto">
        {relatedBlogs.length === 0 ? (
          <div className="text-gray-500">No related blogs found.</div>
        ) : (
          relatedBlogs.map((blog) => <BlogCard key={blog.id} blog={blog} />)
        )}
      </div>
    </aside>
  );
};

export default RelatedBlogs;
