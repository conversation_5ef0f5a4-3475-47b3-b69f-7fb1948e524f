"use client";

import { useState } from "react";
import Link from "next/link";
import { Check, Copy, Share2 } from "lucide-react";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Content,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>T<PERSON>le,
  DialogTrigger,
} from "@acme/ui/components/ui/dialog";

interface Blog {
  id: string;
  title: string;
  slug: string;
  excerpt: string | null;
  content: string;
  banner: string;
  status: "DRAFT" | "PUBLISHED" | "ARCHIVED" | null;
  isActive: boolean | null;
  isFeatured: boolean | null;
  tags: string[] | null;
  publishedAt: Date | null;
  createdAt: Date;
  updatedAt: Date | null;
}

interface BlogDetailProps {
  blog: Blog;
}
export function ShareButton({ blog }: BlogDetailProps) {
  const [showShareDialog, setShowShareDialog] = useState(false);
  const [copied, setCopied] = useState(false);
  if (typeof window === "undefined") {
    return null;
  }

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(window.location.href);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.log("Failed to copy:", err);
    }
  };

  const shareUrls = {
    twitter: `https://twitter.com/intent/tweet?text=${encodeURIComponent(
      blog.title,
    )}&url=${encodeURIComponent(window.location.href)}`,
    facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(
      window.location.href,
    )}`,
    linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(
      window.location.href,
    )}`,
    gmail: `mailto:?subject=${encodeURIComponent(blog.title)}&body=${encodeURIComponent(`Check out this article: ${blog.title}\n\n${blog.excerpt ?? ""}\n\n${window.location.href}`)}`,
    whatsapp: `https://wa.me/?text=${encodeURIComponent(`${blog.title} ${window.location.href}`)}`,
    telegram: `https://t.me/share/url?url=${encodeURIComponent(window.location.href)}&text=${encodeURIComponent(blog.title)}`,
    reddit: `https://reddit.com/submit?url=${encodeURIComponent(window.location.href)}&title=${encodeURIComponent(blog.title)}`,
    pinterest: `https://pinterest.com/pin/create/button/?url=${encodeURIComponent(window.location.href)}&description=${encodeURIComponent(blog.title)}`,
  };
  return (
    <div className="mb-8 flex items-center gap-4">
      <Dialog open={showShareDialog} onOpenChange={setShowShareDialog}>
        <DialogTrigger asChild>
          <button className="flex items-center gap-2 rounded-lg border border-gray-200 bg-white px-4 py-2 text-gray-700 transition-colors hover:bg-gray-50">
            <Share2 size={16} />
            Share
          </button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Share this article</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            {/* Social Media Links */}
            <div className="grid grid-cols-2 gap-3">
              <Link
                href={shareUrls.twitter}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-3 rounded-lg border border-gray-200 p-3 transition-colors hover:bg-gray-50"
              >
                <div className="flex h-6 w-6 items-center justify-center rounded bg-blue-500">
                  <span className="text-xs font-bold text-white">T</span>
                </div>
                <span className="text-sm font-medium">Twitter</span>
              </Link>

              <Link
                href={shareUrls.facebook}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-3 rounded-lg border border-gray-200 p-3 transition-colors hover:bg-gray-50"
              >
                <div className="flex h-6 w-6 items-center justify-center rounded bg-blue-600">
                  <span className="text-xs font-bold text-white">f</span>
                </div>
                <span className="text-sm font-medium">Facebook</span>
              </Link>

              <Link
                href={shareUrls.linkedin}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-3 rounded-lg border border-gray-200 p-3 transition-colors hover:bg-gray-50"
              >
                <div className="flex h-6 w-6 items-center justify-center rounded bg-blue-700">
                  <span className="text-xs font-bold text-white">in</span>
                </div>
                <span className="text-sm font-medium">LinkedIn</span>
              </Link>

              <Link
                href={shareUrls.gmail}
                className="flex items-center gap-3 rounded-lg border border-gray-200 p-3 transition-colors hover:bg-gray-50"
              >
                <div className="flex h-6 w-6 items-center justify-center rounded bg-red-500">
                  <span className="text-xs font-bold text-white">@</span>
                </div>
                <span className="text-sm font-medium">Gmail</span>
              </Link>

              <Link
                href={shareUrls.whatsapp}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-3 rounded-lg border border-gray-200 p-3 transition-colors hover:bg-gray-50"
              >
                <div className="flex h-6 w-6 items-center justify-center rounded bg-green-500">
                  <span className="text-xs font-bold text-white">W</span>
                </div>
                <span className="text-sm font-medium">WhatsApp</span>
              </Link>

              <Link
                href={shareUrls.telegram}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-3 rounded-lg border border-gray-200 p-3 transition-colors hover:bg-gray-50"
              >
                <div className="flex h-6 w-6 items-center justify-center rounded bg-blue-400">
                  <span className="text-xs font-bold text-white">Tg</span>
                </div>
                <span className="text-sm font-medium">Telegram</span>
              </Link>

              <Link
                href={shareUrls.reddit}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-3 rounded-lg border border-gray-200 p-3 transition-colors hover:bg-gray-50"
              >
                <div className="flex h-6 w-6 items-center justify-center rounded bg-orange-500">
                  <span className="text-xs font-bold text-white">R</span>
                </div>
                <span className="text-sm font-medium">Reddit</span>
              </Link>

              <Link
                href={shareUrls.pinterest}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-3 rounded-lg border border-gray-200 p-3 transition-colors hover:bg-gray-50"
              >
                <div className="flex h-6 w-6 items-center justify-center rounded bg-red-600">
                  <span className="text-xs font-bold text-white">P</span>
                </div>
                <span className="text-sm font-medium">Pinterest</span>
              </Link>
            </div>

            {/* Copy URL */}
            <div className="border-t pt-4">
              <div className="flex items-center gap-2 rounded-lg border border-gray-200 p-3">
                <input
                  type="text"
                  value={
                    typeof window !== "undefined" ? window.location.href : ""
                  }
                  readOnly
                  className="flex-1 bg-transparent text-sm text-gray-600 outline-none"
                />
                <button
                  onClick={copyToClipboard}
                  className="flex items-center gap-1 rounded bg-gray-100 px-3 py-1 text-sm font-medium text-gray-700 transition-colors hover:bg-gray-200"
                >
                  {copied ? (
                    <>
                      <Check size={14} />
                      Copied!
                    </>
                  ) : (
                    <>
                      <Copy size={14} />
                      Copy
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
