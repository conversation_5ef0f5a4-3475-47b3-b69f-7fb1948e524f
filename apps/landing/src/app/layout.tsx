import type { Metada<PERSON> } from "next";
import { Inter, Plus_Jakarta_Sans } from "next/font/google";

import "./globals.css";

export const metadata: Metadata = {
  title: "Scraplo - Kabadiwala",
  description: "Scraplo - Kabadiwala",
  generator: "<PERSON><PERSON><PERSON>",
  icons: [{ rel: "icon", url: "/favicon.png" }],
};

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});
const jakarta = Plus_Jakarta_Sans({
  subsets: ["latin"],
  variable: "--font-jakarta",
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        {/* eslint-disable-next-line @next/next/no-page-custom-font */}
        <link
          rel="stylesheet"
          as="style"
          // onLoad="this.rel='stylesheet'"
          href="https://fonts.googleapis.com/css2?display=swap&amp;family=Manrope%3Awght%40400%3B500%3B700%3B800&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900"
        />
      </head>
      <body className={`${inter.variable} ${jakarta.variable}`}>
        {children}
      </body>
    </html>
  );
}
