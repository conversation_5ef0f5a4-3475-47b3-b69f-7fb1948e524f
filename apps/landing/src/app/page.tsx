import { eq } from "@acme/db";
import { db } from "@acme/db/client";
import { answer, question } from "@acme/db/schema";

import BenefitsSection from "~/components/home/<USER>";
import BlogsSection from "~/components/home/<USER>";
import EarnMoreCTA from "~/components/home/<USER>";
import FAQSection from "~/components/home/<USER>";
import FeedBackSection from "~/components/home/<USER>";
import FooterSection from "~/components/home/<USER>";
import GetStarted from "~/components/home/<USER>";
import HeroSection from "~/components/home/<USER>";
import ReadyCTASection from "~/components/home/<USER>";
import Navbar from "~/components/home/<USER>";

export const revalidate = 600;

export default async function HomePage() {
  const sellerQuestions = await db.query.question.findMany({
    where: eq(question.questionFor, "SELLER"),
  });
  const sellerAnswersMap: Record<string, string[]> = {};
  for (const q of sellerQuestions) {
    // Find answers for this question
    const answers = await db.query.answer.findMany({
      where: eq(answer.questionId, q.id),
    });
    sellerAnswersMap[q.id] = answers.map((a) => a.content);
  }
  const kabadiwalaQuestions = await db.query.question.findMany({
    where: eq(question.questionFor, "KABADIWALA"),
  });
  const kabadiwalaAnswersMap: Record<string, string[]> = {};
  for (const q of kabadiwalaQuestions) {
    // Find answers for this question
    const answers = await db.query.answer.findMany({
      where: eq(answer.questionId, q.id),
    });
    kabadiwalaAnswersMap[q.id] = answers.map((a) => a.content);
  }
  return (
    <div>
      {/* Hero section with background, sticky navbar overlays hero bg */}
      <div className="h-full w-full bg-[url('/hero.webp')] bg-cover bg-center bg-no-repeat">
        <div className="sticky top-0 z-20">
          <Navbar />
        </div>
        <HeroSection />
      </div>
      {/* <EcosystemSection /> */}
      <BenefitsSection />
      <GetStarted />
      <EarnMoreCTA />
      <BlogsSection />
      <FeedBackSection />
      <FAQSection
        sellerQuestions={[...sellerQuestions].sort((a, b) => {
          const orderA = Number(a.order);
          const orderB = Number(b.order);
          return orderA - orderB;
        })}
        sellerAnswers={sellerAnswersMap}
        kabadiwalaQuestions={[...kabadiwalaQuestions].sort((a, b) => {
          const orderA = Number(a.order);
          const orderB = Number(b.order);
          return orderA - orderB;
        })}
        kabadiwalaAnswers={kabadiwalaAnswersMap}
      />
      <ReadyCTASection />
      <FooterSection />
    </div>
  );
}
