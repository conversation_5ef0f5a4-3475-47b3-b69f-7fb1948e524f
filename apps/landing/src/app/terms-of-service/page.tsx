import { Suspense } from "react";
import { notFound } from "next/navigation";
import { format } from "date-fns";

import { eq } from "@acme/db";
import { db } from "@acme/db/client";
import { systemConfiguration } from "@acme/db/schema";
import { Editor } from "@acme/ui/editor";

import FooterSection from "~/components/home/<USER>";
import Navbar from "~/components/home/<USER>";
import { ClockIcon } from "~/components/icons/ClockIcon";

export const revalidate = 600;

const TermsAndConditionsPage = async () => {
  const termsAndConditions = await db.query.systemConfiguration.findFirst({
    where: eq(systemConfiguration.key, "TERMS_AND_CONDITIONS"),
  });

  if (!termsAndConditions) {
    return notFound();
  }

  return (
    <Suspense>
      <div className="h-full w-full bg-[url('/hero.webp')] bg-cover bg-center bg-no-repeat">
        <Navbar />
      </div>
      <div className="flex flex-col gap-6 px-8 py-9 md:px-14 xl:px-[120px]">
        <div className="flex flex-col items-center">
          <h1 className="mb-2 text-center text-3xl font-bold text-teal-800">
            Terms of Service
          </h1>
          <div className="flex items-center gap-1 text-sm text-gray-600">
            <ClockIcon className="h-4 w-4" />
            <span>
              Last updated:{" "}
              {format(
                new Date(termsAndConditions.updatedAt ?? ""),
                "MMM d, yyyy",
              )}
            </span>
          </div>
        </div>
        <Editor
          editorSerializedState={termsAndConditions?.value ?? ""}
          editable={false}
        />
      </div>
      <FooterSection />
    </Suspense>
  );
};

export default TermsAndConditionsPage;
