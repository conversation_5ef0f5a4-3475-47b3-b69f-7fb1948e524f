import { Suspense } from "react";

import { eq } from "@acme/db";
import { db } from "@acme/db/client";
import { systemConfiguration } from "@acme/db/schema";
import { Editor } from "@acme/ui/editor";

import FooterSection from "~/components/home/<USER>";
import Navbar from "~/components/home/<USER>";

export const revalidate = 600;

const AboutUsPage = async () => {
  const aboutUs = await db.query.systemConfiguration.findFirst({
    where: eq(systemConfiguration.key, "ABOUT_US"),
  });

  return (
    <Suspense>
      <div className="h-full w-full bg-[url('/hero.webp')] bg-cover bg-center bg-no-repeat">
        <Navbar />
      </div>
      <div className="flex flex-col gap-6 px-8 py-9 md:px-14 xl:px-[120px]">
        <Editor editorSerializedState={aboutUs?.value ?? ""} editable={false} />
      </div>
      <FooterSection />
    </Suspense>
  );
};

export default AboutUsPage;
