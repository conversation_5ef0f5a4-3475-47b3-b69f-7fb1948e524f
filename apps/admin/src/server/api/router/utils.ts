import type { TRPCRouterRecord } from "@trpc/server";
import { TRPCError } from "@trpc/server";
import { z } from "zod";

import { tryCatch } from "@acme/validators/utils";

import { utapi } from "~/lib/utils";
import { protectedProcedure } from "../trpc";

// This router is used to handle utility functions that are not specific to any particular feature
export const utilsRouter = {
  deleteUploadthingFileUsingFileKey: protectedProcedure
    .input(z.object({ fileKey: z.string() }))
    .mutation(async ({ input }) => {
      const { err } = await tryCatch(utapi.deleteFiles(input.fileKey));

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to delete file",
        });
      }
      return {
        success: true,
        message: "File deleted successfully",
      };
    }),
} satisfies TRPCRouterRecord;
