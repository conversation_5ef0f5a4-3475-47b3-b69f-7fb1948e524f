import type { TRPCRouterRecord } from "@trpc/server";
import { TRPCError } from "@trpc/server";
import { desc, eq, inArray } from "drizzle-orm";
import { z } from "zod";

import {
  address,
  kabadiwala,
  order,
  orderStatusEnum,
  seller,
} from "@acme/db/schema";
import { tryCatch } from "@acme/validators/utils";

import { protectedProcedure } from "../trpc";

export const orderRouter = {
  getAllOrders: protectedProcedure
    .input(
      z
        .object({
          status: z.enum(orderStatusEnum.enumValues).optional(),
        })
        .optional(),
    )
    .query(async ({ ctx, input }) => {
      const statusesToFetch: (typeof orderStatusEnum.enumValues)[number][] =
        input?.status
          ? [input.status]
          : ["ACTIVE", "PENDING", "COMPLETED", "CANCELLED"];

      if (input?.status === "CART") {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "CART status is not allowed for this query.",
        });
      }

      const { data, err } = await tryCatch(
        ctx.db
          .select({
            id: order.id,
            status: order.status,
            totalAmount: order.totalAmount,
            createdAt: order.createdAt,
            sellerName: seller.fullName,
            kabadiwalaName: kabadiwala.name,
            addressDisplay: address.display,
            pickupOtpStatus: order.pickupOtpStatus,
            completedAt: order.completedAt,
          })
          .from(order)
          .leftJoin(seller, eq(order.sellerId, seller.id))
          .leftJoin(kabadiwala, eq(order.kabadiwalaId, kabadiwala.id))
          .leftJoin(address, eq(order.addressId, address.id))
          .where(inArray(order.status, statusesToFetch))
          .orderBy(desc(order.createdAt)),
      );

      if (err) {
        console.error("Failed to fetch orders:", err);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch orders",
        });
      }

      return data;
    }),

  getOrderById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const { data, err } = await tryCatch(
        ctx.db.query.order.findFirst({
          where: eq(order.id, input.id),
          with: {
            address: true,
            kabadiwala: {
              columns: {
                id: true,
                name: true,
                image: true,
                isOnDuty: true,
                email: true,
                phoneNumber: true,
                emailVerified: true,
                phoneNumberVerified: true,
                liveLocationCoordinate: true,
                walletBalance: true,
              },
            },
            seller: {
              columns: {
                id: true,
                fullName: true,
                image: true,
                email: true,
                phoneNumber: true,
                emailVerified: true,
                phoneNumberVerified: true,
              },
            },
            items: {
              with: {
                category: true,
              },
            },
          },
        }),
      );

      if (err || !data) {
        console.error("Failed to fetch order:", err);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch order",
        });
      }

      return data;
    }),
} satisfies TRPCRouterRecord;
