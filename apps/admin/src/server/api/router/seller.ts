import type { TRPCRouterRecord } from "@trpc/server";
import { TRPCError } from "@trpc/server";
import { and, eq, not } from "drizzle-orm"; // Import 'not'
import { z } from "zod";

import { seller, sellerAccount } from "@acme/db/schema";
import { SellerCRUDSchema } from "@acme/validators";
import { tryCatch } from "@acme/validators/utils";

import { protectedProcedure } from "../trpc";

export const sellerRouter = {
  getAllSellers: protectedProcedure.query(async ({ ctx }) => {
    const { data, err } = await tryCatch(
      ctx.db
        .select({
          id: seller.id,
          fullName: seller.fullName,
          email: seller.email,
          emailVerified: seller.emailVerified,
          image: seller.image,
          phoneNumber: seller.phoneNumber,
          phoneNumberVerified: seller.phoneNumberVerified,
          onBoardingStep: seller.onBoardingStep,
          createdAt: seller.createdAt,
          isBlocked: seller.isBlocked,
          reason: seller.reason,
        })
        .from(seller),
    );

    if (err) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch sellers",
      });
    }

    return data;
  }),

  getSellerById: protectedProcedure
    .input(
      z.object({
        sellerId: z.string().nonempty({ message: "Seller ID is required" }),
      }),
    )
    .query(async ({ ctx, input }) => {
      const { data, err } = await tryCatch(
        ctx.db
          .select({
            id: seller.id,
            fullName: seller.fullName,
            image: seller.image,
            phoneNumber: seller.phoneNumber,
            phoneNumberVerified: seller.phoneNumberVerified,
            createdAt: seller.createdAt,
          })
          .from(seller)
          .where(eq(seller.id, input.sellerId)),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch seller",
        });
      }

      if (!data[0]) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Seller not found",
        });
      }

      return data[0];
    }),

  createSeller: protectedProcedure
    .input(SellerCRUDSchema)
    .mutation(async ({ ctx, input }) => {
      // Check if email already exists first
      const { data: existingSeller, err: existingErr } = await tryCatch(
        ctx.db
          .select({ id: seller.id })
          .from(seller)
          .where(eq(seller.phoneNumber, input.phoneNumber)),
      );

      if (existingErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to check for existing seller",
        });
      }

      if (existingSeller[0]) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Email already registered",
        });
      }

      const { err: txErr } = await tryCatch(
        ctx.db.transaction(async (tx) => {
          const [sellerRes] = await tx
            .insert(seller)
            .values({
              fullName: input.fullName,
              email: `${input.phoneNumber}@dummy-scraplo.com`,
              emailVerified: false,
              image: input.image,
              phoneNumber: input.phoneNumber,
              phoneNumberVerified: true,
              onBoardingStep: "STEP_3",
              createdAt: new Date(),
              updatedAt: new Date(),
            })
            .returning();

          console.log(sellerRes);

          if (!sellerRes) {
            throw new Error("Failed to create seller");
          }

          await tx.insert(sellerAccount).values({
            sellerId: sellerRes.id,
            accountId: sellerRes.id,
            providerId: "credential",
            createdAt: new Date(),
            updatedAt: new Date(),
          });
        }),
      );

      if (txErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: txErr.message || "Failed to create seller",
        });
      }

      return {
        message:
          "Seller created successfully, System generated password sent to email",
      };
    }),

  updateSeller: protectedProcedure
    .input(
      SellerCRUDSchema.extend({
        sellerId: z.string().nonempty({ message: "Seller ID is required" }),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { data: conflictingSeller, err: conflictingErr } = await tryCatch(
        ctx.db
          .select({ id: seller.id })
          .from(seller)
          .where(
            and(
              eq(seller.phoneNumber, input.phoneNumber),
              not(eq(seller.id, input.sellerId)),
            ),
          ),
      );

      if (conflictingErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to check for conflicting seller phonenumber",
        });
      }

      if (conflictingSeller[0]) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Phone Number already in use by another seller",
        });
      }

      const { err: txErr } = await tryCatch(
        ctx.db
          .update(seller)
          .set({
            fullName: input.fullName,
            image: input.image,
            phoneNumber: input.phoneNumber,
            updatedAt: new Date(),
          })
          .where(eq(seller.id, input.sellerId)),
      );

      if (txErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: txErr.message || "Failed to update seller",
        });
      }

      return {
        message: "Seller updated successfully",
      };
    }),

  deleteSeller: protectedProcedure
    .input(
      z.object({
        sellerId: z.string().nonempty({ message: "Seller ID is required" }),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { err } = await tryCatch(
        ctx.db.transaction(async (tx) => {
          await tx
            .delete(sellerAccount)
            .where(eq(sellerAccount.sellerId, input.sellerId));

          await tx.delete(seller).where(eq(seller.id, input.sellerId));
        }),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to delete seller",
        });
      }

      return {
        message: "Seller deleted successfully",
      };
    }),

  blockSeller: protectedProcedure
    .input(
      z.object({
        sellerId: z.string().nonempty({ message: "Seller ID is required" }),
        reason: z.string().nonempty({ message: "Reason is required" }),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { err } = await tryCatch(
        ctx.db
          .update(seller)
          .set({ isBlocked: true, reason: input.reason, updatedAt: new Date() })
          .where(eq(seller.id, input.sellerId)),
      );
      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: err.message || "Failed to block seller",
        });
      }
      return { message: "Seller blocked successfully" };
    }),

  unblockSeller: protectedProcedure
    .input(
      z.object({
        sellerId: z.string().nonempty({ message: "Seller ID is required" }),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { err } = await tryCatch(
        ctx.db
          .update(seller)
          .set({ isBlocked: false, reason: null, updatedAt: new Date() })
          .where(eq(seller.id, input.sellerId)),
      );
      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: err.message || "Failed to unblock seller",
        });
      }
      return { message: "Seller unblocked successfully" };
    }),
} satisfies TRPCRouterRecord;
