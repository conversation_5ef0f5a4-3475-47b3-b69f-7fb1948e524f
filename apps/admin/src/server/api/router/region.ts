import type { TRPCRouterRecord } from "@trpc/server";
import { TRPCError } from "@trpc/server";
import { z } from "zod";

import { eq } from "@acme/db";
import { regions } from "@acme/db/schema";
import { tryCatch } from "@acme/validators/utils";

import { MarkRegionSchema } from "~/lib/zod-schema";
import { protectedProcedure } from "../trpc";

export const regionRouter = {
  all: protectedProcedure.query(async ({ ctx }) => {
    const { data, err } = await tryCatch(ctx.db.query.regions.findMany());

    if (err) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: `Failed to fetch regions`,
      });
    }

    return data;
  }),

  byId: protectedProcedure
    .input(z.object({ id: z.string().nonempty() }))
    .query(async ({ ctx, input }) => {
      const { data, err } = await tryCatch(
        ctx.db.query.regions.findFirst({
          where: eq(regions.id, input.id),
        }),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: `Failed to fetch region with id ${input.id}`,
        });
      }

      return data;
    }),

  create: protectedProcedure
    .input(MarkRegionSchema)
    .mutation(async ({ ctx, input }) => {
      if (input.markersLatLng.length < 3) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "At least 3 markers are required to create a region",
        });
      }

      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
      const firstMarker = input.markersLatLng.at(0)!;
      const markersLatLng = [...input.markersLatLng, firstMarker];

      const { err } = await tryCatch(
        ctx.db.insert(regions).values({
          name: input.name,
          markers: markersLatLng,
          isActive: input.isActive,
        }),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: `Failed to create region`,
        });
      }

      return {
        message: "Region created successfully",
      };
    }),

  update: protectedProcedure
    .input(MarkRegionSchema.extend({ id: z.string().nonempty() }))
    .mutation(async ({ ctx, input }) => {
      const { err } = await tryCatch(
        ctx.db
          .update(regions)
          .set({
            name: input.name,
            markers: input.markersLatLng,
            isActive: input.isActive,
          })
          .where(eq(regions.id, input.id)),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: `Failed to update region`,
        });
      }

      return {
        message: "Region updated successfully",
      };
    }),

  toggleActive: protectedProcedure
    .input(z.object({ id: z.string().nonempty(), isActive: z.boolean() }))
    .mutation(async ({ ctx, input }) => {
      const { err } = await tryCatch(
        ctx.db
          .update(regions)
          .set({
            isActive: input.isActive,
          })
          .where(eq(regions.id, input.id)),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: `Failed to delete region with id ${input.id}`,
        });
      }

      return {
        message: "Region deleted successfully",
      };
    }),
} satisfies TRPCRouterRecord;
