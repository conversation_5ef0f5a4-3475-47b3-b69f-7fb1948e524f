import type { TRP<PERSON>outerRecord } from "@trpc/server";
import { TRPCError } from "@trpc/server";
import { z } from "zod";

import { and, asc, eq, isNull, not } from "@acme/db";
import { category } from "@acme/db/schema";
import {
  CategoryCreateSchema,
  CategoryFetchByIdInputSchema,
  CategoryIdInputSchema,
  CategoryUpdateSchema,
} from "@acme/validators";
import { tryCatch } from "@acme/validators/utils";

import { protectedProcedure } from "../trpc";

// Add a helper to normalize tag
function normalizeTag(tag: string | null | undefined) {
  return tag ? tag.trim().toLowerCase() : null;
}

export const categoryRouter = {
  getCategoryById: protectedProcedure
    .input(CategoryFetchByIdInputSchema)
    .query(async ({ ctx, input }) => {
      const whereConditions = [eq(category.id, input.id)];
      if (!input.includeDeleted) {
        whereConditions.push(isNull(category.deletedAt));
      }

      const { data: fetchedCategory, err } = await tryCatch(
        ctx.db.query.category.findFirst({
          where: and(...whereConditions),
          with: {
            ...(input.withParent ? { parent: true } : {}),
            ...(input.withChildren
              ? {
                  children: input.includeDeleted
                    ? { orderBy: [asc(category.name)] }
                    : {
                        where: isNull(category.deletedAt),
                        orderBy: [asc(category.name)],
                      },
                }
              : {}),
          },
        }),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch category. " + err.message,
        });
      }
      if (!fetchedCategory) return null;

      const result = { ...fetchedCategory } as typeof fetchedCategory & {
        parent?: typeof fetchedCategory.parent | null;
      };

      if (input.withParent && result.parent) {
        if (!input.includeDeleted && result.parent.deletedAt) {
          result.parent = null;
        }
      }
      return result;
    }),

  getAllWithoutParent: protectedProcedure.query(async ({ ctx }) => {
    const { data, err } = await tryCatch(
      ctx.db.query.category.findMany({
        where: not(isNull(category.parentId)),
      }),
    );

    if (err) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch all categories",
      });
    }

    return data;
  }),

  getTopLevelCategories: protectedProcedure
    .input(z.object({ includeDeleted: z.boolean().optional().default(false) }))
    .query(async ({ ctx, input }) => {
      const whereConditions = [isNull(category.parentId)];
      if (!input.includeDeleted) {
        whereConditions.push(isNull(category.deletedAt));
      }
      const { data, err } = await tryCatch(
        ctx.db.query.category.findMany({
          where: and(...whereConditions),
          orderBy: [asc(category.name)],
        }),
      );
      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch top-level categories. " + err.message,
        });
      }
      return data;
    }),

  createCategory: protectedProcedure
    .input(CategoryCreateSchema)
    .mutation(async ({ ctx, input }) => {
      const { parentId, rate, tag, ...restOfInput } = input;
      const normalizedTag = normalizeTag(tag);
      if (normalizedTag) {
        // Check uniqueness (excluding deleted)
        const { data: tagExists, err: tagErr } = await tryCatch(
          ctx.db.query.category.findFirst({
            where: and(
              eq(category.tag, normalizedTag),
              isNull(category.deletedAt),
            ),
            columns: { id: true },
          }),
        );
        if (tagErr)
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: "Error validating tag. " + tagErr.message,
          });
        if (tagExists)
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: `Tag '${normalizedTag}' already exists. Please create a new tag.`,
          });
      }

      if (parentId) {
        const { data: parentExists, err: parentErr } = await tryCatch(
          ctx.db.query.category.findFirst({
            where: and(eq(category.id, parentId), isNull(category.deletedAt)),
            columns: { id: true },
          }),
        );
        if (parentErr)
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: "Error validating parent. " + parentErr.message,
          });
        if (!parentExists)
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: `Parent category with ID "${parentId}" not found or has been deleted.`,
          });
      }
      const valuesToInsert = {
        ...restOfInput,
        rate: rate,
        parentId: parentId ?? null,
        tag: normalizedTag,
        compensationKabadiwalaRate: input.compensationKabadiwalaRate.toString(),
        compensationRecyclerRate: input.compensationRecyclerRate.toString(),
      };
      const { data, err } = await tryCatch(
        ctx.db
          .insert(category)
          .values({ ...valuesToInsert, rate: rate.toString() })
          .returning(),
      );
      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create category. " + err.message,
        });
      }
      if (data.length === 0)
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create category, no data returned.",
        });

      return {
        message: `Category ${data[0]?.id} created successfully.`,
      };
    }),

  updateCategory: protectedProcedure
    .input(CategoryUpdateSchema)
    .mutation(async ({ ctx, input }) => {
      const { id, parentId, rate, tag, ...restOfUpdates } = input;
      const normalizedTag = normalizeTag(tag);
      if (normalizedTag) {
        // Check uniqueness (excluding deleted, and not self)
        const { data: tagExists, err: tagErr } = await tryCatch(
          ctx.db.query.category.findFirst({
            where: and(
              eq(category.tag, normalizedTag),
              isNull(category.deletedAt),
              // Exclude self
              not(eq(category.id, id)),
            ),
            columns: { id: true },
          }),
        );
        if (tagErr)
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: "Error validating tag. " + tagErr.message,
          });
        if (tagExists)
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: `Tag '${normalizedTag}' already exists. Please create a new tag.`,
          });
      }
      const { data: existingCategory, err: fetchErr } = await tryCatch(
        ctx.db.query.category.findFirst({
          where: and(eq(category.id, id), isNull(category.deletedAt)),
          columns: { id: true, parentId: true },
        }),
      );
      if (fetchErr)
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Error fetching category for update. " + fetchErr.message,
        });
      if (!existingCategory)
        throw new TRPCError({
          code: "NOT_FOUND",
          message: `Category with ID "${id}" not found or has been deleted.`,
        });

      const valuesToUpdate: Partial<typeof category.$inferInsert> & {
        updatedAt: Date;
      } = {
        ...restOfUpdates,
        compensationKabadiwalaRate:
          input.compensationKabadiwalaRate?.toString(),
        compensationRecyclerRate: input.compensationRecyclerRate?.toString(),
        updatedAt: new Date(),
      };

      if (rate !== undefined) valuesToUpdate.rate = rate.toString();
      if (input.compensationKabadiwalaRate !== undefined)
        valuesToUpdate.compensationKabadiwalaRate =
          input.compensationKabadiwalaRate.toString();
      if (input.compensationRecyclerRate !== undefined)
        valuesToUpdate.compensationRecyclerRate =
          input.compensationRecyclerRate.toString();

      if (parentId !== undefined) {
        if (parentId === id)
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "A category cannot be its own parent.",
          });
        if (parentId === null) valuesToUpdate.parentId = null;
        else {
          const { data: parentExists, err: parentErr } = await tryCatch(
            ctx.db.query.category.findFirst({
              where: and(eq(category.id, parentId), isNull(category.deletedAt)),
              columns: { id: true },
            }),
          );
          if (parentErr)
            throw new TRPCError({
              code: "INTERNAL_SERVER_ERROR",
              message: "Error validating new parent. " + parentErr.message,
            });
          if (!parentExists)
            throw new TRPCError({
              code: "BAD_REQUEST",
              message: `New parent category with ID "${parentId}" not found or has been deleted.`,
            });
          valuesToUpdate.parentId = parentId;
        }
      }
      valuesToUpdate.tag = normalizedTag;
      const { data, err } = await tryCatch(
        ctx.db
          .update(category)
          .set(valuesToUpdate)
          .where(eq(category.id, id))
          .returning(),
      );
      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update category. " + err.message,
        });
      }
      if (data.length === 0)
        throw new TRPCError({
          code: "NOT_FOUND",
          message: `Category with ID "${id}" not found during update return.`,
        });

      return {
        message: `Category ${data[0]?.id} updated successfully.`,
      };
    }),

  deleteCategory: protectedProcedure
    .input(CategoryIdInputSchema)
    .mutation(async ({ ctx, input }) => {
      const { data: children, err: childrenErr } = await tryCatch(
        ctx.db.query.category.findMany({
          where: and(
            eq(category.parentId, input.id),
            isNull(category.deletedAt),
          ),
          columns: { id: true },
          limit: 1,
        }),
      );
      if (childrenErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message:
            "Failed to check for child categories. " + childrenErr.message,
        });
      }
      if (children.length > 0)
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: `Cannot delete category "${input.id}" because it has active child categories. Please delete them first.`,
        });
      const { data, err } = await tryCatch(
        ctx.db
          .delete(category)
          .where(eq(category.id, input.id))
          .returning({ deletedId: category.id }),
      );
      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to delete category. " + err.message,
        });
      }
      if (data.length === 0)
        throw new TRPCError({
          code: "NOT_FOUND",
          message: `Category with ID "${input.id}" not found for deletion.`,
        });
      return {
        message: `Category ${data[0]?.deletedId} deleted successfully.`,
      };
    }),
} satisfies TRPCRouterRecord;
