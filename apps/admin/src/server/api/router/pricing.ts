import type { TRP<PERSON>outerRecord } from "@trpc/server";
import { TRPCError } from "@trpc/server";
import { z } from "zod";

import {
  createRegionPricingPeriod,
  createRegionPricingPeriodWithCategoryPricing,
  deleteCategoryPricing,
  deletePricingPeriod,
  getAllCategoryPricingForRegion,
  getAllPricingPeriods,
  getAllPricingPeriodsForRegion,
  getCategoryPricingById,
  getCategoryRateForRegion,
  getPricingHistory,
  searchCategoryPricing,
  setCategoryPricingForRegion,
  updateCategoryPricing,
  updatePricingPeriod,
  updatePricingPeriodWithCategoryPricing,
} from "@acme/core";
import { tryCatch } from "@acme/validators/utils";

import {
  CreateRegionPricingPeriodSchema,
  CreateRegionPricingPeriodWithCategoryPricingSchema,
  DeleteCategoryPricingSchema,
  DeleteRegionPricingPeriodSchema,
  SetCategoryPricingForRegionSchema,
  UpdateCategoryPricingSchema,
  UpdateRegionPricingPeriodSchema,
} from "~/lib/zod-schema";
import { protectedProcedure } from "../trpc";

export const pricingRouter = {
  getCategoryRateForRegion: protectedProcedure
    .input(
      z.object({
        regionId: z.string(),
        categoryId: z.string(),
      }),
    )
    .query(async ({ input }) => {
      const { data, err } = await tryCatch(
        getCategoryRateForRegion(input.regionId, input.categoryId),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: err.message || "Something went wrong",
        });
      }

      return data;
    }),

  getAllCategoryPricingForRegion: protectedProcedure
    .input(z.object({ regionId: z.string() }))
    .query(async ({ input }) => {
      const { data, err } = await tryCatch(
        getAllCategoryPricingForRegion(input.regionId),
      );
      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: err.message || "Something went wrong",
        });
      }
      return data;
    }),

  getPricingHistoryById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ input }) => {
      const { data, err } = await tryCatch(getPricingHistory(input.id));
      if (err) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: err.message || "Pricing history not found",
        });
      }
      return data;
    }),

  //   getRegionPricingForCategory: protectedProcedure
  //     .input(z.object({ categoryId: z.string() }))
  //     .query(async ({ input }) => {
  //       const { data, err } = await tryCatch(
  //         getRegionPricingForCategory(input.categoryId),
  //       );
  //       if (err) {
  //         throw new TRPCError({
  //           code: "INTERNAL_SERVER_ERROR",
  //           message: err.message || "Something went wrong",
  //         });
  //       }
  //       return data;
  //     }),

  createRegionPricingPeriod: protectedProcedure
    .input(CreateRegionPricingPeriodSchema)
    .mutation(async ({ input }) => {
      const { data, err } = await tryCatch(
        createRegionPricingPeriod({
          regionId: input.regionId,
          effectiveFromDate: new Date(input.effectiveFromDate),
          expiresAt: new Date(input.expiresAt),
        }),
      );

      if (err) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: err.message || "Failed to create region pricing period",
        });
      }

      return data;
    }),

  createRegionPricingPeriodWithCategoryPricingRecords: protectedProcedure
    .input(CreateRegionPricingPeriodWithCategoryPricingSchema)
    .mutation(async ({ input }) => {
      const { data, err } = await tryCatch(
        createRegionPricingPeriodWithCategoryPricing({
          regionId: input.regionId,
          effectiveFromDate: new Date(input.effectiveFromDate),
          expiresAt: new Date(input.expiresAt),
          regionCategoryPricing: input.regionCategoryPricing,
        }),
      );

      if (err) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: err.message,
        });
      }

      return data;
    }),

  updatePricingPeriodWithCategoryPricing: protectedProcedure
    .input(
      CreateRegionPricingPeriodWithCategoryPricingSchema.extend({
        historyId: z.string().min(1, "Pricing history ID is required"),
      }),
    )
    .mutation(async ({ input }) => {
      const { data, err } = await tryCatch(
        updatePricingPeriodWithCategoryPricing(input.historyId, {
          regionId: input.regionId,
          effectiveFromDate: new Date(input.effectiveFromDate),
          expiresAt: new Date(input.expiresAt),
          regionCategoryPricing: input.regionCategoryPricing,
        }),
      );
      if (err) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message:
            err.message ||
            "Failed to update region pricing period with category pricing",
        });
      }
      return data;
    }),

  setCategoryPricingForRegion: protectedProcedure
    .input(SetCategoryPricingForRegionSchema)
    .mutation(async ({ input }) => {
      const { data, err } = await tryCatch(
        setCategoryPricingForRegion({
          pricingHistoryId: input.pricingHistoryId,
          categoryId: input.categoryId,
          rate: input.rate,
          compensationKabadiwalaRate: input.compensationKabadiwalaRate,
          compensationRecyclerRate: input.compensationRecyclerRate,
        }),
      );

      if (err) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: err.message || "Failed to set category pricing for region",
        });
      }

      return data;
    }),

  searchCategoryPricing: protectedProcedure
    .input(
      z.object({
        regionId: z.string().optional(),
        categoryId: z.string().optional(),
        includeExpired: z.boolean().optional(),
      }),
    )
    .query(async ({ input }) => {
      const { data, err } = await tryCatch(
        searchCategoryPricing(
          input.regionId,
          input.categoryId,
          input.includeExpired ?? false,
        ),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: err.message || "Something went wrong",
        });
      }

      return data;
    }),

  getCategoryPricingById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ input }) => {
      const { data, err } = await tryCatch(getCategoryPricingById(input.id));
      if (err) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: err.message || "Pricing not found",
        });
      }
      return data;
    }),

  updateCategoryPricing: protectedProcedure
    .input(UpdateCategoryPricingSchema)
    .mutation(async ({ input }) => {
      const { data, err } = await tryCatch(
        updateCategoryPricing(input.id, {
          rate: input.rate,
          compensationKabadiwalaRate: input.compensationKabadiwalaRate,
          compensationRecyclerRate: input.compensationRecyclerRate,
        }),
      );

      if (err) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: err.message || "Failed to update category pricing",
        });
      }

      return data;
    }),

  deleteCategoryPricing: protectedProcedure
    .input(DeleteCategoryPricingSchema)
    .mutation(async ({ input }) => {
      const { data, err } = await tryCatch(deleteCategoryPricing(input.id));
      if (err) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: err.message || "Failed to delete category pricing",
        });
      }

      return data;
    }),

  getAllPricingPeriods: protectedProcedure
    .input(z.object({ regionId: z.string().optional() }))
    .query(async ({ input }) => {
      if (!input.regionId) {
        const { data, err } = await tryCatch(getAllPricingPeriods());

        if (err) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: err.message || "Something went wrong",
          });
        }

        return data;
      }

      const { data, err } = await tryCatch(
        getAllPricingPeriodsForRegion(input.regionId),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: err.message || "Something went wrong",
        });
      }

      return data;
    }),

  updatePricingPeriod: protectedProcedure
    .input(UpdateRegionPricingPeriodSchema)
    .mutation(async ({ input }) => {
      const { data, err } = await tryCatch(
        updatePricingPeriod(input.id, {
          effectiveFromDate: input.effectiveFromDate
            ? new Date(input.effectiveFromDate)
            : undefined,
          expiresAt: input.expiresAt ? new Date(input.expiresAt) : undefined,
        }),
      );

      if (err) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: err.message || "Failed to update pricing period",
        });
      }

      return data;
    }),

  deletePricingPeriod: protectedProcedure
    .input(DeleteRegionPricingPeriodSchema)
    .mutation(async ({ input }) => {
      const { data, err } = await tryCatch(deletePricingPeriod(input.id));
      if (err) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: err.message || "Failed to delete pricing period",
        });
      }

      return data;
    }),
} satisfies TRPCRouterRecord;
