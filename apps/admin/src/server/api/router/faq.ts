import type { TRPCRouterRecord } from "@trpc/server";
import { TRPCError } from "@trpc/server";
import { asc, desc, eq, isNull } from "drizzle-orm";
import { z } from "zod";

import { answer, question } from "@acme/db/schema";
import { tryCatch } from "@acme/validators/utils";

import { protectedProcedure } from "../trpc";

export const faqRouter = {
  // Question CRUD operations
  getAllQuestions: protectedProcedure.query(async ({ ctx }) => {
    const { data, err } = await tryCatch(
      ctx.db
        .select({
          id: question.id,
          content: question.content,
          parentId: question.parentId,
          questionFor: question.questionFor,
          order: question.order,
          createdAt: question.createdAt,
          updatedAt: question.updatedAt,
        })
        .from(question)
        .orderBy(asc(question.order), desc(question.createdAt)),
    );

    if (err) {
      console.error("Failed to fetch questions:", err);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch questions",
      });
    }

    return data;
  }),

  getTopLevelQuestions: protectedProcedure.query(async ({ ctx }) => {
    const { data, err } = await tryCatch(
      ctx.db
        .select({
          id: question.id,
          content: question.content,
          parentId: question.parentId,
          questionFor: question.questionFor,
          order: question.order,
          createdAt: question.createdAt,
          updatedAt: question.updatedAt,
        })
        .from(question)
        .where(isNull(question.parentId))
        .orderBy(asc(question.order), desc(question.createdAt)),
    );

    if (err) {
      console.error("Failed to fetch top-level questions:", err);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch top-level questions",
      });
    }

    return data;
  }),

  getQuestionById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const { data, err } = await tryCatch(
        ctx.db
          .select({
            id: question.id,
            content: question.content,
            parentId: question.parentId,
            questionFor: question.questionFor,
            order: question.order,
            createdAt: question.createdAt,
            updatedAt: question.updatedAt,
          })
          .from(question)
          .where(eq(question.id, input.id))
          .limit(1),
      );

      if (err) {
        console.error("Failed to fetch question:", err);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch question",
        });
      }

      if (data.length === 0) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Question not found",
        });
      }

      return data[0];
    }),

  createQuestion: protectedProcedure
    .input(
      z.object({
        content: z.string().min(1, "Content is required"),
        questionFor: z.enum(["KABADIWALA", "SELLER", "LANDING_PAGE"]),
        order: z.number().optional(),
        parentId: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // Prevent parentId for LANDING_PAGE
      const parentId =
        input.questionFor === "LANDING_PAGE" ? undefined : input.parentId;
      const { data, err } = await tryCatch(
        ctx.db
          .insert(question)
          .values({
            content: input.content,
            questionFor: input.questionFor,
            order: input.order !== undefined ? `${input.order}` : undefined,
            parentId,
          })
          .returning(),
      );

      if (err) {
        console.error("Failed to create question:", err);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create question",
        });
      }

      return data[0];
    }),

  updateQuestion: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        content: z.string().min(1, "Content is required"),
        questionFor: z.enum(["KABADIWALA", "SELLER", "LANDING_PAGE"]),
        order: z.number().optional(),
        parentId: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // Prevent parentId for LANDING_PAGE
      const parentId =
        input.questionFor === "LANDING_PAGE" ? undefined : input.parentId;
      const { data, err } = await tryCatch(
        ctx.db
          .update(question)
          .set({
            content: input.content,
            questionFor: input.questionFor,
            order: input.order !== undefined ? `${input.order}` : undefined,
            parentId,
          })
          .where(eq(question.id, input.id))
          .returning(),
      );

      if (err) {
        console.error("Failed to update question:", err);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update question",
        });
      }

      if (data.length === 0) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Question not found",
        });
      }

      return data[0];
    }),

  deleteQuestion: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const { data, err } = await tryCatch(
        ctx.db.delete(question).where(eq(question.id, input.id)).returning(),
      );

      if (err) {
        console.error("Failed to delete question:", err);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to delete question",
        });
      }

      if (data.length === 0) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Question not found",
        });
      }

      return { success: true };
    }),

  // Answer CRUD operations
  getAnswersByQuestionId: protectedProcedure
    .input(z.object({ questionId: z.string() }))
    .query(async ({ ctx, input }) => {
      const { data, err } = await tryCatch(
        ctx.db
          .select({
            id: answer.id,
            content: answer.content,
            questionId: answer.questionId,
            createdAt: answer.createdAt,
            updatedAt: answer.updatedAt,
          })
          .from(answer)
          .where(eq(answer.questionId, input.questionId))
          .orderBy(desc(answer.createdAt)),
      );

      if (err) {
        console.error("Failed to fetch answers:", err);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch answers",
        });
      }

      return data;
    }),

  getAnswerById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const { data, err } = await tryCatch(
        ctx.db
          .select({
            id: answer.id,
            content: answer.content,
            questionId: answer.questionId,
            createdAt: answer.createdAt,
            updatedAt: answer.updatedAt,
          })
          .from(answer)
          .where(eq(answer.id, input.id))
          .limit(1),
      );

      if (err) {
        console.error("Failed to fetch answer:", err);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch answer",
        });
      }

      if (data.length === 0) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Answer not found",
        });
      }

      return data[0];
    }),

  createAnswer: protectedProcedure
    .input(
      z.object({
        content: z.string().min(1, "Content is required"),
        questionId: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { data, err } = await tryCatch(
        ctx.db
          .insert(answer)
          .values({
            content: input.content,
            questionId: input.questionId,
          })
          .returning(),
      );

      if (err) {
        console.error("Failed to create answer:", err);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create answer",
        });
      }

      return data[0];
    }),

  updateAnswer: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        content: z.string().min(1, "Content is required"),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { data, err } = await tryCatch(
        ctx.db
          .update(answer)
          .set({
            content: input.content,
          })
          .where(eq(answer.id, input.id))
          .returning(),
      );

      if (err) {
        console.error("Failed to update answer:", err);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update answer",
        });
      }

      if (data.length === 0) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Answer not found",
        });
      }

      return data[0];
    }),

  deleteAnswer: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const { data, err } = await tryCatch(
        ctx.db.delete(answer).where(eq(answer.id, input.id)).returning(),
      );

      if (err) {
        console.error("Failed to delete answer:", err);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to delete answer",
        });
      }

      if (data.length === 0) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Answer not found",
        });
      }

      return { success: true };
    }),
} satisfies TRPCRouterRecord;
