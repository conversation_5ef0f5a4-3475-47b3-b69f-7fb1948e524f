import type { TRPCRouterRecord } from "@trpc/server";
import { TRPCError } from "@trpc/server";
import { and, desc, eq } from "drizzle-orm";

import { blogs } from "@acme/db/schema";
import { tryCatch } from "@acme/validators/utils";

import {
  BlogFilterSchema,
  BlogIdSchema,
  BlogSlugSchema,
  CreateBlogSchema,
} from "~/lib/zod-schema";
import { protectedProcedure } from "../trpc";

export const blogRouter = {
  // Get all blogs with optional filtering
  getAllBlogs: protectedProcedure
    .input(BlogFilterSchema.optional())
    .query(async ({ ctx, input }) => {
      const conditions = [];

      if (input?.status) {
        conditions.push(eq(blogs.status, input.status));
      }
      if (input?.isActive !== undefined) {
        conditions.push(eq(blogs.isActive, input.isActive));
      }
      if (input?.isFeatured !== undefined) {
        conditions.push(eq(blogs.isFeatured, input.isFeatured));
      }

      const { data, err } = await tryCatch(
        ctx.db
          .select({
            id: blogs.id,
            title: blogs.title,
            slug: blogs.slug,
            excerpt: blogs.excerpt,
            banner: blogs.banner,
            status: blogs.status,
            isActive: blogs.isActive,
            isFeatured: blogs.isFeatured,
            tags: blogs.tags,
            publishedAt: blogs.publishedAt,
            createdAt: blogs.createdAt,
            updatedAt: blogs.updatedAt,
          })
          .from(blogs)
          .where(conditions.length > 0 ? and(...conditions) : undefined)
          .orderBy(desc(blogs.createdAt)),
      );

      if (err) {
        console.error("Failed to fetch blogs:", err);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch blogs",
        });
      }

      return data;
    }),

  // Get published blogs only
  getPublishedBlogs: protectedProcedure.query(async ({ ctx }) => {
    const { data, err } = await tryCatch(
      ctx.db
        .select({
          id: blogs.id,
          title: blogs.title,
          slug: blogs.slug,
          excerpt: blogs.excerpt,
          banner: blogs.banner,
          tags: blogs.tags,
          publishedAt: blogs.publishedAt,
          createdAt: blogs.createdAt,
        })
        .from(blogs)
        .where(and(eq(blogs.status, "PUBLISHED"), eq(blogs.isActive, true)))
        .orderBy(desc(blogs.publishedAt)),
    );

    if (err) {
      console.error("Failed to fetch published blogs:", err);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch published blogs",
      });
    }

    return data;
  }),

  // Get featured blogs
  getFeaturedBlogs: protectedProcedure.query(async ({ ctx }) => {
    const { data, err } = await tryCatch(
      ctx.db
        .select({
          id: blogs.id,
          title: blogs.title,
          slug: blogs.slug,
          excerpt: blogs.excerpt,
          banner: blogs.banner,
          tags: blogs.tags,
          publishedAt: blogs.publishedAt,
        })
        .from(blogs)
        .where(
          and(
            eq(blogs.status, "PUBLISHED"),
            eq(blogs.isActive, true),
            eq(blogs.isFeatured, true),
          ),
        )
        .orderBy(desc(blogs.publishedAt)),
    );

    if (err) {
      console.error("Failed to fetch featured blogs:", err);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch featured blogs",
      });
    }

    return data;
  }),

  // Get blog by ID
  getBlogById: protectedProcedure
    .input(BlogIdSchema)
    .query(async ({ ctx, input }) => {
      const { data, err } = await tryCatch(
        ctx.db.select().from(blogs).where(eq(blogs.id, input.id)).limit(1),
      );

      if (err) {
        console.error("Failed to fetch blog:", err);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch blog",
        });
      }

      if (data.length === 0) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Blog not found",
        });
      }

      return data[0];
    }),

  // Get blog by slug
  getBlogBySlug: protectedProcedure
    .input(BlogSlugSchema)
    .query(async ({ ctx, input }) => {
      const { data, err } = await tryCatch(
        ctx.db.select().from(blogs).where(eq(blogs.slug, input.slug)).limit(1),
      );

      if (err) {
        console.error("Failed to fetch blog by slug:", err);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch blog",
        });
      }

      if (data.length === 0) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Blog not found",
        });
      }

      return data[0];
    }),

  // Create new blog
  createBlog: protectedProcedure
    .input(CreateBlogSchema)
    .mutation(async ({ ctx, input }) => {
      // Check if slug already exists
      const { data: existingBlog } = await tryCatch(
        ctx.db
          .select({ id: blogs.id })
          .from(blogs)
          .where(eq(blogs.slug, input.slug))
          .limit(1),
      );

      if (existingBlog && existingBlog.length > 0) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "A blog with this slug already exists",
        });
      }

      const { data, err } = await tryCatch(
        ctx.db
          .insert(blogs)
          .values({
            title: input.title,
            slug: input.slug,
            excerpt: input.excerpt,
            content: input.content,
            banner: input.banner,
            status: input.status,
            isActive: input.isActive,
            isFeatured: input.isFeatured,
            tags: input.tags,
            publishedAt:
              input.status === "PUBLISHED"
                ? (input.publishedAt ?? new Date())
                : input.publishedAt,
          })
          .returning(),
      );

      if (err) {
        console.error("Failed to create blog:", err);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create blog",
        });
      }

      return data[0];
    }),

  // Update blog
  updateBlog: protectedProcedure
    .input(CreateBlogSchema.extend({ id: BlogIdSchema.shape.id }))
    .mutation(async ({ ctx, input }) => {
      const { id, ...updateData } = input;

      // Check if slug already exists (excluding current blog)
      if (updateData.slug) {
        const { data: existingBlog } = await tryCatch(
          ctx.db
            .select({ id: blogs.id })
            .from(blogs)
            .where(and(eq(blogs.slug, updateData.slug), eq(blogs.id, id)))
            .limit(1),
        );

        if (existingBlog && existingBlog.length === 0) {
          const { data: duplicateSlug } = await tryCatch(
            ctx.db
              .select({ id: blogs.id })
              .from(blogs)
              .where(eq(blogs.slug, updateData.slug))
              .limit(1),
          );

          if (duplicateSlug && duplicateSlug.length > 0) {
            throw new TRPCError({
              code: "CONFLICT",
              message: "A blog with this slug already exists",
            });
          }
        }
      }

      // Set publishedAt if status is being changed to PUBLISHED
      if (updateData.status === "PUBLISHED" && !updateData.publishedAt) {
        updateData.publishedAt = new Date();
      }

      const { data, err } = await tryCatch(
        ctx.db
          .update(blogs)
          .set(updateData)
          .where(eq(blogs.id, id))
          .returning(),
      );

      if (err) {
        console.error("Failed to update blog:", err);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update blog",
        });
      }

      if (data.length === 0) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Blog not found",
        });
      }

      return data[0];
    }),

  // Delete blog
  deleteBlog: protectedProcedure
    .input(BlogIdSchema)
    .mutation(async ({ ctx, input }) => {
      const { data, err } = await tryCatch(
        ctx.db.delete(blogs).where(eq(blogs.id, input.id)).returning(),
      );

      if (err) {
        console.error("Failed to delete blog:", err);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to delete blog",
        });
      }

      if (data.length === 0) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Blog not found",
        });
      }

      return { success: true };
    }),

  // Toggle blog status (publish/unpublish)
  toggleBlogStatus: protectedProcedure
    .input(BlogIdSchema)
    .mutation(async ({ ctx, input }) => {
      // First get current status
      const { data: currentBlog, err: fetchErr } = await tryCatch(
        ctx.db
          .select({ status: blogs.status })
          .from(blogs)
          .where(eq(blogs.id, input.id))
          .limit(1),
      );

      if (fetchErr || currentBlog.length === 0) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Blog not found",
        });
      }

      const newStatus =
        currentBlog[0]?.status === "PUBLISHED" ? "DRAFT" : "PUBLISHED";

      const { data, err } = await tryCatch(
        ctx.db
          .update(blogs)
          .set({
            status: newStatus,
            ...(newStatus === "PUBLISHED" ? new Date() : undefined),
          })
          .where(eq(blogs.id, input.id))
          .returning(),
      );

      if (err) {
        console.error("Failed to toggle blog status:", err);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to toggle blog status",
        });
      }

      return data[0];
    }),
} satisfies TRPCRouterRecord;
