import type { TRP<PERSON>outerRecord } from "@trpc/server";
import { TRPCError } from "@trpc/server";
import { desc, eq } from "drizzle-orm";
import { z } from "zod";

import { deleteAccountRequest, seller } from "@acme/db/schema";
import { tryCatch } from "@acme/validators/utils";

import { protectedProcedure } from "../trpc";

export const deleteAccountRouter = {
  getAllDeleteAccountRequests: protectedProcedure.query(async ({ ctx }) => {
    const { data, err } = await tryCatch(
      ctx.db
        .select({
          id: deleteAccountRequest.id,
          reason: deleteAccountRequest.reason,
          createdAt: deleteAccountRequest.createdAt,
          userId: deleteAccountRequest.userId,
          userName: seller.fullName,
          userEmail: seller.email,
          userPhoneNumber: seller.phoneNumber,
        })
        .from(deleteAccountRequest)
        .leftJoin(seller, eq(deleteAccountRequest.userId, seller.id))
        .orderBy(desc(deleteAccountRequest.createdAt)),
    );

    if (err) {
      console.error("Failed to fetch delete account requests:", err);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch delete account requests",
      });
    }

    return data;
  }),

  confirmDeleteAccount: protectedProcedure
    .input(z.object({ requestId: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // First, get the request to find the user ID
      const { data: request, err: requestErr } = await tryCatch(
        ctx.db.query.deleteAccountRequest.findFirst({
          where: eq(deleteAccountRequest.id, input.requestId),
        }),
      );

      if (requestErr || !request) {
        console.error("Failed to fetch delete account request:", requestErr);
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Delete account request not found",
        });
      }

      // Delete the user (this will cascade delete related data including the request)
      const { err: deleteErr } = await tryCatch(
        ctx.db.delete(seller).where(eq(seller.id, request.userId)),
      );

      if (deleteErr) {
        console.error("Failed to delete user account:", deleteErr);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to delete user account",
        });
      }

      return { success: true };
    }),
} satisfies TRPCRouterRecord;
