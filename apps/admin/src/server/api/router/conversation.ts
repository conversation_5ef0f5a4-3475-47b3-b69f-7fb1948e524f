import type { TRPCRouterRecord } from "@trpc/server";
import { TRPCError } from "@trpc/server";
import { z } from "zod";

import { and, eq } from "@acme/db";
import {
  conversation,
  customerSupportConversation,
  customerSupportMessage,
  kabadiwalaSupportConversation,
  kabadiwalaSupportMessage,
  message,
} from "@acme/db/schema";
import { tryCatch } from "@acme/validators/utils";

import { protectedProcedure } from "../trpc";
import { onesignalKabadiwalaClient, onesignalSellerClient } from "../utils";

export const conversationRouter = {
  // --- Customer Support Conversation Endpoints ---
  getAllCustomerSupportConversations: protectedProcedure.query(
    async ({ ctx }) => {
      const { data: convos, err } = await tryCatch(
        ctx.db.query.customerSupportConversation.findMany({
          with: {
            messages: {
              orderBy: (msg, { desc }) => [desc(msg.createdAt)],
              limit: 1,
            },
            order: {
              with: {
                seller: true,
              },
            },
            customer: true, // <-- always join seller (customer)
          },
        }),
      );
      if (err)
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch conversations. " + err.message,
        });
      return convos.map((c) => ({
        id: c.id,
        conversationId: c.id,
        orderId: c.orderId,
        customerId: c.customerId,
        kabadiwalaId: null,
        customer: c.order?.seller
          ? { name: c.order.seller.fullName, image: c.order.seller.image }
          : { name: c.customer.fullName, image: c.customer.image },
        lastMessage: c.messages[0] ?? null,
        isOpen: c.isOpen,
        rating: c.rating ?? null,
        isRated: c.isRated,
        reviewText: c.reviewText ?? null,
      }));
    },
  ),
  getActiveCustomerSupportConversations: protectedProcedure.query(
    async ({ ctx }) => {
      const { data: convos, err } = await tryCatch(
        ctx.db.query.customerSupportConversation.findMany({
          where: eq(customerSupportConversation.isOpen, true),
          with: {
            messages: {
              orderBy: (msg, { desc }) => [desc(msg.createdAt)],
              limit: 1,
            },
            order: {
              with: {
                seller: true,
              },
            },
            customer: true, // <-- always join seller (customer)
          },
        }),
      );
      if (err)
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch active conversations. " + err.message,
        });
      return convos.map((c) => ({
        id: c.id,
        conversationId: c.id,
        orderId: c.orderId,
        customerId: c.customerId,
        kabadiwalaId: null,
        customer: c.order?.seller
          ? { name: c.order.seller.fullName, image: c.order.seller.image }
          : { name: c.customer.fullName, image: c.customer.image },
        lastMessage: c.messages[0] ?? null,
        isOpen: c.isOpen,
        rating: c.rating,
        isRated: c.isRated,
        reviewText: c.reviewText ?? null,
      }));
    },
  ),
  getClosedCustomerSupportConversations: protectedProcedure.query(
    async ({ ctx }) => {
      const { data: convos, err } = await tryCatch(
        ctx.db.query.customerSupportConversation.findMany({
          where: eq(customerSupportConversation.isOpen, false),
          with: {
            messages: {
              orderBy: (msg, { desc }) => [desc(msg.createdAt)],
              limit: 1,
            },
            order: {
              with: {
                seller: true,
              },
            },
            customer: true, // <-- always join seller (customer)
          },
        }),
      );
      if (err || convos.length === 0)
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch closed conversations. " + err?.message,
        });
      return convos.map((c) => ({
        id: c.id,
        conversationId: c.id,
        orderId: c.orderId,
        customerId: c.customerId,
        kabadiwalaId: null,
        customer: c.order?.seller
          ? { name: c.order.seller.fullName, image: c.order.seller.image }
          : { name: c.customer.fullName, image: c.customer.image },
        lastMessage: c.messages[0] ?? null,
        isOpen: c.isOpen,
        rating: c.rating,
        isRated: c.isRated,
        reviewText: c.reviewText ?? null,
      }));
    },
  ),
  getCustomerSupportMessages: protectedProcedure
    .input(z.object({ conversationId: z.string() }))
    .query(async ({ ctx, input }) => {
      const { data: convo, err } = await tryCatch(
        ctx.db.query.customerSupportConversation.findFirst({
          where: eq(customerSupportConversation.id, input.conversationId),
          with: { messages: true },
        }),
      );
      if (err)
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch customer conversation. " + err.message,
        });
      if (!convo)
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Conversation not found",
        });
      return convo.messages;
    }),
  sendCustomerSupportMessageAsAdmin: protectedProcedure
    .input(
      z.object({
        conversationId: z.string(),
        content: z.string().trim().nonempty(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { data: convo, err } = await tryCatch(
        ctx.db.query.customerSupportConversation.findFirst({
          where: eq(customerSupportConversation.id, input.conversationId),
        }),
      );
      if (err)
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch customer conversation. " + err.message,
        });
      if (!convo)
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Conversation not found",
        });
      if (!convo.isOpen)
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "This conversation is closed.",
        });
      try {
        await ctx.db.insert(customerSupportMessage).values({
          conversationId: convo.id,
          senderId: ctx.session.user.id,
          senderType: "ADMIN",
          content: input.content,
          createdAt: new Date(),
          isRead: false,
        });
        // Send notification to the customer
        if (convo.customerId) {
          const { data, err } = await tryCatch(
            onesignalSellerClient.sendNotification({
              title: "Support Reply",
              message: input.content,
              externalIds: [convo.customerId],
              url: convo.orderId
                ? `/profile/orders/${convo.orderId}`
                : `/profile/help-and-support?support-chat-id=${convo.id}`,
            }),
          );
          console.log(
            `[admind send notifcation data is]`,
            JSON.stringify(data, null, 2),
          );
          console.log(`[admind send notifcation err is]`, err?.message);
        }
        return { success: true };
      } catch (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to send message. " + (err as Error).message,
        });
      }
    }),
  closeCustomerSupportConversation: protectedProcedure
    .input(z.object({ conversationId: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const { data: convo, err } = await tryCatch(
        ctx.db.query.customerSupportConversation.findFirst({
          where: eq(customerSupportConversation.id, input.conversationId),
        }),
      );
      if (err)
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch customer conversation. " + err.message,
        });
      if (!convo)
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Conversation not found",
        });
      try {
        await ctx.db
          .update(customerSupportConversation)
          .set({
            isOpen: false,
            closedBy: ctx.session.user.id,
            closedAt: new Date(),
          })
          .where(eq(customerSupportConversation.id, convo.id));
        return { success: true };
      } catch (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to close conversation. " + (err as Error).message,
        });
      }
    }),
  deleteCustomerSupportConversation: protectedProcedure
    .input(z.object({ conversationId: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const { data: convo, err } = await tryCatch(
        ctx.db.query.customerSupportConversation.findFirst({
          where: eq(customerSupportConversation.id, input.conversationId),
        }),
      );
      if (err)
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch customer conversation. " + err.message,
        });
      if (!convo)
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Conversation not found",
        });
      try {
        await ctx.db
          .delete(customerSupportConversation)
          .where(eq(customerSupportConversation.id, convo.id));
        return { success: true };
      } catch (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to delete conversation. " + (err as Error).message,
        });
      }
    }),
  getAllKabadiwalaSupportConversations: protectedProcedure.query(
    async ({ ctx }) => {
      const { data: convos, err } = await tryCatch(
        ctx.db.query.kabadiwalaSupportConversation.findMany({
          with: {
            messages: {
              orderBy: (msg, { desc }) => [desc(msg.createdAt)],
              limit: 1,
            },
            order: {
              with: {
                kabadiwala: true,
              },
            },
            kabadiwala: true, // <-- always join kabadiwala
          },
        }),
      );
      if (err)
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch conversations. " + err.message,
        });
      return convos.map((c) => ({
        id: c.id,
        conversationId: c.id,
        orderId: c.orderId,
        customerId: null,
        kabadiwalaId: c.kabadiwalaId,
        kabadiwala: c.order?.kabadiwala
          ? { name: c.order.kabadiwala.name, image: c.order.kabadiwala.image }
          : { name: c.kabadiwala.name, image: c.kabadiwala.image },
        lastMessage: c.messages[0] ?? null,
        isOpen: c.isOpen,
        rating: c.rating ?? null,
        isRated: c.isRated,
        reviewText: c.reviewText ?? null,
      }));
    },
  ),
  getActiveKabadiwalaSupportConversations: protectedProcedure.query(
    async ({ ctx }) => {
      const { data: convos, err } = await tryCatch(
        ctx.db.query.kabadiwalaSupportConversation.findMany({
          where: eq(kabadiwalaSupportConversation.isOpen, true),
          with: {
            messages: {
              orderBy: (msg, { desc }) => [desc(msg.createdAt)],
              limit: 1,
            },
            order: {
              with: {
                kabadiwala: true,
              },
            },
            kabadiwala: true, // <-- always join kabadiwala
          },
        }),
      );
      if (err)
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch active conversations. " + err.message,
        });
      return convos.map((c) => ({
        id: c.id,
        conversationId: c.id,
        orderId: c.orderId,
        customerId: null,
        kabadiwalaId: c.kabadiwalaId,
        kabadiwala: c.order?.kabadiwala
          ? { name: c.order.kabadiwala.name, image: c.order.kabadiwala.image }
          : { name: c.kabadiwala.name, image: c.kabadiwala.image },
        lastMessage: c.messages[0] ?? null,
        isOpen: c.isOpen,
        rating: c.rating,
        isRated: c.isRated,
        reviewText: c.reviewText ?? null,
      }));
    },
  ),
  getClosedKabadiwalaSupportConversations: protectedProcedure.query(
    async ({ ctx }) => {
      const { data: convos, err } = await tryCatch(
        ctx.db.query.kabadiwalaSupportConversation.findMany({
          where: eq(kabadiwalaSupportConversation.isOpen, false),
          with: {
            messages: {
              orderBy: (msg, { desc }) => [desc(msg.createdAt)],
              limit: 1,
            },
            order: {
              with: {
                kabadiwala: true,
              },
            },
            kabadiwala: true, // <-- always join kabadiwala
          },
        }),
      );
      if (err)
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch closed conversations. " + err.message,
        });
      return convos.map((c) => ({
        id: c.id,
        conversationId: c.id,
        orderId: c.orderId,
        customerId: null,
        kabadiwalaId: c.kabadiwalaId,
        kabadiwala: c.order?.kabadiwala
          ? { name: c.order.kabadiwala.name, image: c.order.kabadiwala.image }
          : { name: c.kabadiwala.name, image: c.kabadiwala.image },
        lastMessage: c.messages[0] ?? null,
        isOpen: c.isOpen,
        rating: c.rating ?? null,
        isRated: c.isRated,
        reviewText: c.reviewText ?? null,
      }));
    },
  ),
  getKabadiwalaSupportMessages: protectedProcedure
    .input(z.object({ conversationId: z.string() }))
    .query(async ({ ctx, input }) => {
      const { data: convo, err } = await tryCatch(
        ctx.db.query.kabadiwalaSupportConversation.findFirst({
          where: eq(kabadiwalaSupportConversation.id, input.conversationId),
          with: { messages: true },
        }),
      );
      if (err)
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch kabadiwala conversation. " + err.message,
        });
      if (!convo)
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Conversation not found",
        });
      return convo.messages;
    }),
  sendKabadiwalaSupportMessageAsAdmin: protectedProcedure
    .input(
      z.object({
        conversationId: z.string(),
        content: z.string().trim().nonempty(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { data: convo, err } = await tryCatch(
        ctx.db.query.kabadiwalaSupportConversation.findFirst({
          where: eq(kabadiwalaSupportConversation.id, input.conversationId),
        }),
      );
      if (err)
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch kabadiwala conversation. " + err.message,
        });
      if (!convo)
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Conversation not found",
        });
      if (!convo.isOpen)
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "This conversation is closed.",
        });
      try {
        await ctx.db.insert(kabadiwalaSupportMessage).values({
          conversationId: convo.id,
          senderId: ctx.session.user.id,
          senderType: "ADMIN",
          content: input.content,
          createdAt: new Date(),
          isRead: false,
        });
        // Send notification to the kabadiwala
        if (convo.kabadiwalaId) {
          await tryCatch(
            onesignalKabadiwalaClient.sendNotification({
              title: "Support Reply",
              message: input.content,
              externalIds: [convo.kabadiwalaId],
              //   url: `/profile/help-and-support?support-chat-id=${convo.id}`,
            }),
          );
        }
        return { success: true };
      } catch (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to send message. " + (err as Error).message,
        });
      }
    }),
  closeKabadiwalaSupportConversation: protectedProcedure
    .input(z.object({ conversationId: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const { data: convo, err } = await tryCatch(
        ctx.db.query.kabadiwalaSupportConversation.findFirst({
          where: eq(kabadiwalaSupportConversation.id, input.conversationId),
        }),
      );
      if (err)
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch kabadiwala conversation. " + err.message,
        });
      if (!convo)
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Conversation not found",
        });
      try {
        await ctx.db
          .update(kabadiwalaSupportConversation)
          .set({
            isOpen: false,
            closedBy: ctx.session.user.id,
            closedAt: new Date(),
          })
          .where(eq(kabadiwalaSupportConversation.id, convo.id));
        return { success: true };
      } catch (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to close conversation. " + (err as Error).message,
        });
      }
    }),
  deleteKabadiwalaSupportConversation: protectedProcedure
    .input(z.object({ conversationId: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const { data: convo, err } = await tryCatch(
        ctx.db.query.kabadiwalaSupportConversation.findFirst({
          where: eq(kabadiwalaSupportConversation.id, input.conversationId),
        }),
      );
      if (err)
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch kabadiwala conversation. " + err.message,
        });
      if (!convo)
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Conversation not found",
        });
      try {
        await ctx.db
          .delete(kabadiwalaSupportConversation)
          .where(eq(kabadiwalaSupportConversation.id, convo.id));
        return { success: true };
      } catch (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to delete conversation. " + (err as Error).message,
        });
      }
    }),
  createCustomerSupportConversation: protectedProcedure
    .input(z.object({ customerId: z.string(), orderId: z.string().optional() }))
    .mutation(async ({ ctx, input }) => {
      // Check if conversation exists
      const { data: existing, err } = await tryCatch(
        ctx.db.query.customerSupportConversation.findFirst({
          where: and(
            eq(customerSupportConversation.customerId, input.customerId),
            input.orderId
              ? eq(customerSupportConversation.orderId, input.orderId)
              : undefined,
          ),
        }),
      );
      if (err)
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to check conversation. " + err.message,
        });
      if (existing) return { conversationId: existing.id };
      // Create new conversation
      try {
        const [created] = await ctx.db
          .insert(customerSupportConversation)
          .values({
            customerId: input.customerId,
            orderId: input.orderId,
            isOpen: true,
          })
          .returning();
        if (!created)
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: "Failed to create conversation.",
          });
        return { conversationId: created.id };
      } catch (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create conversation. " + (err as Error).message,
        });
      }
    }),
  createKabadiwalaSupportConversation: protectedProcedure
    .input(
      z.object({ kabadiwalaId: z.string(), orderId: z.string().optional() }),
    )
    .mutation(async ({ ctx, input }) => {
      // Check if conversation exists
      const { data: existing, err } = await tryCatch(
        ctx.db.query.kabadiwalaSupportConversation.findFirst({
          where: and(
            eq(kabadiwalaSupportConversation.kabadiwalaId, input.kabadiwalaId),
            input.orderId
              ? eq(kabadiwalaSupportConversation.orderId, input.orderId)
              : undefined,
          ),
        }),
      );
      if (err)
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to check conversation. " + err.message,
        });
      if (existing) return { conversationId: existing.id };
      // Create new conversation
      try {
        const [created] = await ctx.db
          .insert(kabadiwalaSupportConversation)
          .values({
            kabadiwalaId: input.kabadiwalaId,
            orderId: input.orderId,
            isOpen: true,
          })
          .returning();
        if (!created)
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: "Failed to create conversation.",
          });
        return { conversationId: created.id };
      } catch (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create conversation. " + (err as Error).message,
        });
      }
    }),
  getCustomerSupportConversationByOrderId: protectedProcedure
    .input(z.object({ orderId: z.string() }))
    .query(async ({ ctx, input }) => {
      const { data: convo, err } = await tryCatch(
        ctx.db.query.customerSupportConversation.findFirst({
          where: eq(customerSupportConversation.orderId, input.orderId),
        }),
      );
      if (err)
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch conversation. " + err.message,
        });
      return convo ?? null;
    }),
  getKabadiwalaSupportConversationByOrderId: protectedProcedure
    .input(z.object({ orderId: z.string() }))
    .query(async ({ ctx, input }) => {
      const { data: convo, err } = await tryCatch(
        ctx.db.query.kabadiwalaSupportConversation.findFirst({
          where: eq(kabadiwalaSupportConversation.orderId, input.orderId),
        }),
      );
      if (err)
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch conversation. " + err.message,
        });
      return convo ?? null;
    }),
  getLatestActiveCustomerSupportConversationByOrderId: protectedProcedure
    .input(z.object({ orderId: z.string() }))
    .query(async ({ ctx, input }) => {
      const { data: convos, err } = await tryCatch(
        ctx.db.query.customerSupportConversation.findMany({
          where: and(
            eq(customerSupportConversation.orderId, input.orderId),
            eq(customerSupportConversation.isOpen, true),
          ),
          with: {
            messages: {
              orderBy: (msg, { desc }) => [desc(msg.createdAt)],
              limit: 1,
            },
          },
        }),
      );
      if (err)
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch conversations. " + err.message,
        });
      if (convos.length === 0) return null;
      // Sort by latest message createdAt
      const sorted = convos.sort((a, b) => {
        const aTime = a.messages[0]?.createdAt
          ? new Date(a.messages[0].createdAt).getTime()
          : 0;
        const bTime = b.messages[0]?.createdAt
          ? new Date(b.messages[0].createdAt).getTime()
          : 0;
        return bTime - aTime;
      });
      return sorted[0];
    }),
  // --- Audit Conversation: Seller <-> Kabadiwala ---
  getAuditConversationByOrderId: protectedProcedure
    .input(z.object({ orderId: z.string() }))
    .query(async ({ ctx, input }) => {
      // Fetch the direct conversation for this order
      const { data: convo, err } = await tryCatch(
        ctx.db.query.conversation.findFirst({
          where: eq(conversation.orderId, input.orderId),
        }),
      );
      if (err)
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch conversation. " + err.message,
        });
      if (!convo) return { conversation: null, messages: [] };
      // Fetch all messages for this conversation, ordered by createdAt ASC
      const { data: messages, err: msgErr } = await tryCatch(
        ctx.db.query.message.findMany({
          where: eq(message.conversationId, convo.id),
          orderBy: (msg, { asc }) => [asc(msg.createdAt)],
        }),
      );
      if (msgErr)
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch messages. " + msgErr.message,
        });
      return { conversation: convo, messages };
    }),
} satisfies TRPCRouterRecord;
