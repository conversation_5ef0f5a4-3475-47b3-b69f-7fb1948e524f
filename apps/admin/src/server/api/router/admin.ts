import type { TRPCRouterRecord } from "@trpc/server";
import { TRPCError } from "@trpc/server";
import bcrypt from "bcryptjs";
import { z } from "zod";

import { and, eq } from "@acme/db";
import { admin, adminAccount } from "@acme/db/schema";
import { sendEmail } from "@acme/mail";
import { AdminCRUDSchema } from "@acme/validators";
import { tryCatch } from "@acme/validators/utils";

import { generatePassword } from "~/lib/utils";
import { protectedProcedure } from "../trpc";

export const adminRouter = {
  getDefaultAdmin: protectedProcedure.query(async ({ ctx }) => {
    const { data: defaultAdmin, err: defaultAdminErr } = await tryCatch(
      ctx.db.query.admin.findFirst({
        where: eq(admin.email, "<EMAIL>"),
      }),
    );

    if (defaultAdminErr) {
      console.error("[defaultAdminErr]: ", defaultAdminErr);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch default admin",
      });
    }

    if (!defaultAdmin) {
      console.error("[defaultAdmin]: ", defaultAdmin);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Default admin not found",
      });
    }

    return {
      defaultAdminId: defaultAdmin.id,
    };
  }),

  getAllAdmins: protectedProcedure.query(async ({ ctx }) => {
    const { data, err } = await tryCatch(
      ctx.db
        .select({
          id: admin.id,
          name: admin.name,
          email: admin.email,
          emailVerified: admin.emailVerified,
          image: admin.image,
          createdAt: admin.createdAt,
        })
        .from(admin),
    );

    if (err) {
      console.error("[err]: ", err);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch admins",
      });
    }

    return data;
  }),

  getAdminById: protectedProcedure
    .input(
      z.object({
        adminId: z.string().nonempty({ message: "Admin ID is required" }),
      }),
    )
    .query(async ({ ctx, input }) => {
      const { data, err } = await tryCatch(
        ctx.db
          .select({
            id: admin.id,
            name: admin.name,
            email: admin.email,
            emailVerified: admin.emailVerified,
            image: admin.image,
            createdAt: admin.createdAt,
          })
          .from(admin)
          .where(eq(admin.id, input.adminId)),
      );

      if (err) {
        console.error("[err]: ", err);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch admin",
        });
      }

      if (!data[0]) {
        console.error("[data]: ", data);
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Admin not found",
        });
      }

      return data[0];
    }),

  createAdmin: protectedProcedure
    .input(AdminCRUDSchema)
    .mutation(async ({ ctx, input }) => {
      // Check if email already exists first
      const { data: existingAdmin, err: existingErr } = await tryCatch(
        ctx.db
          .select({ id: admin.id })
          .from(admin)
          .where(eq(admin.email, input.email)),
      );

      if (existingErr) {
        console.error("[existingErr]: ", existingErr);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to check for existing admin",
        });
      }

      if (existingAdmin[0]) {
        console.error("[existingAdmin]: ", existingAdmin);
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Email already registered",
        });
      }

      const generatedPassword = generatePassword(10);
      console.log("Generated password: ", generatedPassword);
      const hashedPassword = await bcrypt.hash(generatedPassword, 10);

      const { err: txErr } = await tryCatch(
        ctx.db.transaction(async (tx) => {
          const [adminRes] = await tx
            .insert(admin)
            .values({
              name: input.name,
              email: input.email,
              emailVerified: true,
              image: input.image,
              createdAt: new Date(),
              updatedAt: new Date(),
            })
            .returning();

          if (!adminRes) {
            throw new Error("Failed to create admin");
          }

          await tx.insert(adminAccount).values({
            adminId: adminRes.id,
            accountId: adminRes.id,
            providerId: "credential",
            password: hashedPassword,
            createdAt: new Date(),
            updatedAt: new Date(),
          });
        }),
      );

      if (txErr) {
        console.error("[txErr]: ", txErr);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: txErr.message || "Failed to create admin",
        });
      }

      // Send email with generated password
      await sendEmail({
        to: input.email,
        subject: "Your System-Generated Password",
        content: `<div>
                <h1>Welcome, ${input.name}</h1>
                <p>This is your system generated password: <strong>${generatedPassword}</strong></p>
                <p>Please log in using this password and reset it after your first login.</p>
                </div>`,
      });

      return {
        message:
          "Admin created successfully, System generated password sent to email",
      };
    }),

  updateAdmin: protectedProcedure
    .input(
      AdminCRUDSchema.extend({
        adminId: z.string().nonempty({ message: "Admin ID is required" }),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // Check if email is already used by another admin
      const { data: existingAdmin, err: existingErr } = await tryCatch(
        ctx.db
          .select({ id: admin.id })
          .from(admin)
          .where(
            and(eq(admin.email, input.email), eq(admin.id, input.adminId)),
          ),
      );

      if (existingErr) {
        console.error("[existingErr]: ", existingErr);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to check for existing admin",
        });
      }

      if (existingAdmin[0]) {
        console.error("[existingAdmin]: ", existingAdmin);
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Email already in use by another admin",
        });
      }

      const { err: txErr } = await tryCatch(
        ctx.db
          .update(admin)
          .set({
            name: input.name,
            email: input.email,
            image: input.image,
            updatedAt: new Date(),
          })
          .where(eq(admin.id, input.adminId)),
      );

      if (txErr) {
        console.error("[txErr]: ", txErr);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: txErr.message || "Failed to update admin",
        });
      }

      return {
        message: "Admin updated successfully",
      };
    }),

  deleteAdmin: protectedProcedure
    .input(
      z.object({
        adminId: z.string().nonempty({ message: "Admin ID is required" }),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { err } = await tryCatch(
        ctx.db.transaction(async (tx) => {
          await tx
            .delete(adminAccount)
            .where(eq(adminAccount.adminId, input.adminId));

          await tx.delete(admin).where(eq(admin.id, input.adminId));
        }),
      );

      if (err) {
        console.error("[err]: ", err);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to delete admin",
        });
      }

      return {
        message: "Admin deleted successfully",
      };
    }),
} satisfies TRPCRouterRecord;
