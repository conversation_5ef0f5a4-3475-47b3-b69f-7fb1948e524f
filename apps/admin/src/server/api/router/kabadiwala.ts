import type { TRPCRouterRecord } from "@trpc/server";
import { TRPCError } from "@trpc/server";
import { and, eq, not } from "drizzle-orm";
import { z } from "zod";

import {
  account,
  kabadiwala,
  kabadiwalaAddress,
  kabadiwalaPaymentTransaction,
  order,
  vehicle,
} from "@acme/db/schema";
import { KabadiwalaCRUDSchema } from "@acme/validators";
import { tryCatch } from "@acme/validators/utils";

import type { DLAdvanceApiResult } from "~/lib/types";
import { protectedProcedure } from "../trpc";

export const kabadiwalaRouter = {
  getAllKabadiwalas: protectedProcedure.query(async ({ ctx }) => {
    const { data, err } = await tryCatch(
      ctx.db
        .select({
          id: kabadiwala.id,
          name: kabadiwala.name,
          email: kabadiwala.email,
          emailVerified: kabadiwala.emailVerified,
          image: kabadiwala.image,
          phoneNumber: kabadiwala.phoneNumber,
          phoneNumberVerified: kabadiwala.phoneNumberVerified,
          createdAt: kabadiwala.createdAt,
          dlVerificationResponse: kabadiwala.dlVerificationResponse,
          isBlocked: kabadiwala.isBlocked,
          reason: kabadiwala.reason,
        })
        .from(kabadiwala),
    );

    if (err) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch kabadiwalas",
      });
    }
    const dataWithDlVerificationResponse = data.map((kabadiwala) => ({
      ...kabadiwala,
      dlVerificationResponse:
        kabadiwala.dlVerificationResponse as DLAdvanceApiResult,
    }));

    return dataWithDlVerificationResponse;
  }),

  getKabadiwalaById: protectedProcedure
    .input(
      z.object({
        kabadiwalaId: z
          .string()
          .nonempty({ message: "Kabadiwala ID is required" }),
      }),
    )
    .query(async ({ ctx, input }) => {
      const { data, err } = await tryCatch(
        ctx.db
          .select({
            id: kabadiwala.id,
            name: kabadiwala.name,
            email: kabadiwala.email,
            image: kabadiwala.image,
            phoneNumber: kabadiwala.phoneNumber,
            phoneNumberVerified: kabadiwala.phoneNumberVerified,
            createdAt: kabadiwala.createdAt,
          })
          .from(kabadiwala)
          .where(eq(kabadiwala.id, input.kabadiwalaId)),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch kabadiwala",
        });
      }

      if (!data[0]) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Kabadiwala not found",
        });
      }

      return data[0];
    }),

  getKabadiwalaDetailsById: protectedProcedure
    .input(
      z.object({
        kabadiwalaId: z
          .string()
          .nonempty({ message: "Kabadiwala ID is required" }),
      }),
    )
    .query(async ({ ctx, input }) => {
      // Fetch kabadiwala main info
      const { data: kabadiwalaData, err } = await tryCatch(
        ctx.db
          .select({
            id: kabadiwala.id,
            name: kabadiwala.name,
            email: kabadiwala.email,
            emailVerified: kabadiwala.emailVerified,
            image: kabadiwala.image,
            phoneNumber: kabadiwala.phoneNumber,
            phoneNumberVerified: kabadiwala.phoneNumberVerified,
            createdAt: kabadiwala.createdAt,
            dlVerificationResponse: kabadiwala.dlVerificationResponse,
            isBlocked: kabadiwala.isBlocked,
            reason: kabadiwala.reason,
            walletBalance: kabadiwala.walletBalance,
          })
          .from(kabadiwala)
          .where(eq(kabadiwala.id, input.kabadiwalaId)),
      );
      if (err || !kabadiwalaData[0]) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch kabadiwala details",
        });
      }
      const kab = kabadiwalaData[0];

      // Fetch vehicles
      const { data: vehicles, err: vehiclesErr } = await tryCatch(
        ctx.db
          .select()
          .from(vehicle)
          .where(eq(vehicle.kabadiwalaId, input.kabadiwalaId)),
      );
      if (vehiclesErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch vehicles",
        });
      }

      // Fetch transactions
      const { data: transactions, err: transactionsErr } = await tryCatch(
        ctx.db
          .select()
          .from(kabadiwalaPaymentTransaction)
          .where(
            eq(kabadiwalaPaymentTransaction.kabadiwalaId, input.kabadiwalaId),
          ),
      );
      if (transactionsErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch transactions",
        });
      }

      // Fetch addresses
      const { data: addresses, err: addressesErr } = await tryCatch(
        ctx.db
          .select()
          .from(kabadiwalaAddress)
          .where(eq(kabadiwalaAddress.kabadiwalaId, input.kabadiwalaId)),
      );
      if (addressesErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch addresses",
        });
      }

      // Fetch orders
      const { data: orders, err: ordersErr } = await tryCatch(
        ctx.db
          .select()
          .from(order)
          .where(eq(order.kabadiwalaId, input.kabadiwalaId)),
      );
      if (ordersErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch kabadiwala orders",
        });
      }

      return {
        ...kab,
        vehicles,
        transactions,
        addresses,
        orders,
        dlVerificationResponse:
          kab.dlVerificationResponse as DLAdvanceApiResult | null,
      };
    }),

  createKabadiwala: protectedProcedure
    .input(KabadiwalaCRUDSchema)
    .mutation(async ({ ctx, input }) => {
      const { data: existingKabadiwala, err: existingErr } = await tryCatch(
        ctx.db
          .select({ id: kabadiwala.id })
          .from(kabadiwala)
          .where(eq(kabadiwala.phoneNumber, input.phoneNumber)),
      );

      if (existingErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to check for existing kabadiwala",
        });
      }

      if (existingKabadiwala[0]) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Phone Number already registered",
        });
      }

      const { err: txErr } = await tryCatch(
        ctx.db.transaction(async (tx) => {
          const [kabadiwalaRes] = await tx
            .insert(kabadiwala)
            .values({
              name: input.name,
              email: `${input.phoneNumber}@dummy-scraplo.com`,
              emailVerified: false,
              image: input.image,
              phoneNumber: input.phoneNumber,
              phoneNumberVerified: true,
              createdAt: new Date(),
              updatedAt: new Date(),
            })
            .returning();

          if (!kabadiwalaRes) {
            throw new Error("Failed to create kabadiwala");
          }

          await tx.insert(account).values({
            kabadiwalaId: kabadiwalaRes.id,
            accountId: kabadiwalaRes.id,
            providerId: "credential",
            createdAt: new Date(),
            updatedAt: new Date(),
          });
        }),
      );

      if (txErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: txErr.message || "Failed to create kabadiwala",
        });
      }

      return {
        message:
          "Kabadiwala created successfully. System generated password sent to email",
      };
    }),

  updateKabadiwala: protectedProcedure
    .input(
      KabadiwalaCRUDSchema.extend({
        kabadiwalaId: z
          .string()
          .nonempty({ message: "Kabadiwala ID is required" }),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { data: conflictingKabadiwala, err: conflictingErr } =
        await tryCatch(
          ctx.db
            .select({ id: kabadiwala.id })
            .from(kabadiwala)
            .where(
              and(
                eq(kabadiwala.phoneNumber, input.phoneNumber),
                not(eq(kabadiwala.id, input.kabadiwalaId)),
              ),
            ),
        );

      if (conflictingErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to check for conflicting kabadiwala email",
        });
      }

      if (conflictingKabadiwala[0]) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Phone number already in use by another kabadiwala",
        });
      }

      const { err: txErr } = await tryCatch(
        ctx.db
          .update(kabadiwala)
          .set({
            name: input.name,
            image: input.image,
            phoneNumber: input.phoneNumber,
            updatedAt: new Date(),
          })
          .where(eq(kabadiwala.id, input.kabadiwalaId)),
      );

      if (txErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: txErr.message || "Failed to update kabadiwala",
        });
      }

      return {
        message: "Kabadiwala updated successfully",
      };
    }),

  deleteKabadiwala: protectedProcedure
    .input(
      z.object({
        kabadiwalaId: z
          .string()
          .nonempty({ message: "Kabadiwala ID is required" }),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { err } = await tryCatch(
        ctx.db.transaction(async (tx) => {
          await tx
            .delete(account)
            .where(eq(account.kabadiwalaId, input.kabadiwalaId));
          await tx
            .delete(kabadiwala)
            .where(eq(kabadiwala.id, input.kabadiwalaId));
        }),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to delete kabadiwala",
        });
      }

      return {
        message: "Kabadiwala deleted successfully",
      };
    }),

  blockKabadiwala: protectedProcedure
    .input(
      z.object({
        kabadiwalaId: z
          .string()
          .nonempty({ message: "Kabadiwala ID is required" }),
        reason: z.string().nonempty({ message: "Reason is required" }),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { err } = await tryCatch(
        ctx.db
          .update(kabadiwala)
          .set({ isBlocked: true, reason: input.reason, updatedAt: new Date() })
          .where(eq(kabadiwala.id, input.kabadiwalaId)),
      );
      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: err.message || "Failed to block kabadiwala",
        });
      }
      return { message: "Kabadiwala blocked successfully" };
    }),

  unblockKabadiwala: protectedProcedure
    .input(
      z.object({
        kabadiwalaId: z
          .string()
          .nonempty({ message: "Kabadiwala ID is required" }),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { err } = await tryCatch(
        ctx.db
          .update(kabadiwala)
          .set({ isBlocked: false, reason: null, updatedAt: new Date() })
          .where(eq(kabadiwala.id, input.kabadiwalaId)),
      );
      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: err.message || "Failed to approve kabadiwala",
        });
      }
      return { message: "Kabadiwala Approved successfully" };
    }),
} satisfies TRPCRouterRecord;
