import type { TRPCRouterRecord } from "@trpc/server";
import { TRPCError } from "@trpc/server";
import { and, count, eq, gte, lt } from "drizzle-orm";
import { z } from "zod";

import {
  category,
  customerSupportConversation,
  kabadiwala,
  kabadiwalaSupportConversation,
  order,
  seller,
} from "@acme/db/schema";
import { tryCatch } from "@acme/validators/utils";

import { protectedProcedure } from "../trpc";

// --- Types ---
// Minimal type for support chat rows
interface SupportChat {
  isRated: boolean;
  rating: number | null;
  isOpen: boolean;
}

// Helper to parse dd-mm-yyyy to Date
function parseDateString(dateStr?: string): Date | undefined {
  if (!dateStr) return undefined;
  const [dd, mm, yyyy] = dateStr.split("-").map(Number);
  if (!dd || !mm || !yyyy) return undefined;
  return new Date(yyyy, mm - 1, dd);
}

export const dashboardRouter = {
  getStats: protectedProcedure
    .input(
      z
        .object({
          startDate: z.string().optional(),
          endDate: z.string().optional(),
        })
        .optional(),
    )
    .query(async ({ ctx, input }) => {
      const today = new Date();
      const startOfToday = new Date(
        today.getFullYear(),
        today.getMonth(),
        today.getDate(),
      );
      const startOfWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);

      // Default to last 1 month if no date range provided
      let startDate = input?.startDate;
      let endDate = input?.endDate;
      if (!startDate && !endDate) {
        const end = new Date();
        const start = new Date();
        start.setMonth(start.getMonth() - 1);
        // Format as dd-mm-yyyy
        const pad = (n: number) => n.toString().padStart(2, "0");
        startDate = `${pad(start.getDate())}-${pad(start.getMonth() + 1)}-${start.getFullYear()}`;
        endDate = `${pad(end.getDate())}-${pad(end.getMonth() + 1)}-${end.getFullYear()}`;
      }

      // Date range filter
      let createdAtFilter = undefined;
      if (startDate || endDate) {
        const start = parseDateString(startDate);
        const end = parseDateString(endDate);
        if (start && end) {
          createdAtFilter = and(
            gte(order.createdAt, start),
            lt(order.createdAt, new Date(end.getTime() + 24 * 60 * 60 * 1000)),
          );
        } else if (start) {
          createdAtFilter = gte(order.createdAt, start);
        } else if (end) {
          createdAtFilter = lt(
            order.createdAt,
            new Date(end.getTime() + 24 * 60 * 60 * 1000),
          );
        }
      }

      // User/category date filter (by createdAt)
      let userCreatedAtFilter = undefined;
      if (startDate || endDate) {
        const start = parseDateString(startDate);
        const end = parseDateString(endDate);
        if (start && end) {
          userCreatedAtFilter = and(
            gte(seller.createdAt, start),
            lt(seller.createdAt, new Date(end.getTime() + 24 * 60 * 60 * 1000)),
          );
        } else if (start) {
          userCreatedAtFilter = gte(seller.createdAt, start);
        } else if (end) {
          userCreatedAtFilter = lt(
            seller.createdAt,
            new Date(end.getTime() + 24 * 60 * 60 * 1000),
          );
        }
      }
      let kabadiwalaCreatedAtFilter = undefined;
      if (startDate || endDate) {
        const start = parseDateString(startDate);
        const end = parseDateString(endDate);
        if (start && end) {
          kabadiwalaCreatedAtFilter = and(
            gte(kabadiwala.createdAt, start),
            lt(
              kabadiwala.createdAt,
              new Date(end.getTime() + 24 * 60 * 60 * 1000),
            ),
          );
        } else if (start) {
          kabadiwalaCreatedAtFilter = gte(kabadiwala.createdAt, start);
        } else if (end) {
          kabadiwalaCreatedAtFilter = lt(
            kabadiwala.createdAt,
            new Date(end.getTime() + 24 * 60 * 60 * 1000),
          );
        }
      }
      let categoryCreatedAtFilter = undefined;
      if (startDate || endDate) {
        const start = parseDateString(startDate);
        const end = parseDateString(endDate);
        if (start && end) {
          categoryCreatedAtFilter = and(
            gte(category.createdAt, start),
            lt(
              category.createdAt,
              new Date(end.getTime() + 24 * 60 * 60 * 1000),
            ),
          );
        } else if (start) {
          categoryCreatedAtFilter = gte(category.createdAt, start);
        } else if (end) {
          categoryCreatedAtFilter = lt(
            category.createdAt,
            new Date(end.getTime() + 24 * 60 * 60 * 1000),
          );
        }
      }

      // Get total counts (filtered by createdAt if date range is provided)
      const { data: totalSellers, err: sellersErr } = await tryCatch(
        ctx.db
          .select({ count: count() })
          .from(seller)
          .where(userCreatedAtFilter),
      );

      const { data: totalKabadiwalas, err: kabadiwalasErr } = await tryCatch(
        ctx.db
          .select({ count: count() })
          .from(kabadiwala)
          .where(kabadiwalaCreatedAtFilter),
      );

      const { data: totalCategories, err: categoriesErr } = await tryCatch(
        ctx.db
          .select({ count: count() })
          .from(category)
          .where(and(eq(category.isActive, true), categoryCreatedAtFilter)),
      );

      // Get order counts by status
      const { data: activeOrders, err: activeOrdersErr } = await tryCatch(
        ctx.db
          .select({ count: count() })
          .from(order)
          .where(
            createdAtFilter
              ? and(eq(order.status, "ACTIVE"), createdAtFilter)
              : eq(order.status, "ACTIVE"),
          ),
      );

      const { data: completedOrders, err: completedOrdersErr } = await tryCatch(
        ctx.db
          .select({ count: count() })
          .from(order)
          .where(
            createdAtFilter
              ? and(eq(order.status, "COMPLETED"), createdAtFilter)
              : eq(order.status, "COMPLETED"),
          ),
      );

      const { data: pendingOrders, err: pendingOrdersErr } = await tryCatch(
        ctx.db
          .select({ count: count() })
          .from(order)
          .where(
            createdAtFilter
              ? and(eq(order.status, "PENDING"), createdAtFilter)
              : eq(order.status, "PENDING"),
          ),
      );

      // Get today's orders
      const { data: todayOrders, err: todayOrdersErr } = await tryCatch(
        ctx.db
          .select({ count: count() })
          .from(order)
          .where(
            and(
              gte(order.createdAt, startOfToday),
              lt(
                order.createdAt,
                new Date(startOfToday.getTime() + 24 * 60 * 60 * 1000),
              ),
              createdAtFilter,
            ),
          ),
      );

      // Get this week's orders
      const { data: weekOrders, err: weekOrdersErr } = await tryCatch(
        ctx.db
          .select({ count: count() })
          .from(order)
          .where(and(gte(order.createdAt, startOfWeek), createdAtFilter)),
      );

      // Get verified users (filtered by createdAt if date range is provided)
      const { data: verifiedSellers, err: verifiedSellersErr } = await tryCatch(
        ctx.db
          .select({ count: count() })
          .from(seller)
          .where(
            and(
              eq(seller.emailVerified, true),
              eq(seller.phoneNumberVerified, true),
              userCreatedAtFilter,
            ),
          ),
      );

      const { data: verifiedKabadiwalas, err: verifiedKabadiwalasErr } =
        await tryCatch(
          ctx.db
            .select({ count: count() })
            .from(kabadiwala)
            .where(
              and(
                eq(kabadiwala.emailVerified, true),
                eq(kabadiwala.phoneNumberVerified, true),
                kabadiwalaCreatedAtFilter,
              ),
            ),
        );

      if (
        sellersErr ||
        kabadiwalasErr ||
        categoriesErr ||
        activeOrdersErr ||
        completedOrdersErr ||
        pendingOrdersErr ||
        todayOrdersErr ||
        weekOrdersErr ||
        verifiedSellersErr ||
        verifiedKabadiwalasErr
      ) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch dashboard statistics",
        });
      }

      return {
        users: {
          totalSellers: totalSellers[0]?.count ?? 0,
          totalKabadiwalas: totalKabadiwalas[0]?.count ?? 0,
          verifiedSellers: verifiedSellers[0]?.count ?? 0,
          verifiedKabadiwalas: verifiedKabadiwalas[0]?.count ?? 0,
        },
        orders: {
          active: activeOrders[0]?.count ?? 0,
          completed: completedOrders[0]?.count ?? 0,
          pending: pendingOrders[0]?.count ?? 0,
          today: todayOrders[0]?.count ?? 0,
          thisWeek: weekOrders[0]?.count ?? 0,
        },
        categories: {
          total: totalCategories[0]?.count ?? 0,
        },
      };
    }),

  // Support Chat Stats (Overall)
  getSupportChatStats: protectedProcedure
    .input(
      z
        .object({
          startDate: z.string().optional(),
          endDate: z.string().optional(),
        })
        .optional(),
    )
    .query(async ({ ctx, input }) => {
      // Date range filter for each table
      let customerCreatedAtFilter = undefined;
      let kabadiwalaCreatedAtFilter = undefined;
      if (input?.startDate || input?.endDate) {
        const start = parseDateString(input.startDate);
        const end = parseDateString(input.endDate);
        if (start && end) {
          customerCreatedAtFilter = and(
            gte(customerSupportConversation.createdAt, start),
            lt(
              customerSupportConversation.createdAt,
              new Date(end.getTime() + 24 * 60 * 60 * 1000),
            ),
          );
          kabadiwalaCreatedAtFilter = and(
            gte(kabadiwalaSupportConversation.createdAt, start),
            lt(
              kabadiwalaSupportConversation.createdAt,
              new Date(end.getTime() + 24 * 60 * 60 * 1000),
            ),
          );
        } else if (start) {
          customerCreatedAtFilter = gte(
            customerSupportConversation.createdAt,
            start,
          );
          kabadiwalaCreatedAtFilter = gte(
            kabadiwalaSupportConversation.createdAt,
            start,
          );
        } else if (end) {
          customerCreatedAtFilter = lt(
            customerSupportConversation.createdAt,
            new Date(end.getTime() + 24 * 60 * 60 * 1000),
          );
          kabadiwalaCreatedAtFilter = lt(
            kabadiwalaSupportConversation.createdAt,
            new Date(end.getTime() + 24 * 60 * 60 * 1000),
          );
        }
      }
      // Customer support
      const { data: customerChats, err: customerErr } = await tryCatch(
        ctx.db
          .select()
          .from(customerSupportConversation)
          .where(customerCreatedAtFilter),
      );
      // Kabadiwala support
      const { data: kabadiwalaChats, err: kabadiwalaErr } = await tryCatch(
        ctx.db
          .select()
          .from(kabadiwalaSupportConversation)
          .where(kabadiwalaCreatedAtFilter),
      );
      if (customerErr || kabadiwalaErr) {
        console.log("[customer Error]", customerErr);
        console.log("[kabadiwala Error]", kabadiwalaErr);

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch support chat stats",
        });
      }
      // Helper to filter rated
      const filterRated = (arr: SupportChat[]) =>
        arr.filter((c) => c.isRated && c.rating !== null);
      // Helper to average
      const avg = (arr: SupportChat[]) =>
        arr.length
          ? arr.reduce((a, b) => a + Number(b.rating), 0) / arr.length
          : 0;
      const allChats: SupportChat[] = [
        ...(customerChats as SupportChat[]),
        ...(kabadiwalaChats as SupportChat[]),
      ];
      const ratedChats = filterRated(allChats);
      return {
        total: allChats.length,
        totalRated: ratedChats.length,
        averageRating: avg(ratedChats),
      };
    }),

  // Seller-specific support chat stats
  getSellerSupportChatStats: protectedProcedure
    .input(
      z
        .object({
          startDate: z.string().optional(),
          endDate: z.string().optional(),
        })
        .optional(),
    )
    .query(async ({ ctx, input }) => {
      let customerCreatedAtFilter = undefined;
      if (input?.startDate || input?.endDate) {
        const start = parseDateString(input.startDate);
        const end = parseDateString(input.endDate);
        if (start && end) {
          customerCreatedAtFilter = and(
            gte(customerSupportConversation.createdAt, start),
            lt(
              customerSupportConversation.createdAt,
              new Date(end.getTime() + 24 * 60 * 60 * 1000),
            ),
          );
        } else if (start) {
          customerCreatedAtFilter = gte(
            customerSupportConversation.createdAt,
            start,
          );
        } else if (end) {
          customerCreatedAtFilter = lt(
            customerSupportConversation.createdAt,
            new Date(end.getTime() + 24 * 60 * 60 * 1000),
          );
        }
      }
      const { data: chats, err } = await tryCatch(
        ctx.db
          .select()
          .from(customerSupportConversation)
          .where(customerCreatedAtFilter),
      );
      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch seller support chat stats",
        });
      }
      const filterRated = (arr: SupportChat[]) =>
        arr.filter((c) => c.isRated && c.rating !== null);
      const avg = (arr: SupportChat[]) =>
        arr.length
          ? arr.reduce((a, b) => a + Number(b.rating), 0) / arr.length
          : 0;
      const closed = (chats as SupportChat[]).filter((c) => !c.isOpen);
      const ongoing = (chats as SupportChat[]).filter((c) => c.isOpen);
      const rated = filterRated(chats as SupportChat[]);
      return {
        total: (chats as SupportChat[]).length,
        averageRating: avg(rated),
        closed: closed.length,
        ongoing: ongoing.length,
      };
    }),

  // Kabadiwala-specific support chat stats
  getKabadiwalaSupportChatStats: protectedProcedure
    .input(
      z
        .object({
          startDate: z.string().optional(),
          endDate: z.string().optional(),
        })
        .optional(),
    )
    .query(async ({ ctx, input }) => {
      let kabadiwalaCreatedAtFilter = undefined;
      if (input?.startDate || input?.endDate) {
        const start = parseDateString(input.startDate);
        const end = parseDateString(input.endDate);
        if (start && end) {
          kabadiwalaCreatedAtFilter = and(
            gte(kabadiwalaSupportConversation.createdAt, start),
            lt(
              kabadiwalaSupportConversation.createdAt,
              new Date(end.getTime() + 24 * 60 * 60 * 1000),
            ),
          );
        } else if (start) {
          kabadiwalaCreatedAtFilter = gte(
            kabadiwalaSupportConversation.createdAt,
            start,
          );
        } else if (end) {
          kabadiwalaCreatedAtFilter = lt(
            kabadiwalaSupportConversation.createdAt,
            new Date(end.getTime() + 24 * 60 * 60 * 1000),
          );
        }
      }
      const { data: chats, err } = await tryCatch(
        ctx.db
          .select()
          .from(kabadiwalaSupportConversation)
          .where(kabadiwalaCreatedAtFilter),
      );
      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch kabadiwala support chat stats",
        });
      }
      const filterRated = (arr: SupportChat[]) =>
        arr.filter((c) => c.isRated && c.rating !== null);
      const avg = (arr: SupportChat[]) =>
        arr.length
          ? arr.reduce((a, b) => a + Number(b.rating), 0) / arr.length
          : 0;
      const closed = (chats as SupportChat[]).filter((c) => !c.isOpen);
      const ongoing = (chats as SupportChat[]).filter((c) => c.isOpen);
      const rated = filterRated(chats as SupportChat[]);
      return {
        total: (chats as SupportChat[]).length,
        averageRating: avg(rated),
        closed: closed.length,
        ongoing: ongoing.length,
      };
    }),
} satisfies TRPCRouterRecord;
