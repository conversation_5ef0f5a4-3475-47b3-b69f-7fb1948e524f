import type { TRP<PERSON>outerRecord } from "@trpc/server";
import { TRPCError } from "@trpc/server";
import { z } from "zod";

import { eq } from "@acme/db";
import { testimonials } from "@acme/db/schema";
import { tryCatch } from "@acme/validators/utils";

import { CreateTestimonialSchema } from "~/lib/zod-schema";
import { protectedProcedure } from "../trpc";

export const testimonialRouter = {
  all: protectedProcedure.query(async ({ ctx }) => {
    const { data: testimonials, err: testimonialsErr } = await tryCatch(
      ctx.db.query.testimonials.findMany(),
    );

    if (testimonialsErr) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch testimonials",
      });
    }

    return testimonials;
  }),

  create: protectedProcedure
    .input(CreateTestimonialSchema)
    .mutation(async ({ ctx, input }) => {
      const { err: testimonialErr } = await tryCatch(
        ctx.db.insert(testimonials).values({
          title: input.title,
          message: input.message,
          stars: String(input.stars),
          userName: input.userName,
          userImage: input.userImage,
          userDesignation: input.userDesignation,
          userLocation: input.userLocation,
        }),
      );

      if (testimonialErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create testimonial",
        });
      }

      return {
        message: "Testimonial created successfully",
      };
    }),

  byId: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const { data: testimonial, err: testimonialErr } = await tryCatch(
        ctx.db.query.testimonials.findFirst({
          where: eq(testimonials.id, input.id),
        }),
      );

      if (testimonialErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch testimonial",
        });
      }

      if (!testimonial) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Testimonial not found",
        });
      }

      return testimonial;
    }),

  update: protectedProcedure
    .input(CreateTestimonialSchema.extend({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const { err: testimonialErr } = await tryCatch(
        ctx.db
          .update(testimonials)
          .set({
            title: input.title,
            message: input.message,
            stars: String(input.stars),
            userName: input.userName,
            userImage: input.userImage,
            userDesignation: input.userDesignation,
            userLocation: input.userLocation,
          })
          .where(eq(testimonials.id, input.id)),
      );

      if (testimonialErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update testimonial",
        });
      }

      return {
        message: "Testimonial updated successfully",
      };
    }),

  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const { err: testimonialErr } = await tryCatch(
        ctx.db.delete(testimonials).where(eq(testimonials.id, input.id)),
      );

      if (testimonialErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to delete testimonial",
        });
      }

      return {
        message: "Testimonial deleted successfully",
      };
    }),
} satisfies TRPCRouterRecord;
