import type { T<PERSON><PERSON><PERSON>er<PERSON><PERSON><PERSON> } from "@trpc/server";
import { TRPCError } from "@trpc/server";
import { z } from "zod";

import { eq } from "@acme/db";
import { systemConfiguration, systemConfigurationEnum } from "@acme/db/schema";
import { emailSchema, phoneNumberSchema } from "@acme/validators";
import { tryCatch } from "@acme/validators/utils";

import { protectedProcedure } from "../trpc";

// Zod schema for config value by key
const configValueSchemaByKey = (
  key: (typeof systemConfigurationEnum.enumValues)[number],
) => {
  switch (key) {
    case "CONTACT_EMAIL":
    case "SUPPORT_EMAIL":
      return emailSchema;
    case "CONTACT_PHONE":
    case "SUPPORT_PHONE":
      return phoneNumberSchema;
    case "TERMS_AND_CONDITIONS":
    case "PRIVACY_POLICY":
    case "ABOUT_US":
    case "CANCELLATION_POLICY":
    case "REFUND_POLICY":
      return z.string();
    default:
      return z.coerce.number();
  }
};

const ConfigKeySchema = z.enum(systemConfigurationEnum.enumValues);

const ConfigCreateSchema = z.object({
  key: ConfigKeySchema,
  value: z.string(), // Will be refined below
});

const ConfigUpdateSchema = z.object({
  id: z.string().nonempty({ message: "Config ID is required" }),
  value: z.string(), // Will be refined below
});

export const configRouter = {
  getAllConfigs: protectedProcedure.query(async ({ ctx }) => {
    const { data, err } = await tryCatch(
      ctx.db.select().from(systemConfiguration),
    );
    if (err) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch configs",
      });
    }
    return data;
  }),

  // Get a single config by key
  getConfigByKey: protectedProcedure
    .input(z.object({ key: ConfigKeySchema }))
    .query(async ({ ctx, input }) => {
      const { data, err } = await tryCatch(
        ctx.db
          .select()
          .from(systemConfiguration)
          .where(eq(systemConfiguration.key, input.key)),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch config by key",
        });
      }
      // Return the first config for the given key, or null
      return data[0] ?? null;
    }),

  createConfig: protectedProcedure
    .input(ConfigCreateSchema)
    .mutation(async ({ ctx, input }) => {
      // Check if config with the same key already exists
      const { data: existing, err: findErr } = await tryCatch(
        ctx.db
          .select()
          .from(systemConfiguration)
          .where(eq(systemConfiguration.key, input.key)),
      );
      if (findErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to check for existing config",
        });
      }
      if (existing.length > 0) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "A config with this key already exists.",
        });
      }
      // Validate value type based on key
      const valueSchema = configValueSchemaByKey(input.key);
      const parsed = valueSchema.safeParse(input.value);
      if (!parsed.success) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: parsed.error.errors[0]?.message ?? "Invalid value",
        });
      }
      const { err } = await tryCatch(
        ctx.db.insert(systemConfiguration).values({
          key: input.key,
          value: input.value,
          createdAt: new Date(),
          updatedAt: new Date(),
        }),
      );
      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: err.message || "Failed to create config",
        });
      }
      return { message: "Config created successfully" };
    }),

  updateConfig: protectedProcedure
    .input(ConfigUpdateSchema)
    .mutation(async ({ ctx, input }) => {
      // Find config by id
      const { data: config, err: findErr } = await tryCatch(
        ctx.db
          .select()
          .from(systemConfiguration)
          .where(eq(systemConfiguration.id, input.id)),
      );
      if (findErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch config",
        });
      }
      if (!config[0]) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Config not found",
        });
      }
      // Validate value type based on key
      const key = config[0].key;
      if (!key) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Config key is missing.",
        });
      }
      const valueSchema = configValueSchemaByKey(key);
      const parsed = valueSchema.safeParse(input.value);
      if (!parsed.success) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: parsed.error.errors[0]?.message ?? "Invalid value",
        });
      }
      const { err } = await tryCatch(
        ctx.db
          .update(systemConfiguration)
          .set({ value: input.value, updatedAt: new Date() })
          .where(eq(systemConfiguration.id, input.id)),
      );
      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: err.message,
        });
      }
      return { message: "Config updated successfully" };
    }),

  deleteConfig: protectedProcedure
    .input(
      z.object({
        id: z.string().nonempty({ message: "Config ID is required" }),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { err } = await tryCatch(
        ctx.db
          .delete(systemConfiguration)
          .where(eq(systemConfiguration.id, input.id)),
      );
      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to delete config",
        });
      }
      return { message: "Config deleted successfully" };
    }),

  // Get a single config by id
  getConfigById: protectedProcedure
    .input(
      z.object({
        id: z.string().nonempty({ message: "Config ID is required" }),
      }),
    )
    .query(async ({ ctx, input }) => {
      const { data, err } = await tryCatch(
        ctx.db
          .select()
          .from(systemConfiguration)
          .where(eq(systemConfiguration.id, input.id)),
      );
      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch config by id",
        });
      }
      return data[0] ?? null;
    }),
} satisfies TRPCRouterRecord;
