import { TRPCError } from "@trpc/server";
import { and, eq } from "drizzle-orm";
import { z } from "zod";

import {
  customerPaymentStatusEnum,
  customerPaymentTransaction,
  customerTransactionForEnum,
  customerTransactionTypeEnum,
  issue,
  kabadiwala,
  order,
  seller,
} from "@acme/db/schema";
import { Razorpay } from "@acme/razorpay-sdk";
import { tryCatch } from "@acme/validators/utils";

import { env } from "~/env";
import { protectedProcedure } from "../trpc";

export const UpdateIssueStatusSchema = z.object({
  id: z.string(),
  status: z.enum(["ACKNOWLEDGED", "CLOSED", "REJECTED"]),
});

export const issueAdminRouter = {
  getAllIssues: protectedProcedure
    .input(
      z
        .object({
          status: z
            .enum(["OPEN", "ACKNOWLEDGED", "CLOSED", "REJECTED"])
            .optional(),
          kabadiwalaId: z.string().optional(),
          customerId: z.string().optional(),
        })
        .optional(),
    )
    .query(async ({ ctx, input }) => {
      const where = [];
      if (input?.status) where.push(eq(issue.status, input.status));
      if (input?.kabadiwalaId)
        where.push(eq(issue.kabadiwalaId, input.kabadiwalaId));
      if (input?.customerId) where.push(eq(issue.customerId, input.customerId));
      const { data, err } = await tryCatch(
        ctx.db
          .select({
            id: issue.id,
            status: issue.status,
            createdAt: issue.createdAt,
            updatedAt: issue.updatedAt,
            description: issue.description,
            imageUrls: issue.imageUrls,
            orderId: issue.orderId,
            kabadiwalaId: issue.kabadiwalaId,
            customerId: issue.customerId,
            adminId: issue.adminId,
            acknowledgedAt: issue.acknowledgedAt,
            closedAt: issue.closedAt,
            customerName: seller.fullName,
            kabadiwalaName: kabadiwala.name,
          })
          .from(issue)
          .leftJoin(seller, eq(issue.customerId, seller.id))
          .leftJoin(kabadiwala, eq(issue.kabadiwalaId, kabadiwala.id))
          .where(where.length ? and(...where) : undefined),
      );
      if (err)
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: err.message,
        });
      return data;
    }),

  getIssueById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const { data, err } = await tryCatch(
        ctx.db
          .select({
            id: issue.id,
            status: issue.status,
            createdAt: issue.createdAt,
            updatedAt: issue.updatedAt,
            description: issue.description,
            imageUrls: issue.imageUrls,
            orderId: issue.orderId,
            kabadiwalaId: issue.kabadiwalaId,
            customerId: issue.customerId,
            adminId: issue.adminId,
            acknowledgedAt: issue.acknowledgedAt,
            closedAt: issue.closedAt,
            customerName: seller.fullName,
            kabadiwalaName: kabadiwala.name,
          })
          .from(issue)
          .leftJoin(seller, eq(issue.customerId, seller.id))
          .leftJoin(kabadiwala, eq(issue.kabadiwalaId, kabadiwala.id))
          .where(eq(issue.id, input.id)),
      );
      if (err)
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: err.message,
        });
      const found = data[0];
      if (!found)
        throw new TRPCError({ code: "NOT_FOUND", message: "Issue not found" });
      return found;
    }),

  getIssueDetails: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const { data, err } = await tryCatch(
        ctx.db
          .select({
            id: issue.id,
            status: issue.status,
            createdAt: issue.createdAt,
            updatedAt: issue.updatedAt,
            description: issue.description,
            imageUrls: issue.imageUrls,
            orderId: issue.orderId,
            kabadiwalaId: issue.kabadiwalaId,
            customerId: issue.customerId,
            adminId: issue.adminId,
            acknowledgedAt: issue.acknowledgedAt,
            closedAt: issue.closedAt,
            customer: {
              id: seller.id,
              fullName: seller.fullName,
              phoneNumber: seller.phoneNumber,
              email: seller.email,
              image: seller.image,
            },
            kabadiwala: {
              id: kabadiwala.id,
              name: kabadiwala.name,
              phoneNumber: kabadiwala.phoneNumber,
              email: kabadiwala.email,
              image: kabadiwala.image,
              isBlocked: kabadiwala.isBlocked,
              violationCount: kabadiwala.violationCount,
              issueCount: kabadiwala.issueCount,
            },
            order: {
              id: order.id,
              status: order.status,
              totalAmount: order.totalAmount,
              completedAt: order.completedAt,
              createdAt: order.createdAt,
              updatedAt: order.updatedAt,
              paymentStatus: order.paymentStatus,
              addressId: order.addressId,
              kabadiwalaId: order.kabadiwalaId,
              sellerId: order.sellerId,
            },
          })
          .from(issue)
          .leftJoin(seller, eq(issue.customerId, seller.id))
          .leftJoin(kabadiwala, eq(issue.kabadiwalaId, kabadiwala.id))
          .leftJoin(order, eq(issue.orderId, order.id))
          .where(eq(issue.id, input.id)),
      );
      if (err)
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: err.message,
        });
      const found = data[0];
      if (!found)
        throw new TRPCError({ code: "NOT_FOUND", message: "Issue not found" });
      return found;
    }),

  updateIssueStatus: protectedProcedure
    .input(UpdateIssueStatusSchema)
    .mutation(async ({ ctx, input }) => {
      // Fetch current status first
      const { data: currentArr, err: fetchErr } = await tryCatch(
        ctx.db
          .select({ status: issue.status })
          .from(issue)
          .where(eq(issue.id, input.id)),
      );
      if (fetchErr)
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: fetchErr.message,
        });
      const currentStatus = currentArr[0]?.status;
      if (!currentStatus)
        throw new TRPCError({ code: "NOT_FOUND", message: "Issue not found" });
      if (currentStatus === "CLOSED" || currentStatus === "REJECTED") {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "Cannot update status of a closed or rejected issue.",
        });
      }
      const now = new Date();
      // Store previous status for rollback
      const previousStatus = currentStatus;
      const { data: updatedArr, err: updateErr } = await tryCatch(
        ctx.db
          .update(issue)
          .set({
            status: input.status,
            updatedAt: now,
            adminId: ctx.session.user.id,
            acknowledgedAt: input.status === "ACKNOWLEDGED" ? now : null,
            closedAt: input.status === "CLOSED" ? now : null,
          })
          .where(eq(issue.id, input.id))
          .returning(),
      );
      if (updateErr)
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: updateErr.message,
        });
      const updated = updatedArr[0];
      if (!updated)
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Failed to update issue",
        });

      // If CLOSED, trigger payout to customer (seller)
      if (input.status === "CLOSED") {
        // Fetch seller and order details
        const { data: issueDetails, err: detailsErr } = await tryCatch(
          ctx.db
            .select({
              order: {
                id: order.id,
                totalAmount: order.totalAmount,
                paymentStatus: order.paymentStatus,
                sellerId: order.sellerId,
                kabadiwalaId: order.kabadiwalaId,
                addressId: order.addressId,
              },
              seller: {
                id: seller.id,
                razorpayFundAccountId: seller.razorpayFundAccountId,
                razorpayFundValidationResponseId:
                  seller.razorpayFundValidationResponseId,
                razorpayContactId: seller.razorpayContactId,
                isPaymentVerified: seller.isPaymentVerified,
              },
            })
            .from(issue)
            .leftJoin(order, eq(issue.orderId, order.id))
            .leftJoin(seller, eq(issue.customerId, seller.id))
            .where(eq(issue.id, input.id)),
        );
        if (detailsErr)
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: detailsErr.message,
          });
        const details = issueDetails[0];
        if (!details?.order || !details.seller)
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Order or seller not found for payout",
          });
        const { order: ord, seller: sel } = details;
        if (
          !sel.razorpayFundAccountId ||
          !sel.razorpayFundValidationResponseId ||
          !sel.isPaymentVerified
        ) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Seller payout account not set up or not verified",
          });
        }
        // Create Razorpay instance
        const razorpay = new Razorpay({
          keyId: env.RAZORPAY_KEY_ID,
          keySecret: env.RAZORPAY_SECRET_KEY,
        });
        // Prepare payout request
        const payoutReq = {
          account_number: env.RAZORPAYX_ACCOUNT_NUMBER,
          fund_account_id: sel.razorpayFundAccountId,
          amount: 500 * 100, // Razorpay expects paise
          currency: "INR",
          mode: "IMPS" as const,
          purpose: "payout",
          reference_id: ord.id,
          narration: `Payout for order ${ord.id}`,
        };
        const payoutRes = await razorpay.createPayout(payoutReq);
        if (payoutRes.error || !payoutRes.data) {
          // Rollback: revert issue status to previous value if payout fails
          await ctx.db
            .update(issue)
            .set({
              status: previousStatus,
              closedAt: null,
              adminId: null,
              updatedAt: new Date(),
            })
            .where(eq(issue.id, input.id));
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: payoutRes.error?.message ?? "Failed to create payout",
          });
        }
        // Insert customer payment transaction record
        await ctx.db.insert(customerPaymentTransaction).values({
          customerId: sel.id,
          amount: "500", // Amount in rupees (string, as per schema)
          razorpayOrderId: null, // Not an order payment, so null
          razorpayPaymentId: payoutRes.data.id, // Use payout id
          currency: "INR",
          status: customerPaymentStatusEnum.enumValues[1], // "COMPLETED"
          transactionType: customerTransactionTypeEnum.enumValues[0], // "CREDIT"
          transactionFor: customerTransactionForEnum.enumValues[3], // "OTHER"
          orderId: ord.id,
          createdAt: new Date(),
          updatedAt: new Date(),
        });
        // Increment violationCount for kabadiwala
        const kabadiwalaId = updated.kabadiwalaId;
        const { data: kabadiwalaData, err: kabadiwalaErr } = await tryCatch(
          ctx.db.query.kabadiwala.findFirst({
            where: eq(kabadiwala.id, kabadiwalaId),
            columns: { violationCount: true },
          }),
        );
        if (kabadiwalaErr)
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: kabadiwalaErr.message,
          });
        const prevViolationCount = Number(
          kabadiwalaData?.violationCount ?? "0",
        );
        const newViolationCount = (prevViolationCount + 1).toString();
        const { err: kabUpdateErr } = await tryCatch(
          ctx.db
            .update(kabadiwala)
            .set({
              violationCount: newViolationCount,
              lastViolationAt: now,
            })
            .where(eq(kabadiwala.id, kabadiwalaId)),
        );
        if (kabUpdateErr)
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: kabUpdateErr.message,
          });
        // Count all CLOSED issues for this kabadiwala
        const { data: closedIssues, err: closedIssuesErr } = await tryCatch(
          ctx.db
            .select({ id: issue.id })
            .from(issue)
            .where(
              and(
                eq(issue.kabadiwalaId, kabadiwalaId),
                eq(issue.status, "CLOSED"),
              ),
            ),
        );
        if (closedIssuesErr)
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: closedIssuesErr.message,
          });
        if (closedIssues.length >= 2) {
          const { err: blockErr } = await tryCatch(
            ctx.db
              .update(kabadiwala)
              .set({ isBlocked: true })
              .where(eq(kabadiwala.id, kabadiwalaId)),
          );
          if (blockErr)
            throw new TRPCError({
              code: "INTERNAL_SERVER_ERROR",
              message: blockErr.message,
            });
        }
      }
      // Do not increment violationCount/issueCount or block on ACKNOWLEDGED
      return updated;
    }),
};
