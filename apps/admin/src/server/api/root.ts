import { adminRouter } from "./router/admin";
import { blogRouter } from "./router/blog";
import { categoryRouter } from "./router/category";
import { configRouter } from "./router/config";
import { conversationRouter } from "./router/conversation";
import { dashboardRouter } from "./router/dashboard";
import { deleteAccountRouter } from "./router/delete-account";
import { faqRouter } from "./router/faq";
import { issueAdminRouter } from "./router/issue";
import { kabadiwalaRouter } from "./router/kabadiwala";
import { orderRouter } from "./router/order";
import { pricingRouter } from "./router/pricing";
import { regionRouter } from "./router/region";
import { scraphubRouter } from "./router/scraphub";
import { sellerRouter } from "./router/seller";
import { testimonialRouter } from "./router/testimonial";
import { utilsRouter } from "./router/utils";
import { createTRPCRouter } from "./trpc";

export const appRouter = createTRPCRouter({
  util: utilsRouter,
  category: categoryRouter,
  admin: adminRouter,
  seller: sellerRouter,
  kabadiwala: kabadiwalaRouter,
  order: orderRouter,
  faq: faqRouter,
  blog: blogRouter,
  dashboard: dashboardRouter,
  deleteAccount: deleteAccountRouter,
  testimonial: testimonialRouter,
  config: configRouter,
  conversation: conversationRouter,
  issue: issueAdminRouter,
  scraphub: scraphubRouter,
  region: regionRouter,
  pricing: pricingRouter,
});

// export type definition of API
export type AppRouter = typeof appRouter;
