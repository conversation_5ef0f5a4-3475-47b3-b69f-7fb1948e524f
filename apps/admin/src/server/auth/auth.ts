import type { BetterAuthOptions } from "better-auth";
import bcrypt from "bcryptjs";
import { betterAuth } from "better-auth";
import { drizzleAdapter } from "better-auth/adapters/drizzle";

import { db } from "@acme/db/client";
import { sendEmail } from "@acme/mail";

import { env } from "../../env";

export const config = {
  database: drizzleAdapter(db, {
    provider: "pg",
  }),
  user: {
    modelName: "admin",
  },
  account: {
    modelName: "adminAccount",
    fields: {
      userId: "adminId",
    },
  },
  session: {
    modelName: "adminSession",
    fields: {
      userId: "adminId",
    },
  },
  verification: {
    modelName: "adminVerification",
  },
  emailAndPassword: {
    enabled: true,
    password: {
      hash: async (password) => {
        return bcrypt.hash(password, 10);
      },
      verify: async ({ hash, password }) => {
        return bcrypt.compare(password, hash);
      },
    },
    sendResetPassword: async ({ user, url, token }) => {
      const res = await sendEmail({
        to: user.email,
        subject: "Reset Password",
        content: `<div>
                    <h1>Reset Password</h1>
                    <p>Click <a href="${url}?token=${token}">here</a> to reset your password</p>
                    <p>Or copy and paste the link below into your browser: ${url}?token=${token}</p>
                </div>`,
      });
      console.log("mail response is: ", res);
    },
  },
  baseURL: env.NEXT_PUBLIC_ADMIN_BETTER_AUTH_URL,
  secret: env.ADMIN_BETTER_AUTH_SECRET,
  trustedOrigins: ["exp://"],
} satisfies BetterAuthOptions;

export const auth = betterAuth(config);
export type Session = typeof auth.$Infer.Session;
