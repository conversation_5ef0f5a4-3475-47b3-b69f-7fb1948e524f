import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { getSessionCookie } from "better-auth/cookies";

const PRIVATE_ROUTES = [
  "/",
  "/auth/otp-verification/success",
  "/onboarding/1",
  "/onboarding/2",
  "/onboarding/3",
  "/onboarding/success",
];

export function authMiddleware(request: NextRequest) {
  const cookies = getSessionCookie(request);

  const isPrivateRoute = PRIVATE_ROUTES.includes(request.nextUrl.pathname);

  if (isPrivateRoute && !cookies) {
    return NextResponse.redirect(new URL("/auth/login", request.url));
  }

  return NextResponse.next();
}
