import Image from "next/image";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>alogTrigger,
} from "@acme/ui/components/ui/dialog";

interface ImageDialogProps {
  imageUrl?: string;
}

const ImageDialog = ({ imageUrl }: ImageDialogProps) => {
  return (
    <Dialog>
      <DialogTrigger className="underline underline-offset-4">
        View Image
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          {imageUrl ? (
            <div className="relative aspect-video overflow-hidden">
              <Image
                src={imageUrl}
                alt="image"
                fill
                className="relative object-contain"
              />
            </div>
          ) : (
            <div className="text-center">No image available</div>
          )}
        </DialogHeader>
      </DialogContent>
    </Dialog>
  );
};

export default ImageDialog;
