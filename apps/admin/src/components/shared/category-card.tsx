"use client";

import Image from "next/image";
import { formatDistanceToNow } from "date-fns";

import type { category } from "@acme/db/schema";

import CategoriesActions from "~/app/(with-layout)/manage-categories/categories-actions";

interface CategoryCardProps {
  category: typeof category.$inferSelect;
}

const CategoryCard = ({ category }: CategoryCardProps) => {
  return (
    <div className="flex overflow-hidden rounded-xl border bg-white shadow-sm transition-shadow hover:shadow-md">
      {/* Category image on left */}
      <div className="relative h-auto w-40 flex-shrink-0 overflow-hidden">
        <Image
          src={category.image}
          alt={category.name}
          width={160}
          height={160}
          className="h-full w-full object-cover"
          style={{ aspectRatio: "1/1" }}
        />
      </div>

      {/* Category content on right */}
      <div className="flex flex-1 flex-col justify-between p-4">
        <div>
          <div className="mb-1 flex items-center justify-between">
            <h3 className="line-clamp-1 text-lg font-bold">{category.name}</h3>
            {category.isActive !== null && (
              <span
                className={`ml-2 rounded-full px-2 py-0.5 text-xs font-medium ${
                  category.isActive
                    ? "bg-green-100 text-green-800"
                    : "bg-red-100 text-red-800"
                }`}
              >
                {category.isActive ? "Active" : "Inactive"}
              </span>
            )}
            <CategoriesActions category={category} />
          </div>

          {category.description && (
            <p className="mb-2 line-clamp-2 text-sm text-gray-500">
              {category.description}
            </p>
          )}
        </div>

        <div className="flex items-center justify-between text-xs">
          {category.rate && (
            <span className="rounded-md bg-blue-50 px-2 py-1 font-medium text-blue-700">
              Rate: {category.rate}
            </span>
          )}
          <span className="text-gray-400">
            {formatDistanceToNow(new Date(category.createdAt), {
              addSuffix: true,
            })}
          </span>
        </div>
      </div>
    </div>
  );
};

export default CategoryCard;
