import { ClipboardCopy } from "lucide-react";
import { toast } from "sonner";

import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@acme/ui/components/ui/avatar";
import { Card } from "@acme/ui/components/ui/card";
import { cn } from "@acme/ui/lib/utils";

interface ConversationCardProps {
  avatarUrl?: string;
  name: string;
  orderId: string;
  lastMessage?: string;
  selected?: boolean;
  onClick?: () => void;
  isClosed?: boolean;
  rating?: string | null;
  isRated?: boolean;
}

const ConversationCard = ({
  avatarUrl,
  name,
  orderId,
  lastMessage,
  selected,
  onClick,
  isClosed,
  rating,
  isRated,
}: ConversationCardProps) => {
  const copyToClipboard = (text: string) => {
    void navigator.clipboard.writeText(text);
    toast.success("Order ID copied to clipboard");
  };
  return (
    <Card
      className={cn(
        "flex cursor-pointer items-center gap-3 border p-3 transition-colors",
        selected ? "border-yellow-500 bg-yellow-50" : "hover:bg-muted/50",
      )}
      onClick={onClick}
      tabIndex={0}
      role="button"
    >
      <Avatar>
        <AvatarImage src={avatarUrl} alt={name} />
        <AvatarFallback>{name[0]}</AvatarFallback>
      </Avatar>
      <div className="flex min-w-0 flex-1 flex-col">
        <span className="truncate text-sm font-medium text-black-900">
          {name}
        </span>
        <span className="flex flex-row items-center truncate text-xs text-muted-foreground">
          {orderId ? `Order: ${orderId}` : "Help/Support"}
          <button
            className="ml-1 h-fit w-fit rounded-md border p-1 hover:bg-muted"
            title="Copy Order ID"
            aria-label="Copy Order ID"
            onClick={(e) => {
              e.stopPropagation();
              copyToClipboard(orderId);
            }}
          >
            <ClipboardCopy className="size-3" />
          </button>
        </span>
        {lastMessage && (
          <span className="mt-0.5 truncate text-xs text-black-700">
            {lastMessage}
          </span>
        )}
        {isClosed && (
          <span className="mt-1 inline-block w-fit rounded bg-gray-200 px-2 py-0.5 text-xs font-semibold text-gray-700">
            Closed
          </span>
        )}
        {isClosed && isRated && (
          <span className="ml-2 mt-1 inline-block w-fit rounded bg-blue-100 px-2 py-0.5 text-xs font-semibold text-blue-700">
            {rating === null
              ? "Rating: Skipped"
              : rating === "1"
                ? "😡"
                : rating === "2"
                  ? "😕"
                  : rating === "3"
                    ? "😐"
                    : rating === "4"
                      ? "🙂"
                      : rating === "5"
                        ? "😍"
                        : `Rating: ${rating}`}
          </span>
        )}
      </div>
    </Card>
  );
};

export default ConversationCard;
