import { useQueryState } from "nuqs";

import { SidebarInset } from "@acme/ui/components/ui/sidebar";
import { Tabs, TabsList, TabsTrigger } from "@acme/ui/components/ui/tabs";
import { cn } from "@acme/ui/lib/utils";

import ConversationCard from "./conversation-card";

interface ConversationSidebarProps<T> {
  conversations: T[];
  selectedOrderId?: string;
  onSelect: (orderId: string) => void;
  renderCard?: (
    c: T,
    selected: boolean,
    onClick: () => void,
  ) => React.ReactNode;
  getOrderId: (c: T) => string;
  className?: string;
  tabKey: string;
  tabLabels?: { active: string; closed: string };
  onTabChange?: (tab: string) => void;
  isLoading?: boolean;
}

const ConversationSidebar = <T,>({
  conversations,
  selectedOrderId,
  onSelect,
  renderCard,
  getOrderId,
  className,
  tabKey,
  tabLabels = { active: "Active", closed: "Closed" },
  onTabChange,
  isLoading = false,
}: ConversationSidebarProps<T>) => {
  const [tab, setTab] = useQueryState(tabKey, { defaultValue: "active" });
  const handleTabChange = (val: string) => {
    void setTab(val);
    onTabChange?.(val);
  };
  return (
    <SidebarInset
      className={cn(
        "hide-scrollbar h-full w-[360px] min-w-[16rem] max-w-md border-r bg-white",
        className,
      )}
    >
      <Tabs
        value={tab}
        onValueChange={handleTabChange}
        className="w-full border-b bg-white p-1"
      >
        <TabsList className="w-full">
          <TabsTrigger value="active" className="w-full">
            {tabLabels.active}
          </TabsTrigger>
          <TabsTrigger value="closed" className="w-full">
            {tabLabels.closed}
          </TabsTrigger>
        </TabsList>
      </Tabs>
      <div className="hide-scrollbar flex max-h-[80dvh] min-h-[80dvh] flex-col gap-2 overflow-y-auto p-2 py-3">
        {isLoading ? (
          <div className="flex items-center justify-center py-8 text-muted-foreground">
            Loading conversations...
          </div>
        ) : (
          conversations.map((c) => {
            const orderId = getOrderId(c);
            const selected = orderId === selectedOrderId;
            return renderCard ? (
              renderCard(c, selected, () => onSelect(orderId))
            ) : (
              <ConversationCard
                key={orderId}
                name={"Name"}
                orderId={orderId}
                selected={selected}
                onClick={() => onSelect(orderId)}
              />
            );
          })
        )}
      </div>
    </SidebarInset>
  );
};
export default ConversationSidebar;
