import { forwardRef } from "react";
import { Send } from "lucide-react";

import { Button } from "@acme/ui/components/ui/button";
import { Input } from "@acme/ui/components/ui/input";

interface ChatInputProps {
  value: string;
  onChange: (v: string) => void;
  onSend: () => void;
  loading?: boolean;
}

const ChatInput = forwardRef<HTMLInputElement, ChatInputProps>(
  ({ value, onChange, onSend, loading }, ref) => {
    return (
      <div className="flex items-center gap-3 border-t bg-white px-4 pb-4 pt-2">
        <Input
          ref={ref}
          placeholder="Type your message here..."
          className="rounded-full border-black-200 bg-black-0 px-3 py-3"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          onKeyDown={(e) => {
            if (e.key === "Enter" && !e.shiftKey) {
              e.preventDefault();
              onSend();
            }
          }}
          disabled={loading}
        />
        <Button
          className="rounded-full bg-yellow-450 p-3"
          onClick={onSend}
          disabled={loading ?? !value.trim()}
        >
          <Send />
        </Button>
      </div>
    );
  },
);
ChatInput.displayName = "ChatInput";
export default ChatInput;
