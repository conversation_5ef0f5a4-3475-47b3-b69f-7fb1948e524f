import { Suspense } from "react";

import { Separator } from "@acme/ui/components/ui/separator";

import DataTableSkeleton from "~/components/skeleton/data-table-skeleton";
import { getQueryClient, HydrateClient, trpc } from "~/trpc/server";
import DeleteAccountRequestsDataTable from "./delete-account-requests-data-table";

export const metadata = {
  title: "Delete Account Requests",
  description: "Manage user delete account requests",
};

const ManageDeleteAccountRequests = async () => {
  const queryClient = getQueryClient();

  await queryClient.prefetchQuery(
    trpc.deleteAccount.getAllDeleteAccountRequests.queryOptions(),
  );

  return (
    <HydrateClient>
      <div>
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Delete Account Requests
            </h1>
            <p className="text-muted-foreground">
              Review and process user account deletion requests.
            </p>
          </div>
        </div>
        <Separator className="my-6" />
        <Suspense fallback={<DataTableSkeleton />}>
          <DeleteAccountRequestsDataTable />
        </Suspense>
      </div>
    </HydrateClient>
  );
};

export default ManageDeleteAccountRequests;
