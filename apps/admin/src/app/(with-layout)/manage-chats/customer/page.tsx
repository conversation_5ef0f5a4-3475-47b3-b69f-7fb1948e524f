"use client";

import { Suspense, useEffect, useRef, useState } from "react";
import Link from "next/link";
import {
  skipToken,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";
import { ArrowUpRight, RotateCw } from "lucide-react";
import { useQueryState } from "nuqs";
import { toast } from "sonner";

import type { CustomerSupportConversation, SupportMessage } from "~/lib/types";
import AlertWrapper from "~/components/shared/alert-wrapper";
import ChatInput from "~/components/shared/chat-input";
import ConversationCard from "~/components/shared/conversation-card";
import ConversationMessagesList from "~/components/shared/conversation-messages-list";
import ConversationSidebar from "~/components/shared/conversation-sidebar";
import { CHAT_REFETCH_TIME_INTERVAL } from "~/lib/constants";
import { useSession } from "~/server/auth/client";
import { useTRPC } from "~/trpc/react";

const ManageChatsCustomerPage = () => {
  const [tab] = useQueryState("customerChatTab", { defaultValue: "active" });
  const trpc = useTRPC();
  const queryClient = useQueryClient();
  const { data: session } = useSession();

  // Fetch active/closed conversations for sidebar
  const {
    data: activeConversations = [],
    isLoading: isLoadingActive,
    refetch: refetchActiveConversations,
    isRefetching: isRefetchingActive,
  } = useQuery(
    trpc.conversation.getActiveCustomerSupportConversations.queryOptions(
      undefined,
      {
        refetchInterval: CHAT_REFETCH_TIME_INTERVAL,
      },
    ),
  );
  const {
    data: closedConversations = [],
    isLoading: isLoadingClosed,
    refetch: refetchClosedConversations,
    isRefetching: isRefetchingClosed,
  } = useQuery(
    trpc.conversation.getClosedCustomerSupportConversations.queryOptions(
      tab !== "closed" ? skipToken : undefined,
      {
        refetchInterval: tab === "closed" ? CHAT_REFETCH_TIME_INTERVAL : false,
      },
    ),
  );
  const conversations: CustomerSupportConversation[] =
    tab === "active" ? activeConversations : closedConversations;
  const isLoadingConvos = tab === "active" ? isLoadingActive : isLoadingClosed;
  const isRefetchingConvos =
    tab === "active" ? isRefetchingActive : isRefetchingClosed;
  // Use conversationId for selection
  const [conversationId, setConversationId] = useQueryState(
    "customerChatConversationId",
  );

  // Fetch selected conversation messages
  const { data: messages } = useQuery(
    trpc.conversation.getCustomerSupportMessages.queryOptions(
      conversationId ? { conversationId } : skipToken,
      {
        enabled: !!conversationId,
        refetchInterval: CHAT_REFETCH_TIME_INTERVAL,
      },
    ),
  );

  const [message, setMessage] = useState("");
  const inputRef = useRef<HTMLInputElement>(null);
  const { mutate: sendMessage, isPending: isSending } = useMutation(
    trpc.conversation.sendCustomerSupportMessageAsAdmin.mutationOptions({
      onSuccess: async () => {
        if (conversationId) {
          await queryClient.invalidateQueries(
            trpc.conversation.getCustomerSupportMessages.queryOptions({
              conversationId,
            }),
          );
        }
        setMessage("");
        inputRef.current?.focus();
      },
    }),
  );

  // Close and delete conversation mutations
  const { mutate: closeConversation } = useMutation(
    trpc.conversation.closeCustomerSupportConversation.mutationOptions({
      onSuccess: async () => {
        if (conversationId) {
          await queryClient.invalidateQueries(
            trpc.conversation.getAllCustomerSupportConversations.queryOptions(),
          );
          void setConversationId(null);
        }
      },
    }),
  );
  const { mutate: deleteConversation } = useMutation(
    trpc.conversation.deleteCustomerSupportConversation.mutationOptions({
      onSuccess: async () => {
        if (conversationId) {
          await queryClient.invalidateQueries(
            trpc.conversation.getAllCustomerSupportConversations.queryOptions(),
          );
          void setConversationId(null);
        }
      },
    }),
  );

  // Sort conversations: latest on top
  const sortedConversations: CustomerSupportConversation[] = [
    ...conversations,
  ].sort((a, b) => {
    const aTime = a.lastMessage?.createdAt
      ? new Date(a.lastMessage.createdAt).getTime()
      : 0;
    const bTime = b.lastMessage?.createdAt
      ? new Date(b.lastMessage.createdAt).getTime()
      : 0;
    return bTime - aTime;
  });

  const [, setDialog] = useState<null | "close" | "delete">(null);

  const invalidateConvoQueries = async () => {
    await Promise.all([
      queryClient.invalidateQueries({
        queryKey:
          trpc.conversation.getActiveCustomerSupportConversations.queryKey(),
      }),
      queryClient.invalidateQueries({
        queryKey:
          trpc.conversation.getClosedCustomerSupportConversations.queryKey(),
      }),
    ]);
  };

  const handleCloseConversation = () => {
    if (!conversationId) return;
    // setDialogLoading(true);
    closeConversation(
      { conversationId },
      {
        onSuccess: () => {
          void invalidateConvoQueries();
          void setConversationId(null);
          setDialog(null);
          //   setDialogLoading(false);
        },
        onError: () => {
          toast.error("Failed to close conversation");
        },
        // onError: () => setDialogLoading(false),
      },
    );
  };

  const handleDeleteConversation = () => {
    if (!conversationId) return;
    // setDialogLoading(true);
    deleteConversation(
      { conversationId },
      {
        onSuccess: () => {
          void invalidateConvoQueries();
          void setConversationId(null);
          setDialog(null);
          //   setDialogLoading(false);
        },
        onError: () => {
          toast.error("Failed to delete conversation");
        },
        // onError: () => setDialogLoading(false),
      },
    );
  };

  // Add refresh handlers
  const handleRefresh = async () => {
    if (tab === "active") {
      await refetchActiveConversations();
    } else {
      await refetchClosedConversations();
    }
  };

  // Deselect conversation if it doesn't belong to the current tab
  useEffect(() => {
    if (!conversationId) return;
    const currentConvos =
      tab === "active" ? activeConversations : closedConversations;
    const found = currentConvos.some(
      (c) => c.conversationId === conversationId,
    );
    if (!found) {
      void setConversationId(null);
    }
  }, [
    tab,
    activeConversations,
    closedConversations,
    conversationId,
    setConversationId,
  ]);

  return (
    <div className="flex h-fit">
      <div className="flexflex-col">
        <div className="flex items-center gap-2 px-2 pb-1 pt-2">
          <span className="text-lg font-semibold">
            {tab === "active" ? "Active" : "Closed"} Chats
          </span>
          <button
            className="rounded p-1 hover:bg-gray-100"
            title="Refresh"
            onClick={handleRefresh}
            aria-label="Refresh conversations"
          >
            <RotateCw
              className={isRefetchingConvos ? "animate-spin" : ""}
              size={18}
            />
          </button>
        </div>
        <ConversationSidebar
          conversations={sortedConversations}
          selectedOrderId={conversationId ?? undefined}
          onSelect={setConversationId}
          getOrderId={(c) => c.conversationId}
          renderCard={(c, selected, onClick) => (
            <ConversationCard
              key={c.conversationId}
              avatarUrl={c.customer.image ?? undefined}
              name={c.customer.name}
              orderId={c.orderId ?? "Help/Support"}
              lastMessage={c.lastMessage?.content}
              selected={selected}
              onClick={onClick}
              isClosed={c.isOpen === false}
              rating={c.rating}
              isRated={c.isRated}
            />
          )}
          tabKey="customerChatTab"
          tabLabels={{ active: "Active", closed: "Closed" }}
          isLoading={tab === "active" ? isLoadingActive : isLoadingClosed}
        />
      </div>
      <main className="hide-scrollbar flex max-h-[95dvh] flex-1 flex-col overflow-y-auto bg-white px-2">
        {conversationId ? (
          <>
            {(() => {
              const convo = sortedConversations.find(
                (c) => c.conversationId === conversationId,
              );
              if (convo?.orderId) {
                return (
                  <div className="mb-2">
                    <Link
                      href={`/manage-orders/${convo.orderId}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-md flex flex-row items-center gap-1 rounded-md"
                    >
                      View Order Details
                      <ArrowUpRight className="size-4" />
                    </Link>
                  </div>
                );
              }
              return null;
            })()}
            <div className="flex-1 overflow-y-auto">
              <ConversationMessagesList
                messages={messages as SupportMessage[]}
                currentUserId={session?.user.id ?? ""}
              />
            </div>
            <div className="flex items-center gap-2 border-t p-2">
              {(() => {
                const convo = sortedConversations.find(
                  (c) => c.conversationId === conversationId,
                );
                if (convo && convo.isOpen === false) {
                  return (
                    <>
                      <AlertWrapper
                        trigger={
                          <button className="rounded bg-red-200 px-3 py-1 text-xs font-semibold text-red-900 hover:bg-red-300">
                            Delete Conversation
                          </button>
                        }
                        onConfirm={handleDeleteConversation}
                        title="Delete Conversation"
                        description="Are you sure you want to delete this conversation? This action cannot be undone."
                      />
                    </>
                  );
                }
                return (
                  <>
                    <AlertWrapper
                      trigger={
                        <button className="rounded bg-yellow-200 px-3 py-1 text-xs font-semibold text-yellow-900 hover:bg-yellow-300">
                          Close Conversation
                        </button>
                      }
                      onConfirm={handleCloseConversation}
                      title="Close Conversation"
                      description="Are you sure you want to close this conversation? You can still delete it later."
                    />
                    <AlertWrapper
                      trigger={
                        <button className="rounded bg-red-200 px-3 py-1 text-xs font-semibold text-red-900 hover:bg-red-300">
                          Delete Conversation
                        </button>
                      }
                      onConfirm={handleDeleteConversation}
                      title="Delete Conversation"
                      description="Are you sure you want to delete this conversation? This action cannot be undone."
                    />
                  </>
                );
              })()}
            </div>
            {(() => {
              const convo = sortedConversations.find(
                (c) => c.conversationId === conversationId,
              );
              if (convo && convo.isOpen === false) {
                return (
                  <div className="flex flex-col items-center justify-center py-4 font-semibold text-red-600">
                    <div>
                      Conversation Closed
                      {convo.isRated && (
                        <span className="ml-4 text-base font-normal text-blue-700">
                          {convo.rating === null
                            ? "(Rating: Skipped)"
                            : convo.rating === "1"
                              ? "😡"
                              : convo.rating === "2"
                                ? "😕"
                                : convo.rating === "3"
                                  ? "😐"
                                  : convo.rating === "4"
                                    ? "🙂"
                                    : convo.rating === "5"
                                      ? "😄"
                                      : `(Rating: ${convo.rating})`}
                        </span>
                      )}
                    </div>
                    {/* Show review text if present and rated */}
                    {convo.isRated && convo.reviewText && (
                      <div className="mt-2 max-w-xl rounded bg-blue-50 px-4 py-2 text-sm font-normal text-blue-900">
                        <span className="font-semibold">Review:</span>{" "}
                        {convo.reviewText}
                      </div>
                    )}
                  </div>
                );
              }
              return (
                <ChatInput
                  ref={inputRef}
                  value={message}
                  onChange={setMessage}
                  onSend={() => {
                    if (message.trim()) {
                      sendMessage({ conversationId, content: message });
                    }
                  }}
                  loading={isSending}
                />
              );
            })()}
          </>
        ) : (
          <div className="flex flex-1 items-center justify-center text-muted-foreground">
            {isLoadingConvos
              ? "Loading..."
              : "Select a conversation to start chatting."}
          </div>
        )}
      </main>
    </div>
  );
};

const MainManageChatsCustomerPage = () => {
  return (
    <Suspense>
      <ManageChatsCustomerPage />
    </Suspense>
  );
};
export default MainManageChatsCustomerPage;
