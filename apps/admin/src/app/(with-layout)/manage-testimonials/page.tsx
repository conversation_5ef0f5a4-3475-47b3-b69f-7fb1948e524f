import type { SearchParams } from "nuqs/server";
import { Suspense } from "react";

import { Separator } from "@acme/ui/components/ui/separator";

import DataTableSkeleton from "~/components/skeleton/data-table-skeleton";
import { loadContentSearchParams } from "~/lib/nuqs-utils";
import { getQueryClient, HydrateClient, trpc } from "~/trpc/server";
import TestimonialsDataTable from "./testimonials-data-table";

export const metadata = {
  title: "Testimonials Management",
  description: "Manage your website's testimonials",
};

interface ManageTestimonialsProps {
  searchParams: Promise<SearchParams>;
}

const ManageTestimonials = async ({
  searchParams,
}: ManageTestimonialsProps) => {
  const { testimonial_id } = await loadContentSearchParams(searchParams);
  const queryClient = getQueryClient();

  // Prefetch list data
  await queryClient.prefetchQuery(trpc.testimonial.all.queryOptions());

  // Prefetch individual item data if ID exists
  if (testimonial_id) {
    await queryClient.prefetchQuery(
      trpc.testimonial.byId.queryOptions({
        id: testimonial_id,
      }),
    );
  }

  return (
    <HydrateClient>
      <div>
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Testimonials Management
            </h1>
            <p className="text-muted-foreground">
              Create, edit and manage testimonials on your website.
            </p>
          </div>
        </div>
        <Separator className="my-6" />
        <Suspense fallback={<DataTableSkeleton />}>
          <TestimonialsDataTable />
        </Suspense>
      </div>
    </HydrateClient>
  );
};

export default ManageTestimonials;
