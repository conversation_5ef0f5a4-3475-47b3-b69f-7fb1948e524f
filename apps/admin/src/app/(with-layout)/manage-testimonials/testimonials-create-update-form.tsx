"use client";

import type { z } from "zod";
import { useEffect } from "react";
import Image from "next/image";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  skipToken,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";
import { generateUploadButton } from "@uploadthing/react";
import { Loader2, Trash, Upload } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

import { Button } from "@acme/ui/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@acme/ui/components/ui/form";
import { Input } from "@acme/ui/components/ui/input";
import { Textarea } from "@acme/ui/components/ui/textarea";

import type { OurFileRouter } from "~/app/api/uploadthing/core";
import { useSheet } from "~/hooks/use-sheet";
import { testimonialParamName } from "~/lib/constants";
import { CreateTestimonialSchema } from "~/lib/zod-schema";
import { useTRPC } from "~/trpc/react";

type TestimonialFormValues = z.infer<typeof CreateTestimonialSchema>;

export const UploadButton = generateUploadButton<OurFileRouter>();

export function TestimonialsCreateUpdateForm() {
  const trpc = useTRPC();
  const trpcUtils = useQueryClient();
  const { paramValue: testimonialId, closeSheet } =
    useSheet(testimonialParamName);
  const isEditMode = !!testimonialId;

  const { data: testimonialData, isLoading } = useQuery(
    trpc.testimonial.byId.queryOptions(
      testimonialId ? { id: testimonialId } : skipToken,
    ),
  );

  const form = useForm<TestimonialFormValues>({
    resolver: zodResolver(CreateTestimonialSchema),
    defaultValues: testimonialData
      ? {
          title: testimonialData.title,
          message: testimonialData.message,
          stars: Number(testimonialData.stars),
          userImage: testimonialData.userImage ?? undefined,
          userName: testimonialData.userName ?? undefined,
          userDesignation: testimonialData.userDesignation ?? undefined,
          userLocation: testimonialData.userLocation ?? undefined,
        }
      : {
          title: "",
          message: "",
          stars: 5,
          userImage: undefined,
          userDesignation: undefined,
          userLocation: undefined,
        },
  });

  const createTestimonial = useMutation(
    trpc.testimonial.create.mutationOptions({
      onSuccess: (opts) => {
        toast.success(opts.message);
        void trpcUtils.invalidateQueries({
          queryKey: trpc.testimonial.all.queryKey(),
        });
        closeSheet();
      },
      onError: (error) => {
        toast.error(error.message);
      },
    }),
  );

  const updateTestimonial = useMutation(
    trpc.testimonial.update.mutationOptions({
      onSuccess: (opts) => {
        toast.success(opts.message);
        void trpcUtils.invalidateQueries({
          queryKey: trpc.testimonial.all.queryKey(),
        });
        closeSheet();
      },
      onError: (error) => {
        toast.error(error.message);
      },
    }),
  );

  const deleteImage = useMutation(
    trpc.util.deleteUploadthingFileUsingFileKey.mutationOptions({
      onSuccess: (opts) => {
        toast.success(opts.message);
        form.setValue("userImage", "");
      },
      onError: (error) => {
        toast.error(error.message);
      },
    }),
  );

  const onSubmit = (data: TestimonialFormValues) => {
    if (isEditMode) {
      updateTestimonial.mutate({ ...data, id: testimonialId });
    } else {
      createTestimonial.mutate(data);
    }
  };

  useEffect(() => {
    if (isEditMode && testimonialData) {
      form.reset({
        title: testimonialData.title,
        message: testimonialData.message,
        stars: Number(testimonialData.stars),
        userImage: testimonialData.userImage ?? undefined,
        userName: testimonialData.userName ?? undefined,
        userDesignation: testimonialData.userDesignation ?? undefined,
        userLocation: testimonialData.userLocation ?? undefined,
      });
    }
  }, [isEditMode, testimonialData, form]);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <FormField
          control={form.control}
          name="title"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Title</FormLabel>
              <FormControl>
                <Input placeholder="Enter testimonial title" {...field} />
              </FormControl>
              <FormDescription>The title of the testimonial.</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="message"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Message</FormLabel>
              <FormControl>
                <Textarea placeholder="Enter testimonial message" {...field} />
              </FormControl>
              <FormDescription>The message of the testimonial.</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="stars"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Stars</FormLabel>
              <FormControl>
                <Input
                  placeholder="Enter testimonial stars"
                  min={1}
                  max={5}
                  type="number"
                  {...field}
                />
              </FormControl>
              <FormDescription>
                The number of stars for the testimonial.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="userImage"
          render={({ field }) => (
            <FormItem>
              <FormLabel>User Image</FormLabel>
              <FormControl>
                {field.value ? (
                  <div className="relative">
                    <Image
                      src={field.value}
                      alt="user avatar"
                      height={200}
                      width={200}
                      className="h-32 w-32 rounded-full object-cover"
                    />
                    <Button
                      type="button"
                      variant="destructive"
                      size="sm"
                      className="mt-2"
                      disabled={deleteImage.isPending}
                      onClick={() =>
                        deleteImage.mutate({ fileKey: field.value ?? "" })
                      }
                    >
                      {deleteImage.isPending ? (
                        <>
                          <Loader2 className="animate-spin" />
                          Removing Image
                        </>
                      ) : (
                        <>
                          <Trash className="mr-2 h-4 w-4" />
                          Remove Image
                        </>
                      )}
                    </Button>
                  </div>
                ) : (
                  <UploadButton
                    endpoint="imageUploader"
                    appearance={{
                      button:
                        "bg-primary text-primary-foreground hover:bg-primary/90 rounded-md px-4 py-2 text-sm font-medium shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring",
                      allowedContent: "text-muted-foreground text-xs",
                      container: "flex flex-col gap-2 w-full",
                    }}
                    content={{
                      button: ({ isUploading }) => (
                        <span className="flex items-center gap-2">
                          {isUploading ? (
                            <Loader2 className="animate-spin" />
                          ) : (
                            <Upload className="size-4" />
                          )}
                          {isUploading
                            ? "Uploading..."
                            : "Upload Profile Image"}
                        </span>
                      ),
                      allowedContent: "JPEG, PNG or WebP (max 4MB)",
                    }}
                    onClientUploadComplete={(res) => {
                      const imageUrl = res[0]?.ufsUrl;
                      if (!imageUrl) {
                        toast.error("Image upload failed");
                        return;
                      }

                      field.onChange(imageUrl);
                      toast.success("Image uploaded successfully");
                    }}
                    onUploadError={(error: Error) => {
                      toast.error(`Upload error: ${error.message}`);
                    }}
                  />
                )}
              </FormControl>
              <FormDescription>
                Upload a user image for the user.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="userName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>User Name</FormLabel>
              <FormControl>
                <Input placeholder="Enter user name" {...field} />
              </FormControl>
              <FormDescription>The name of the user.</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="userDesignation"
          render={({ field }) => (
            <FormItem>
              <FormLabel>User Designation</FormLabel>
              <FormControl>
                <Input placeholder="Enter user designation" {...field} />
              </FormControl>
              <FormDescription>The designation of the user.</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="userLocation"
          render={({ field }) => (
            <FormItem>
              <FormLabel>User Location</FormLabel>
              <FormControl>
                <Input placeholder="eg: Delhi, Mumbai etc" {...field} />
              </FormControl>
              <FormDescription>The location of the user.</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex gap-2">
          <Button
            type="submit"
            disabled={
              createTestimonial.isPending ||
              updateTestimonial.isPending ||
              deleteImage.isPending
            }
          >
            {createTestimonial.isPending || updateTestimonial.isPending ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {isEditMode ? "Updating..." : "Creating..."}
              </>
            ) : (
              <>{isEditMode ? "Update Testimonial" : "Create Testimonial"}</>
            )}
          </Button>
          <Button type="button" variant="outline" onClick={closeSheet}>
            Cancel
          </Button>
        </div>
      </form>
    </Form>
  );
}

export default TestimonialsCreateUpdateForm;
