"use client";

import type {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
} from "@tanstack/react-table";
import { useState } from "react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { ChevronDown, Edit, MoreHorizontal, Trash } from "lucide-react";
import { toast } from "sonner";

import { Button } from "@acme/ui/components/ui/button";
import { Checkbox } from "@acme/ui/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@acme/ui/components/ui/dropdown-menu";
import { Input } from "@acme/ui/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@acme/ui/components/ui/table";
import { cn } from "@acme/ui/lib/utils";

import AlertWrapper from "~/components/shared/alert-wrapper";
import ImageDialog from "~/components/shared/image-dialog";
import SortableColumnHeader from "~/components/shared/sortable-column-header";
import { useSheet } from "~/hooks/use-sheet";
import { testimonialParamName } from "~/lib/constants";
import { useTRPC } from "~/trpc/react";
import TestimonialsFormSheet from "./testimonials-form-sheet";

const TestimonialsDataTable = () => {
  const trpc = useTRPC();
  const trpcUtils = useQueryClient();
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState({});
  const { openSheet } = useSheet(testimonialParamName);

  const { data: testimonials = [] } = useQuery(
    trpc.testimonial.all.queryOptions(),
  );

  const deleteTestimonial = useMutation(
    trpc.testimonial.delete.mutationOptions({
      onSuccess: () => {
        toast.success("Testimonial deleted successfully");
        void trpcUtils.invalidateQueries({
          queryKey: trpc.testimonial.all.queryKey(),
        });
      },
      onError: (error) => {
        toast.error(error.message);
      },
    }),
  );

  const columns: ColumnDef<(typeof testimonials)[number]>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "title",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Title" />
      ),
      cell: ({ row }) => (
        <div className="max-w-[200px] truncate font-medium">
          {row.original.title}
        </div>
      ),
    },
    {
      accessorKey: "message",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Message" />
      ),
      cell: ({ row }) => (
        <div className="max-w-[150px] truncate font-mono text-sm">
          {row.original.message}
        </div>
      ),
    },
    {
      accessorKey: "stars",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Stars" />
      ),
      cell: ({ row }) => <div>{row.original.stars}</div>,
    },
    {
      accessorKey: "userImage",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="User Image" />
      ),
      cell: ({ row }) => (
        <ImageDialog imageUrl={row.original.userImage ?? undefined} />
      ),
    },
    {
      accessorKey: "userName",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="User Name" />
      ),
      cell: ({ row }) => <div>{row.original.userName ?? "Unknown"}</div>,
    },
    {
      accessorKey: "userDesignation",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="User Designation" />
      ),
      cell: ({ row }) => <div>{row.original.userDesignation ?? "Unknown"}</div>,
    },
    {
      accessorKey: "userLocation",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="User Location" />
      ),
      cell: ({ row }) => <div>{row.original.userLocation ?? "Unknown"}</div>,
    },
    {
      accessorKey: "updatedAt",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Updated On" />
      ),
      cell: ({ row }) => {
        const date = row.getValue("updatedAt");
        return date ? (
          <div>{new Date(date as string).toLocaleDateString()}</div>
        ) : (
          <div className="text-muted-foreground">Not updated</div>
        );
      },
    },
    {
      accessorKey: "createdAt",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Created On" />
      ),
      cell: ({ row }) => {
        const date = new Date(row.getValue("createdAt"));
        return <div>{date.toLocaleDateString()}</div>;
      },
    },
    {
      id: "actions",
      enableHiding: false,
      cell: ({ row }) => {
        const testimonial = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => openSheet(testimonial.id)}>
                <Edit className="h-4 w-4" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                className="text-red-600"
                onClick={(e) => {
                  e.stopPropagation();
                  e.preventDefault();
                }}
              >
                <AlertWrapper
                  trigger={
                    <button
                      className="flex items-center gap-2"
                      onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                      }}
                    >
                      <Trash className="size-4" /> Delete
                    </button>
                  }
                  onConfirm={() =>
                    deleteTestimonial.mutate({ id: testimonial.id })
                  }
                  title="Are you sure you want to delete this testimonial?"
                  description="This action cannot be undone."
                />
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  const table = useReactTable({
    data: testimonials,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  return (
    <div className="w-full">
      <div className="flex items-center justify-between py-4">
        <Input
          placeholder="Filter testimonials..."
          value={table.getColumn("title")?.getFilterValue() as string}
          onChange={(event) =>
            table.getColumn("title")?.setFilterValue(event.target.value)
          }
          className="max-w-sm"
        />
        <div className="flex gap-2">
          <TestimonialsFormSheet />
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                Columns <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {table
                .getAllColumns()
                .filter((column) => column.getCanHide())
                .map((column) => {
                  return (
                    <DropdownMenuCheckboxItem
                      key={column.id}
                      className="capitalize"
                      checked={column.getIsVisible()}
                      onCheckedChange={(value) =>
                        column.toggleVisibility(!!value)
                      }
                    >
                      {column.id}
                    </DropdownMenuCheckboxItem>
                  );
                })}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext(),
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody className="relative text-start">
            {table.getRowModel().rows.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell
                      className={cn(
                        cell.column.id === "actions" &&
                          "sticky right-0 backdrop-blur-3xl",
                      )}
                      key={cell.id}
                    >
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-end space-x-2 py-4">
        <div className="flex-1 text-sm text-muted-foreground">
          {table.getFilteredSelectedRowModel().rows.length} of{" "}
          {table.getFilteredRowModel().rows.length} row(s) selected.
        </div>
        <div className="space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  );
};

export default TestimonialsDataTable;
