"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useQueryState } from "nuqs";

import { DateRangePicker } from "@acme/ui/components/ui/date-range-picker";

const monthNames = [
  "jan",
  "feb",
  "mar",
  "apr",
  "may",
  "jun",
  "jul",
  "aug",
  "sep",
  "oct",
  "nov",
  "dec",
];
function formatDate(date: Date | undefined): string | null {
  if (!date) return null;
  const pad = (n: number) => n.toString().padStart(2, "0");
  return `${pad(date.getDate())}-${monthNames[date.getMonth()]}-${date.getFullYear()}`;
}

function parseDate(str?: string | null): Date | undefined {
  if (!str) return undefined;
  const [dd, mm, yyyy] = str.split("-");

  if (!dd || !mm || !yyyy) {
    return undefined;
  }

  return new Date(Number(yyyy), Number(monthNames.indexOf(mm)) - 1, Number(dd));
}

const StatsRangePicker = ({
  startParamName = "start-date",
  endParamName = "end-date",
}: {
  startParamName?: string;
  endParamName?: string;
}) => {
  const [startDate, setStartDate] = useQueryState(startParamName);
  const [endDate, setEndDate] = useQueryState(endParamName);
  const [range, setRange] = useState<{
    from: Date | undefined;
    to: Date | undefined;
  }>({
    from: parseDate(startDate),
    to: parseDate(endDate),
  });
  const router = useRouter();

  useEffect(() => {
    setRange({ from: parseDate(startDate), to: parseDate(endDate) });
  }, [startDate, endDate]);

  function handleSelect(selected?: { from?: Date; to?: Date }) {
    const from = selected?.from;
    const to = selected?.to;

    setRange({ from, to });
    void setStartDate(from ? formatDate(from) : null);
    void setEndDate(to ? formatDate(to) : null);
  }

  return (
    <div className="flex h-fit flex-col gap-2">
      <DateRangePicker
        initialDateFrom={range.from}
        initialDateTo={range.to}
        locale="in"
        onUpdate={(val) => {
          handleSelect(val.range);
          router.refresh();
        }}
      />
    </div>
  );
};

export default StatsRangePicker;
