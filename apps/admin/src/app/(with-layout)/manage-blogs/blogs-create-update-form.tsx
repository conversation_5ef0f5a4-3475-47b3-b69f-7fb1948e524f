"use client";

import type { z } from "zod";
import { Suspense, useEffect } from "react";
import Image from "next/image";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  skipToken,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";
import { generateUploadButton } from "@uploadthing/react";
import { Loader2, Trash, Upload, X } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

import { Badge } from "@acme/ui/components/ui/badge";
import { Button } from "@acme/ui/components/ui/button";
import { Checkbox } from "@acme/ui/components/ui/checkbox";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@acme/ui/components/ui/form";
import { Input } from "@acme/ui/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@acme/ui/components/ui/select";
import { Textarea } from "@acme/ui/components/ui/textarea";
import { Editor } from "@acme/ui/editor";

import type { OurFileRouter } from "~/app/api/uploadthing/core";
import { useSheet } from "~/hooks/use-sheet";
import { blogParamName } from "~/lib/constants";
import { CreateBlogSchema } from "~/lib/zod-schema";
import { useTRPC } from "~/trpc/react";

type BlogFormValues = z.infer<typeof CreateBlogSchema>;

export const UploadButton = generateUploadButton<OurFileRouter>();

export function BlogsCreateUpdateForm() {
  const trpc = useTRPC();
  const trpcUtils = useQueryClient();
  const { paramValue: blogId, closeSheet } = useSheet(blogParamName);
  const isEditMode = !!blogId;

  const { data: blogData, isLoading } = useQuery(
    trpc.blog.getBlogById.queryOptions(blogId ? { id: blogId } : skipToken),
  );

  const form = useForm<BlogFormValues>({
    resolver: zodResolver(CreateBlogSchema),
    defaultValues: blogData
      ? {
          title: blogData.title,
          slug: blogData.slug,
          excerpt: blogData.excerpt ?? "",
          content: JSON.stringify(blogData.content),
          banner: blogData.banner,
          status: blogData.status ?? undefined,
          isActive: blogData.isActive ?? undefined,
          isFeatured: blogData.isFeatured ?? undefined,
          tags: blogData.tags ?? [],
          publishedAt: blogData.publishedAt ?? undefined,
        }
      : {
          title: "",
          slug: "",
          excerpt: "",
          banner: "",
          content: JSON.stringify({
            root: {
              children: [
                {
                  children: [
                    {
                      detail: 0,
                      format: 0,
                      mode: "normal",
                      style: "",
                      text: "Hello World 🚀",
                      type: "text",
                      version: 1,
                    },
                  ],
                  direction: "ltr",
                  format: "",
                  indent: 0,
                  type: "paragraph",
                  version: 1,
                },
              ],
              direction: "ltr",
              format: "",
              indent: 0,
              type: "root",
              version: 1,
            },
          }),
          status: "DRAFT",
          isActive: true,
          isFeatured: false,
          tags: [],
        },
  });

  const createBlog = useMutation(
    trpc.blog.createBlog.mutationOptions({
      onSuccess: () => {
        toast.success("Blog created successfully");
        void trpcUtils.invalidateQueries({
          queryKey: trpc.blog.getAllBlogs.queryKey(),
        });
        closeSheet();
      },
      onError: (error) => {
        toast.error(error.message);
      },
    }),
  );

  const updateBlog = useMutation(
    trpc.blog.updateBlog.mutationOptions({
      onSuccess: () => {
        toast.success("Blog updated successfully");
        void trpcUtils.invalidateQueries({
          queryKey: trpc.blog.getAllBlogs.queryKey(),
        });
        closeSheet();
      },
      onError: (error) => {
        toast.error(error.message);
      },
    }),
  );

  const deleteImage = useMutation(
    trpc.util.deleteUploadthingFileUsingFileKey.mutationOptions({
      onSuccess: () => {
        toast.success("Image deleted successfully");
        form.setValue("banner", "");
      },
      onError: (error) => {
        toast.error(error.message);
      },
    }),
  );

  const onSubmit = (data: BlogFormValues) => {
    if (isEditMode) {
      updateBlog.mutate({ ...data, id: blogId });
    } else {
      createBlog.mutate(data);
    }
  };

  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, "")
      .replace(/\s+/g, "-")
      .replace(/-+/g, "-")
      .trim();
  };

  const addTag = (tag: string) => {
    if (tag.trim() && !form.getValues("tags").includes(tag.trim())) {
      const currentTags = form.getValues("tags");
      form.setValue("tags", [...currentTags, tag.trim()]);
    }
  };

  const removeTag = (tagToRemove: string) => {
    const currentTags = form.getValues("tags");
    form.setValue(
      "tags",
      currentTags.filter((tag) => tag !== tagToRemove),
    );
  };

  useEffect(() => {
    if (!isLoading && isEditMode && blogData) {
      form.reset({
        title: blogData.title,
        slug: blogData.slug,
        excerpt: blogData.excerpt ?? "",
        content: JSON.stringify(blogData.content),
        banner: blogData.banner,
        status: blogData.status ?? undefined,
        isActive: blogData.isActive ?? undefined,
        isFeatured: blogData.isFeatured ?? undefined,
        tags: blogData.tags ?? [],
        publishedAt: blogData.publishedAt ?? undefined,
      });
    }
  }, [isEditMode, blogData, form, isLoading]);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <Suspense>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <FormField
            control={form.control}
            name="title"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Title</FormLabel>
                <FormControl>
                  <Input
                    placeholder="Enter blog title"
                    {...field}
                    onChange={(e) => {
                      field.onChange(e);
                      form.setValue("slug", generateSlug(e.target.value));
                    }}
                  />
                </FormControl>
                <FormDescription>The title of your blog post.</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="slug"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Slug</FormLabel>
                <FormControl>
                  <Input disabled placeholder="blog-post-slug" {...field} />
                </FormControl>
                <FormDescription>
                  URL-friendly version of the title. Use lowercase letters,
                  numbers, and hyphens only.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="excerpt"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Excerpt</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Brief description of the blog post..."
                    rows={3}
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  A short summary that will be displayed in blog previews.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="content"
            render={({ field }) => (
              <FormItem className="h-fit">
                <FormLabel>Content</FormLabel>
                <FormControl>
                  {
                    <Editor
                      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
                      editorSerializedState={JSON.parse(field.value)}
                      onChange={(value) =>
                        field.onChange(JSON.stringify(value.toJSON()))
                      }
                    />
                  }
                </FormControl>
                <FormDescription>
                  The main content of your blog post. This will be replaced with
                  a rich editor.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="banner"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Banner Image</FormLabel>
                <FormControl>
                  {field.value ? (
                    <div className="relative">
                      <Image
                        src={field.value}
                        alt="blog banner"
                        height={200}
                        width={400}
                        className="h-48 w-full rounded-lg object-cover"
                      />
                      <Button
                        type="button"
                        variant="destructive"
                        size="sm"
                        className="mt-2"
                        disabled={deleteImage.isPending}
                        onClick={() =>
                          deleteImage.mutate({ fileKey: field.value })
                        }
                      >
                        {deleteImage.isPending ? (
                          <>
                            <Loader2 className="animate-spin" />
                            Removing Image
                          </>
                        ) : (
                          <>
                            <Trash className="mr-2 h-4 w-4" />
                            Remove Image
                          </>
                        )}
                      </Button>
                    </div>
                  ) : (
                    <UploadButton
                      endpoint="imageUploader"
                      appearance={{
                        button:
                          "bg-primary text-primary-foreground hover:bg-primary/90 rounded-md px-4 py-2 text-sm font-medium shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring",
                        allowedContent: "text-muted-foreground text-xs",
                        container: "flex flex-col gap-2 w-full",
                      }}
                      content={{
                        button: ({ isUploading }) => (
                          <span className="flex items-center gap-2">
                            {isUploading ? (
                              <Loader2 className="animate-spin" />
                            ) : (
                              <Upload className="size-4" />
                            )}
                            {isUploading
                              ? "Uploading..."
                              : "Upload Banner Image"}
                          </span>
                        ),
                        allowedContent: "JPEG, PNG or WebP (max 4MB)",
                      }}
                      onClientUploadComplete={(res) => {
                        const imageUrl = res[0]?.ufsUrl;
                        if (!imageUrl) {
                          toast.error("Image upload failed");
                          return;
                        }

                        field.onChange(imageUrl);
                        toast.success("Image uploaded successfully");
                      }}
                      onUploadError={(error: Error) => {
                        toast.error(`Upload error: ${error.message}`);
                      }}
                    />
                  )}
                </FormControl>
                <FormDescription>
                  Upload a banner image for your blog post.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="status"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Status</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="DRAFT">Draft</SelectItem>
                      <SelectItem value="PUBLISHED">Published</SelectItem>
                      <SelectItem value="ARCHIVED">Archived</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    Current publication status of the blog.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="flex gap-4">
            <FormField
              control={form.control}
              name="isActive"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>Active</FormLabel>
                    <FormDescription>
                      Whether this blog is active and visible.
                    </FormDescription>
                  </div>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="isFeatured"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>Featured</FormLabel>
                    <FormDescription>
                      Mark this blog as featured content.
                    </FormDescription>
                  </div>
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="tags"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Tags</FormLabel>
                <FormControl>
                  <div>
                    <div className="mb-2 flex flex-wrap gap-2">
                      {field.value.map((tag) => (
                        <Badge
                          key={tag}
                          variant="secondary"
                          className="flex items-center gap-1"
                        >
                          {tag}
                          <X
                            className="h-3 w-3 cursor-pointer"
                            onClick={() => removeTag(tag)}
                          />
                        </Badge>
                      ))}
                    </div>
                    <Input
                      placeholder="Add a tag and press Enter"
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          e.preventDefault();
                          addTag(e.currentTarget.value);
                          e.currentTarget.value = "";
                        }
                      }}
                    />
                  </div>
                </FormControl>
                <FormDescription>
                  Add tags to categorize your blog post. Press Enter to add a
                  tag.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="flex gap-2">
            <Button
              type="submit"
              disabled={
                createBlog.isPending ||
                updateBlog.isPending ||
                deleteImage.isPending
              }
            >
              {createBlog.isPending || updateBlog.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {isEditMode ? "Updating..." : "Creating..."}
                </>
              ) : (
                <>{isEditMode ? "Update Blog" : "Create Blog"}</>
              )}
            </Button>
            <Button type="button" variant="outline" onClick={closeSheet}>
              Cancel
            </Button>
          </div>
        </form>
      </Form>
    </Suspense>
  );
}

export default BlogsCreateUpdateForm;
