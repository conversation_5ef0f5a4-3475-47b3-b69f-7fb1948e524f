"use client";

import { Suspense } from "react";
import { PlusIcon } from "lucide-react";

import { But<PERSON> } from "@acme/ui/components/ui/button";

import { GenericSheet } from "~/components/shared/generic-sheet";
import { useSheet } from "~/hooks/use-sheet";
import { blogParamName } from "~/lib/constants";
import { BlogsCreateUpdateForm } from "./blogs-create-update-form";

const BlogsFormSheet = () => {
  const { isOpen, paramValue, openSheet, closeSheet } = useSheet(blogParamName);

  return (
    <div>
      <Button onClick={() => openSheet()}>
        <PlusIcon className="size-4" />
        Create Blog
      </Button>
      <GenericSheet
        isOpen={isOpen}
        onClose={closeSheet}
        title={paramValue ? "Edit Blog" : "Create Blog"}
        className="md:min-w-full md:max-w-full 2xl:max-w-[1400px]"
      >
        <Suspense fallback={<p>loading ...</p>}>
          <BlogsCreateUpdateForm />
        </Suspense>{" "}
      </GenericSheet>
    </div>
  );
};

export default BlogsFormSheet;
