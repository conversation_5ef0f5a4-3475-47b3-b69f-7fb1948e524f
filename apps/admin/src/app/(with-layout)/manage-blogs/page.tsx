import type { SearchParams } from "nuqs/server";
import { Suspense } from "react";

import { Separator } from "@acme/ui/components/ui/separator";

import DataTableSkeleton from "~/components/skeleton/data-table-skeleton";
import { loadContentSearchParams } from "~/lib/nuqs-utils";
import { getQueryClient, HydrateClient, trpc } from "~/trpc/server";
import BlogsDataTable from "./blogs-data-table";
import BlogsFormSheet from "./blogs-form-sheet";

export const metadata = {
  title: "Blogs Management",
  description: "Manage your website's blog posts",
};

interface ManageBlogsPageProps {
  searchParams: Promise<SearchParams>;
}

const ManageBlogsPage = async ({ searchParams }: ManageBlogsPageProps) => {
  const { blog_id } = await loadContentSearchParams(searchParams);
  const queryClient = getQueryClient();

  // Prefetch list data
  void queryClient.prefetchQuery(trpc.blog.getAllBlogs.queryOptions());

  // Prefetch individual item data if ID exists
  if (blog_id.length > 0) {
    console.log("blog_id", blog_id);
    await queryClient.prefetchQuery(
      trpc.blog.getBlogById.queryOptions({ id: blog_id }),
    );
  }

  return (
    <HydrateClient>
      <div>
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Blogs Management
            </h1>
            <p className="text-muted-foreground">
              Create, edit and manage blog posts on your website.
            </p>
          </div>
        </div>
        <Separator className="my-6" />
        <Suspense fallback={<DataTableSkeleton />}>
          <BlogsDataTable />
          <BlogsFormSheet />
        </Suspense>
      </div>
    </HydrateClient>
  );
};

export default ManageBlogsPage;
