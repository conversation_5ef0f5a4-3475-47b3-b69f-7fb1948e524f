import type { SearchParams } from "nuqs/server";
import { Suspense } from "react";

import { Separator } from "@acme/ui/components/ui/separator";

import DataTableSkeleton from "~/components/skeleton/data-table-skeleton";
import { loadCategorySearchParams } from "~/lib/nuqs-utils";
import { getQueryClient, HydrateClient, trpc } from "~/trpc/server";
import { CategoryDataTable } from "./categories-data-table";

export const metadata = {
  title: "Categories Management",
  description: "Manage your website's Categories",
};

interface ManageCategoriesPageProps {
  searchParams: Promise<SearchParams>;
}

const ManageCategoriesPage = async ({
  searchParams,
}: ManageCategoriesPageProps) => {
  const { category_id } = await loadCategorySearchParams(searchParams);
  const queryClient = getQueryClient();

  // Prefetch list data
  await queryClient.prefetchQuery(
    trpc.category.getTopLevelCategories.queryOptions({ includeDeleted: false }),
  );

  // Prefetch individual item data if ID exists
  if (category_id) {
    await queryClient.prefetchQuery(
      trpc.category.getCategoryById.queryOptions({ id: category_id }),
    );
  }

  return (
    <HydrateClient>
      <div>
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Categories Management
            </h1>
            <p className="text-muted-foreground">
              Create, edit and manage categories that are displayed on your
              website.
            </p>
          </div>
        </div>
        <Separator className="my-6" />
        <Suspense fallback={<DataTableSkeleton />}>
          <CategoryDataTable />
        </Suspense>
      </div>
    </HydrateClient>
  );
};

export default ManageCategoriesPage;
