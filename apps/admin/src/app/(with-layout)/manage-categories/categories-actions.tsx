"use client";

import Link from "next/link";
import { useRouter } from "next/navigation";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Edit, EyeIcon, MoreHorizontal, Pencil, Trash } from "lucide-react";
import { toast } from "sonner";

import type { category } from "@acme/db/schema";
import { Button } from "@acme/ui/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@acme/ui/components/ui/dropdown-menu";

import AlertWrapper from "~/components/shared/alert-wrapper";
import { useSheet } from "~/hooks/use-sheet";
import { categoryParamName, subCategoryParamName } from "~/lib/constants";
import { useTRPC } from "~/trpc/react";

const CategoriesActions = (props: {
  category: typeof category.$inferSelect;
}) => {
  const trpc = useTRPC();
  const router = useRouter();
  const trpcUtils = useQueryClient();

  const { openSheet } = useSheet(categoryParamName);

  const { category } = props;

  const deleteCategory = useMutation(
    trpc.category.deleteCategory.mutationOptions({
      onSuccess: (opts) => {
        toast.success(opts.message);
        void trpcUtils.invalidateQueries(
          trpc.category.getTopLevelCategories.queryOptions({
            includeDeleted: false,
          }),
        );
      },
      onError: (error) => {
        toast.error(error.message);
      },
    }),
  );

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Open menu</span>
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Actions</DropdownMenuLabel>
        <DropdownMenuItem
          onClick={() => {
            router.push(
              `?${categoryParamName}=${category.id}&${subCategoryParamName}=yes`,
            );
          }}
        >
          <Pencil className="h-4 w-4" />
          Add Sub Category
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => {
            openSheet(category.id);
          }}
        >
          <Edit className="h-4 w-4" />
          Edit Category
        </DropdownMenuItem>
        <DropdownMenuItem>
          <EyeIcon className="h-4 w-4" />
          <Link href={`/manage-categories/${category.id}`}>
            View Sub Categories
          </Link>
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem
          className="text-red-600"
          onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
          }}
        >
          <AlertWrapper
            trigger={
              <button
                className="flex items-center gap-2"
                onClick={(e) => {
                  e.stopPropagation();
                  e.preventDefault();
                }}
              >
                <Trash className="size-4" /> Delete Category
              </button>
            }
            onConfirm={() => deleteCategory.mutate({ id: category.id })}
            title="Are you sure you want to delete this category?"
            description="This action cannot be undone."
          />
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default CategoriesActions;
