"use client";

import { Suspense } from "react";
import { PlusIcon } from "lucide-react";
import { useQueryState } from "nuqs";

import { Button } from "@acme/ui/components/ui/button";

import { GenericSheet } from "~/components/shared/generic-sheet";
import { categoryParamName, subCategoryParamName } from "~/lib/constants";
import { CategoryCreateUpdateForm } from "./categories-create-update-form";

const CategoryFormSheet = () => {
  const [, setSubCategoryParam] = useQueryState(subCategoryParamName, {
    clearOnDefault: true,
  });
  const [categoryParam, setCategoryParam] = useQueryState(categoryParamName, {
    clearOnDefault: true,
  });
  const isOpen = categoryParam !== null;
  const paramValue = categoryParam;
  const openSheet = (value?: string | null) => {
    void setCategoryParam(value ?? "");
  };
  const closeSheet = () => {
    void setCategoryParam(null);
    void setSubCategoryParam(null);
  };

  return (
    <div>
      <Button onClick={() => openSheet()}>
        <PlusIcon className="size-4" />
        Create Category
      </Button>
      <GenericSheet
        isOpen={isOpen}
        onClose={closeSheet}
        title={paramValue ? "Edit Category" : "Create Category"}
      >
        <Suspense fallback={null}>
          <CategoryCreateUpdateForm />
        </Suspense>{" "}
      </GenericSheet>
    </div>
  );
};

export default CategoryFormSheet;
