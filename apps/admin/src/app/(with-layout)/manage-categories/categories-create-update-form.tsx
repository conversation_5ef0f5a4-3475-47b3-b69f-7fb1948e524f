"use client";

import type { z } from "zod";
import { useEffect } from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  skipToken,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";
import { generateUploadButton } from "@uploadthing/react";
import { Loader2, Trash, Upload } from "lucide-react";
import { useQueryState } from "nuqs";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

import { categoryRateTypeEnum } from "@acme/db/schema";
import { Button } from "@acme/ui/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@acme/ui/components/ui/form";
import { Input } from "@acme/ui/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@acme/ui/components/ui/select";
import { Switch } from "@acme/ui/components/ui/switch";
import { Textarea } from "@acme/ui/components/ui/textarea";
import { CategoryBaseSchema } from "@acme/validators";

import type { OurFileRouter } from "~/app/api/uploadthing/core";
import { useSheet } from "~/hooks/use-sheet";
import { categoryParamName, subCategoryParamName } from "~/lib/constants";
import { useTRPC } from "~/trpc/react";

type CategoryFormValues = z.infer<typeof CategoryBaseSchema>;

export const UploadButton = generateUploadButton<OurFileRouter>();

export function CategoryCreateUpdateForm() {
  const trpc = useTRPC();
  const trpcUtils = useQueryClient();
  const [subCategoryParam] = useQueryState(subCategoryParamName, {
    clearOnDefault: true,
  });
  const [categoryId] = useQueryState(categoryParamName, {
    clearOnDefault: true,
  });
  const wantToAddSubCategory = !!subCategoryParam;

  const { closeSheet } = useSheet(categoryParamName);

  const router = useRouter();
  const isEditMode = !!categoryId;

  const { data: categoryData, isLoading } = useQuery(
    trpc.category.getCategoryById.queryOptions(
      categoryId ? { id: categoryId } : skipToken,
    ),
  );

  const form = useForm<CategoryFormValues>({
    resolver: zodResolver(CategoryBaseSchema),
    defaultValues:
      !wantToAddSubCategory && categoryData
        ? {
            name: categoryData.name,
            description: categoryData.description ?? undefined,
            image: categoryData.image,
            isActive: categoryData.isActive ?? undefined,
            rate: Number(categoryData.rate),
            rateType: categoryData.rateType ?? undefined,
            tag: categoryData.tag ?? undefined,
            compensationKabadiwalaRate: Number(
              categoryData.compensationKabadiwalaRate,
            ),
            compensationRecyclerRate: Number(
              categoryData.compensationRecyclerRate,
            ),
          }
        : {
            name: "",
            description: "",
            image: "",
            isActive: true,
          },
  });

  const createCategory = useMutation(
    trpc.category.createCategory.mutationOptions({
      onSuccess: (opts) => {
        toast.success(opts.message);
        void trpcUtils.invalidateQueries({
          queryKey: trpc.category.getTopLevelCategories.queryKey(),
        });
        void trpcUtils.invalidateQueries({
          queryKey: trpc.category.getCategoryById.queryKey(),
        });
        router.refresh();
        void closeSheet();
      },
      onError: (error) => {
        toast.error(error.message);
      },
    }),
  );

  const updateCategory = useMutation(
    trpc.category.updateCategory.mutationOptions({
      onSuccess: (opts) => {
        toast.success(opts.message);
        void trpcUtils.invalidateQueries({
          queryKey: trpc.category.updateCategory.mutationKey(),
        });
        void closeSheet();
      },
      onError: (error) => {
        toast.error(error.message);
      },
    }),
  );

  const deleteBlogImage = useMutation(
    trpc.util.deleteUploadthingFileUsingFileKey.mutationOptions({
      onSuccess: (opts) => {
        toast.success(opts.message);
        form.setValue("image", "");
      },
      onError: (error) => {
        toast.error(error.message);
      },
    }),
  );

  const onSubmit = (data: CategoryFormValues) => {
    if (isEditMode) {
      if (wantToAddSubCategory) {
        createCategory.mutate({ ...data, parentId: categoryId });
        return;
      }

      updateCategory.mutate({ ...data, id: categoryId });
    } else {
      createCategory.mutate(data);
    }
  };

  useEffect(() => {
    if (isEditMode && categoryData && !wantToAddSubCategory) {
      form.reset({
        name: categoryData.name,
        description: categoryData.description ?? undefined,
        image: categoryData.image,
        isActive: categoryData.isActive ?? undefined,
        rate: Number(categoryData.rate),
        rateType: categoryData.rateType ?? undefined,
        tag: categoryData.tag ?? undefined,
        compensationKabadiwalaRate: Number(
          categoryData.compensationKabadiwalaRate,
        ),
        compensationRecyclerRate: Number(categoryData.compensationRecyclerRate),
      });
    }
  }, [isEditMode, categoryData, form, wantToAddSubCategory]);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                {wantToAddSubCategory ? "Sub-Category Name" : "Category Name"}
              </FormLabel>
              <FormControl>
                <Input
                  placeholder={
                    wantToAddSubCategory
                      ? "Enter sub-category name"
                      : "Enter category name"
                  }
                  {...field}
                />
              </FormControl>
              <FormDescription>
                {wantToAddSubCategory
                  ? "The name of the sub-category to be displayed."
                  : "The name of the category to be displayed."}
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="tag"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Tag (optional)</FormLabel>
              <FormControl>
                <Input
                  placeholder="Enter unique tag (alphanumeric, e.g. 'paper2024')"
                  value={field.value ?? undefined}
                  onChange={(e) => field.onChange(e.target.value)}
                />
              </FormControl>
              <FormDescription>
                Tag must be unique, alphanumeric, and is only visible to admin.
                Leave blank if not needed.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />{" "}
        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description (optional)</FormLabel>
              <FormControl>
                <Textarea
                  placeholder={
                    wantToAddSubCategory
                      ? "Enter the sub-category description"
                      : "Enter the category description"
                  }
                  className="min-h-[150px]"
                  value={field.value ?? undefined}
                  onChange={(e) => field.onChange(e.target.value)}
                />
              </FormControl>
              <FormDescription>
                {wantToAddSubCategory
                  ? "A detailed description of the sub-category."
                  : "A detailed description of the category."}
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="rate"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Rate</FormLabel>
              <FormControl>
                <Input
                  type="number"
                  placeholder="Enter rate value"
                  value={field.value}
                  onChange={(e) => field.onChange(parseFloat(e.target.value))}
                />
              </FormControl>
              <FormDescription>
                {wantToAddSubCategory
                  ? "The rate value for this sub-category."
                  : "The rate value for this category."}
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="rateType"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Rate Type</FormLabel>
              <FormControl>
                <Select
                  value={field.value}
                  onValueChange={(value) => field.onChange(value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select Rate Type" />
                  </SelectTrigger>
                  <SelectContent>
                    {categoryRateTypeEnum.enumValues.map((rateType) => (
                      <SelectItem key={rateType} value={rateType}>
                        {rateType.replaceAll("_", " ").toLowerCase()}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormControl>
              <FormDescription>
                {wantToAddSubCategory
                  ? "The rate type for this sub-category."
                  : "The rate type for this category."}
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="image"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Image</FormLabel>
              <FormControl>
                {field.value ? (
                  <div className="relative">
                    <Image
                      src={field.value}
                      alt="testimonial person"
                      height={200}
                      width={200}
                      className="h-32 w-32 rounded-full object-cover"
                    />

                    <Button
                      type="button"
                      variant="destructive"
                      size="sm"
                      className="mt-2"
                      disabled={deleteBlogImage.isPending}
                      onClick={() =>
                        deleteBlogImage.mutate({ fileKey: field.value })
                      }
                    >
                      {deleteBlogImage.isPending ? (
                        <>
                          <Loader2 className="animate-spin" />
                          Removing Image
                        </>
                      ) : (
                        <>
                          <Trash />
                          Remove Image
                        </>
                      )}
                    </Button>
                  </div>
                ) : (
                  <UploadButton
                    endpoint="imageUploader"
                    appearance={{
                      button:
                        "bg-primary text-primary-foreground hover:bg-primary/90 rounded-md px-4 py-2 text-sm font-medium shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring",
                      allowedContent: "text-muted-foreground text-xs",
                      container: "flex flex-col gap-2 w-full",
                    }}
                    content={{
                      button: ({ isUploading }) => (
                        <span className="flex items-center gap-2">
                          {isUploading ? (
                            <Loader2 className="animate-spin" />
                          ) : (
                            <Upload className="size-4" />
                          )}
                          {isUploading
                            ? "Uploading..."
                            : "Upload Profile Image"}
                        </span>
                      ),
                      allowedContent: "JPEG, PNG or WebP (max 4MB)",
                    }}
                    onClientUploadComplete={(res) => {
                      const imageUrl = res[0]?.ufsUrl;
                      if (!imageUrl) {
                        toast.error("Image upload failed");
                        return;
                      }

                      field.onChange(imageUrl);
                      toast.success("Image uploaded successfully");
                    }}
                    onUploadError={(error: Error) => {
                      toast.error(`Upload error: ${error.message}`);
                    }}
                  />
                )}
              </FormControl>
              <FormDescription>
                Image for the category's banner.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="isActive"
          render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <FormLabel className="text-base">Active Status</FormLabel>
                <FormDescription>
                  {wantToAddSubCategory
                    ? "Set whether this sub-category is active or not."
                    : "Set whether this category is active or not."}
                </FormDescription>
              </div>
              <FormControl>
                <Switch
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="compensationKabadiwalaRate"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Kabadiwala Rate</FormLabel>
              <FormControl>
                <Input
                  type="number"
                  placeholder="Enter kabadiwala rate"
                  value={field.value}
                  onChange={(e) => field.onChange(parseFloat(e.target.value))}
                />
              </FormControl>
              <FormDescription>
                Set the rate for kabadiwala compensation for this category.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="compensationRecyclerRate"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Recycler Rate</FormLabel>
              <FormControl>
                <Input
                  type="number"
                  placeholder="Enter recycler rate"
                  value={field.value}
                  onChange={(e) => field.onChange(parseFloat(e.target.value))}
                />
              </FormControl>
              <FormDescription>
                Set the rate for recycler compensation for this category.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="flex gap-2">
          <Button type="submit">
            {wantToAddSubCategory
              ? "Create Sub-Category"
              : isEditMode
                ? "Update Category"
                : "Create Category"}
          </Button>
          <Button
            type="button"
            variant="outline"
            onClick={() => router.push("/categories")}
          >
            Cancel
          </Button>
        </div>
      </form>
    </Form>
  );
}
