import Image from "next/image";
import Link from "next/link";
import { redirect } from "next/navigation";
import { format } from "date-fns";
import { ArrowLeft } from "lucide-react";

import { Button } from "@acme/ui/components/ui/button";

import CategoryCard from "~/components/shared/category-card";
import { categoryParamName } from "~/lib/constants";
import { getQueryClient, HydrateClient, trpc } from "~/trpc/server";
import CategoryFormSheet from "../categories-form-sheet";

interface ViewCategoriesPageProps {
  params?: Promise<{ parentId: string }>;
  searchParams: Promise<{ [categoryParamName]?: string }>;
}

const ViewCategoriesPage = async ({
  params,
  searchParams,
}: ViewCategoriesPageProps) => {
  const categoryId = (await searchParams)[categoryParamName];
  const parentId = (await params)?.parentId;

  if (!parentId) {
    redirect("/manage-categories");
  }

  const queryClient = getQueryClient();

  if (categoryId) {
    await queryClient.prefetchQuery(
      trpc.category.getCategoryById.queryOptions({ id: categoryId }),
    );
  }

  const data = await queryClient.fetchQuery(
    trpc.category.getCategoryById.queryOptions({
      id: parentId,
      withParent: true,
      withChildren: true,
    }),
  );

  if (!data) {
    return (
      <div className="flex h-full flex-col items-center justify-center">
        <h1 className="text-2xl font-bold">Category not found</h1>
        <Button asChild className="mt-4">
          <Link href="/manage-categories">Back to Categories</Link>
        </Button>
      </div>
    );
  }

  return (
    <HydrateClient>
      {" "}
      <div className="">
        {/* Current category details */}
        <div className="relative mb-8 overflow-hidden rounded-xl border-2 border-primary/10 bg-white shadow-md">
          {/* "Current Category" banner */}
          <div className="absolute left-0 top-0 z-10 bg-primary px-4 py-1 text-xs font-medium text-white">
            Current Category
          </div>

          <div className="flex flex-col md:flex-row">
            {/* Category image */}
            <div className="relative h-52 w-full flex-shrink-0 overflow-hidden md:h-auto md:w-60">
              <Image
                src={data.image}
                alt={data.name}
                fill
                className="object-cover"
                sizes="(max-width: 768px) 100vw, 240px"
              />
            </div>

            {/* Category content */}
            <div className="flex flex-1 flex-col justify-between p-5">
              <div>
                <div className="mb-3 flex items-center justify-between">
                  <h2 className="text-xl font-bold text-gray-900">
                    {data.name}
                  </h2>
                  <div className="flex items-center gap-2">
                    {data.rate && (
                      <span className="rounded-md bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700">
                        Rate: {data.rate}
                      </span>
                    )}
                    {data.isActive !== null && (
                      <span
                        className={`rounded-full px-2 py-0.5 text-xs font-medium ${
                          data.isActive
                            ? "bg-green-100 text-green-800"
                            : "bg-red-100 text-red-800"
                        }`}
                      >
                        {data.isActive ? "Active" : "Inactive"}
                      </span>
                    )}
                  </div>
                </div>

                {data.description && (
                  <p className="text-sm text-gray-600">{data.description}</p>
                )}
              </div>
              <div className="mt-4 flex flex-wrap items-center gap-x-6 gap-y-2 text-xs text-gray-500">
                <div className="flex items-center">
                  <span className="mr-1 font-medium">Created:</span>
                  {format(new Date(data.createdAt), "MMM d, yyyy")}
                </div>
                {data.updatedAt && (
                  <div className="flex items-center">
                    <span className="mr-1 font-medium">Updated:</span>
                    {format(new Date(data.updatedAt), "MMM d, yyyy")}
                  </div>
                )}
                {data.parent && (
                  <div className="flex items-center">
                    <span className="mr-1 font-medium">Parent:</span>
                    <Link
                      href={`/manage-categories/${data.parent.id}`}
                      className="text-primary underline-offset-2 hover:underline"
                    >
                      {data.parent.name}
                    </Link>
                  </div>
                )}
                <div className="flex items-center">
                  <span className="mr-1 font-medium">
                    Total Subcategories: {data.children.length}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Back and action buttons */}
        <div className="mb-6 flex justify-between">
          <Button variant="outline" asChild>
            <Link
              href={
                data.parent
                  ? `/manage-categories/${data.parent.id}`
                  : "/manage-categories"
              }
            >
              <ArrowLeft size={16} className="mr-2" />
              Back
            </Link>
          </Button>
          {/* <CategoryFormSheet /> */}
        </div>

        {/* Children categories */}
        <div>
          <h2 className="mb-4 text-xl font-semibold">Subcategories</h2>
          {data.children.length > 0 ? (
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
              {data.children.map((item) => (
                <CategoryCard key={item.id} category={item} />
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center rounded-lg border border-dashed py-12">
              <p className="mb-4 text-gray-500">No subcategories found</p>
            </div>
          )}
        </div>

        {/* Keep it hidden because this will render the sheet if somebody click on edit actions */}
        <div className="hidden">
          <CategoryFormSheet />
        </div>
      </div>
    </HydrateClient>
  );
};

export default ViewCategoriesPage;
