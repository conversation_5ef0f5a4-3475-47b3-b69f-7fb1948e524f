import type { SearchParams } from "nuqs/server";
import { Suspense } from "react";

import { Separator } from "@acme/ui/components/ui/separator";

import DataTableSkeleton from "~/components/skeleton/data-table-skeleton";
import { loadContentSearchParams } from "~/lib/nuqs-utils";
import { getQueryClient, HydrateClient, trpc } from "~/trpc/server";
import FaqsDataTable from "./faqs-data-table";

export const metadata = {
  title: "FAQs Management",
  description: "Manage your website's FAQs",
};

interface ManageFaqsPageProps {
  searchParams: Promise<SearchParams>;
}

const ManageFaqsPage = async ({ searchParams }: ManageFaqsPageProps) => {
  const { faq_id } = await loadContentSearchParams(searchParams);
  const queryClient = getQueryClient();

  // Prefetch list data
  await queryClient.prefetchQuery(trpc.faq.getTopLevelQuestions.queryOptions());

  // Prefetch individual item data if ID exists
  if (faq_id) {
    await queryClient.prefetchQuery(
      trpc.faq.getQuestionById.queryOptions({ id: faq_id }),
    );
  }

  return (
    <HydrateClient>
      <div>
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              FAQs Management
            </h1>
            <p className="text-muted-foreground">
              Create, edit and manage FAQs that are displayed on your website.
            </p>
          </div>
        </div>
        <Separator className="my-6" />
        <Suspense fallback={<DataTableSkeleton />}>
          <FaqsDataTable />
        </Suspense>
      </div>
    </HydrateClient>
  );
};

export default ManageFaqsPage;
