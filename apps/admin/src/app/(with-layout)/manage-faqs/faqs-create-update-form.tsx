"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  skipToken,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";

import { Button } from "@acme/ui/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@acme/ui/components/ui/form";
import { Input } from "@acme/ui/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@acme/ui/components/ui/select";
import { Textarea } from "@acme/ui/components/ui/textarea";
import { Editor } from "@acme/ui/editor";

import { RichTextInput } from "~/components/shared/rich-text-input";
import { useSheet } from "~/hooks/use-sheet";
import { faqParamName } from "~/lib/constants";
import { useTRPC } from "~/trpc/react";

interface FaqsCreateUpdateFormProps {
  questionId?: string;
  isSubQuestion?: boolean;
  mode: "create" | "edit";
}

const QuestionSchema = z.object({
  content: z.string().min(1, "Question content is required"),
  questionFor: z.enum(["KABADIWALA", "SELLER", "LANDING_PAGE"], {
    required_error: "Please select who this question is for",
  }),
  order: z.coerce.number().int().nonnegative().default(0),
});

type QuestionFormValues = z.infer<typeof QuestionSchema>;

const AnswerSchema = z.object({
  content: z.string().min(1, "Answer content is required"),
});

type AnswerFormValues = z.infer<typeof AnswerSchema>;

const FaqsCreateUpdateForm = ({
  questionId,
  isSubQuestion,
  mode,
}: FaqsCreateUpdateFormProps) => {
  const trpc = useTRPC();
  const router = useRouter();
  const { closeSheet } = useSheet(faqParamName);
  const trpcUtils = useQueryClient();
  const [showAnswerForm, setShowAnswerForm] = useState(false);

  const { data: questionData, isLoading } = useQuery(
    trpc.faq.getQuestionById.queryOptions(
      questionId && mode === "edit" ? { id: questionId } : skipToken,
    ),
  );

  const { data: existingAnswers = [], isLoading: isAnswersLoading } = useQuery(
    trpc.faq.getAnswersByQuestionId.queryOptions(
      questionId ? { questionId } : skipToken,
    ),
  );

  const questionForm = useForm<QuestionFormValues>({
    resolver: zodResolver(QuestionSchema),
    defaultValues: {
      content: questionData?.content ?? "",
      questionFor:
        questionData?.questionFor === "LANDING_PAGE"
          ? "LANDING_PAGE"
          : (questionData?.questionFor ?? "SELLER"),
      order: questionData?.order ? Number(questionData.order) : 0,
    },
  });

  const answerForm = useForm<AnswerFormValues>({
    resolver: zodResolver(AnswerSchema),
    defaultValues: {
      content: "",
    },
  });

  useEffect(() => {
    if (questionData && mode === "edit") {
      questionForm.reset({
        content: questionData.content,
        questionFor:
          questionData.questionFor === "LANDING_PAGE"
            ? "LANDING_PAGE"
            : questionData.questionFor,
        order: questionData.order ? Number(questionData.order) : 0,
      });
    }
  }, [questionData, questionForm, mode]);

  const createQuestion = useMutation(
    trpc.faq.createQuestion.mutationOptions({
      onSuccess: () => {
        toast.success("FAQ created successfully");
        void trpcUtils.invalidateQueries(
          trpc.faq.getTopLevelQuestions.queryOptions(),
        );
        closeSheet();
        router.refresh();
      },
      onError: (error) => {
        toast.error(error.message);
      },
    }),
  );

  const updateQuestion = useMutation(
    trpc.faq.updateQuestion.mutationOptions({
      onSuccess: () => {
        toast.success("FAQ updated successfully");
        void trpcUtils.invalidateQueries(
          trpc.faq.getTopLevelQuestions.queryOptions(),
        );
        if (questionId) {
          void trpcUtils.invalidateQueries(
            trpc.faq.getQuestionById.queryOptions({ id: questionId }),
          );
        }
        closeSheet();
      },
      onError: (error) => {
        toast.error(error.message);
      },
    }),
  );

  const createAnswer = useMutation(
    trpc.faq.createAnswer.mutationOptions({
      onSuccess: () => {
        toast.success("Answer added successfully");
        if (questionId) {
          void trpcUtils.invalidateQueries(
            trpc.faq.getAnswersByQuestionId.queryOptions({ questionId }),
          );
        }
        answerForm.reset();
        setShowAnswerForm(false);
      },
      onError: (error) => {
        toast.error(error.message);
      },
    }),
  );

  const deleteAnswer = useMutation(
    trpc.faq.deleteAnswer.mutationOptions({
      onSuccess: () => {
        toast.success("Answer deleted successfully");
        if (questionId) {
          void trpcUtils.invalidateQueries(
            trpc.faq.getAnswersByQuestionId.queryOptions({ questionId }),
          );
        }
      },
      onError: (error) => {
        toast.error(error.message);
      },
    }),
  );

  const onQuestionSubmit = (data: QuestionFormValues) => {
    // Prevent sub-questions for LANDING_PAGE
    if (
      isSubQuestion &&
      (questionData?.questionFor === "LANDING_PAGE" ||
        data.questionFor === "LANDING_PAGE")
    ) {
      toast.error("Landing Page FAQs cannot have sub-questions.");
      return;
    }
    if (mode === "edit" && questionId) {
      updateQuestion.mutate({
        id: questionId,
        content: data.content,
        questionFor: data.questionFor,
        order: data.order,
        // Never send parentId for LANDING_PAGE
        ...(data.questionFor !== "LANDING_PAGE" && questionData?.parentId
          ? { parentId: questionData.parentId }
          : {}),
      });
    } else if (isSubQuestion && questionId) {
      // For sub-questions, use the parent question's audience type (never LANDING_PAGE)
      const parentType =
        questionData?.questionFor === "LANDING_PAGE"
          ? "SELLER"
          : (questionData?.questionFor ?? data.questionFor);
      createQuestion.mutate({
        content: data.content,
        questionFor: parentType,
        order: data.order,
        parentId: questionId,
      });
    } else {
      createQuestion.mutate({
        content: data.content,
        questionFor: data.questionFor,
        order: data.order,
        // Never send parentId for LANDING_PAGE
        ...(data.questionFor !== "LANDING_PAGE" && questionId
          ? { parentId: questionId }
          : {}),
      });
    }
  };

  const onAnswerSubmit = (data: AnswerFormValues) => {
    if (!questionId) return;

    createAnswer.mutate({
      content: data.content,
      questionId,
    });
  };

  if (isLoading || isAnswersLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="space-y-6">
      <Form {...questionForm}>
        <form
          onSubmit={questionForm.handleSubmit(onQuestionSubmit)}
          className="space-y-8"
        >
          <FormField
            control={questionForm.control}
            name="content"
            render={({ field }) => (
              <FormItem>
                <FormLabel>
                  {isSubQuestion ? "Sub-Question" : "Question"}
                </FormLabel>
                <FormControl>
                  <Textarea
                    placeholder={
                      isSubQuestion ? "Enter sub-question" : "Enter question"
                    }
                    className="min-h-[100px]"
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  {isSubQuestion
                    ? "The sub-question to be displayed in the FAQ section."
                    : "The question to be displayed in the FAQ section."}
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="flex flex-col gap-6 sm:flex-row">
            {/* Only show Question For if not sub-question and not editing a LANDING_PAGE question */}
            {!isSubQuestion && (
              <FormField
                control={questionForm.control}
                name="questionFor"
                render={({ field }) => (
                  <FormItem className="flex-1">
                    <FormLabel>Question For</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      value={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select who this question is for" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="KABADIWALA">Kabadiwala</SelectItem>
                        <SelectItem value="SELLER">Seller</SelectItem>
                        <SelectItem value="LANDING_PAGE">
                          Landing Page
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    <FormDescription>
                      Select which type of user this FAQ is intended for.
                      Landing Page FAQs cannot have sub-questions.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}

            <FormField
              control={questionForm.control}
              name="order"
              render={({ field }) => (
                <FormItem className="flex-1">
                  <FormLabel>Display Order</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      min="0"
                      {...field}
                      onChange={(e) =>
                        field.onChange(parseInt(e.target.value, 10) || 0)
                      }
                    />
                  </FormControl>
                  <FormDescription>
                    Lower numbers display first (0 is highest priority).
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <Button type="submit" disabled={questionForm.formState.isSubmitting}>
            {mode === "edit" ? "Update Question" : "Create Question"}
          </Button>
        </form>
      </Form>

      {mode === "edit" && questionId && (
        <div className="border-t pt-6">
          <div className="mb-4 flex justify-between">
            <h3 className="text-lg font-medium">Answers</h3>
            <div className="flex flex-row gap-2">
              <Button
                variant="outline"
                onClick={() => setShowAnswerForm(!showAnswerForm)}
              >
                {showAnswerForm ? "Cancel" : "Add Answer"}
              </Button>
              {showAnswerForm && (
                <Button onClick={() => onAnswerSubmit(answerForm.getValues())}>
                  {"Save"}
                </Button>
              )}
            </div>
          </div>

          {showAnswerForm && (
            <Form {...answerForm}>
              <form
                onSubmit={answerForm.handleSubmit(onAnswerSubmit)}
                className="mb-6 space-y-6"
              >
                <FormField
                  control={answerForm.control}
                  name="content"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Answer</FormLabel>
                      <FormControl>
                        <RichTextInput
                          value={field.value}
                          onChange={field.onChange}
                        />
                      </FormControl>
                      <FormDescription>
                        The answer to the selected question.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <Button
                  type="submit"
                  disabled={answerForm.formState.isSubmitting}
                >
                  Add Answer
                </Button>
              </form>
            </Form>
          )}

          {existingAnswers.length > 0 ? (
            <div className="space-y-4">
              {existingAnswers.map((answer) => (
                <div key={answer.id} className="rounded-md border p-4">
                  <div className="flex items-start justify-between">
                    <Editor
                      editorSerializedState={answer.content}
                      editable={false}
                    />
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-red-600 hover:text-red-800"
                      onClick={() => deleteAnswer.mutate({ id: answer.id })}
                    >
                      Delete
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-muted-foreground">No answers yet.</p>
          )}
        </div>
      )}
    </div>
  );
};

export default FaqsCreateUpdateForm;
