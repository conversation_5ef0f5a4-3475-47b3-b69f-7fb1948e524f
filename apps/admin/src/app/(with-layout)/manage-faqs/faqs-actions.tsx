"use client";

import { useRouter } from "next/navigation";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Edit, EyeIcon, MoreHorizontal, Pencil, Trash } from "lucide-react";
import { toast } from "sonner";

import { <PERSON><PERSON> } from "@acme/ui/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@acme/ui/components/ui/dropdown-menu";

import AlertWrapper from "~/components/shared/alert-wrapper";
import { useSheet } from "~/hooks/use-sheet";
import { faqParamName } from "~/lib/constants";
import { useTRPC } from "~/trpc/react";

interface FaqsActionsProps {
  question: {
    id: string;
    content: string;
    parentId: string | null;
    createdAt: Date;
    updatedAt: Date;
    questionFor?: string; // add optional questionFor
  };
  questionFor: string; // new prop
}

const FaqsActions = ({ question, questionFor }: FaqsActionsProps) => {
  const trpc = useTRPC();
  const router = useRouter();
  const trpcUtils = useQueryClient();

  const { openSheet } = useSheet(faqParamName);

  const deleteQuestion = useMutation(
    trpc.faq.deleteQuestion.mutationOptions({
      onSuccess: () => {
        toast.success("FAQ deleted successfully");
        void trpcUtils.invalidateQueries(
          trpc.faq.getTopLevelQuestions.queryOptions(),
        );
      },
      onError: (error) => {
        toast.error(error.message);
      },
    }),
  );

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Open menu</span>
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Actions</DropdownMenuLabel>
        <DropdownMenuItem onClick={() => openSheet(question.id)}>
          <Edit className="mr-2 h-4 w-4" />
          Edit Question
        </DropdownMenuItem>
        <DropdownMenuItem
          onClick={() => router.push(`/manage-faqs/${question.id}`)}
        >
          <EyeIcon className="mr-2 h-4 w-4" />
          View Answers
        </DropdownMenuItem>
        {/* Only show Add Sub-Question if not LANDING_PAGE */}
        {questionFor !== "LANDING_PAGE" && (
          <DropdownMenuItem onClick={() => openSheet(`${question.id}:sub`)}>
            <Pencil className="mr-2 h-4 w-4" />
            Add Sub-Question
          </DropdownMenuItem>
        )}
        <DropdownMenuSeparator />
        <DropdownMenuItem
          className="text-red-600"
          onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
          }}
        >
          <AlertWrapper
            trigger={
              <button
                className="flex items-center gap-2"
                onClick={(e) => {
                  e.stopPropagation();
                  e.preventDefault();
                }}
              >
                <Trash className="h-4 w-4" /> Delete Question
              </button>
            }
            onConfirm={() => deleteQuestion.mutate({ id: question.id })}
            title="Are you sure you want to delete this question?"
            description="This action cannot be undone and will delete all answers associated with this question."
          />
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default FaqsActions;
