"use client";

import { useRouter } from "next/navigation";

import { Button } from "@acme/ui/components/ui/button";

import { faqParamName } from "~/lib/constants";

interface AddAnswerButtonProps {
  questionId: string;
}

const AddAnswerButton = ({ questionId }: AddAnswerButtonProps) => {
  const router = useRouter();

  const handleAddAnswerClick = () => {
    // Navigate to the question page with the faqId parameter set for editing
    router.push(`/manage-faqs/${questionId}?${faqParamName}=${questionId}`);
  };

  return (
    <Button variant="outline" onClick={handleAddAnswerClick}>
      Add Answer
    </Button>
  );
};

export default AddAnswerButton;
