"use client";

import { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Edit, Trash } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";

import { Button } from "@acme/ui/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@acme/ui/components/ui/form";
import { Textarea } from "@acme/ui/components/ui/textarea";

import AlertWrapper from "~/components/shared/alert-wrapper";
import { useTRPC } from "~/trpc/react";

interface QuestionAnswerDisplayProps {
  answer: {
    id: string;
    content: string;
    questionId: string;
    createdAt: Date;
    updatedAt: Date;
  };
  questionId: string;
}

const AnswerSchema = z.object({
  content: z.string().min(1, "Answer content is required"),
});

type AnswerFormValues = z.infer<typeof AnswerSchema>;

const QuestionAnswerDisplay = ({
  answer,
  questionId,
}: QuestionAnswerDisplayProps) => {
  const [isEditing, setIsEditing] = useState(false);
  const trpc = useTRPC();
  const trpcUtils = useQueryClient();

  const form = useForm<AnswerFormValues>({
    resolver: zodResolver(AnswerSchema),
    defaultValues: {
      content: answer.content,
    },
  });

  const updateAnswer = useMutation(
    trpc.faq.updateAnswer.mutationOptions({
      onSuccess: () => {
        toast.success("Answer updated successfully");
        void trpcUtils.invalidateQueries(
          trpc.faq.getAnswersByQuestionId.queryOptions({ questionId }),
        );
        setIsEditing(false);
      },
      onError: (error) => {
        toast.error(error.message);
      },
    }),
  );

  const deleteAnswer = useMutation(
    trpc.faq.deleteAnswer.mutationOptions({
      onSuccess: () => {
        toast.success("Answer deleted successfully");
        void trpcUtils.invalidateQueries(
          trpc.faq.getAnswersByQuestionId.queryOptions({ questionId }),
        );
      },
      onError: (error) => {
        toast.error(error.message);
      },
    }),
  );

  const onSubmit = (data: AnswerFormValues) => {
    updateAnswer.mutate({
      id: answer.id,
      content: data.content,
    });
  };

  return (
    <div className="rounded-lg border p-4">
      {isEditing ? (
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="content"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Textarea
                      placeholder="Enter answer"
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="flex gap-2">
              <Button type="submit" size="sm">
                Save
              </Button>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => setIsEditing(false)}
              >
                Cancel
              </Button>
            </div>
          </form>
        </Form>
      ) : (
        <div>
          <div className="flex items-start justify-between">
            <p className="flex-1">{answer.content}</p>
            <div className="flex gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsEditing(true)}
              >
                <Edit className="h-4 w-4" />
                <span className="sr-only">Edit</span>
              </Button>
              <AlertWrapper
                trigger={
                  <Button variant="ghost" size="sm" className="text-red-600">
                    <Trash className="h-4 w-4" />
                    <span className="sr-only">Delete</span>
                  </Button>
                }
                onConfirm={() => deleteAnswer.mutate({ id: answer.id })}
                title="Are you sure you want to delete this answer?"
                description="This action cannot be undone."
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default QuestionAnswerDisplay;
