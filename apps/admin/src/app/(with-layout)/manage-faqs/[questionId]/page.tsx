import Link from "next/link";
import { redirect } from "next/navigation";
import { format } from "date-fns";
import { ArrowLeft } from "lucide-react";

import { But<PERSON> } from "@acme/ui/components/ui/button";
import { Separator } from "@acme/ui/components/ui/separator";

import { faqParamName } from "~/lib/constants";
import { getQueryClient, HydrateClient, trpc } from "~/trpc/server";
import FaqsFormSheet from "../faqs-form-sheet";
import AddAnswerButton from "./add-answer-button";
import AddSubQuestionButton from "./add-sub-question-button";
import QuestionAnswerDisplay from "./question-answer-display";

interface ViewQuestionPageProps {
  params?: Promise<{ questionId: string }>;
  searchParams: Promise<{ [faqParamName]?: string }>;
}

const ViewQuestionPage = async ({
  params,
  searchParams,
}: ViewQuestionPageProps) => {
  const faqId = (await searchParams)[faqParamName];
  const questionId = (await params)?.questionId;

  if (!questionId) {
    redirect("/manage-faqs");
  }

  const queryClient = getQueryClient();

  if (faqId) {
    await queryClient.prefetchQuery(
      trpc.faq.getQuestionById.queryOptions({ id: faqId }),
    );
  }

  const data = await queryClient.fetchQuery(
    trpc.faq.getQuestionById.queryOptions({ id: questionId }),
  );

  if (!data) {
    return (
      <div className="flex h-full flex-col items-center justify-center">
        <h1 className="text-2xl font-bold">Question not found</h1>
        <Button asChild className="mt-4">
          <Link href="/manage-faqs">Back to FAQs</Link>
        </Button>
      </div>
    );
  }

  const answers = await queryClient.fetchQuery(
    trpc.faq.getAnswersByQuestionId.queryOptions({
      questionId,
    }),
  );

  const subQuestions = data.parentId
    ? []
    : await queryClient
        .fetchQuery(trpc.faq.getAllQuestions.queryOptions())
        .then((questions) =>
          questions.filter((q) => q.parentId === questionId),
        );

  return (
    <HydrateClient>
      <div>
        <div className="mb-6 flex items-center justify-between">
          <Button variant="outline" asChild>
            <Link href="/manage-faqs">
              <ArrowLeft size={16} className="mr-2" />
              Back to FAQs
            </Link>
          </Button>
        </div>

        <div className="rounded-lg border bg-card p-6 shadow-sm">
          <h1 className="text-2xl font-bold">Question</h1>
          <p className="mt-2 text-lg">{data.content}</p>
          <div className="mt-2 text-sm text-muted-foreground">
            Created: {format(new Date(data.createdAt), "PPP")} | Last updated:{" "}
            {format(new Date(data.updatedAt), "PPP")}
          </div>
        </div>

        <Separator className="my-6" />

        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">Answers</h2>
            <AddAnswerButton questionId={questionId} />
          </div>

          {answers.length > 0 ? (
            <div className="space-y-4">
              {answers.map((answer) => (
                <QuestionAnswerDisplay
                  key={answer.id}
                  answer={answer}
                  questionId={questionId}
                />
              ))}
            </div>
          ) : (
            <div className="rounded-md border border-dashed p-8 text-center">
              <p className="text-muted-foreground">No answers available.</p>
            </div>
          )}
        </div>

        {!data.parentId && data.questionFor !== "LANDING_PAGE" && (
          <>
            <Separator className="my-6" />
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold">Sub Questions</h2>
                <AddSubQuestionButton questionId={questionId} />
              </div>

              {subQuestions.length > 0 ? (
                <div className="space-y-4">
                  {subQuestions.map((subQuestion) => (
                    <div key={subQuestion.id} className="rounded-lg border p-4">
                      <Link
                        href={`/manage-faqs/${subQuestion.id}`}
                        className="font-medium hover:underline"
                      >
                        {subQuestion.content}
                      </Link>
                      <div className="mt-1 text-xs text-muted-foreground">
                        Created: {format(new Date(subQuestion.createdAt), "PP")}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="rounded-md border border-dashed p-8 text-center">
                  <p className="text-muted-foreground">
                    No sub questions available.
                  </p>
                </div>
              )}
            </div>
          </>
        )}

        {/* Hidden component to handle form sheets */}
        <div className="hidden">
          <FaqsFormSheet />
        </div>
      </div>
    </HydrateClient>
  );
};

export default ViewQuestionPage;
