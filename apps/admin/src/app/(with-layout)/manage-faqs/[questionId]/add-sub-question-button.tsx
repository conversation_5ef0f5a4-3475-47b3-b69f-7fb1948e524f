"use client";

import { useRouter } from "next/navigation";

import { Button } from "@acme/ui/components/ui/button";

import { faqParamName } from "~/lib/constants";

interface AddSubQuestionButtonProps {
  questionId: string;
}

const AddSubQuestionButton = ({ questionId }: AddSubQuestionButtonProps) => {
  const router = useRouter();

  const handleAddSubQuestionClick = () => {
    // Navigate with the sub-question parameter format
    router.push(`/manage-faqs/${questionId}?${faqParamName}=${questionId}:sub`);
  };

  return (
    <Button variant="outline" onClick={handleAddSubQuestionClick}>
      Add Sub Question
    </Button>
  );
};

export default AddSubQuestionButton;
