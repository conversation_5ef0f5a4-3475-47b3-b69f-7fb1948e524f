"use client";

import { PlusIcon } from "lucide-react";

import { But<PERSON> } from "@acme/ui/components/ui/button";

import { GenericSheet } from "~/components/shared/generic-sheet";
import { useSheet } from "~/hooks/use-sheet";
import { faqParamName } from "~/lib/constants";
import FaqsCreateUpdateForm from "./faqs-create-update-form";

const FaqsFormSheet = () => {
  const { isOpen, paramValue, openSheet, closeSheet } = useSheet(faqParamName);

  const isSubQuestion = paramValue?.includes(":sub");
  const questionId = isSubQuestion ? paramValue?.split(":")[0] : paramValue;
  const mode = paramValue ? (isSubQuestion ? "create" : "edit") : "create";
  console.log(">>>> mode: ", mode);

  return (
    <div>
      <Button onClick={() => openSheet()}>
        <PlusIcon className="mr-2 size-4" />
        Create FAQ
      </Button>
      <GenericSheet
        isOpen={isOpen}
        onClose={closeSheet}
        title={
          paramValue
            ? isSubQuestion
              ? "Add Sub-Question"
              : "Edit FAQ"
            : "Create FAQ"
        }
      >
        <FaqsCreateUpdateForm
          questionId={questionId ?? undefined}
          isSubQuestion={isSubQuestion}
          mode={mode}
        />
      </GenericSheet>
    </div>
  );
};

export default FaqsFormSheet;
