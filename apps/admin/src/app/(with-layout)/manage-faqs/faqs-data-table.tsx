"use client";

import type {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
} from "@tanstack/react-table";
import { useEffect, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import {
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { ChevronDown } from "lucide-react";
import { parseAsStringEnum, useQueryState } from "nuqs";

import { Badge } from "@acme/ui/components/ui/badge";
import { Button } from "@acme/ui/components/ui/button";
import { Checkbox } from "@acme/ui/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@acme/ui/components/ui/dropdown-menu";
import { Input } from "@acme/ui/components/ui/input";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@acme/ui/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@acme/ui/components/ui/table";
import { cn } from "@acme/ui/lib/utils";

import SortableColumnHeader from "~/components/shared/sortable-column-header";
import DataTableSkeleton from "~/components/skeleton/data-table-skeleton";
import { useTRPC } from "~/trpc/react";
import FaqsActions from "./faqs-actions";
import FaqsFormSheet from "./faqs-form-sheet";

const QUESTION_FOR_OPTIONS = [
  { label: "Seller", value: "SELLER" },
  { label: "Kabadiwala", value: "KABADIWALA" },
  { label: "Landing Page", value: "LANDING_PAGE" },
] as const;

const FaqsDataTable = () => {
  const trpc = useTRPC();
  const [sorting, setSorting] = useState<SortingState>([
    {
      id: "order",
      desc: false,
    },
  ]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState({});
  const [questionFor, setQuestionFor] = useQueryState(
    "questionFor",
    parseAsStringEnum(
      QUESTION_FOR_OPTIONS.map((option) => option.value),
    ).withDefault("SELLER"),
  );

  const { data: questions = [], isLoading } = useQuery(
    trpc.faq.getTopLevelQuestions.queryOptions(),
  );
  const [filteredQuestions, setFilteredQuestions] = useState<
    (typeof questions)[number][]
  >([]);

  const columns: ColumnDef<(typeof questions)[number]>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "content",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Question" />
      ),
      cell: ({ row }) => (
        <div className="max-w-[300px] font-medium">{row.original.content}</div>
      ),
    },
    {
      accessorKey: "order",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Order" />
      ),
      cell: ({ row }) => <div>{row.original.order}</div>,
    },
    {
      accessorKey: "questionFor",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="For" />
      ),
      cell: ({ row }) => <Badge>{row.original.questionFor}</Badge>,
      enableSorting: true,
    },
    {
      accessorKey: "createdAt",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Created" />
      ),
      cell: ({ row }) => {
        const date = new Date(row.original.createdAt);
        return <div>{date.toLocaleDateString()}</div>;
      },
    },
    {
      accessorKey: "updatedAt",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Updated" />
      ),
      cell: ({ row }) => {
        const date = new Date(row.original.updatedAt);
        return <div>{date.toLocaleDateString()}</div>;
      },
    },
    {
      id: "actions",
      enableHiding: false,
      cell: ({ row }) => (
        <FaqsActions question={row.original} questionFor={questionFor} />
      ),
    },
  ];

  useEffect(() => {
    const filtered = questions.filter((q) => q.questionFor === questionFor);
    setFilteredQuestions(filtered);
  }, [questionFor, questions]);

  const table = useReactTable({
    data: filteredQuestions,
    columns,
    onSortingChange: (sort) => {
      setSorting(sort);
      console.log("[FAQ DataTable] Sorting changed", sort);
    },
    onColumnFiltersChange: (filters) => {
      setColumnFilters(filters);
      console.log("[FAQ DataTable] Column filters changed", filters);
    },
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: (vis) => {
      setColumnVisibility(vis);
      console.log("[FAQ DataTable] Column visibility changed", vis);
    },
    onRowSelectionChange: (sel) => {
      setRowSelection(sel);
      console.log("[FAQ DataTable] Row selection changed", sel);
    },
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  if (isLoading) {
    return <DataTableSkeleton />;
  }
  return (
    <div className="w-full">
      <div className="flex items-center justify-between py-4">
        <div className="flex items-center gap-2">
          <Input
            placeholder="Filter questions..."
            value={table.getColumn("content")?.getFilterValue() as string}
            onChange={(event) => {
              table.getColumn("content")?.setFilterValue(event.target.value);
              console.log(
                "[FAQ DataTable] Content filter changed",
                event.target.value,
              );
            }}
            className="max-w-sm"
          />
        </div>
        <div className="flex gap-2">
          <FaqsFormSheet />
          {/* Replaced DropdownMenu with controlled Shadcn Select */}
          <Select
            value={questionFor}
            onValueChange={(value) => {
              void setQuestionFor(
                value as "SELLER" | "KABADIWALA" | "LANDING_PAGE",
              );
            }}
          >
            <SelectTrigger className="w-[180px] border border-black-900">
              <SelectValue placeholder="Select type" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectLabel>Type</SelectLabel>
                {QUESTION_FOR_OPTIONS.map((option) => (
                  <SelectItem key={option.label} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectGroup>
            </SelectContent>
          </Select>
          {/* Columns DropdownMenu remains unchanged */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                Columns <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {table
                .getAllColumns()
                .filter((column) => column.getCanHide())
                .map((column) => {
                  return (
                    <DropdownMenuCheckboxItem
                      key={column.id}
                      className="capitalize"
                      checked={column.getIsVisible()}
                      onCheckedChange={(value) =>
                        column.toggleVisibility(!!value)
                      }
                    >
                      {column.id}
                    </DropdownMenuCheckboxItem>
                  );
                })}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext(),
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody className="relative text-start">
            {table.getRowModel().rows.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell
                      className={cn(
                        cell.column.id === "actions" &&
                          "sticky right-0 backdrop-blur-3xl",
                      )}
                      key={cell.id}
                    >
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-end space-x-2 py-4">
        <div className="flex-1 text-sm text-muted-foreground">
          {table.getFilteredSelectedRowModel().rows.length} of{" "}
          {table.getFilteredRowModel().rows.length} row(s) selected.
        </div>
        <div className="space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  );
};

export default FaqsDataTable;
