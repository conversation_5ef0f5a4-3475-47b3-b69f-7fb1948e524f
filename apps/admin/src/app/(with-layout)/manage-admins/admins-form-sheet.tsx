"use client";

import { PlusIcon } from "lucide-react";

import { But<PERSON> } from "@acme/ui/components/ui/button";

import { GenericSheet } from "~/components/shared/generic-sheet";
import { useSheet } from "~/hooks/use-sheet";
import { AdminsCreateUpdateForm } from "./admins-create-update-form";

const adminParamName = "admin_id";

const AdminsFormSheet = () => {
  const { isOpen, paramValue, openSheet, closeSheet } =
    useSheet(adminParamName);

  return (
    <div>
      <Button onClick={() => openSheet()}>
        <PlusIcon className="size-4" />
        Create Admin
      </Button>
      <GenericSheet
        isOpen={isOpen}
        onClose={closeSheet}
        title={paramValue ? "Edit Admin" : "Create Admin"}
      >
        <AdminsCreateUpdateForm />
      </GenericSheet>
    </div>
  );
};

export default AdminsFormSheet;
