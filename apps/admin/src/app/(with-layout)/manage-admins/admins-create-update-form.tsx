"use client";

import type { z } from "zod";
import { useEffect } from "react";
import Image from "next/image";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  skipToken,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";
import { generateUploadButton } from "@uploadthing/react";
import { Loader2, Trash, Upload } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

import { Button } from "@acme/ui/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@acme/ui/components/ui/form";
import { Input } from "@acme/ui/components/ui/input";
import { AdminCRUDSchema } from "@acme/validators";

import type { OurFileRouter } from "~/app/api/uploadthing/core";
import { useSheet } from "~/hooks/use-sheet";
import { adminParamName } from "~/lib/constants";
import { useTRPC } from "~/trpc/react";

type AdminFormValues = z.infer<typeof AdminCRUDSchema>;

export const UploadButton = generateUploadButton<OurFileRouter>();

export function AdminsCreateUpdateForm() {
  const trpc = useTRPC();
  const trpcUtils = useQueryClient();
  const { paramValue: adminId, closeSheet } = useSheet(adminParamName);
  const isEditMode = !!adminId;

  const { data: adminData, isLoading } = useQuery(
    trpc.admin.getAdminById.queryOptions(
      adminId ? { adminId: adminId } : skipToken,
    ),
  );

  const form = useForm<AdminFormValues>({
    resolver: zodResolver(AdminCRUDSchema),
    defaultValues: adminData
      ? {
          name: adminData.name,
          email: adminData.email,
          image: adminData.image ?? undefined,
        }
      : {
          name: "",
          email: "",
          image: "",
        },
  });

  useEffect(() => {
    if (isEditMode && adminData) {
      form.reset({
        name: adminData.name,
        email: adminData.email,
        image: adminData.image ?? undefined,
      });
    }
  }, [isEditMode, adminData, form]);

  const createAdmin = useMutation(
    trpc.admin.createAdmin.mutationOptions({
      onSuccess: (opts) => {
        toast.success(opts.message);
        void trpcUtils.invalidateQueries({
          queryKey: trpc.admin.getAllAdmins.queryKey(),
        });
        closeSheet();
      },
      onError: (error) => {
        toast.error(error.message);
      },
    }),
  );

  const updateAdmin = useMutation(
    trpc.admin.updateAdmin.mutationOptions({
      onSuccess: (opts) => {
        toast.success(opts.message);
        void trpcUtils.invalidateQueries({
          queryKey: trpc.admin.getAllAdmins.queryKey(),
        });
        closeSheet();
      },
      onError: (error) => {
        toast.error(error.message);
      },
    }),
  );

  const deleteImage = useMutation(
    trpc.util.deleteUploadthingFileUsingFileKey.mutationOptions({
      onSuccess: (opts) => {
        toast.success(opts.message);
        form.setValue("image", "");
      },
      onError: (error) => {
        toast.error(error.message);
      },
    }),
  );

  const onSubmit = (data: AdminFormValues) => {
    if (isEditMode) {
      updateAdmin.mutate({ ...data, adminId: adminId });
    } else {
      createAdmin.mutate(data);
    }
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Name</FormLabel>
              <FormControl>
                <Input placeholder="Enter admin name" {...field} />
              </FormControl>
              <FormDescription>The name of the administrator.</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <Input
                  type="email"
                  placeholder="Enter admin email"
                  {...field}
                />
              </FormControl>
              <FormDescription>
                The email address used for login.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="image"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Profile Image</FormLabel>
              <FormControl>
                {field.value ? (
                  <div className="relative">
                    <Image
                      src={field.value}
                      alt="admin avatar"
                      height={200}
                      width={200}
                      className="h-32 w-32 rounded-full object-cover"
                    />
                    <Button
                      type="button"
                      variant="destructive"
                      size="sm"
                      className="mt-2"
                      disabled={deleteImage.isPending}
                      onClick={() =>
                        deleteImage.mutate({ fileKey: field.value ?? "" })
                      }
                    >
                      {deleteImage.isPending ? (
                        <>
                          <Loader2 className="animate-spin" />
                          Removing Image
                        </>
                      ) : (
                        <>
                          <Trash className="mr-2 h-4 w-4" />
                          Remove Image
                        </>
                      )}
                    </Button>
                  </div>
                ) : (
                  <UploadButton
                    endpoint="imageUploader"
                    appearance={{
                      button:
                        "bg-primary text-primary-foreground hover:bg-primary/90 rounded-md px-4 py-2 text-sm font-medium shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring",
                      allowedContent: "text-muted-foreground text-xs",
                      container: "flex flex-col gap-2 w-full",
                    }}
                    content={{
                      button: ({ isUploading }) => (
                        <span className="flex items-center gap-2">
                          {isUploading ? (
                            <Loader2 className="animate-spin" />
                          ) : (
                            <Upload className="size-4" />
                          )}
                          {isUploading
                            ? "Uploading..."
                            : "Upload Profile Image"}
                        </span>
                      ),
                      allowedContent: "JPEG, PNG or WebP (max 4MB)",
                    }}
                    onClientUploadComplete={(res) => {
                      const imageUrl = res[0]?.ufsUrl;
                      if (!imageUrl) {
                        toast.error("Image upload failed");
                        return;
                      }

                      field.onChange(imageUrl);
                      toast.success("Image uploaded successfully");
                    }}
                    onUploadError={(error: Error) => {
                      toast.error(`Upload error: ${error.message}`);
                    }}
                  />
                )}
              </FormControl>
              <FormDescription>
                Upload a profile image for the administrator.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex gap-2">
          <Button
            type="submit"
            disabled={
              createAdmin.isPending ||
              updateAdmin.isPending ||
              deleteImage.isPending
            }
          >
            {createAdmin.isPending || updateAdmin.isPending ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {isEditMode ? "Updating..." : "Creating..."}
              </>
            ) : (
              <>{isEditMode ? "Update Admin" : "Create Admin"}</>
            )}
          </Button>
          <Button type="button" variant="outline" onClick={closeSheet}>
            Cancel
          </Button>
        </div>
      </form>
    </Form>
  );
}

export default AdminsCreateUpdateForm;
