import type { SearchParams } from "nuqs/server";
import { Suspense } from "react";

import { Separator } from "@acme/ui/components/ui/separator";

import DataTableSkeleton from "~/components/skeleton/data-table-skeleton";
import { loadUserSearchParams } from "~/lib/nuqs-utils";
import { getQueryClient, HydrateClient, trpc } from "~/trpc/server";
import AdminsDataTable from "./admins-data-table";

export const metadata = {
  title: "Admins Management",
  description: "Manage your website's administrators",
};

interface ManageAdminsPageProps {
  searchParams: Promise<SearchParams>;
}

const ManageAdminsPage = async ({ searchParams }: ManageAdminsPageProps) => {
  const { admin_id } = await loadUserSearchParams(searchParams);
  const queryClient = getQueryClient();

  // Prefetch list data
  await queryClient.prefetchQuery(trpc.admin.getAllAdmins.queryOptions());

  // Prefetch individual item data if ID exists
  if (admin_id) {
    await queryClient.prefetchQuery(
      trpc.admin.getAdminById.queryOptions({ adminId: admin_id }),
    );
  }

  return (
    <HydrateClient>
      <div>
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Admins Management
            </h1>
            <p className="text-muted-foreground">
              Create, edit and manage administrators of your website.
            </p>
          </div>
        </div>
        <Separator className="my-6" />
        <Suspense fallback={<DataTableSkeleton />}>
          <AdminsDataTable />
        </Suspense>
      </div>
    </HydrateClient>
  );
};

export default ManageAdminsPage;
