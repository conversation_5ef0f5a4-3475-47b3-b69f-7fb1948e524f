"use client";

import Image from "next/image";
import { useQuery } from "@tanstack/react-query";
import { Loader2 } from "lucide-react";

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader as CardHeaderUI,
  CardTitle,
} from "@acme/ui/components/ui/card";

import type { DLAdvanceApiResult } from "~/lib/types";
import { useTRPC } from "~/trpc/react";

function stringToColor(str: string) {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    hash = str.charCodeAt(i) + ((hash << 5) - hash);
  }
  const color = `hsl(${hash % 360}, 70%, 60%)`;
  return color;
}

const KabadiwalaDLCard = ({ dlData }: { dlData: DLAdvanceApiResult }) => {
  const imageUrl = dlData.user_image
    ? `data:image/jpeg;base64,${dlData.user_image}`
    : undefined;
  const name = dlData.user_full_name || "?";
  const initial = name.charAt(0).toUpperCase();
  const avatarBg = stringToColor(name);
  return (
    <Card className="mt-4">
      <CardHeaderUI className="flex flex-row items-center gap-4">
        {imageUrl ? (
          <Image
            src={imageUrl}
            alt={name}
            className="h-16 w-16 rounded-full border object-cover"
            width={64}
            height={64}
          />
        ) : (
          <div
            className="flex h-16 w-16 items-center justify-center rounded-full border text-2xl font-bold text-white"
            style={{ background: avatarBg }}
          >
            {initial}
          </div>
        )}
        <div>
          <CardTitle>{name}</CardTitle>
          <CardDescription>DL Number: {dlData.dl_number}</CardDescription>
        </div>
      </CardHeaderUI>
      <CardContent>
        <div className="grid grid-cols-1 gap-x-8 gap-y-2 text-sm md:grid-cols-2">
          <div>
            <span className="font-semibold">DOB:</span> {dlData.user_dob}
          </div>
          <div>
            <span className="font-semibold">Blood Group:</span>{" "}
            {dlData.user_blood_group}
          </div>
          <div>
            <span className="font-semibold">Father/Husband:</span>{" "}
            {dlData.father_or_husband}
          </div>
          <div>
            <span className="font-semibold">State:</span> {dlData.state}
          </div>
          <div>
            <span className="font-semibold">Status:</span> {dlData.status}
          </div>
          <div>
            <span className="font-semibold">Issued Date:</span>{" "}
            {dlData.issued_date}
          </div>
          <div>
            <span className="font-semibold">Expiry Date:</span>{" "}
            {dlData.expiry_date}
          </div>
          <div>
            <span className="font-semibold">Endorse Number:</span>{" "}
            {dlData.endorse_number}
          </div>
          <div>
            <span className="font-semibold">Endorse Date:</span>{" "}
            {dlData.endorse_date}
          </div>
          <div>
            <span className="font-semibold">Non-Transport Validity:</span>{" "}
            {dlData.non_transport_validity.from} to{" "}
            {dlData.non_transport_validity.to}
          </div>
          <div>
            <span className="font-semibold">Transport Validity:</span>{" "}
            {dlData.transport_validity.from} to {dlData.transport_validity.to}
          </div>
          <div className="col-span-1 md:col-span-2">
            <span className="font-semibold">Status Details:</span>{" "}
            {dlData.status_details.remarks} ({dlData.status_details.from} to{" "}
            {dlData.status_details.to})
          </div>
        </div>
        <div className="mt-4">
          <div className="mb-1 font-semibold">Addresses:</div>
          <div className="grid grid-cols-1 gap-2 md:grid-cols-2">
            {dlData.user_address.map((addr, idx) => (
              <div key={idx} className="rounded border bg-muted/50 p-2">
                <div className="font-medium">{addr.type}</div>
                <div>{addr.completeAddress}</div>
                <div>
                  Pin: {addr.pin}, Country: {addr.country}
                </div>
              </div>
            ))}
          </div>
        </div>
        <div className="mt-4">
          <div className="mb-1 font-semibold">Vehicle Categories:</div>
          <div className="grid grid-cols-1 gap-2 md:grid-cols-2">
            {dlData.vehicle_category_details.map((veh, idx) => (
              <div key={idx} className="rounded border bg-muted/50 p-2">
                <div className="font-medium">{veh.cov}</div>
                <div>Issue: {veh.issueDate}</div>
                {veh.expiryDate && <div>Expiry: {veh.expiryDate}</div>}
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

const KabadiwalaDetailsClient = ({
  kabadiwalaId,
}: {
  kabadiwalaId: string;
}) => {
  const trpc = useTRPC();
  const { data, isLoading, isError } = useQuery(
    trpc.kabadiwala.getKabadiwalaDetailsById.queryOptions({ kabadiwalaId }),
  );

  if (isLoading) {
    return (
      <div className="flex h-96 items-center justify-center">
        <Loader2 className="size-5 animate-spin" />
      </div>
    );
  }
  if (isError || !data) {
    return (
      <div className="flex h-96 items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-semibold text-gray-900">
            Kabadiwala Not Found
          </h2>
          <p className="mt-2 text-gray-600">
            The kabadiwala you're looking for doesn't exist.
          </p>
        </div>
      </div>
    );
  }

  // Earnings summary
  const totalEarnings = data.transactions
    .filter((t) => t.status === "COMPLETED" && t.transactionType === "CREDIT")
    .reduce((sum: number, t) => sum + Number(t.amount), 0);

  return (
    <div className="px-6 py-6 lg:px-8">
      <div className="mb-6 grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* Kabadiwala Info */}
        <Card>
          <CardHeaderUI>
            <CardTitle>{data.name}</CardTitle>
            <CardDescription>ID: {data.id}</CardDescription>
          </CardHeaderUI>
          <CardContent>
            <div className="flex items-center gap-4">
              {data.image ? (
                <Image
                  src={data.image}
                  alt={data.name}
                  width={64}
                  height={64}
                  className="h-16 w-16 rounded-full object-cover"
                />
              ) : (
                <div className="flex h-16 w-16 items-center justify-center rounded-full bg-gray-200">
                  <span className="text-xl font-medium text-gray-600">
                    {data.name.charAt(0).toUpperCase()}
                  </span>
                </div>
              )}
              <div>
                <div className="text-lg font-medium">{data.name}</div>
                <div className="text-sm text-gray-500">{data.email}</div>
                <div className="text-sm text-gray-500">{data.phoneNumber}</div>
                <div className="text-sm text-gray-500">
                  Wallet: ₹{data.walletBalance ?? 0}
                </div>
                <div className="text-sm text-gray-500">
                  Status: {data.isBlocked ? "Blocked" : "Active"}
                </div>
                <div className="text-sm text-gray-500">
                  Created: {new Date(data.createdAt).toLocaleString()}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        {/* Earnings Summary */}
        <Card>
          <CardHeaderUI>
            <CardTitle>Earnings</CardTitle>
            <CardDescription>
              Total earnings from completed transactions
            </CardDescription>
          </CardHeaderUI>
          <CardContent>
            <div className="text-2xl font-bold">
              ₹{totalEarnings.toLocaleString()}
            </div>
            <div className="mt-2 text-sm text-gray-500">
              Total Transactions: {data.transactions.length}
            </div>
          </CardContent>
        </Card>
      </div>
      {/* DL Card */}
      {data.dlVerificationResponse && (
        <KabadiwalaDLCard dlData={data.dlVerificationResponse} />
      )}
      {/* Vehicles */}
      <div className="mt-8">
        <Card>
          <CardHeaderUI>
            <CardTitle>Vehicles</CardTitle>
            <CardDescription>Registered vehicles</CardDescription>
          </CardHeaderUI>
          <CardContent>
            {data.vehicles.length === 0 ? (
              <div className="text-gray-500">No vehicles found.</div>
            ) : (
              <ul className="space-y-2">
                {data.vehicles.map((v) => (
                  <li
                    key={v.id}
                    className="border-b pb-2 last:border-b-0 last:pb-0"
                  >
                    <div className="font-medium">{v.vehicleType}</div>
                    <div className="text-sm text-gray-500">
                      Number: {v.vehicleNumber}
                    </div>
                    <div className="text-sm text-gray-500">
                      Active: {v.isActive ? "Yes" : "No"}
                    </div>
                  </li>
                ))}
              </ul>
            )}
          </CardContent>
        </Card>
      </div>
      {/* Orders */}
      <div className="mt-8">
        <Card>
          <CardHeaderUI>
            <CardTitle>Orders</CardTitle>
            <CardDescription>
              Orders assigned to this kabadiwala
            </CardDescription>
          </CardHeaderUI>
          <CardContent>
            {data.orders.length === 0 ? (
              <div className="text-gray-500">No orders found.</div>
            ) : (
              <ul className="max-h-80 space-y-2 overflow-y-auto pr-2">
                {data.orders.map((o) => (
                  <li
                    key={o.id}
                    className="flex items-center justify-between rounded border-b px-2 pb-2 transition-colors last:border-b-0 last:pb-0 hover:bg-muted/50"
                  >
                    <div>
                      <div className="flex items-center gap-2 font-medium">
                        Order ID: {o.id}
                        <a
                          href={`/manage-orders/${o.id}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="ml-1 flex items-center text-primary hover:underline"
                        >
                          <span className="sr-only">View Order</span>
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            strokeWidth={1.5}
                            stroke="currentColor"
                            className="h-4 w-4"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              d="M17.25 6.75H6.75m10.5 0v10.5m0-10.5L6.75 17.25"
                            />
                          </svg>
                        </a>
                      </div>
                      <div className="text-sm text-gray-500">
                        Status: {o.status}
                      </div>
                      <div className="text-sm text-gray-500">
                        Amount: ₹{o.totalAmount}
                      </div>
                      <div className="text-sm text-gray-500">
                        Created: {new Date(o.createdAt).toLocaleString()}
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            )}
          </CardContent>
        </Card>
      </div>
      {/* Transactions */}
      <div className="mt-8">
        <Card>
          <CardHeaderUI>
            <CardTitle>Transactions</CardTitle>
            <CardDescription>Payment transactions</CardDescription>
          </CardHeaderUI>
          <CardContent>
            {data.transactions.length === 0 ? (
              <div className="text-gray-500">No transactions found.</div>
            ) : (
              <ul className="max-h-80 space-y-2 overflow-y-auto pr-2">
                {data.transactions.map((t) => (
                  <li
                    key={t.id}
                    className="rounded border-b px-2 pb-2 transition-colors last:border-b-0 last:pb-0 hover:bg-muted/50"
                  >
                    <div className="font-medium">Amount: ₹{t.amount}</div>
                    <div className="text-sm text-gray-500">
                      Type: {t.transactionType}
                    </div>
                    <div className="text-sm text-gray-500">
                      Status: {t.status}
                    </div>
                    <div className="text-sm text-gray-500">
                      For: {t.transactionFor}
                    </div>
                    <div className="text-sm text-gray-500">
                      Created: {new Date(t.createdAt).toLocaleString()}
                    </div>
                  </li>
                ))}
              </ul>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default KabadiwalaDetailsClient;
