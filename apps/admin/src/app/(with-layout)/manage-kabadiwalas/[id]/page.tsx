import { getQueryClient, HydrateClient, trpc } from "~/trpc/server";
import KabadiwalaDetailsClient from "./KabadiwalaDetailsClient";

interface KabadiwalaDetailPageProps {
  params: Promise<{ id: string }>;
}

const KabadiwalaDetailPage = async ({ params }: KabadiwalaDetailPageProps) => {
  const kabadiwalaId = (await params).id;

  const queryClient = getQueryClient();
  void queryClient.prefetchQuery(
    trpc.kabadiwala.getKabadiwalaDetailsById.queryOptions({ kabadiwalaId }),
  );

  return (
    <HydrateClient>
      <KabadiwalaDetailsClient kabadiwalaId={kabadiwalaId} />
    </HydrateClient>
  );
};

export default KabadiwalaDetailPage;
