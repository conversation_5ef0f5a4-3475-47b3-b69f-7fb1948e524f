"use client";

import type {
  ColumnDef,
  ColumnFiltersState,
  FilterFn,
  SortingState,
  VisibilityState,
} from "@tanstack/react-table";
import { useEffect, useState } from "react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { rankItem } from "@tanstack/match-sorter-utils";
import { useMutation, useQuery } from "@tanstack/react-query";
import {
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { formatDate } from "date-fns";
import {
  ArrowUpRight,
  Ban,
  ChevronDown,
  Edit,
  MoreHorizontal,
  Trash,
  Unlock,
} from "lucide-react";
import { useQueryState } from "nuqs";
import { toast } from "sonner";

import { Button } from "@acme/ui/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader as CardHeaderUI,
  CardTitle,
} from "@acme/ui/components/ui/card";
import { Checkbox } from "@acme/ui/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@acme/ui/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@acme/ui/components/ui/dropdown-menu";
import { Input } from "@acme/ui/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@acme/ui/components/ui/table";
import { Textarea } from "@acme/ui/components/ui/textarea";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@acme/ui/components/ui/tooltip";
import { cn } from "@acme/ui/lib/utils";

import type { DLAdvanceApiResult } from "~/lib/types";
import type { RouterOutputs } from "~/server/api";
import AlertWrapper from "~/components/shared/alert-wrapper";
import ImageDialog from "~/components/shared/image-dialog";
import SortableColumnHeader from "~/components/shared/sortable-column-header";
import { useSheet } from "~/hooks/use-sheet";
import { useTRPC } from "~/trpc/react";
import KabadiwalasFormSheet from "./kabadiwalas-form-sheet";

type KabadiwalaRow = RouterOutputs["kabadiwala"]["getAllKabadiwalas"][number];

const KabadiwalasDataTable = () => {
  const trpc = useTRPC();
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState({});
  const router = useRouter();
  const [globalFilter, setGlobalFilter] = useState("");

  const { openSheet } = useSheet("kabadiwala_id");
  const [blockKabadiwalaId, setBlockKabadiwalaId] = useQueryState(
    "blockKabadiwalaId",
    {
      parse: (v) => (typeof v === "string" ? v : undefined),
    },
  );
  const [blockReason, setBlockReason] = useState("");
  useEffect(() => {
    setBlockReason("");
  }, [blockKabadiwalaId]);

  const { data: kabadiwalas = [], refetch } = useQuery(
    trpc.kabadiwala.getAllKabadiwalas.queryOptions(),
  );

  const deleteKabadiwala = useMutation(
    trpc.kabadiwala.deleteKabadiwala.mutationOptions({
      onSuccess: (opts) => {
        toast.success(opts.message);
        void refetch();
      },
      onError: (error) => {
        toast.error(error.message);
      },
    }),
  );
  const blockKabadiwala = useMutation(
    trpc.kabadiwala.blockKabadiwala.mutationOptions({
      onSuccess: (opts) => {
        toast.success(opts.message);
        void setBlockKabadiwalaId(null);
        setBlockReason("");
        void refetch();
      },
      onError: (error) => {
        toast.error(error.message);
      },
    }),
  );
  const unblockKabadiwala = useMutation(
    trpc.kabadiwala.unblockKabadiwala.mutationOptions({
      onSuccess: (opts) => {
        toast.success(opts.message);
        void refetch();
      },
      onError: (error) => {
        toast.error(error.message);
      },
    }),
  );

  const columns: ColumnDef<(typeof kabadiwalas)[number]>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "name",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Full Name" />
      ),
      cell: ({ row }) => (
        <div className="font-medium">{row.getValue("name")}</div>
      ),
    },
    {
      accessorKey: "phoneNumber",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Phone" />
      ),
      cell: ({ row }) => <div>{row.getValue("phoneNumber")}</div>,
    },
    {
      accessorKey: "dlVerificationResponse",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Driving Details" />
      ),
      cell: ({ row }) => {
        // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion
        const details = row.getValue(
          "dlVerificationResponse",
        ) as DLAdvanceApiResult | null;
        const dlData = details;
        if (!dlData) {
          return <div>no dl info </div>;
        }
        // For avatar: use image if available, else initial with random bg
        const imageUrl = dlData.user_image
          ? `data:image/jpeg;base64,${dlData.user_image}`
          : undefined;
        const name = dlData.user_full_name || "?";
        const initial = name.charAt(0).toUpperCase();
        // Generate a random color based on name
        function stringToColor(str: string) {
          let hash = 0;
          for (let i = 0; i < str.length; i++) {
            hash = str.charCodeAt(i) + ((hash << 5) - hash);
          }
          const color = `hsl(${hash % 360}, 70%, 60%)`;
          return color;
        }
        const avatarBg = stringToColor(name);
        return (
          <Dialog>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm">
                View DL Info
              </Button>
            </DialogTrigger>
            <DialogContent className="min-w-[80dvw]">
              <DialogHeader>
                <DialogTitle>Driving License Details</DialogTitle>
              </DialogHeader>
              <Card className="mt-4">
                <CardHeaderUI className="flex flex-row items-center gap-4">
                  {imageUrl ? (
                    <Image
                      src={imageUrl}
                      alt={name}
                      className="h-16 w-16 rounded-full border object-cover"
                      width={64}
                      height={64}
                    />
                  ) : (
                    <div
                      className="flex h-16 w-16 items-center justify-center rounded-full border text-2xl font-bold text-white"
                      style={{ background: avatarBg }}
                    >
                      {initial}
                    </div>
                  )}
                  <div>
                    <CardTitle>{name}</CardTitle>
                    <CardDescription>
                      DL Number: {dlData.dl_number}
                    </CardDescription>
                  </div>
                </CardHeaderUI>
                <CardContent>
                  <div className="grid grid-cols-1 gap-x-8 gap-y-2 text-sm md:grid-cols-2">
                    <div>
                      <span className="font-semibold">DOB:</span>{" "}
                      {dlData.user_dob}
                    </div>
                    <div>
                      <span className="font-semibold">Blood Group:</span>{" "}
                      {dlData.user_blood_group}
                    </div>
                    <div>
                      <span className="font-semibold">Father/Husband:</span>{" "}
                      {dlData.father_or_husband}
                    </div>
                    <div>
                      <span className="font-semibold">State:</span>{" "}
                      {dlData.state}
                    </div>
                    <div>
                      <span className="font-semibold">Status:</span>{" "}
                      {dlData.status}
                    </div>
                    <div>
                      <span className="font-semibold">Issued Date:</span>{" "}
                      {dlData.issued_date}
                    </div>
                    <div>
                      <span className="font-semibold">Expiry Date:</span>{" "}
                      {dlData.expiry_date}
                    </div>
                    <div>
                      <span className="font-semibold">Endorse Number:</span>{" "}
                      {dlData.endorse_number}
                    </div>
                    <div>
                      <span className="font-semibold">Endorse Date:</span>{" "}
                      {dlData.endorse_date}
                    </div>
                    <div>
                      <span className="font-semibold">
                        Non-Transport Validity:
                      </span>{" "}
                      {dlData.non_transport_validity.from} to{" "}
                      {dlData.non_transport_validity.to}
                    </div>
                    <div>
                      <span className="font-semibold">Transport Validity:</span>{" "}
                      {dlData.transport_validity.from} to{" "}
                      {dlData.transport_validity.to}
                    </div>
                    <div className="col-span-1 md:col-span-2">
                      <span className="font-semibold">Status Details:</span>{" "}
                      {dlData.status_details.remarks} (
                      {dlData.status_details.from} to {dlData.status_details.to}
                      )
                    </div>
                  </div>
                  <div className="mt-4">
                    <div className="mb-1 font-semibold">Addresses:</div>
                    <div className="grid grid-cols-1 gap-2 md:grid-cols-2">
                      {dlData.user_address.map((addr, idx) => (
                        <div
                          key={idx}
                          className="rounded border bg-muted/50 p-2"
                        >
                          <div className="font-medium">{addr.type}</div>
                          <div>{addr.completeAddress}</div>
                          <div>
                            Pin: {addr.pin}, Country: {addr.country}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                  <div className="mt-4">
                    <div className="mb-1 font-semibold">
                      Vehicle Categories:
                    </div>
                    <div className="grid grid-cols-1 gap-2 md:grid-cols-2">
                      {dlData.vehicle_category_details.map((veh, idx) => (
                        <div
                          key={idx}
                          className="rounded border bg-muted/50 p-2"
                        >
                          <div className="font-medium">{veh.cov}</div>
                          <div>Issue: {veh.issueDate}</div>
                          {veh.expiryDate && (
                            <div>Expiry: {veh.expiryDate}</div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </DialogContent>
          </Dialog>
        );
      },
    },
    {
      accessorKey: "phoneNumberVerified",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Phone Verified" />
      ),
      cell: ({ row }) => {
        const isVerified = row.getValue("phoneNumberVerified");
        return (
          <div
            className={`font-medium ${
              isVerified ? "text-green-500" : "text-red-500"
            }`}
          >
            {isVerified ? "Yes" : "No"}
          </div>
        );
      },
    },
    {
      accessorKey: "isBlocked",
      header: "Blocked",
      cell: ({ row }) => {
        const isBlocked = row.getValue("isBlocked");
        if (isBlocked === null || isBlocked === undefined) {
          return <span className="text-muted-foreground">-</span>;
        }
        return isBlocked === true ? (
          <div className="font-semibold text-red-600">Yes</div>
        ) : (
          <div className="font-semibold text-green-600">No</div>
        );
      },
    },
    {
      accessorKey: "reason",
      header: "Block Reason",
      cell: ({ row }) => {
        const isBlocked = row.getValue("isBlocked");
        const reason = row.original.reason;
        if (isBlocked === null || isBlocked === undefined) {
          return <span className="text-muted-foreground">-</span>;
        }
        if (isBlocked === true && reason) {
          const truncated =
            reason.length > 20 ? reason.slice(0, 20) + "..." : reason;
          return (
            <Tooltip>
              <TooltipTrigger asChild>
                <span className="cursor-pointer text-red-600">{truncated}</span>
              </TooltipTrigger>
              <TooltipContent>{reason}</TooltipContent>
            </Tooltip>
          );
        }
        return <span className="text-muted-foreground">-</span>;
      },
    },
    {
      accessorKey: "image",
      header: "Avatar",
      cell: ({ row }) => <ImageDialog imageUrl={row.getValue("image")} />,
    },
    {
      accessorKey: "createdAt",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Created" />
      ),
      cell: ({ row }) => {
        const date = new Date(row.getValue("createdAt"));
        // Use consistent date formatting to avoid hydration errors
        const formattedDate = formatDate(date, "dd/MM/yyyy");
        return <div>{formattedDate}</div>;
      },
    },
    {
      id: "actions",
      enableHiding: false,
      cell: ({ row }) => {
        const kabadiwala = row.original;
        return (
          <div className="flex gap-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                <DropdownMenuItem
                  onClick={() =>
                    router.push(`/manage-kabadiwalas/${kabadiwala.id}`)
                  }
                  className="text-blue-600"
                >
                  <ArrowUpRight className="mr-2 h-4 w-4" />
                  View Details
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => openSheet(kabadiwala.id)}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  className="text-red-600"
                  onClick={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                  }}
                >
                  <AlertWrapper
                    trigger={
                      <button
                        className="flex items-center gap-2"
                        onClick={(e) => {
                          e.stopPropagation();
                          e.preventDefault();
                        }}
                      >
                        <Trash className="size-4" /> Delete
                      </button>
                    }
                    onConfirm={() =>
                      deleteKabadiwala.mutate({ kabadiwalaId: kabadiwala.id })
                    }
                    title="Are you sure you want to delete this kabadiwala?"
                    description="This action cannot be undone."
                  />
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                {(kabadiwala.isBlocked === null ||
                  kabadiwala.isBlocked === true) && (
                  <DropdownMenuItem
                    className="text-green-600"
                    onClick={(e) => {
                      e.stopPropagation();
                      e.preventDefault();
                    }}
                    disabled={unblockKabadiwala.isPending}
                  >
                    <AlertWrapper
                      trigger={
                        <button className="flex items-center gap-2">
                          <Unlock className="mr-2 h-4 w-4" />
                          Approve
                        </button>
                      }
                      onConfirm={() =>
                        unblockKabadiwala.mutate({
                          kabadiwalaId: kabadiwala.id,
                        })
                      }
                      title="Are you sure you want to approve this kabadiwala?"
                      description="This will unblock the kabadiwala."
                    />
                  </DropdownMenuItem>
                )}
                {kabadiwala.isBlocked === false ? (
                  <DropdownMenuItem
                    className="text-red-600"
                    onClick={() => void setBlockKabadiwalaId(kabadiwala.id)}
                  >
                    <Ban className="mr-2 h-4 w-4" />
                    Block
                  </DropdownMenuItem>
                ) : null}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        );
      },
    },
  ];

  // Fuzzy filter for global search (name/phone)
  const fuzzyFilter: FilterFn<KabadiwalaRow> = (
    row,
    columnId, // string
    value,
    addMeta,
  ) => {
    const itemRank = rankItem(
      String(row.getValue(columnId) ?? ""),
      value as string,
    );
    addMeta(itemRank);
    return itemRank.passed;
  };

  const table = useReactTable({
    data: kabadiwalas,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      globalFilter,
    },
    onGlobalFilterChange: setGlobalFilter,
    globalFilterFn: fuzzyFilter,
    filterFns: { fuzzy: fuzzyFilter },
    enableGlobalFilter: true,
  });

  return (
    <div className="w-full">
      <div className="flex items-center justify-between py-4">
        <Input
          placeholder="Filter by name | number ..."
          value={globalFilter}
          onChange={(event) => setGlobalFilter(event.target.value)}
          className="max-w-sm"
        />
        <div className="flex gap-2">
          <KabadiwalasFormSheet />
          {/* Verified filter select */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                Columns <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {table
                .getAllColumns()
                .filter((column) => column.getCanHide())
                .map((column) => {
                  return (
                    <DropdownMenuCheckboxItem
                      key={column.id}
                      className="capitalize"
                      checked={column.getIsVisible()}
                      onCheckedChange={(value) =>
                        column.toggleVisibility(!!value)
                      }
                    >
                      {column.id}
                    </DropdownMenuCheckboxItem>
                  );
                })}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id} className="whitespace-nowrap">
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext(),
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody className="relative text-start">
            {table.getRowModel().rows.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell
                      className={cn(
                        cell.column.id === "actions" &&
                          "sticky right-0 backdrop-blur-3xl",
                      )}
                      key={cell.id}
                    >
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-end space-x-2 py-4">
        <div className="flex-1 text-sm text-muted-foreground">
          {table.getFilteredSelectedRowModel().rows.length} of{" "}
          {table.getFilteredRowModel().rows.length} row(s) selected.
        </div>
        <div className="space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            Next
          </Button>
        </div>
      </div>
      <Dialog
        open={!!blockKabadiwalaId}
        onOpenChange={(open) => {
          void setBlockKabadiwalaId(open ? (blockKabadiwalaId ?? null) : null);
          if (!open) setBlockReason("");
        }}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Block Kabadiwala</DialogTitle>
          </DialogHeader>
          <div className="space-y-2">
            <Textarea
              placeholder="Enter reason for blocking"
              value={blockReason}
              onChange={(e) => setBlockReason(e.target.value)}
              className="w-full"
            />
            <Button
              variant="destructive"
              disabled={!blockReason.trim() || blockKabadiwala.isPending}
              onClick={() =>
                blockKabadiwalaId &&
                blockKabadiwala.mutate({
                  kabadiwalaId: blockKabadiwalaId,
                  reason: blockReason,
                })
              }
            >
              Confirm Block
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default KabadiwalasDataTable;
