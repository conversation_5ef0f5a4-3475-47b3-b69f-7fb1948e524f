"use client";

import { PlusIcon } from "lucide-react";

import { But<PERSON> } from "@acme/ui/components/ui/button";

import { GenericSheet } from "~/components/shared/generic-sheet";
import { useSheet } from "~/hooks/use-sheet";
import { kabadiwalaParamName } from "~/lib/constants";
import { KabadiwalaCreateUpdateForm } from "./kabadiwalas-create-update-form";

const KabadiwalasFormSheet = () => {
  const { isOpen, paramValue, openSheet, closeSheet } =
    useSheet(kabadiwalaParamName);

  return (
    <div>
      <Button onClick={() => openSheet()}>
        <PlusIcon className="size-4" />
        Create Kabadiwala
      </Button>
      <GenericSheet
        isOpen={isOpen}
        onClose={closeSheet}
        title={paramValue ? "Edit Kabadiwala" : "Create Kabadiwala"}
      >
        <KabadiwalaCreateUpdateForm />
      </GenericSheet>
    </div>
  );
};

export default KabadiwalasFormSheet;
