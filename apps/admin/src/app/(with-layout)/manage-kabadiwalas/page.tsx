import type { SearchParams } from "nuqs/server";
import { Suspense } from "react";

import { Separator } from "@acme/ui/components/ui/separator";

import DataTableSkeleton from "~/components/skeleton/data-table-skeleton";
import { loadUserSearchParams } from "~/lib/nuqs-utils";
import { getQueryClient, HydrateClient, trpc } from "~/trpc/server";
import KabadiwalasDataTable from "./kabadiwalas-data-table";

export const metadata = {
  title: "Kabadiwalas Management",
  description: "Manage your website's kabadiwalas",
};

interface ManageKabadiwalasPageProps {
  searchParams: Promise<SearchParams>;
}

const ManageKabadiwalasPage = async ({
  searchParams,
}: ManageKabadiwalasPageProps) => {
  const { kabadiwala_id } = await loadUserSearchParams(searchParams);
  const queryClient = getQueryClient();

  // Prefetch list data
  await queryClient.prefetchQuery(
    trpc.kabadiwala.getAllKabadiwalas.queryOptions(),
  );

  // Prefetch individual item data if ID exists
  if (kabadiwala_id) {
    await queryClient.prefetchQuery(
      trpc.kabadiwala.getKabadiwalaById.queryOptions({
        kabadiwalaId: kabadiwala_id,
      }),
    );
  }

  return (
    <HydrateClient>
      <div>
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Kabadiwalas Management
            </h1>
            <p className="text-muted-foreground">
              Create, edit and manage kabadiwalas of your website.
            </p>
          </div>
        </div>
        <Separator className="my-6" />
        <Suspense fallback={<DataTableSkeleton />}>
          <KabadiwalasDataTable />
        </Suspense>
      </div>
    </HydrateClient>
  );
};

export default ManageKabadiwalasPage;
