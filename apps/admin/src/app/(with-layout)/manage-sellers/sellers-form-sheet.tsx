"use client";

import { PlusIcon } from "lucide-react";

import { But<PERSON> } from "@acme/ui/components/ui/button";

import { GenericSheet } from "~/components/shared/generic-sheet";
import { useSheet } from "~/hooks/use-sheet";
import { sellerParamName } from "~/lib/constants";
import { SellerCreateUpdateForm } from "./sellers-create-update-form";

const SellersFormSheet = () => {
  const { isOpen, paramValue, openSheet, closeSheet } =
    useSheet(sellerParamName);

  return (
    <div>
      <Button onClick={() => openSheet()}>
        <PlusIcon className="size-4" />
        Create Seller
      </Button>
      <GenericSheet
        isOpen={isOpen}
        onClose={closeSheet}
        title={paramValue ? "Edit Seller" : "Create Seller"}
      >
        <SellerCreateUpdateForm />
      </GenericSheet>
    </div>
  );
};

export default SellersFormSheet;
