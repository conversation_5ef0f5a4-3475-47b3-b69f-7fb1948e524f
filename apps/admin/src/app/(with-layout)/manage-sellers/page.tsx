import type { SearchParams } from "nuqs/server";
import { Suspense } from "react";

import { Separator } from "@acme/ui/components/ui/separator";

import DataTableSkeleton from "~/components/skeleton/data-table-skeleton";
import { loadUserSearchParams } from "~/lib/nuqs-utils";
import { getQueryClient, HydrateClient, trpc } from "~/trpc/server";
import SellersDataTable from "./sellers-data-table";

export const metadata = {
  title: "Sellers Management",
  description: "Manage your website's sellers",
};

interface ManageSellersPageProps {
  searchParams: Promise<SearchParams>;
}

const ManageSellersPage = async ({ searchParams }: ManageSellersPageProps) => {
  const { seller_id } = await loadUserSearchParams(searchParams);
  const queryClient = getQueryClient();

  // Prefetch list data
  await queryClient.prefetchQuery(trpc.seller.getAllSellers.queryOptions());

  // Prefetch individual item data if ID exists
  if (seller_id) {
    await queryClient.prefetchQuery(
      trpc.seller.getSellerById.queryOptions({ sellerId: seller_id }),
    );
  }

  return (
    <HydrateClient>
      <div>
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Sellers Management
            </h1>
            <p className="text-muted-foreground">
              Create, edit and manage sellers of your website.
            </p>
          </div>
        </div>
        <Separator className="my-6" />
        <Suspense fallback={<DataTableSkeleton />}>
          <SellersDataTable />
        </Suspense>
      </div>
    </HydrateClient>
  );
};

export default ManageSellersPage;
