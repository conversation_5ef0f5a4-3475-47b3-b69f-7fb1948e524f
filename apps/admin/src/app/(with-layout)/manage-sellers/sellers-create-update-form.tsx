"use client";

import type { z } from "zod";
import { useEffect } from "react";
import Image from "next/image";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  skipToken,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";
import { generateUploadButton } from "@uploadthing/react";
import { Loader2, Trash, Upload } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

import { Button } from "@acme/ui/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@acme/ui/components/ui/form";
import GoogleAutoCompleteInput from "@acme/ui/components/ui/google-autocomplete-input";
import { Input } from "@acme/ui/components/ui/input";
import { SellerCRUDSchema } from "@acme/validators";

import type { OurFileRouter } from "~/app/api/uploadthing/core";
import { useSheet } from "~/hooks/use-sheet";
import { sellerParamName } from "~/lib/constants";
import { useTRPC } from "~/trpc/react";

type SellerFormValues = z.infer<typeof SellerCRUDSchema>;

export const UploadButton = generateUploadButton<OurFileRouter>();

export function SellerCreateUpdateForm() {
  const trpc = useTRPC();
  const trpcUtils = useQueryClient();
  const { paramValue: sellerId, closeSheet } = useSheet(sellerParamName);
  const isEditMode = !!sellerId;

  const { data: sellerData, isLoading } = useQuery(
    trpc.seller.getSellerById.queryOptions(
      sellerId ? { sellerId: sellerId } : skipToken,
    ),
  );

  const form = useForm<SellerFormValues>({
    resolver: zodResolver(SellerCRUDSchema),
    defaultValues: sellerData
      ? {
          fullName: sellerData.fullName,
          image: sellerData.image ?? undefined,
          phoneNumber: sellerData.phoneNumber ?? undefined,
        }
      : {
          fullName: "",
          image: "",
          phoneNumber: "",
        },
  });

  const createSeller = useMutation(
    trpc.seller.createSeller.mutationOptions({
      onSuccess: (opts) => {
        toast.success(opts.message);
        void trpcUtils.invalidateQueries({
          queryKey: trpc.seller.getAllSellers.queryKey(),
        });
        closeSheet();
      },
      onError: (error) => {
        toast.error(error.message);
      },
    }),
  );

  const updateSeller = useMutation(
    trpc.seller.updateSeller.mutationOptions({
      onSuccess: (opts) => {
        toast.success(opts.message);
        void trpcUtils.invalidateQueries({
          queryKey: trpc.seller.getAllSellers.queryKey(),
        });
        closeSheet();
      },
      onError: (error) => {
        toast.error(error.message);
      },
    }),
  );

  const deleteImage = useMutation(
    trpc.util.deleteUploadthingFileUsingFileKey.mutationOptions({
      onSuccess: (opts) => {
        toast.success(opts.message);
        form.setValue("image", "");
      },
      onError: (error) => {
        toast.error(error.message);
      },
    }),
  );

  const onSubmit = (data: SellerFormValues) => {
    if (isEditMode) {
      updateSeller.mutate({ ...data, sellerId: sellerId });
    } else {
      createSeller.mutate(data);
    }
  };

  useEffect(() => {
    if (isEditMode && sellerData) {
      form.reset({
        fullName: sellerData.fullName,
        image: sellerData.image ?? undefined,
        phoneNumber: sellerData.phoneNumber ?? "",
      });
    }
  }, [isEditMode, sellerData, form]);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit, (err) => console.log(err))}
        className="space-y-8"
      >
        <FormField
          control={form.control}
          name="fullName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Full Name</FormLabel>
              <FormControl>
                <Input placeholder="Enter seller's full name" {...field} />
              </FormControl>
              <FormDescription>The full name of the seller.</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="phoneNumber"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Phone Number</FormLabel>
              <FormControl>
                <Input placeholder="Enter seller's phone number" {...field} />
              </FormControl>
              <FormDescription>The phone number of the seller.</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="address"
          render={() => (
            <FormItem>
              <FormLabel>Address</FormLabel>
              <FormControl>
                <GoogleAutoCompleteInput
                  showSearchIcon
                  showClearButton
                  onLocationSelect={(location) => {
                    if (location.address?.display) {
                      form.setValue(
                        "address.display",
                        location.address.display,
                      );
                    }
                    if (location.address?.street) {
                      form.setValue("address.street", location.address.street);
                    }
                    if (location.address?.city) {
                      form.setValue("address.city", location.address.city);
                    }
                    if (location.address?.state) {
                      form.setValue("address.state", location.address.state);
                    }
                    if (location.address?.country) {
                      form.setValue(
                        "address.country",
                        location.address.country,
                      );
                    }
                    if (location.address?.postalCode) {
                      form.setValue(
                        "address.postalCode",
                        location.address.postalCode,
                      );
                    }
                    form.setValue("address.coordinates", {
                      latitude: location.latitude,
                      longitude: location.longitude,
                    });

                    form.setValue("address.name", "My Address");
                    form.setValue("address.addressType", "OTHER");
                    form.setValue(
                      "address.landmark",
                      location.address?.display ?? "",
                    );
                    form.setValue(
                      "address.localAddress",
                      location.address?.display ?? "",
                    );
                  }}
                />
              </FormControl>
              <FormDescription>The address of the seller.</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="image"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Profile Image</FormLabel>
              <FormControl>
                {field.value ? (
                  <div className="relative">
                    <Image
                      src={field.value || ""}
                      alt="seller avatar"
                      height={200}
                      width={200}
                      className="h-32 w-32 rounded-full object-cover"
                    />
                    <Button
                      type="button"
                      variant="destructive"
                      size="sm"
                      className="mt-2"
                      disabled={deleteImage.isPending}
                      onClick={() =>
                        deleteImage.mutate({ fileKey: field.value ?? "" })
                      }
                    >
                      {deleteImage.isPending ? (
                        <>
                          <Loader2 className="animate-spin" />
                          Removing Image
                        </>
                      ) : (
                        <>
                          <Trash className="mr-2 h-4 w-4" />
                          Remove Image
                        </>
                      )}
                    </Button>
                  </div>
                ) : (
                  <UploadButton
                    endpoint="imageUploader"
                    appearance={{
                      button:
                        "bg-primary text-primary-foreground hover:bg-primary/90 rounded-md px-4 py-2 text-sm font-medium shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring",
                      allowedContent: "text-muted-foreground text-xs",
                      container: "flex flex-col gap-2 w-full",
                    }}
                    content={{
                      button: ({ isUploading }) => (
                        <span className="flex items-center gap-2">
                          {isUploading ? (
                            <Loader2 className="animate-spin" />
                          ) : (
                            <Upload className="size-4" />
                          )}
                          {isUploading
                            ? "Uploading..."
                            : "Upload Profile Image"}
                        </span>
                      ),
                      allowedContent: "JPEG, PNG or WebP (max 4MB)",
                    }}
                    onClientUploadComplete={(res) => {
                      const imageUrl = res[0]?.ufsUrl;
                      if (!imageUrl) {
                        toast.error("Image upload failed");
                        return;
                      }

                      field.onChange(imageUrl);
                      toast.success("Image uploaded successfully");
                    }}
                    onUploadError={(error: Error) => {
                      toast.error(`Upload error: ${error.message}`);
                    }}
                  />
                )}
              </FormControl>
              <FormDescription>
                Upload a profile image for the seller.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex gap-2">
          <Button
            type="submit"
            disabled={
              createSeller.isPending ||
              updateSeller.isPending ||
              deleteImage.isPending
            }
          >
            {createSeller.isPending || updateSeller.isPending ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {isEditMode ? "Updating..." : "Creating..."}
              </>
            ) : (
              <>{isEditMode ? "Update Seller" : "Create Seller"}</>
            )}
          </Button>
          <Button type="button" variant="outline" onClick={closeSheet}>
            Cancel
          </Button>
        </div>
      </form>
    </Form>
  );
}

export default SellerCreateUpdateForm;
