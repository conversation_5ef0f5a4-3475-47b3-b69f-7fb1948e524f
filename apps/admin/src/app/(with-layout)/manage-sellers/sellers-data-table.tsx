"use client";

import type {
  ColumnDef,
  ColumnFiltersState,
  FilterFn,
  SortingState,
  VisibilityState,
} from "@tanstack/react-table";
import { useEffect, useState } from "react";
import { rankItem } from "@tanstack/match-sorter-utils";
import { useMutation, useQuery } from "@tanstack/react-query";
import {
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import {
  Ban,
  ChevronDown,
  Edit,
  MoreHorizontal,
  Trash,
  Unlock,
} from "lucide-react";
import { useQueryState } from "nuqs";
import { toast } from "sonner";

import { Button } from "@acme/ui/components/ui/button";
import { Checkbox } from "@acme/ui/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@acme/ui/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@acme/ui/components/ui/dropdown-menu";
import { Input } from "@acme/ui/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@acme/ui/components/ui/table";
import { Textarea } from "@acme/ui/components/ui/textarea";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@acme/ui/components/ui/tooltip";
import { cn } from "@acme/ui/lib/utils";

import AlertWrapper from "~/components/shared/alert-wrapper";
import ImageDialog from "~/components/shared/image-dialog";
import SortableColumnHeader from "~/components/shared/sortable-column-header";
import { useSheet } from "~/hooks/use-sheet";
import { useTRPC } from "~/trpc/react";
import SellersFormSheet from "./sellers-form-sheet";

const SellersDataTable = () => {
  const trpc = useTRPC();
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState({});
  const { openSheet } = useSheet("seller_id");
  const [blockSellerId, setBlockSellerId] = useQueryState("blockSellerId", {
    parse: (v) => (typeof v === "string" ? v : undefined),
  });
  const [blockReason, setBlockReason] = useState("");
  useEffect(() => {
    setBlockReason("");
  }, [blockSellerId]);
  const [globalFilter, setGlobalFilter] = useState("");

  const { data: sellers = [], refetch } = useQuery(
    trpc.seller.getAllSellers.queryOptions(),
  );

  const deleteSeller = useMutation(
    trpc.seller.deleteSeller.mutationOptions({
      onSuccess: (opts) => {
        toast.success(opts.message);
        void refetch();
      },
      onError: (error) => {
        toast.error(error.message);
      },
    }),
  );
  const blockSeller = useMutation(
    trpc.seller.blockSeller.mutationOptions({
      onSuccess: (opts) => {
        toast.success(opts.message);
        void setBlockSellerId(null);
        setBlockReason("");
        void refetch();
      },
      onError: (error) => {
        toast.error(error.message);
      },
    }),
  );
  const unblockSeller = useMutation(
    trpc.seller.unblockSeller.mutationOptions({
      onSuccess: (opts) => {
        toast.success(opts.message);
        void refetch();
      },
      onError: (error) => {
        toast.error(error.message);
      },
    }),
  );

  // Fuzzy global filter for fullName, phoneNumber, email
  const fuzzyGlobalFilter: FilterFn<(typeof sellers)[number]> = (
    row,
    _columnId,
    value,

    addMeta,
  ) => {
    const fields = ["fullName", "phoneNumber", "email"] as const;
    return fields.some((field) => {
      const itemRank = rankItem(
        String(row.getValue(field) ?? ""),
        value as string,
      );
      if (itemRank.passed) addMeta(itemRank);
      return itemRank.passed;
    });
  };

  const columns: ColumnDef<
    (typeof sellers)[number] & { isBlocked?: boolean; reason?: string | null }
  >[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "fullName",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Full Name" />
      ),
      cell: ({ row }) => (
        <div className="font-medium">{row.getValue("fullName")}</div>
      ),
    },
    {
      accessorKey: "phoneNumber",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Phone" />
      ),
      cell: ({ row }) => <div>{row.getValue("phoneNumber")}</div>,
    },
    {
      accessorKey: "phoneNumberVerified",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Phone Verified" />
      ),
      cell: ({ row }) => {
        const isVerified = row.getValue("phoneNumberVerified");
        return (
          <div
            className={`font-medium ${
              isVerified ? "text-green-500" : "text-red-500"
            }`}
          >
            {isVerified ? "Yes" : "No"}
          </div>
        );
      },
    },
    {
      accessorKey: "isBlocked",
      header: "Blocked",
      cell: ({ row }) => {
        const isBlocked = row.getValue("isBlocked");
        return isBlocked ? (
          <div className="font-semibold text-red-600">Yes</div>
        ) : (
          <div className="font-semibold text-green-600">No</div>
        );
      },
    },
    {
      accessorKey: "reason",
      header: "Block Reason",
      cell: ({ row }) => {
        const isBlocked = row.getValue("isBlocked");
        const reason = row.original.reason;
        if (isBlocked && reason) {
          const truncated =
            reason.length > 20 ? reason.slice(0, 20) + "..." : reason;
          return (
            <Tooltip>
              <TooltipTrigger asChild>
                <span className="cursor-pointer text-red-600">{truncated}</span>
              </TooltipTrigger>
              <TooltipContent>{reason}</TooltipContent>
            </Tooltip>
          );
        }
        return <span className="text-muted-foreground">-</span>;
      },
    },
    {
      accessorKey: "image",
      header: "Avatar",
      cell: ({ row }) => <ImageDialog imageUrl={row.getValue("image")} />,
    },
    {
      accessorKey: "address",
      header: "Address",
      cell: ({ row }) => <div>{row.getValue("address")}</div>,
    },
    {
      accessorKey: "createdAt",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Created" />
      ),
      cell: ({ row }) => {
        const date = new Date(row.getValue("createdAt"));
        return <div>{date.toLocaleDateString()}</div>;
      },
    },
    {
      id: "actions",
      enableHiding: false,
      cell: ({ row }) => {
        const seller = row.original;
        return (
          <>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">Open menu</span>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                <DropdownMenuItem onClick={() => openSheet(seller.id)}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  className="text-red-600"
                  onClick={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                  }}
                >
                  <AlertWrapper
                    trigger={
                      <button
                        className="flex items-center gap-2"
                        onClick={(e) => {
                          e.stopPropagation();
                          e.preventDefault();
                        }}
                      >
                        <Trash className="size-4" /> Delete
                      </button>
                    }
                    onConfirm={() =>
                      deleteSeller.mutate({ sellerId: seller.id })
                    }
                    title="Are you sure you want to delete this seller?"
                    description="This action cannot be undone."
                  />
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                {seller.isBlocked ? (
                  <DropdownMenuItem
                    className="text-green-600"
                    onClick={(e) => {
                      e.stopPropagation();
                      e.preventDefault();
                    }}
                    disabled={unblockSeller.isPending}
                  >
                    <AlertWrapper
                      trigger={
                        <button
                          className="flex items-center gap-2"
                          onClick={(e) => {
                            e.stopPropagation();
                            e.preventDefault();
                          }}
                        >
                          <Unlock className="mr-2 h-4 w-4" /> Unblock
                        </button>
                      }
                      onConfirm={() =>
                        unblockSeller.mutate({ sellerId: seller.id })
                      }
                      title="Are you sure you want to unblock this seller?"
                      description="This will unblock the seller and allow them to use the platform."
                    />
                  </DropdownMenuItem>
                ) : (
                  <DropdownMenuItem
                    className="text-red-600"
                    onClick={() => void setBlockSellerId(seller.id)}
                  >
                    <Ban className="mr-2 h-4 w-4" />
                    Block
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </>
        );
      },
    },
  ];

  const table = useReactTable({
    data: sellers,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      globalFilter,
    },
    onGlobalFilterChange: setGlobalFilter,
    globalFilterFn: fuzzyGlobalFilter,
    filterFns: { fuzzy: fuzzyGlobalFilter },
    enableGlobalFilter: true,
  });

  return (
    <div className="w-full">
      <div className="flex items-center justify-between py-4">
        <Input
          placeholder="Search by name, phone, or email..."
          value={globalFilter}
          onChange={(event) => setGlobalFilter(event.target.value)}
          className="max-w-sm"
        />
        <div className="flex gap-2">
          <SellersFormSheet />
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                Columns <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {table
                .getAllColumns()
                .filter((column) => column.getCanHide())
                .map((column) => {
                  return (
                    <DropdownMenuCheckboxItem
                      key={column.id}
                      className="capitalize"
                      checked={column.getIsVisible()}
                      onCheckedChange={(value) =>
                        column.toggleVisibility(!!value)
                      }
                    >
                      {column.id}
                    </DropdownMenuCheckboxItem>
                  );
                })}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext(),
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody className="relative text-start">
            {table.getRowModel().rows.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell
                      className={cn(
                        cell.column.id === "actions" &&
                          "sticky right-0 backdrop-blur-3xl",
                      )}
                      key={cell.id}
                    >
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-end space-x-2 py-4">
        <div className="flex-1 text-sm text-muted-foreground">
          {table.getFilteredSelectedRowModel().rows.length} of{" "}
          {table.getFilteredRowModel().rows.length} row(s) selected.
        </div>
        <div className="space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            Next
          </Button>
        </div>
      </div>
      <Dialog
        open={!!blockSellerId}
        onOpenChange={(open) => {
          void setBlockSellerId(open ? (blockSellerId ?? null) : null);
          if (!open) setBlockReason("");
        }}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Block Seller</DialogTitle>
          </DialogHeader>
          <div className="space-y-2">
            <Textarea
              placeholder="Enter reason for blocking"
              value={blockReason}
              onChange={(e) => setBlockReason(e.target.value)}
              className="w-full"
            />
            <Button
              variant="destructive"
              disabled={!blockReason.trim() || blockSeller.isPending}
              onClick={() =>
                blockSellerId &&
                blockSeller.mutate({
                  sellerId: blockSellerId,
                  reason: blockReason,
                })
              }
            >
              Confirm Block
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default SellersDataTable;
