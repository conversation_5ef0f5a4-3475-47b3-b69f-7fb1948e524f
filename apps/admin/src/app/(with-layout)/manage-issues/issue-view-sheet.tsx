"use client";

import { useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { ArrowUpRight, Loader2 } from "lucide-react";
import { toast } from "sonner";

import { Badge } from "@acme/ui/components/ui/badge";
import { Button } from "@acme/ui/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@acme/ui/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogHeader as DialogDialogHeader,
} from "@acme/ui/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@acme/ui/components/ui/select";
import { Separator } from "@acme/ui/components/ui/separator";

import { GenericSheet } from "~/components/shared/generic-sheet";
import { useSheet } from "~/hooks/use-sheet";
import { issueParamName } from "~/lib/constants";
import { useTRPC } from "~/trpc/react";

const statusOptions = [
  { value: "ACKNOWLEDGED", label: "Acknowledged" },
  { value: "CLOSED", label: "Resolved (initiate payout)" },
  { value: "REJECTED", label: "Rejected" },
];

type IssueStatus = "ACKNOWLEDGED" | "CLOSED" | "REJECTED";

// Status color mapping for badges
const statusBadgeMap: Record<string, { label: string; color: string }> = {
  ACKNOWLEDGED: { label: "Acknowledged", color: "bg-blue-500 text-white" },
  CLOSED: {
    label: "Resolved (payout initiated)",
    color: "bg-green-600 text-white",
  },
  REJECTED: { label: "Rejected", color: "bg-gray-400 text-white" },
};

const IssueViewSheet = () => {
  const { isOpen, paramValue: issueId, closeSheet } = useSheet(issueParamName);
  const trpc = useTRPC();
  const trpcUtils = useQueryClient();
  const { data } = useQuery(
    trpc.issue.getIssueDetails.queryOptions(
      { id: issueId ?? "" },
      {
        enabled: !!issueId,
      },
    ),
  );
  const [dialogOpen, setDialogOpen] = useState(false);
  const [dialogImageUrl, setDialogImageUrl] = useState<string | null>(null);
  const validStatuses: IssueStatus[] = ["ACKNOWLEDGED", "CLOSED", "REJECTED"];
  const initialStatus = validStatuses.includes(data?.status as IssueStatus)
    ? (data?.status as IssueStatus)
    : "ACKNOWLEDGED";
  const [selectedStatus, setSelectedStatus] =
    useState<IssueStatus>(initialStatus);

  const updateStatusMutation = useMutation(
    trpc.issue.updateIssueStatus.mutationOptions({
      onSuccess: () => {
        toast.success("Status updated");
        void trpcUtils.invalidateQueries({
          queryKey: trpc.issue.getIssueDetails.queryKey({ id: issueId ?? "" }),
        });
      },
      onError: (err) => {
        toast.error(err.message || "Failed to update status");
      },
    }),
  );

  if (!data) return null;

  const { customer, kabadiwala, order, ...issue } = data;

  const isStatusLocked =
    issue.status === "CLOSED" || issue.status === "REJECTED";

  return (
    <GenericSheet
      isOpen={isOpen}
      onClose={closeSheet}
      title="Issue Details"
      className="min-w-[80dvw] max-w-[80dvw] md:min-w-[80dvw] md:max-w-[80dvw] lg:min-w-[80dvw] lg:max-w-[80dvw] xl:min-w-[80dvw] xl:max-w-[80dvw]"
    >
      <div className="mt-4 flex flex-col gap-8">
        {/* Issue Summary Section */}
        <div className="flex flex-col gap-4">
          <div className="flex items-center gap-4">
            <Badge
              className={`px-4 py-2 text-lg ${statusBadgeMap[issue.status]?.color ?? "bg-gray-300"}`}
            >
              {statusBadgeMap[issue.status]?.label ?? issue.status}
            </Badge>
            <span className="text-sm text-muted-foreground">
              ID: <span className="font-mono">{issue.id}</span>
            </span>
            <span className="text-sm text-muted-foreground">
              Created: {new Date(issue.createdAt).toLocaleString()}
            </span>
          </div>
          <div className="text-xl font-semibold">{issue.description}</div>
        </div>
        <Separator />
        {/* People Section */}
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
          {customer && (
            <Card>
              <CardHeader className="flex flex-row items-center gap-2 space-y-0 pb-2">
                <CardTitle className="text-base">Seller</CardTitle>
                <Badge variant="secondary" className="text-xs">
                  Customer Info
                </Badge>
              </CardHeader>
              <CardContent className="flex items-center gap-4">
                {customer.image && (
                  <Image
                    width={48}
                    height={48}
                    src={customer.image}
                    alt="Customer"
                    className="h-12 w-12 rounded-full border"
                  />
                )}
                <div className="space-y-1">
                  <div className="font-medium">{customer.fullName}</div>
                  <div className="text-sm text-muted-foreground">
                    {customer.email}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {customer.phoneNumber}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
          {kabadiwala && (
            <Card>
              <CardHeader className="flex flex-row items-center gap-2 space-y-0 pb-2">
                <CardTitle className="text-base">Kabadiwala</CardTitle>
                <Badge variant="secondary" className="text-xs">
                  Partner
                </Badge>
              </CardHeader>
              <CardContent className="flex items-center gap-4">
                {kabadiwala.image && (
                  <Image
                    width={48}
                    height={48}
                    src={kabadiwala.image}
                    alt="Kabadiwala"
                    className="h-12 w-12 rounded-full border"
                  />
                )}
                <div className="space-y-1">
                  <div className="font-medium">{kabadiwala.name}</div>
                  <div className="text-sm text-muted-foreground">
                    {kabadiwala.email}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {kabadiwala.phoneNumber}
                  </div>
                  <div className="mt-1 flex gap-2">
                    <Badge
                      variant={
                        kabadiwala.isBlocked ? "destructive" : "secondary"
                      }
                      className="text-xs"
                    >
                      {kabadiwala.isBlocked ? "Blocked" : "Active"}
                    </Badge>
                    <Badge variant="secondary" className="text-xs">
                      Violations: {kabadiwala.violationCount}
                    </Badge>
                    <Badge variant="secondary" className="text-xs">
                      Issues: {kabadiwala.issueCount}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
          {order && (
            <Card>
              <CardHeader className="flex flex-row items-center gap-2 space-y-0 pb-2">
                <CardTitle className="text-base">Order</CardTitle>
                <Badge variant="outline" className="text-xs">
                  Order Info
                </Badge>
              </CardHeader>
              <CardContent className="space-y-1">
                <div className="flex flex-wrap items-center gap-2">
                  <Link
                    href={`/manage-orders/${order.id}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-1 font-mono text-xs text-primary underline transition-colors hover:text-primary/80"
                  >
                    ID: {order.id}
                    <ArrowUpRight className="h-3.5 w-3.5" />
                  </Link>
                  <Badge variant="secondary" className="text-xs">
                    {order.status}
                  </Badge>
                  <Badge variant="secondary" className="text-xs">
                    Payment: {order.paymentStatus}
                  </Badge>
                </div>
                <div>
                  Total Amount:{" "}
                  <span className="font-semibold">{order.totalAmount}</span>
                </div>
                <div>
                  Completed At:{" "}
                  {order.completedAt
                    ? new Date(order.completedAt).toLocaleString()
                    : "-"}
                </div>
                <div>
                  Created At: {new Date(order.createdAt).toLocaleString()}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
        <Separator />
        {/* Images Section */}
        {Array.isArray(issue.imageUrls) && issue.imageUrls.length > 0 && (
          <div>
            <div className="mb-2 font-medium text-muted-foreground">Images</div>
            <div className="flex flex-wrap gap-2">
              {issue.imageUrls.map((url: string) => (
                <button
                  type="button"
                  key={url}
                  className="focus:outline-none"
                  onClick={() => {
                    setDialogImageUrl(url);
                    setDialogOpen(true);
                  }}
                >
                  <Image
                    width={80}
                    height={80}
                    src={url}
                    alt="Issue"
                    className="h-20 w-20 rounded border object-cover transition-transform hover:scale-105"
                  />
                </button>
              ))}
            </div>
          </div>
        )}
        <Separator />
        {/* Status Update Section */}
        <div className="flex items-center gap-4 py-2">
          <div className="font-medium text-muted-foreground">
            Update Status:
          </div>
          <Select
            value={selectedStatus}
            onValueChange={(value: IssueStatus) => setSelectedStatus(value)}
            disabled={updateStatusMutation.isPending || isStatusLocked}
          >
            <SelectTrigger className="w-[280px]">
              <SelectValue placeholder="Select status" />
            </SelectTrigger>
            <SelectContent>
              {statusOptions.map((opt) => (
                <SelectItem key={opt.value} value={opt.value}>
                  {opt.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button
            onClick={() =>
              updateStatusMutation.mutate({
                id: issue.id,
                status: selectedStatus,
              })
            }
            disabled={
              updateStatusMutation.isPending ||
              selectedStatus === issue.status ||
              isStatusLocked
            }
            className="ml-2"
          >
            {updateStatusMutation.isPending ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              "Update Status"
            )}
          </Button>
          {isStatusLocked && (
            <span className="ml-2 text-xs text-muted-foreground">
              Status cannot be changed after it is closed or rejected.
            </span>
          )}
        </div>
        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          <DialogContent className="flex h-[70dvh] w-[80dvw] items-center justify-center">
            <DialogDialogHeader />
            {dialogImageUrl && (
              <div className="relative flex h-full w-full items-center justify-center">
                <Image
                  src={dialogImageUrl}
                  alt="Preview"
                  fill
                  className="rounded-lg object-contain"
                />
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </GenericSheet>
  );
};

export default IssueViewSheet;
