/* eslint-disable react-hooks/rules-of-hooks */
import type { ColumnDef } from "@tanstack/react-table";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Edit, MoreHorizontal, Trash } from "lucide-react";
import { toast } from "sonner";

import { Badge } from "@acme/ui/components/ui/badge";
import { Button } from "@acme/ui/components/ui/button";
import { Checkbox } from "@acme/ui/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@acme/ui/components/ui/dropdown-menu";

import type { PricingPeriod } from "~/lib/types";
import AlertWrapper from "~/components/shared/alert-wrapper";
import SortableColumnHeader from "~/components/shared/sortable-column-header";
import { useSheet } from "~/hooks/use-sheet";
import { pricingByRegionParamName } from "~/lib/constants";
import { useTRPC } from "~/trpc/react";

export const pricingColumns: ColumnDef<PricingPeriod>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "id",
    header: ({ column }) => <SortableColumnHeader column={column} title="ID" />,
    cell: ({ row }) => (
      <div className="max-w-[200px] truncate font-medium">
        {row.original.id}
      </div>
    ),
  },
  {
    accessorKey: "region",
    header: ({ column }) => (
      <SortableColumnHeader column={column} title="Region" />
    ),
    cell: ({ row }) => (
      <div className="max-w-[200px] truncate font-medium">
        {row.original.region.name}
      </div>
    ),
  },
  {
    accessorKey: "isActive",
    header: ({ column }) => (
      <SortableColumnHeader column={column} title="Active" />
    ),
    cell: ({ row }) => {
      const effectiveFromDate = row.original.effectiveFromDate;
      const expiryDate = row.original.expiresAt;
      const now = new Date();

      let status = "";
      let badgeVariant: "default" | "secondary" | "destructive" = "secondary";
      let info = "";

      if (now < effectiveFromDate) {
        // Not yet active
        const daysLeft = Math.ceil(
          (effectiveFromDate.getTime() - now.getTime()) / (1000 * 3600 * 24),
        );
        status = "Inactive";
        info = `(${daysLeft} days left to be active)`;
        badgeVariant = "secondary";
      } else if (now >= effectiveFromDate && now <= expiryDate) {
        // Active
        const daysLeft = Math.ceil(
          (expiryDate.getTime() - now.getTime()) / (1000 * 3600 * 24),
        );
        status = "Active";
        info = `(${daysLeft} days left)`;
        badgeVariant = "default";
      } else if (now > expiryDate) {
        // Expired
        const daysExpired = Math.ceil(
          (now.getTime() - expiryDate.getTime()) / (1000 * 3600 * 24),
        );
        status = "Expired";
        info = `(${daysExpired} days expired)`;
        badgeVariant = "destructive";
      }

      return (
        <Badge variant={badgeVariant}>
          {status} {info}
        </Badge>
      );
    },
  },
  {
    accessorKey: "createdAt",
    header: ({ column }) => (
      <SortableColumnHeader column={column} title="Created" />
    ),
    cell: ({ row }) => {
      const date = new Date(row.getValue("createdAt"));
      return <div>{date.toLocaleDateString()}</div>;
    },
  },
  {
    id: "actions",
    enableHiding: false,
    cell: ({ row }) => {
      const trpc = useTRPC();
      const queryClient = useQueryClient();
      const { openSheet } = useSheet(pricingByRegionParamName);
      const pricingPeriodId = row.original.id;

      const { mutate: deletePricingPeriod } = useMutation(
        trpc.pricing.deletePricingPeriod.mutationOptions({
          onSuccess: async () => {
            await queryClient.invalidateQueries(
              trpc.pricing.getAllPricingPeriods.queryOptions({}),
            );
            toast.success("Pricing period deleted successfully");
          },
          onError: (error) => {
            toast.error(`Failed to delete pricing period: ${error.message}`);
          },
        }),
      );

      const handleDelete = () => {
        deletePricingPeriod({ id: pricingPeriodId });
      };

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem onClick={() => openSheet(pricingPeriodId)}>
              <Edit className="mr-2 h-4 w-4" />
              Edit
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem className="text-red-600">
              <AlertWrapper
                trigger={
                  <button
                    className="flex w-full items-center gap-2 text-left"
                    onClick={(e) => {
                      e.stopPropagation();
                    }}
                  >
                    <Trash className="size-4" /> Delete
                  </button>
                }
                title="Are you sure you want to delete this pricing period?"
                description="This action cannot be undone."
                onConfirm={handleDelete}
              />
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];
