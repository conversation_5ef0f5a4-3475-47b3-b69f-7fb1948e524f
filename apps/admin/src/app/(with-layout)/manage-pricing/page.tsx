import type { SearchParams } from "nuqs/server";
import { Suspense } from "react";

import { Separator } from "@acme/ui/components/ui/separator";

import DataTableSkeleton from "~/components/skeleton/data-table-skeleton";
import { loadRegionSearchParams } from "~/lib/nuqs-utils";
import { getQueryClient, HydrateClient, trpc } from "~/trpc/server";
import PricingDataTable from "./pricing-data-table";

export const metadata = {
  title: "Pricing Management",
  description: "Manage your website's pricing plans",
};

interface ManagePricingProps {
  searchParams: Promise<SearchParams>;
}

const ManagePricing = async ({ searchParams }: ManagePricingProps) => {
  const { region_category_pricing_id, region_id } =
    await loadRegionSearchParams(searchParams);
  const queryClient = getQueryClient();

  // Prefetch list data
  await queryClient.prefetchQuery(
    trpc.pricing.getAllPricingPeriods.queryOptions({ regionId: undefined }),
  );

  // Prefetch individual item data if ID exists
  if (region_category_pricing_id) {
    await queryClient.prefetchQuery(
      trpc.pricing.getCategoryRateForRegion.queryOptions({
        regionId: region_id,
        categoryId: region_category_pricing_id,
      }),
    );
  }

  return (
    <HydrateClient>
      <div>
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Region Pricing Management
            </h1>
            <p className="text-muted-foreground">
              Create, edit and manage region pricing on your website.
            </p>
          </div>
        </div>
        <Separator className="my-6" />
        <Suspense fallback={<DataTableSkeleton />}>
          <PricingDataTable />
        </Suspense>
      </div>
    </HydrateClient>
  );
};

export default ManagePricing;
