"use client";

import { PlusIcon } from "lucide-react";

import { But<PERSON> } from "@acme/ui/components/ui/button";

import { GenericSheet } from "~/components/shared/generic-sheet";
import { useSheet } from "~/hooks/use-sheet";
import { pricingByRegionParamName } from "~/lib/constants";
import PricingCreateUpdateForm from "./pricing-create-update-form";

const PricingFormSheet = () => {
  const { isOpen, paramValue, openSheet, closeSheet } = useSheet(
    pricingByRegionParamName,
  );

  return (
    <div>
      <Button onClick={() => openSheet()}>
        <PlusIcon className="size-4" />
        Create Regional Price
      </Button>
      <GenericSheet
        isOpen={isOpen}
        onClose={closeSheet}
        title={
          paramValue ? "Edit Pricing For Region" : "Create Pricing For Region"
        }
        className="md:min-w-[600px] lg:min-w-[800px] xl:min-w-[1000px] 2xl:min-w-[1200px]"
      >
        <PricingCreateUpdateForm />
      </GenericSheet>
    </div>
  );
};

export default PricingFormSheet;
