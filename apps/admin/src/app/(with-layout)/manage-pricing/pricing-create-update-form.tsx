/* eslint-disable @typescript-eslint/no-unnecessary-condition */
"use client";

import type { z } from "zod";
import { useEffect, useState } from "react";
import { download, generateCsv, mkConfig } from "export-to-csv";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  skipToken,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";
import { CSVImporter } from "csv-import-react";
import { Download, Loader2, Upload } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

import type { categoryRateTypeEnum } from "@acme/db/schema";
import { Button } from "@acme/ui/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@acme/ui/components/ui/form";
import { Input } from "@acme/ui/components/ui/input";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@acme/ui/components/ui/select";

import { useSheet } from "~/hooks/use-sheet";
import { pricingByRegionParamName } from "~/lib/constants";
import { CreateRegionPricingPeriodWithCategoryPricingSchema } from "~/lib/zod-schema";
import { useTRPC } from "~/trpc/react";

type RegionPricingSchema = z.infer<
  typeof CreateRegionPricingPeriodWithCategoryPricingSchema
>;

const PricingCreateUpdateForm = () => {
  const { closeSheet, paramValue } = useSheet(pricingByRegionParamName);

  const [csvUploadOpen, setCsvUploadOpen] = useState(false);

  const isUpdateMode = !!paramValue;

  const form = useForm<RegionPricingSchema>({
    resolver: zodResolver(CreateRegionPricingPeriodWithCategoryPricingSchema),
    defaultValues: {
      regionCategoryPricing: [],
    },
  });

  const trpc = useTRPC();
  const queryClient = useQueryClient();
  const { data: pricingHistory, isLoading: isPricingHistoryLoading } = useQuery(
    trpc.pricing.getPricingHistoryById.queryOptions(
      paramValue ? { id: paramValue } : skipToken,
    ),
  );

  const {
    mutate: createRegionPricingPeriod,
    isPending: isPendingCreateRegionPricing,
  } = useMutation(
    trpc.pricing.createRegionPricingPeriodWithCategoryPricingRecords.mutationOptions(),
  );

  const {
    mutate: updateRegionPricingPeriod,
    isPending: isPendingUpdateRegionPricing,
  } = useMutation(
    trpc.pricing.updatePricingPeriodWithCategoryPricing.mutationOptions(),
  );

  const { data: regions, isLoading: isRegionsLoading } = useQuery(
    trpc.region.all.queryOptions(),
  );
  const { data: categories, isLoading: isCategoriesLoading } = useQuery(
    trpc.category.getAllWithoutParent.queryOptions(undefined, {
      refetchOnWindowFocus: false,
    }),
  );

  useEffect(() => {
    if (isUpdateMode && pricingHistory) {
      form.reset({
        regionId: pricingHistory.regionId,
        effectiveFromDate: pricingHistory.effectiveFromDate
          .toISOString()
          .split("T")[0],
        expiresAt: pricingHistory.expiresAt.toISOString().split("T")[0],
        regionCategoryPricing: pricingHistory.regionCategoryPricing.map(
          (cat) => ({
            id: cat.categoryId,
            name: cat.category.name,
            rate: Number(cat.rate),
            rateType: cat.category.rateType ?? "PER_ITEM",
            compensationKabadiwalaRate: Number(cat.compensationKabadiwalaRate),
            compensationRecyclerRate: Number(cat.compensationRecyclerRate),
          }),
        ),
      });
    } else if (categories && !isUpdateMode) {
      form.setValue(
        "regionCategoryPricing",
        categories.map((cat) => ({
          id: cat.id,
          name: cat.name,
          rate: Number(cat.rate),
          rateType: cat.rateType ?? "PER_ITEM",
          compensationKabadiwalaRate: Number(cat.compensationKabadiwalaRate),
          compensationRecyclerRate: Number(cat.compensationRecyclerRate),
        })),
      );
    }
  }, [categories, form, isUpdateMode, pricingHistory]);

  const onSubmit = (data: RegionPricingSchema) => {
    if (isPendingCreateRegionPricing || isPendingUpdateRegionPricing) return;

    if (isUpdateMode && paramValue) {
      updateRegionPricingPeriod(
        { ...data, historyId: paramValue },
        {
          onSuccess: () => {
            void queryClient.invalidateQueries(
              trpc.pricing.getAllPricingPeriods.queryOptions({}),
            );
            void queryClient.invalidateQueries(
              trpc.pricing.getPricingHistoryById.queryOptions({
                id: paramValue,
              }),
            );
            toast.success("Region price updated successfully");
            closeSheet();
          },
          onError: (error) => {
            toast.error(error.message);
          },
        },
      );
    } else {
      createRegionPricingPeriod(data, {
        onSuccess: () => {
          void queryClient.invalidateQueries(
            trpc.pricing.getAllPricingPeriods.queryOptions({}),
          );
          toast.success("Region price created successfully");
          closeSheet();
        },
        onError: (error) => {
          toast.error(error.message);
        },
      });
    }
  };

  const csvConfig = mkConfig({
    useKeysAsHeaders: true,
    filename: "region-pricing.csv",
  });

  const downloadCsv = () => {
    const dataForCsv = form.getValues("regionCategoryPricing");
    if (dataForCsv.length === 0) {
      toast.error("There is no pricing data to download.");
      return;
    }
    const csv = generateCsv(csvConfig)(dataForCsv);
    download(csvConfig)(csv);
  };

  // Handle CSV import completion
  const handleCsvImportComplete = (result: {
    num_rows: number;
    num_columns: number;
    error: string | null;
    columns: { key: string; name: string }[];
    rows: {
      index: number;
      values: {
        id: string;
        rate: string;
        name: string;
        rateType: string;
        compensationKabadiwalaRate: string;
        compensationRecyclerRate: string;
      };
    }[];
  }) => {
    if (!Array.isArray(result.rows)) {
      toast.error("Invalid CSV data.");
      setCsvUploadOpen(false);
      return;
    }

    const mapped = result.rows.map((row) => ({
      id: row.values.id,
      name: row.values.name,
      rate: Number(row.values.rate),
      rateType: row.values
        .rateType as (typeof categoryRateTypeEnum.enumValues)[number],
      compensationKabadiwalaRate: Number(row.values.compensationKabadiwalaRate),
      compensationRecyclerRate: Number(row.values.compensationRecyclerRate),
    }));
    form.setValue("regionCategoryPricing", mapped);
    setCsvUploadOpen(false);
  };

  if (isPricingHistoryLoading || isRegionsLoading || isCategoriesLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="space-y-6">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <FormField
            control={form.control}
            name="regionId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Select Region</FormLabel>
                <FormControl>
                  <Select
                    onValueChange={(val) => field.onChange(val)}
                    value={field.value}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select a region" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectGroup>
                        <SelectLabel>Regions </SelectLabel>
                        {regions?.map((item) => (
                          <SelectItem key={item.id} value={item.id}>
                            {item.name}
                          </SelectItem>
                        ))}
                      </SelectGroup>
                    </SelectContent>
                  </Select>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="effectiveFromDate"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Effective From Date</FormLabel>
                <FormControl>
                  <Input
                    type="date"
                    value={field.value}
                    onChange={(e) => field.onChange(e.target.value)}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="expiresAt"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Expires On Date</FormLabel>
                <FormControl>
                  <Input
                    type="date"
                    value={field.value}
                    onChange={(e) => field.onChange(e.target.value)}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="regionCategoryPricing"
            render={({ field }) => (
              <FormItem>
                <div className="flex items-center justify-between gap-4">
                  <FormLabel>All Categories Pricing for this Region</FormLabel>
                  <div className="flex items-center gap-2">
                    <Button
                      type="button"
                      onClick={() => setCsvUploadOpen(true)}
                    >
                      <Upload className="mr-2 h-4 w-4" />
                      Upload CSV
                    </Button>
                    <Button
                      type="button"
                      onClick={downloadCsv}
                      className="ml-auto"
                    >
                      <Download className="mr-2 h-4 w-4" />
                      Download CSV
                    </Button>
                  </div>
                </div>
                <FormControl>
                  <div>
                    <table
                      style={{ width: "100%", borderCollapse: "collapse" }}
                    >
                      <thead>
                        <tr>
                          <th
                            style={{ border: "1px solid #ccc", padding: "8px" }}
                          >
                            ID
                          </th>
                          <th
                            style={{ border: "1px solid #ccc", padding: "8px" }}
                          >
                            Name
                          </th>
                          <th
                            style={{ border: "1px solid #ccc", padding: "8px" }}
                          >
                            Rate
                          </th>
                          <th
                            style={{ border: "1px solid #ccc", padding: "8px" }}
                          >
                            Rate Type
                          </th>
                          <th
                            style={{ border: "1px solid #ccc", padding: "8px" }}
                          >
                            Compensation Kabadiwala Rate
                          </th>
                          <th
                            style={{ border: "1px solid #ccc", padding: "8px" }}
                          >
                            Recycler Selling Price
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {field.value?.map((item) => {
                          return (
                            <tr key={item.id}>
                              <td
                                style={{
                                  border: "1px solid #ccc",
                                  padding: "8px",
                                }}
                              >
                                {item.id}
                              </td>
                              <td
                                style={{
                                  border: "1px solid #ccc",
                                  padding: "8px",
                                }}
                              >
                                {item.name}
                              </td>
                              <td
                                style={{
                                  border: "1px solid #ccc",
                                  padding: "8px",
                                }}
                              >
                                <Input
                                  type="number"
                                  style={{ width: "100%" }}
                                  value={item.rate}
                                  onChange={(e) => {
                                    const val = Number(e.target.value);
                                    const updated = field.value.map((p) =>
                                      p.id === item.id
                                        ? { ...p, rate: val }
                                        : p,
                                    );
                                    field.onChange(updated);
                                  }}
                                />
                              </td>
                              <td
                                style={{
                                  border: "1px solid #ccc",
                                  padding: "8px",
                                }}
                              >
                                {item.rateType.replaceAll("_", " ")}
                              </td>
                              <td
                                style={{
                                  border: "1px solid #ccc",
                                  padding: "8px",
                                }}
                              >
                                <Input
                                  type="number"
                                  style={{ width: "100%" }}
                                  value={item.compensationKabadiwalaRate}
                                  onChange={(e) => {
                                    const val = Number(e.target.value);
                                    const updated = field.value.map((p) =>
                                      p.id === item.id
                                        ? {
                                            ...p,
                                            compensationKabadiwalaRate: val,
                                          }
                                        : p,
                                    );
                                    field.onChange(updated);
                                  }}
                                />
                              </td>
                              <td
                                style={{
                                  border: "1px solid #ccc",
                                  padding: "8px",
                                }}
                              >
                                <Input
                                  type="number"
                                  style={{ width: "100%" }}
                                  value={item.compensationRecyclerRate}
                                  onChange={(e) => {
                                    const val = Number(e.target.value);
                                    const updated = field.value.map((p) =>
                                      p.id === item.id
                                        ? {
                                            ...p,
                                            compensationRecyclerRate: val,
                                          }
                                        : p,
                                    );
                                    field.onChange(updated);
                                  }}
                                />
                              </td>
                            </tr>
                          );
                        })}
                      </tbody>
                    </table>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="flex gap-2">
            <Button type="submit" disabled={form.formState.isSubmitting}>
              {form.formState.isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {isUpdateMode ? "Updating..." : "Creating..."}
                </>
              ) : isUpdateMode ? (
                "Update Price"
              ) : (
                "Create Price"
              )}
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                form.reset();
              }}
            >
              Reset
            </Button>
          </div>
        </form>
      </Form>

      <CSVImporter
        modalIsOpen={csvUploadOpen}
        modalOnCloseTriggered={() => setCsvUploadOpen(false)}
        darkMode={true}
        onComplete={handleCsvImportComplete}
        template={{
          columns: [
            {
              name: "Category ID",
              key: "id",
              required: true,
              description: "The ID of the category",
            },
            {
              name: "Category Name",
              key: "name",
              required: true,
              description: "The name of the category",
            },
            {
              name: "Rate",
              key: "rate",
              data_type: "number",
              required: true,
              description: "The rate for this category",
            },
            {
              name: "Rate Type",
              key: "rateType",
              required: true,
              description: "The type of rate (PER_ITEM, PER_KG, etc.)",
            },
            {
              name: "Compensation Kabadiwala Rate",
              key: "compensationKabadiwalaRate",
              data_type: "number",
              required: true,
              description: "Compensation rate for kabadiwala",
            },
            {
              name: "Compensation Recycler Rate",
              key: "compensationRecyclerRate",
              data_type: "number",
              required: true,
              description: "Compensation rate for recycler",
            },
          ],
        }}
      />
    </div>
  );
};

export default PricingCreateUpdateForm;
