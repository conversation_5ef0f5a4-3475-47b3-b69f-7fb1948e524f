"use client";

import type {
  ColumnDef,
  ColumnFiltersState,
  FilterFn,
  SortingState,
  VisibilityState,
} from "@tanstack/react-table";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { rankItem } from "@tanstack/match-sorter-utils";
import { useQuery } from "@tanstack/react-query";
import {
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { ChevronDown, ExternalLink } from "lucide-react";
import { useQueryState } from "nuqs";

import type { orderStatusEnum } from "@acme/db/schema";
import { Button } from "@acme/ui/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@acme/ui/components/ui/dropdown-menu";
import { Input } from "@acme/ui/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@acme/ui/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@acme/ui/components/ui/table";
import { cn } from "@acme/ui/lib/utils";

import SortableColumnHeader from "~/components/shared/sortable-column-header";
import { orderFilterParamName } from "~/lib/constants";
import { useTRPC } from "~/trpc/react";
import StatsRangePicker from "../stats-range-picker";

type OrderStatus = (typeof orderStatusEnum.enumValues)[number];
type FilterStatus = "ALL" | OrderStatus;

const statusColors: Record<OrderStatus, string> = {
  CART: "text-gray-500", // Should not appear based on query
  ACTIVE: "text-blue-500",
  PENDING: "text-yellow-500",
  COMPLETED: "text-green-500",
  CANCELLED: "text-red-500",
};

interface OrdersDataTableProps {
  statusFilter?: FilterStatus;
}

interface OrdersGlobalFilter {
  search: string;
  status: FilterStatus;
}

const monthNames = [
  "jan",
  "feb",
  "mar",
  "apr",
  "may",
  "jun",
  "jul",
  "aug",
  "sep",
  "oct",
  "nov",
  "dec",
];

function parseDate(str?: string | null): Date | undefined {
  if (!str) return undefined;
  const [dd, mm, yyyy] = str.split("-");
  if (!dd || !mm || !yyyy) return undefined;
  const monthIdx = monthNames.indexOf(mm.toLowerCase());
  if (monthIdx === -1) return undefined;
  return new Date(Number(yyyy), monthIdx, Number(dd));
}

const OrdersDataTable = ({ statusFilter }: OrdersDataTableProps) => {
  const trpc = useTRPC();
  const router = useRouter();
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState({});
  const [, setOrderFilter] = useQueryState(orderFilterParamName, {
    clearOnDefault: true,
  });

  // Global filter state: { search, status }
  const [globalFilter, setGlobalFilter] = useState<OrdersGlobalFilter>({
    search: "",
    status: statusFilter ?? "ALL",
  });

  // Add after globalFilter state
  const [orderStartDate] = useQueryState("order-start-date");
  const [orderEndDate] = useQueryState("order-end-date");

  // Update status in global filter
  const handleStatusFilterChange = (value: FilterStatus) => {
    setGlobalFilter((prev) => ({ ...prev, status: value }));
    void setOrderFilter(value === "ALL" ? null : value);
  };

  // Update search in global filter
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setGlobalFilter((prev) => ({ ...prev, search: event.target.value }));
  };

  const { data: orders = [], isLoading } = useQuery(
    trpc.order.getAllOrders.queryOptions({ status: undefined }),
  );

  // Fuzzy global filter function for status and search
  const globalFuzzyFilter: FilterFn<(typeof orders)[number]> = (
    row,
    _columnId,
    filter: OrdersGlobalFilter,
  ) => {
    // Status filter
    if (filter.status !== "ALL" && row.original.status !== filter.status) {
      return false;
    }
    // Date range filter
    const createdAt = new Date(row.original.createdAt);
    let inRange = true;
    const start = parseDate(orderStartDate);
    if (start && createdAt < start) inRange = false;
    const end = parseDate(orderEndDate);
    if (end && createdAt > end) inRange = false;
    if (!inRange) return false;
    // Fuzzy search filter
    const search = filter.search.trim();
    if (!search) return true;
    const sellerName = String(row.original.sellerName ?? "");
    const kabadiwalaName = String(row.original.kabadiwalaName ?? "");
    const orderId = String(row.original.id);
    // Match if any field passes fuzzy
    return (
      rankItem(sellerName, search).passed ||
      rankItem(kabadiwalaName, search).passed ||
      rankItem(orderId, search).passed
    );
  };

  const columns: ColumnDef<(typeof orders)[number]>[] = [
    {
      accessorKey: "id",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Order ID" />
      ),
      cell: ({ row }) => (
        <div className="font-medium">{row.getValue("id")}</div>
      ),
    },
    {
      accessorKey: "sellerName",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Seller Name" />
      ),
      cell: ({ row }) => <div>{row.getValue("sellerName")}</div>,
    },
    {
      accessorKey: "kabadiwalaName",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Kabadiwala Name" />
      ),
      cell: ({ row }) => <div>{row.getValue("kabadiwalaName") ?? "N/A"}</div>,
    },
    {
      accessorKey: "status",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Status" />
      ),
      cell: ({ row }) => {
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        const status = row.original.status!;

        return (
          <div className={cn("font-medium", statusColors[status])}>
            {status}
          </div>
        );
      },
    },
    {
      accessorKey: "totalAmount",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Total Amount" />
      ),
      cell: ({ row }) => {
        const amount = parseFloat(row.getValue("totalAmount"));
        const formatted = new Intl.NumberFormat("en-IN", {
          style: "currency",
          currency: "INR",
        }).format(amount);
        return <div className="font-medium">{formatted}</div>;
      },
    },
    {
      accessorKey: "addressDisplay",
      header: "Address",
      cell: ({ row }) => <div>{row.getValue("addressDisplay")}</div>,
    },
    {
      accessorKey: "createdAt",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Created At" />
      ),
      cell: ({ row }) => {
        const date = new Date(row.getValue("createdAt"));
        return <div>{date.toLocaleDateString()}</div>;
      },
    },
    {
      accessorKey: "actions",
      cell: ({ row }) => {
        return (
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.push(`/manage-orders/${row.original.id}`)}
            >
              <ExternalLink />
            </Button>
          </div>
        );
      },
    },
  ];

  const table = useReactTable({
    data: orders,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      globalFilter,
    },
    onGlobalFilterChange: setGlobalFilter,
    globalFilterFn: globalFuzzyFilter,
    enableGlobalFilter: true,
  });

  return (
    <div className="w-full">
      <div className="flex items-center justify-between gap-4 py-4">
        <Input
          placeholder="Search by Order ID, Seller, or Kabadiwala..."
          value={globalFilter.search}
          onChange={handleSearchChange}
          className="max-w-sm"
          disabled={isLoading}
        />
        <div className="flex gap-2">
          <StatsRangePicker
            startParamName="order-start-date"
            endParamName="order-end-date"
          />
          <Select
            value={globalFilter.status}
            onValueChange={(value) =>
              handleStatusFilterChange(value as FilterStatus)
            }
            disabled={isLoading}
          >
            <SelectTrigger className="w-[160px]">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="ALL">ALL</SelectItem>
              <SelectItem value="ACTIVE">ACTIVE</SelectItem>
              <SelectItem value="PENDING">PENDING</SelectItem>
              <SelectItem value="COMPLETED">COMPLETED</SelectItem>
            </SelectContent>
          </Select>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" disabled={isLoading}>
                Columns <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {table
                .getAllColumns()
                .filter((column) => column.getCanHide())
                .map((column) => {
                  return (
                    <DropdownMenuCheckboxItem
                      key={column.id}
                      className="capitalize"
                      checked={column.getIsVisible()}
                      onCheckedChange={(value) =>
                        column.toggleVisibility(!!value)
                      }
                    >
                      {column.id}
                    </DropdownMenuCheckboxItem>
                  );
                })}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext(),
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody className="relative">
            {isLoading ? (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  Loading...
                </TableCell>
              </TableRow>
            ) : table.getRowModel().rows.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-end space-x-2 py-4">
        <div className="flex-1 text-sm text-muted-foreground">
          {table.getFilteredSelectedRowModel().rows.length} of{" "}
          {table.getFilteredRowModel().rows.length} row(s) selected.
        </div>
        <div className="space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  );
};

export default OrdersDataTable;
