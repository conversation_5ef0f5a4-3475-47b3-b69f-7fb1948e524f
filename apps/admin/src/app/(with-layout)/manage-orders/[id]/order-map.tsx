"use client";

import { useEffect, useState } from "react";
import {
  APIProvider,
  Map,
  Marker,
  useMap,
  useMapsLibrary,
} from "@vis.gl/react-google-maps";

import { env } from "~/env";

interface OrderDetailsMapProps {
  pickupLocationCoordinates: {
    latitude: number;
    longitude: number;
  };
  kabadiwalaLocationCoordinates: {
    latitude: number;
    longitude: number;
  } | null;
  showKabadiwalaNotAssignedMessage: boolean;
}

interface DirectionsRendererComponentProps {
  origin: { latitude: number; longitude: number };
  destination: { latitude: number; longitude: number };
}

const DirectionsRendererComponent = ({
  origin,
  destination,
}: DirectionsRendererComponentProps) => {
  const map = useMap();
  const routesLibrary = useMapsLibrary("routes");
  const [directionsService, setDirectionsService] =
    useState<google.maps.DirectionsService>();
  const [directionsRenderer, setDirectionsRenderer] =
    useState<google.maps.DirectionsRenderer>();
  const [, setRoutes] = useState<google.maps.DirectionsRoute[]>([]);

  useEffect(() => {
    if (!routesLibrary || !map) return;
    setDirectionsService(new routesLibrary.DirectionsService());
    setDirectionsRenderer(
      new routesLibrary.DirectionsRenderer({ map, draggable: false }),
    );
  }, [routesLibrary, map]);

  useEffect(() => {
    if (!directionsService || !directionsRenderer) return;

    const request: google.maps.DirectionsRequest = {
      origin: { lat: origin.latitude, lng: origin.longitude },
      destination: { lat: destination.latitude, lng: destination.longitude },
      travelMode: google.maps.TravelMode.DRIVING,
      provideRouteAlternatives: true,
    };

    directionsService
      .route(request)
      .then((response) => {
        directionsRenderer.setDirections(response);
        setRoutes(response.routes);
      })
      .catch((e) => console.error("Directions request failed: ", e));
  }, [directionsService, directionsRenderer, origin, destination]);

  useEffect(() => {
    if (!directionsRenderer) return;

    const listener = directionsRenderer.addListener(
      "directions_changed",
      () => {
        const result = directionsRenderer.getDirections();
        if (result) {
          setRoutes(result.routes);
        }
      },
    );

    return () => {
      google.maps.event.removeListener(listener);
    };
  }, [directionsRenderer]);

  // Cleanup on component unmount
  useEffect(() => {
    return () => {
      if (directionsRenderer) {
        directionsRenderer.setMap(null);
      }
    };
    // eslint-disable-next-line react-hooks/react-compiler
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return null;
};

const OrderDetailsMap = ({
  pickupLocationCoordinates,
  kabadiwalaLocationCoordinates,
  showKabadiwalaNotAssignedMessage,
}: OrderDetailsMapProps) => {
  return (
    <APIProvider apiKey={env.NEXT_PUBLIC_GOOGLE_MAPS_KEY}>
      <div className="relative">
        <Map gestureHandling="greedy" className="h-[500px] w-full shadow-lg">
          <Marker
            position={{
              lat: pickupLocationCoordinates.latitude,
              lng: pickupLocationCoordinates.longitude,
            }}
          />

          {kabadiwalaLocationCoordinates && (
            <>
              <Marker
                position={{
                  lat: kabadiwalaLocationCoordinates.latitude,
                  lng: kabadiwalaLocationCoordinates.longitude,
                }}
              />

              <DirectionsRendererComponent
                origin={pickupLocationCoordinates}
                destination={kabadiwalaLocationCoordinates}
              />
            </>
          )}
        </Map>

        {showKabadiwalaNotAssignedMessage && (
          <div className="absolute left-4 right-4 top-4 z-10">
            <div className="rounded-lg border border-yellow-200 bg-yellow-50 p-3 shadow-sm">
              <p className="text-sm font-medium text-yellow-800">
                Kabadiwala not assigned yet
              </p>
              <p className="text-xs text-yellow-600">
                Only pickup location is shown on the map
              </p>
            </div>
          </div>
        )}
      </div>
    </APIProvider>
  );
};

export default OrderDetailsMap;
