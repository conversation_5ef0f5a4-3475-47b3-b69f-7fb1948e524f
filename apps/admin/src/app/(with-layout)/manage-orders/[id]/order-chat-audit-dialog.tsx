import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Loader2 } from "lucide-react";

import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@acme/ui/components/ui/avatar";
import { Button } from "@acme/ui/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@acme/ui/components/ui/dialog";

import type { OrderDetails } from "~/lib/types";
import { REFETCH_TIME_INTERVAL_FOR_ORDER_DETAILS_PAGE } from "~/lib/constants";
import { useTRPC } from "~/trpc/react";

interface OrderChatAuditDialogProps {
  orderId: string;
  seller: OrderDetails["seller"];
  kabadiwala: OrderDetails["kabadiwala"];
  orderStatus: OrderDetails["status"];
}

const getSenderTag = (
  senderId: string,
  sellerId: string,
  kabadiwalaId: string,
) => {
  if (senderId === sellerId) return "[seller]";
  if (senderId === kabadiwalaId) return "[kabadiwala]";
  return "[admin]";
};

export default function OrderChatAuditDialog({
  orderId,
  seller,
  kabadiwala,
  orderStatus,
}: OrderChatAuditDialogProps) {
  const trpc = useTRPC();
  const [open, setOpen] = useState(false);
  const { data, isLoading, isError } = useQuery(
    trpc.conversation.getAuditConversationByOrderId.queryOptions(
      {
        orderId,
      },
      {
        enabled: open,
        refetchInterval:
          orderStatus === "ACTIVE"
            ? REFETCH_TIME_INTERVAL_FOR_ORDER_DETAILS_PAGE
            : false,
      },
    ),
  );

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button type="button" className="">
          Audit Seller-Kabadiwala Chat
        </Button>
      </DialogTrigger>
      <DialogContent className="h-[70dvh] max-h-[70dvh] max-w-2xl">
        <DialogHeader>
          <DialogTitle>Seller-Kabadiwala Chat Audit</DialogTitle>
        </DialogHeader>
        {isLoading ? (
          <div className="flex h-60 items-center justify-center">
            <Loader2 className="size-6 animate-spin" />
          </div>
        ) : isError ? (
          <div className="text-center text-red-500">Failed to load chat.</div>
        ) : !data?.conversation ? (
          <div className="py-8 text-center text-gray-500">
            No chat found for this order.
          </div>
        ) : (
          <div className="hide-scrollbar flex max-h-[70vh] flex-col items-start gap-3 overflow-y-auto py-4">
            {data.messages.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-10 text-center text-gray-500">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="mb-2 h-12 w-12 text-gray-400"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={1.5}
                    d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"
                  />
                </svg>
                <p className="text-lg font-medium">No messages yet</p>
                <p className="mt-1 text-sm">
                  No chat activity between seller and kabadiwala for this order.
                </p>
              </div>
            ) : (
              data.messages.map((msg) => {
                const isSeller = msg.senderId === seller.id;
                const isKabadiwala = msg.senderId === kabadiwala?.id;
                const isRight = isSeller;
                return (
                  <div
                    key={msg.id}
                    className={`flex w-full items-end gap-4 ${isRight ? "justify-end" : "justify-start"}`}
                  >
                    {!isRight && (
                      <div className="flex flex-col items-center">
                        <Avatar>
                          <AvatarImage
                            src={
                              isKabadiwala
                                ? (kabadiwala.image ??
                                  "/static/images/default-user.png")
                                : (seller.image ??
                                  "/static/images/default-user.png")
                            }
                            alt="Avatar"
                          />
                          <AvatarFallback>U</AvatarFallback>
                        </Avatar>
                        <span className="mt-1 text-xs text-gray-400">
                          {getSenderTag(
                            msg.senderId,
                            seller.id,
                            kabadiwala?.id ?? "",
                          )}
                        </span>
                      </div>
                    )}
                    <div
                      className={`max-w-[75%] rounded-2xl px-4 py-3 ${isRight ? "bg-teal-600 text-black-800" : "bg-yellow-100 text-black-800"}`}
                    >
                      <div className="break-words leading-4">{msg.content}</div>
                    </div>
                    {isRight && (
                      <div className="flex flex-col items-center">
                        <Avatar>
                          <AvatarImage
                            src={
                              seller.image ?? "/static/images/default-user.png"
                            }
                            alt="Avatar"
                          />
                          <AvatarFallback>U</AvatarFallback>
                        </Avatar>
                        <span className="mt-1 text-xs text-gray-400">
                          {getSenderTag(
                            msg.senderId,
                            seller.id,
                            kabadiwala?.id ?? "",
                          )}
                        </span>
                      </div>
                    )}
                  </div>
                );
              })
            )}
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
