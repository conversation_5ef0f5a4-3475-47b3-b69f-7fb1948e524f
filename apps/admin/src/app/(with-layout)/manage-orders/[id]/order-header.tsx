"use client";

import { formatDistanceToNow } from "date-fns";

import type { OrderDetails } from "~/lib/types";

interface OrderHeaderProps {
  order: Omit<OrderDetails, "items" | "seller" | "address" | "kabadiwala">;
}

const getStatusColor = (status?: string) => {
  switch (status) {
    case "ACTIVE":
      return "bg-blue-100 text-blue-800";
    case "PENDING":
      return "bg-yellow-100 text-yellow-800";
    case "COMPLETED":
      return "bg-green-100 text-green-800";
    case "CANCELLED":
      return "bg-red-100 text-red-800";
    case "CART":
      return "bg-gray-100 text-gray-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

const getOtpStatusColor = (status: string) => {
  switch (status) {
    case "VERIFIED":
      return "bg-green-100 text-green-800";
    case "PENDING":
      return "bg-yellow-100 text-yellow-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

const OrderHeader = ({ order }: OrderHeaderProps) => {
  return (
    <div className="rounded-lg border bg-white p-6 shadow-sm">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            Order #{order.id}
          </h1>
          <p className="mt-1 text-sm text-gray-500">
            Created{" "}
            {formatDistanceToNow(new Date(order.createdAt), {
              addSuffix: true,
            })}
          </p>
        </div>

        <div className="mt-4 flex flex-wrap gap-2 sm:mt-0">
          <span
            className={`inline-flex rounded-full px-3 py-1 text-xs font-medium ${getStatusColor(order.status ?? undefined)}`}
          >
            {order.status}
          </span>
          {order.pickupOtpStatus && (
            <span
              className={`inline-flex rounded-full px-3 py-1 text-xs font-medium ${getOtpStatusColor(order.pickupOtpStatus)}`}
            >
              OTP: {order.pickupOtpStatus}
            </span>
          )}
        </div>
      </div>

      {(order.totalAmount ?? order.earnings) && (
        <div className="mt-6 grid grid-cols-1 gap-4 sm:grid-cols-3">
          {order.totalAmount && (
            <div className="rounded-lg bg-gray-50 p-4">
              <p className="text-sm font-medium text-gray-500">Total Amount</p>
              <p className="text-2xl font-bold text-gray-900">
                ₹{order.totalAmount}
              </p>
            </div>
          )}
          {order.earnings && (
            <div className="rounded-lg bg-green-50 p-4">
              <p className="text-sm font-medium text-green-600">Earnings</p>
              <p className="text-2xl font-bold text-green-900">
                ₹{order.earnings}
              </p>
            </div>
          )}
          {order.completedAt && (
            <div className="rounded-lg bg-blue-50 p-4">
              <p className="text-sm font-medium text-blue-600">Completed</p>
              <p className="text-sm font-semibold text-blue-900">
                {formatDistanceToNow(new Date(order.completedAt), {
                  addSuffix: true,
                })}
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default OrderHeader;
