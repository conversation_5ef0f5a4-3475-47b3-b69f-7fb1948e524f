"use client";

import { Building, Home, MapPin, Navigation } from "lucide-react";

import type { OrderDetails } from "~/lib/types";

interface OrderAddressInfoProps {
  address: OrderDetails["address"];
}

const getAddressTypeIcon = (type: string | null) => {
  switch (type) {
    case "HOME":
      return <Home className="h-4 w-4 text-gray-400" />;
    case "WORK":
      return <Building className="h-4 w-4 text-gray-400" />;
    default:
      return <MapPin className="h-4 w-4 text-gray-400" />;
  }
};

const OrderAddressInfo = ({ address }: OrderAddressInfoProps) => {
  return (
    <div className="rounded-lg border bg-white p-6 shadow-sm">
      <div className="mb-4 flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">Pickup Address</h3>
        <div className="flex items-center space-x-2">
          {getAddressTypeIcon(address.addressType)}
          <span className="text-sm capitalize text-gray-500">
            {address.addressType?.toLowerCase()}
          </span>
          {address.isDefault && (
            <span className="inline-flex rounded-full bg-blue-100 px-2 py-1 text-xs font-medium text-blue-800">
              Default
            </span>
          )}
        </div>
      </div>

      <div className="space-y-3">
        <div>
          <h4 className="font-medium text-gray-900">{address.name}</h4>
          <p className="text-sm text-gray-600">{address.display}</p>
        </div>

        <div className="text-sm text-gray-600">
          <p>{address.localAddress}</p>
          {address.landmark && (
            <p className="mt-1">
              <span className="font-medium">Landmark:</span> {address.landmark}
            </p>
          )}
        </div>

        {(address.street ??
          address.city ??
          address.state ??
          address.postalCode) && (
          <div className="text-sm text-gray-600">
            {address.street && <p>{address.street}</p>}
            <p>
              {[address.city, address.state, address.postalCode]
                .filter(Boolean)
                .join(", ")}
              {address.country && `, ${address.country}`}
            </p>
          </div>
        )}

        {address.coordinates && (
          <div className="flex items-center text-sm text-gray-500">
            <Navigation className="mr-2 h-4 w-4" />
            <span>
              {address.coordinates.latitude.toFixed(4)},{" "}
              {address.coordinates.longitude.toFixed(4)}
            </span>
          </div>
        )}
      </div>
    </div>
  );
};

export default OrderAddressInfo;
