"use client";

import Image from "next/image";
import {
  CheckCircle,
  Mail,
  MapPin,
  Phone,
  Wallet,
  XCircle,
} from "lucide-react";

import type { OrderDetails } from "~/lib/types";

interface OrderKabadiwalaInfoProps {
  kabadiwala: OrderDetails["kabadiwala"];
}

const OrderKabadiwalaInfo = ({ kabadiwala }: OrderKabadiwalaInfoProps) => {
  if (!kabadiwala) {
    return (
      <div className="rounded-lg border bg-white p-6 shadow-sm">
        <h3 className="text-lg font-semibold text-gray-900">
          Kabadiwala Information
        </h3>
        <p className="mt-2 text-gray-500">No kabadiwala assigned yet</p>
      </div>
    );
  }

  return (
    <div className="rounded-lg border bg-white p-6 shadow-sm">
      <div className="mb-4 flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">
          Kabadiwala Information
        </h3>

        <span
          className={`inline-flex rounded-full px-2 py-1 text-xs font-medium ${
            kabadiwala.isOnDuty
              ? "bg-green-100 text-green-800"
              : "bg-gray-100 text-gray-800"
          }`}
        >
          {kabadiwala.isOnDuty ? "On Duty" : "Off Duty"}
        </span>
      </div>

      <div className="flex items-start space-x-4">
        <div className="flex-shrink-0">
          {kabadiwala.image ? (
            <Image
              src={kabadiwala.image}
              alt={kabadiwala.name}
              width={64}
              height={64}
              className="h-16 w-16 rounded-full object-cover"
            />
          ) : (
            <div className="flex h-16 w-16 items-center justify-center rounded-full bg-gray-200">
              <span className="text-xl font-medium text-gray-600">
                {kabadiwala.name.charAt(0).toUpperCase()}
              </span>
            </div>
          )}
        </div>

        <div className="min-w-0 flex-1">
          <h4 className="text-lg font-medium text-gray-900">
            {kabadiwala.name}
          </h4>
          <p className="text-sm text-gray-500">ID: {kabadiwala.id}</p>

          <div className="mt-3 space-y-2">
            <div className="flex items-center text-sm">
              <Mail className="mr-2 h-4 w-4 text-gray-400" />
              <span className="text-gray-900">{kabadiwala.email}</span>
              {kabadiwala.emailVerified ? (
                <CheckCircle className="ml-2 h-4 w-4 text-green-500" />
              ) : (
                <XCircle className="ml-2 h-4 w-4 text-red-500" />
              )}
            </div>

            {kabadiwala.phoneNumber && (
              <div className="flex items-center text-sm">
                <Phone className="mr-2 h-4 w-4 text-gray-400" />
                <span className="text-gray-900">{kabadiwala.phoneNumber}</span>
                {kabadiwala.phoneNumberVerified ? (
                  <CheckCircle className="ml-2 h-4 w-4 text-green-500" />
                ) : (
                  <XCircle className="ml-2 h-4 w-4 text-red-500" />
                )}
              </div>
            )}

            {kabadiwala.walletBalance && (
              <div className="flex items-center text-sm">
                <Wallet className="mr-2 h-4 w-4 text-gray-400" />
                <span className="text-gray-900">
                  Balance: ₹{kabadiwala.walletBalance}
                </span>
              </div>
            )}

            {kabadiwala.liveLocationCoordinate && (
              <div className="flex items-center text-sm">
                <MapPin className="mr-2 h-4 w-4 text-gray-400" />
                <span className="text-gray-900">
                  {kabadiwala.liveLocationCoordinate.latitude.toFixed(4)},{" "}
                  {kabadiwala.liveLocationCoordinate.longitude.toFixed(4)}
                </span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderKabadiwalaInfo;
