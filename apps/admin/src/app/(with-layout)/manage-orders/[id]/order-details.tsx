"use client";

import dynamic from "next/dynamic";
import { useMutation, useQuery } from "@tanstack/react-query";
import { Loader2, MessageCircle } from "lucide-react";

import { Button } from "@acme/ui/components/ui/button";

import { REFETCH_TIME_INTERVAL_FOR_ORDER_DETAILS_PAGE } from "~/lib/constants";
import { useTRPC } from "~/trpc/react";
import OrderAddressInfo from "./order-address-info";
import OrderChatAuditDialog from "./order-chat-audit-dialog";
import OrderHeader from "./order-header";
import OrderItemsList from "./order-items-list";
import OrderKabadiwalaInfo from "./order-kabadiwala-info";
import OrderSellerInfo from "./order-seller-info";

const OrderDetailsMap = dynamic(() => import("./order-map"), {
  ssr: false,
});

interface OrderDetailsProps {
  orderId: string;
}

const OrderDetails = ({ orderId }: OrderDetailsProps) => {
  const trpc = useTRPC();
  const {
    data: order,
    isPending,
    isError,
  } = useQuery(
    trpc.order.getOrderById.queryOptions(
      {
        id: orderId,
      },
      {
        refetchInterval: REFETCH_TIME_INTERVAL_FOR_ORDER_DETAILS_PAGE, // Refetch every 10 seconds
      },
    ),
  );

  // Fetch the latest active customer support conversation for this order via API
  const { data: latestActiveCustomerSupportConvo } = useQuery(
    trpc.conversation.getLatestActiveCustomerSupportConversationByOrderId.queryOptions(
      { orderId },
    ),
  );
  const customerConversationId = latestActiveCustomerSupportConvo?.id;

  // Fetch support conversations for this order using new endpoints
  const { data: kabadiwalaSupportConvo } = useQuery(
    trpc.conversation.getKabadiwalaSupportConversationByOrderId.queryOptions({
      orderId,
    }),
  );
  const kabadiwalaConversationId = kabadiwalaSupportConvo?.id;

  // Mutations to create conversation if not exists
  const {
    mutateAsync: createCustomerConvo,
    isPending: isCreatingCustomerConvo,
  } = useMutation(
    trpc.conversation.createCustomerSupportConversation.mutationOptions(),
  );
  const {
    mutateAsync: createKabadiwalaConvo,
    isPending: isCreatingKabadiwalaConvo,
  } = useMutation(
    trpc.conversation.createKabadiwalaSupportConversation.mutationOptions(),
  );

  const handleOpenCustomerChat = async () => {
    let id = customerConversationId;
    if (!id) {
      const res = await createCustomerConvo({
        customerId: order?.sellerId ?? "",
        orderId,
      });
      id = res.conversationId;
    }
    if (id) {
      window.open(
        `/manage-chats/customer?customerChatConversationId=${id}`,
        "_blank",
      );
    }
  };

  const handleOpenKabadiwalaChat = async () => {
    let id = kabadiwalaConversationId;
    if (!id) {
      const res = await createKabadiwalaConvo({
        kabadiwalaId: order?.kabadiwalaId ?? "",
        orderId,
      });
      id = res.conversationId;
    }
    if (id) {
      window.open(
        `/manage-chats/kabadiwala?kabadiwalaChatConversationId=${id}`,
        "_blank",
      );
    }
  };

  if (isPending) {
    return (
      <div className="flex h-96 items-center justify-center">
        <Loader2 className="size-5 animate-spin" />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex h-96 items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-semibold text-gray-900">
            Order Not Found
          </h2>
          <p className="mt-2 text-gray-600">
            The order you're looking for doesn't exist.
          </p>
        </div>
      </div>
    );
  }

  const pickupLocationCoordinates = {
    latitude: order.address.coordinates?.latitude,
    longitude: order.address.coordinates?.longitude,
  };

  const kabadiwalaLocationCoordinates = {
    latitude: order.kabadiwala?.liveLocationCoordinate?.latitude,
    longitude: order.kabadiwala?.liveLocationCoordinate?.longitude,
  };

  // Check if coordinates are valid (not null, undefined, or 0)
  const hasValidPickupCoordinates =
    pickupLocationCoordinates.latitude && pickupLocationCoordinates.longitude;

  const hasValidKabadiwalaCoordinates =
    kabadiwalaLocationCoordinates.latitude &&
    kabadiwalaLocationCoordinates.longitude;

  // NOTE: Don't need to worry about below es-lint errors as we are using non-null assertion operator disables becoz i am already checking for valid coordinates above so it's safe to use non-null assertion operator and es-lint dont know about it becoz eslint is not just smart enough

  return (
    <div className="px-6 py-6 lg:px-8">
      <div className="mb-6">
        {!hasValidPickupCoordinates && !hasValidKabadiwalaCoordinates ? (
          <div className="flex h-[300px] items-center justify-center rounded-lg border border-gray-200 bg-gray-50">
            <div className="text-center">
              <p className="text-lg font-medium text-gray-600">
                Unable to render map
              </p>
              <p className="mt-1 text-sm text-gray-500">
                Location coordinates are not available
              </p>
            </div>
          </div>
        ) : hasValidPickupCoordinates && !hasValidKabadiwalaCoordinates ? (
          <OrderDetailsMap
            pickupLocationCoordinates={{
              // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
              latitude: pickupLocationCoordinates.latitude!,
              // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
              longitude: pickupLocationCoordinates.longitude!,
            }}
            kabadiwalaLocationCoordinates={null}
            showKabadiwalaNotAssignedMessage={
              !order.kabadiwalaId ? true : false
            }
          />
        ) : (
          <OrderDetailsMap
            pickupLocationCoordinates={{
              // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
              latitude: pickupLocationCoordinates.latitude!,
              // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
              longitude: pickupLocationCoordinates.longitude!,
            }}
            kabadiwalaLocationCoordinates={{
              // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
              latitude: kabadiwalaLocationCoordinates.latitude!,
              // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
              longitude: kabadiwalaLocationCoordinates.longitude!,
            }}
            showKabadiwalaNotAssignedMessage={false}
          />
        )}
      </div>

      <div className="space-y-6">
        <OrderHeader order={order} />

        <div className="flex h-fit flex-row gap-2">
          <Button
            type="button"
            onClick={handleOpenCustomerChat}
            disabled={isCreatingCustomerConvo}
          >
            {isCreatingCustomerConvo ? (
              <Loader2 className="size-4 animate-spin" />
            ) : (
              <MessageCircle className="size-4" />
            )}
            Seller Chat
          </Button>
          <Button
            type="button"
            onClick={handleOpenKabadiwalaChat}
            disabled={isCreatingKabadiwalaConvo}
          >
            {isCreatingKabadiwalaConvo ? (
              <Loader2 className="size-4 animate-spin" />
            ) : (
              <MessageCircle className="size-4" />
            )}
            Kabadiwala Chat
          </Button>
          {/* Audit Chat Button */}
          <OrderChatAuditDialog
            orderId={order.id}
            seller={order.seller}
            kabadiwala={order.kabadiwala}
            orderStatus={order.status}
          />
        </div>
        <div className="grid w-full grid-cols-1 gap-6 lg:grid-cols-2">
          {/* Conversation links for customer and kabadiwala */}

          <OrderSellerInfo seller={order.seller} />
          <OrderAddressInfo address={order.address} />
          <OrderKabadiwalaInfo kabadiwala={order.kabadiwala} />
          <OrderItemsList items={order.items} />
        </div>
      </div>
    </div>
  );
};

export default OrderDetails;
