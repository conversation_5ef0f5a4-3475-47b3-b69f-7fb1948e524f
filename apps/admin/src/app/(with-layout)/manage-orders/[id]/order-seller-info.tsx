"use client";

import Image from "next/image";
import { CheckCircle, Mail, Phone, XCircle } from "lucide-react";

import type { OrderDetails } from "~/lib/types";

interface OrderSellerInfoProps {
  seller: OrderDetails["seller"];
}

const OrderSellerInfo = ({ seller }: OrderSellerInfoProps) => {
  return (
    <div className="rounded-lg border bg-white p-6 shadow-sm">
      <h3 className="mb-4 text-lg font-semibold text-gray-900">
        Seller Information
      </h3>

      <div className="flex items-start space-x-4">
        <div className="flex-shrink-0">
          {seller.image ? (
            <Image
              src={seller.image}
              alt={seller.fullName}
              width={64}
              height={64}
              className="h-16 w-16 rounded-full object-cover"
            />
          ) : (
            <div className="flex h-16 w-16 items-center justify-center rounded-full bg-gray-200">
              <span className="text-xl font-medium text-gray-600">
                {seller.fullName.charAt(0).toUpperCase()}
              </span>
            </div>
          )}
        </div>

        <div className="min-w-0 flex-1">
          <h4 className="text-lg font-medium text-gray-900">
            {seller.fullName}
          </h4>
          <p className="text-sm text-gray-500">ID: {seller.id}</p>

          <div className="mt-3 space-y-2">
            <div className="flex items-center text-sm">
              <Mail className="mr-2 h-4 w-4 text-gray-400" />
              <span className="text-gray-900">{seller.email}</span>
              {seller.emailVerified ? (
                <CheckCircle className="ml-2 h-4 w-4 text-green-500" />
              ) : (
                <XCircle className="ml-2 h-4 w-4 text-red-500" />
              )}
            </div>

            {seller.phoneNumber && (
              <div className="flex items-center text-sm">
                <Phone className="mr-2 h-4 w-4 text-gray-400" />
                <span className="text-gray-900">{seller.phoneNumber}</span>
                {seller.phoneNumberVerified ? (
                  <CheckCircle className="ml-2 h-4 w-4 text-green-500" />
                ) : (
                  <XCircle className="ml-2 h-4 w-4 text-red-500" />
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderSellerInfo;
