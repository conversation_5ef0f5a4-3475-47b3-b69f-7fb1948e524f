import { getQueryClient, HydrateClient, trpc } from "~/trpc/server";
import OrderDetails from "./order-details";

interface OrderDetailPageProps {
  params: Promise<{ id: string }>;
}

const OrderDetailPage = async ({ params }: OrderDetailPageProps) => {
  const orderId = (await params).id;

  const queryClient = getQueryClient();
  void queryClient.prefetchQuery(
    trpc.order.getOrderById.queryOptions({
      id: orderId,
    }),
  );

  return (
    <HydrateClient>
      <OrderDetails orderId={orderId} />
    </HydrateClient>
  );
};

export default OrderDetailPage;
