"use client";

import Image from "next/image";

import type { OrderDetails } from "~/lib/types";

interface OrderItemsListProps {
  items: OrderDetails["items"];
}

const OrderItemsList = ({ items }: OrderItemsListProps) => {
  const calculateItemTotal = (quantity: string, rate: string) => {
    return (parseFloat(quantity) * parseFloat(rate)).toFixed(2);
  };

  const calculateGrandTotal = () => {
    return items
      .reduce((total, item) => {
        return (
          total +
          parseFloat(calculateItemTotal(item.quantity, item.category.rate))
        );
      }, 0)
      .toFixed(2);
  };

  return (
    <div className="rounded-lg border bg-white p-6 shadow-sm">
      <h3 className="mb-4 text-lg font-semibold text-gray-900">Order Items</h3>

      {items.length === 0 ? (
        <p className="text-gray-500">No items in this order</p>
      ) : (
        <div className="space-y-4">
          {items.map((item) => (
            <div
              key={item.id}
              className="flex items-center space-x-4 rounded-lg border p-4"
            >
              <div className="flex-shrink-0">
                <Image
                  src={item.category.image}
                  alt={item.category.name}
                  width={64}
                  height={64}
                  className="h-16 w-16 rounded-lg object-cover"
                />
              </div>

              <div className="min-w-0 flex-1">
                <h4 className="text-sm font-medium text-gray-900">
                  {item.category.name}
                </h4>
                {item.category.description && (
                  <p className="line-clamp-2 text-sm text-gray-500">
                    {item.category.description}
                  </p>
                )}
                <div className="mt-1 flex items-center space-x-4 text-sm text-gray-500">
                  <span>
                    Qty: {item.quantity}{" "}
                    {item.category.rateType === "PER_KG" ? "kg" : "items"}
                  </span>
                  <span>
                    Rate: ₹{item.category.rate}/
                    {item.category.rateType === "PER_KG" ? "kg" : "item"}
                  </span>
                </div>
              </div>

              <div className="text-right">
                <p className="text-lg font-semibold text-gray-900">
                  ₹{calculateItemTotal(item.quantity, item.category.rate)}
                </p>
              </div>
            </div>
          ))}

          <div className="border-t pt-4">
            <div className="flex items-center justify-between">
              <span className="text-lg font-semibold text-gray-900">
                Total:
              </span>
              <span className="text-xl font-bold text-gray-900">
                ₹{calculateGrandTotal()}
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default OrderItemsList;
