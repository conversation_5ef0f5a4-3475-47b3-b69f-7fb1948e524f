import { Suspense } from "react";

import type { orderStatusEnum } from "@acme/db/schema";
import { Separator } from "@acme/ui/components/ui/separator";

import DataTableSkeleton from "~/components/skeleton/data-table-skeleton";
import { orderFilterParamName } from "~/lib/constants";
import { getQueryClient, HydrateClient, trpc } from "~/trpc/server";
import OrdersDataTable from "./orders-data-table";

export const metadata = {
  title: "Orders Management",
  description: "View and manage orders",
};

interface ManageOrdersPageProps {
  searchParams: Promise<{
    [orderFilterParamName]?: (typeof orderStatusEnum.enumValues)[number];
  }>;
}

const ManageOrdersPage = async (props: ManageOrdersPageProps) => {
  const filter = (await props.searchParams)[orderFilterParamName];
  const queryClient = getQueryClient();

  await queryClient.prefetchQuery(
    trpc.order.getAllOrders.queryOptions({
      status: filter,
    }),
  );

  return (
    <HydrateClient>
      <div>
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Orders Management
            </h1>
            <p className="text-muted-foreground">View and filter orders.</p>
          </div>
        </div>
        <Separator className="my-6" />
        <Suspense fallback={<DataTableSkeleton />}>
          <OrdersDataTable statusFilter={filter} />
        </Suspense>
      </div>
    </HydrateClient>
  );
};

export default ManageOrdersPage;
