import { useEffect } from "react";
import { Map, Marker, useMap } from "@vis.gl/react-google-maps";

import { cn } from "@acme/ui/lib/cn";

interface ScraphubMapProps {
  selectedLocationCoordinates: {
    latitude: number;
    longitude: number;
  };
}

const ScraphubMap = ({ selectedLocationCoordinates }: ScraphubMapProps) => {
  const map = useMap();

  useEffect(() => {
    if (!map) return;

    map.moveCamera({
      center: {
        lat: selectedLocationCoordinates.latitude,
        lng: selectedLocationCoordinates.longitude,
      },
      zoom: 15,
    });
  }, [map, selectedLocationCoordinates]);

  return (
    <Map
      defaultCenter={{
        lat: selectedLocationCoordinates.latitude,
        lng: selectedLocationCoordinates.longitude,
      }}
      defaultZoom={15}
      gestureHandling={"greedy"}
      disableDefaultUI={true}
      className={cn("h-[243px] w-full")}
    >
      <Marker
        position={{
          lat: selectedLocationCoordinates.latitude,
          lng: selectedLocationCoordinates.longitude,
        }}
      />
    </Map>
  );
};

export default ScraphubMap;
