import { HydrateClient, prefetch, trpc } from "~/trpc/server";
import ScraphubDetails from "./scraphub-details";

interface ScraphubDetailPageProps {
  params: Promise<{ id: string }>;
}

const ScraphubDetailPage = async ({ params }: ScraphubDetailPageProps) => {
  const { id } = await params;

  void prefetch(trpc.scraphub.getScraphubById.queryOptions({ scraphubId: id }));
  void prefetch(
    trpc.scraphub.getAllAssociatedKabadiwalas.queryOptions({ scraphubId: id }),
  );

  return (
    <HydrateClient>
      <ScraphubDetails scraphubId={id} />
    </HydrateClient>
  );
};

export default ScraphubDetailPage;
