"use client";

import { useEffect, useState } from "react";
import {
  skipToken,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";
import { Calendar, Phone, Plus, Search, Star, User } from "lucide-react";
import { toast } from "sonner";

import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@acme/ui/components/ui/avatar";
import { Badge } from "@acme/ui/components/ui/badge";
import { Button } from "@acme/ui/components/ui/button";
import { Card, CardContent, CardHeader } from "@acme/ui/components/ui/card";
import { Input } from "@acme/ui/components/ui/input";
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@acme/ui/components/ui/sheet";

import { useTRPC } from "~/trpc/react";

interface AssignKabadiwalaSheetProps {
  scraphubId: string;
}

const AssignKabadiwalaSheet = ({ scraphubId }: AssignKabadiwalaSheetProps) => {
  const [query, setQuery] = useState("");
  const [debouncedQuery, setDebouncedQuery] = useState("");

  const trpc = useTRPC();
  const queryClient = useQueryClient();

  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(query);
    }, 300);

    return () => clearTimeout(timer);
  }, [query]);

  const { data: searchResults, isLoading: isLoadingKabadiwalas } = useQuery(
    trpc.scraphub.searchKabadiwalas.queryOptions(
      debouncedQuery.trim()
        ? {
            query: debouncedQuery.trim(),
            scraphubId: scraphubId,
          }
        : skipToken,
    ),
  );

  const { mutate: assignKabadiwala, isPending: isAssigningKabadiwala } =
    useMutation(
      trpc.scraphub.assignKabadiwala.mutationOptions({
        onSuccess: async (opts) => {
          await queryClient.invalidateQueries(
            trpc.scraphub.getAllAssociatedKabadiwalas.queryOptions({
              scraphubId,
            }),
          );
          // Refetch search results to update assignment status
          await queryClient.invalidateQueries(
            trpc.scraphub.searchKabadiwalas.queryOptions({
              query: debouncedQuery.trim(),
              scraphubId: scraphubId,
            }),
          );
          toast.success(opts.message);
        },
        onError: (error) => {
          toast.error(error.message || "Failed to assign Kabadiwala");
        },
      }),
    );

  const { mutate: unAssignKabadiwala, isPending: isUnAssigningKabadiwala } =
    useMutation(
      trpc.scraphub.removeAssignedKabadiwala.mutationOptions({
        onSuccess: async (opts) => {
          await queryClient.invalidateQueries(
            trpc.scraphub.getAllAssociatedKabadiwalas.queryOptions({
              scraphubId,
            }),
          );
          // Refetch search results to update assignment status
          await queryClient.invalidateQueries(
            trpc.scraphub.searchKabadiwalas.queryOptions({
              query: debouncedQuery.trim(),
              scraphubId,
            }),
          );
          toast.success(opts.message);
        },
        onError: (error) => {
          toast.error(error.message || "Failed to remove Kabadiwala");
        },
      }),
    );

  const handleAssignKabadiwala = (kabadiwalaId: string) => {
    assignKabadiwala({ scraphubId, kabadiwalaId });
  };

  const handleUnAssignKabadiwala = (kabadiwalaId: string) => {
    unAssignKabadiwala({ scraphubId, kabadiwalaId });
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat("en-IN", {
      day: "2-digit",
      month: "short",
      year: "numeric",
    }).format(new Date(date));
  };

  return (
    <Sheet>
      <SheetTrigger>
        <Button>
          {" "}
          <Plus className="h-4 w-4" /> Assign Kabadiwala
        </Button>
      </SheetTrigger>
      <SheetContent className="w-full min-w-full overflow-y-auto md:min-w-[500px] xl:min-w-[1000px]">
        <SheetHeader>
          <SheetTitle>Assign Kabadiwala to Scraphub</SheetTitle>
        </SheetHeader>

        <div className="mt-6 space-y-4">
          {/* Search Input */}
          <div className="relative">
            <Search className="absolute left-3 top-5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search kabadiwalas by name or phone number..."
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Search Results */}
          <div className="space-y-3">
            {isLoadingKabadiwalas && debouncedQuery && (
              <div className="py-8 text-center text-muted-foreground">
                Searching kabadiwalas...
              </div>
            )}

            {!debouncedQuery && (
              <div className="py-8 text-center text-muted-foreground">
                <Search className="mx-auto mb-2 h-12 w-12 opacity-50" />
                <p>Start typing to search for kabadiwalas</p>
              </div>
            )}

            {debouncedQuery &&
              !isLoadingKabadiwalas &&
              !searchResults?.data.length && (
                <div className="py-8 text-center text-muted-foreground">
                  <User className="mx-auto mb-2 h-12 w-12 opacity-50" />
                  <p>No kabadiwalas found matching your search</p>
                </div>
              )}

            {searchResults?.data.map((kabadiwala) => {
              const isAssigned = !!kabadiwala.scraphubId;
              const isAssignedToThisScraphub =
                kabadiwala.scraphubId === scraphubId;

              return (
                <Card
                  key={kabadiwala.id}
                  className="transition-shadow hover:shadow-md"
                >
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <Avatar className="h-12 w-12">
                          <AvatarImage src={kabadiwala.image ?? undefined} />
                          <AvatarFallback>
                            {kabadiwala.name.charAt(0).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <h3 className="text-lg font-semibold">
                            {kabadiwala.name}
                          </h3>
                          <div className="mt-1 flex items-center text-sm text-muted-foreground">
                            <Phone className="mr-1 h-3 w-3" />
                            {kabadiwala.phoneNumber}
                            {kabadiwala.phoneNumberVerified && (
                              <Badge
                                variant="secondary"
                                className="ml-2 text-xs"
                              >
                                Verified
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        {isAssigned && !isAssignedToThisScraphub && (
                          <Badge variant="destructive">Assigned</Badge>
                        )}
                        {isAssignedToThisScraphub && (
                          <Badge variant="default">Assigned Here</Badge>
                        )}
                        {!isAssigned && (
                          <Badge variant="outline">Available</Badge>
                        )}
                      </div>
                    </div>
                  </CardHeader>

                  <CardContent className="pt-0">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                        {kabadiwala.averageRating && (
                          <div className="flex items-center">
                            <Star className="mr-1 h-3 w-3 fill-yellow-400 text-yellow-400" />
                            {kabadiwala.averageRating}
                          </div>
                        )}
                        <div className="flex items-center">
                          <Calendar className="mr-1 h-3 w-3" />
                          Joined {formatDate(kabadiwala.createdAt)}
                        </div>
                      </div>

                      <div>
                        {!isAssigned ? (
                          <Button
                            onClick={() =>
                              handleAssignKabadiwala(kabadiwala.id)
                            }
                            disabled={isAssigningKabadiwala}
                            size="sm"
                          >
                            {isAssigningKabadiwala ? "Assigning..." : "Assign"}
                          </Button>
                        ) : isAssignedToThisScraphub ? (
                          <Button
                            variant="destructive"
                            onClick={() =>
                              handleUnAssignKabadiwala(kabadiwala.id)
                            }
                            disabled={isUnAssigningKabadiwala}
                            size="sm"
                          >
                            {isUnAssigningKabadiwala
                              ? "Unassigning..."
                              : "Unassign"}
                          </Button>
                        ) : (
                          <Button variant="outline" disabled size="sm">
                            Unavailable
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
};

export default AssignKabadiwalaSheet;
