"use client";

import { PlusIcon } from "lucide-react";

import { But<PERSON> } from "@acme/ui/components/ui/button";

import { GenericSheet } from "~/components/shared/generic-sheet";
import { useSheet } from "~/hooks/use-sheet";
import { employeeParamName } from "~/lib/constants";
import EmployeeCreateUpdateForm from "./employee-create-update-form";

const EmployeeFormSheet = () => {
  const { isOpen, paramValue, openSheet, closeSheet } =
    useSheet(employeeParamName);

  return (
    <div>
      <Button onClick={() => openSheet()}>
        <PlusIcon className="size-4" />
        Add Employee
      </Button>
      <GenericSheet
        isOpen={isOpen}
        onClose={closeSheet}
        title={paramValue ? "Edit Employee" : "Add New Employee"}
        className="md:min-w-[500px] lg:min-w-[600px]"
      >
        <EmployeeCreateUpdateForm />
      </GenericSheet>
    </div>
  );
};

export default EmployeeFormSheet;
