import type { SearchParams } from "nuqs/server";
import { Suspense } from "react";

import { Separator } from "@acme/ui/components/ui/separator";

import DataTableSkeleton from "~/components/skeleton/data-table-skeleton";
import { loadScraphubSearchParams } from "~/lib/nuqs-utils";
import { getQueryClient, HydrateClient, trpc } from "~/trpc/server";
import ScraphubsDataTable from "./scraphubs-data-table";

export const metadata = {
  title: "Scraphubs Management",
  description: "Manage your website's scraphubs",
};

interface ManageScraphubsPageProps {
  searchParams: Promise<SearchParams>;
}

const ManageScraphub = async ({ searchParams }: ManageScraphubsPageProps) => {
  const { scraphub_id } = await loadScraphubSearchParams(searchParams);
  const queryClient = getQueryClient();

  // Prefetch list data
  await queryClient.prefetchQuery(trpc.scraphub.getAllScraphubs.queryOptions());

  // Prefetch individual item data if ID exists
  if (scraphub_id) {
    await queryClient.prefetchQuery(
      trpc.scraphub.getScraphubById.queryOptions({ scraphubId: scraphub_id }),
    );
  }

  return (
    <HydrateClient>
      <div>
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Scraphubs Management
            </h1>
            <p className="text-muted-foreground">
              Create, edit and manage scraphubs of your website.
            </p>
          </div>
        </div>
        <Separator className="my-6" />
        <Suspense fallback={<DataTableSkeleton />}>
          <ScraphubsDataTable />
        </Suspense>
      </div>
    </HydrateClient>
  );
};

export default ManageScraphub;
