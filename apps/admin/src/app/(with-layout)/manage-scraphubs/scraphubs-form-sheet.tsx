"use client";

import { PlusIcon } from "lucide-react";

import { But<PERSON> } from "@acme/ui/components/ui/button";

import { GenericSheet } from "~/components/shared/generic-sheet";
import { useSheet } from "~/hooks/use-sheet";
import { scraphubParamName } from "~/lib/constants";
import { ScraphubsCreateUpdateForm } from "./scraphubs-create-update-form";

const ScraphubFormSheet = () => {
  const { isOpen, paramValue, openSheet, closeSheet } =
    useSheet(scraphubParamName);

  return (
    <div>
      <Button onClick={() => openSheet()}>
        <PlusIcon className="size-4" />
        Create Scraphub
      </Button>
      <GenericSheet
        isOpen={isOpen}
        onClose={closeSheet}
        title={paramValue ? "Edit Scraphub" : "Create Scraphub"}
      >
        <ScraphubsCreateUpdateForm />
      </GenericSheet>
    </div>
  );
};

export default ScraphubFormSheet;
