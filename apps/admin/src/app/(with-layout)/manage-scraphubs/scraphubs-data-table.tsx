"use client";

import type {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
} from "@tanstack/react-table";
import { useState } from "react";
import Link from "next/link";
import { useMutation, useQuery } from "@tanstack/react-query";
import {
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import {
  ChevronDown,
  Edit,
  ExternalLink,
  MapPin,
  MoreHorizontal,
  Trash,
} from "lucide-react";
import { toast } from "sonner";

import { Button } from "@acme/ui/components/ui/button";
import { Checkbox } from "@acme/ui/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@acme/ui/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@acme/ui/components/ui/dropdown-menu";
import { Input } from "@acme/ui/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@acme/ui/components/ui/table";
import { cn } from "@acme/ui/lib/utils";

import AlertWrapper from "~/components/shared/alert-wrapper";
import SortableColumnHeader from "~/components/shared/sortable-column-header";
import { useSheet } from "~/hooks/use-sheet";
import { scraphubParamName } from "~/lib/constants";
import { useTRPC } from "~/trpc/react";
import ScraphubFormSheet from "./scraphubs-form-sheet";

const ScraphubsDataTable = () => {
  const trpc = useTRPC();
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState({});

  const { openSheet } = useSheet(scraphubParamName);

  const { data: scraphubs = [], refetch } = useQuery(
    trpc.scraphub.getAllScraphubs.queryOptions(),
  );

  const deleteScraphub = useMutation(
    trpc.scraphub.deleteScraphub.mutationOptions({
      onSuccess: (opts) => {
        toast.success(opts.message);
        void refetch();
      },
      onError: (error) => {
        toast.error(error.message);
      },
    }),
  );

  const columns: ColumnDef<(typeof scraphubs)[number]>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "name",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Scraphub Name" />
      ),
      cell: ({ row }) => (
        <Link
          href={`/manage-scraphubs/${row.original.id}`}
          className="font-medium"
        >
          {row.getValue("name")}
        </Link>
      ),
    },
    {
      accessorKey: "phoneNumber",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Phone Number" />
      ),
      cell: ({ row }) => <div>{row.original.phoneNumber ?? "N/A"}</div>,
    },
    {
      accessorKey: "address.display",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Display Address" />
      ),
      cell: ({ row }) => <div>{row.original.address?.display ?? "N/A"}</div>,
    },
    {
      accessorKey: "address.city",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="City" />
      ),
      cell: ({ row }) => <div>{row.original.address?.city ?? "N/A"}</div>,
    },
    {
      accessorKey: "address.state",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="State" />
      ),
      cell: ({ row }) => <div>{row.original.address?.state ?? "N/A"}</div>,
    },
    {
      accessorKey: "address.coordinates",
      header: "Location",
      cell: ({ row }) => {
        const coordinates = row.original.address?.coordinates;
        if (!coordinates) return <div>N/A</div>;

        return (
          <Dialog>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm">
                <MapPin className="mr-2 h-4 w-4" />
                View Location
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Location Details</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <strong>Coordinates:</strong> {coordinates.latitude},{" "}
                  {coordinates.longitude}
                </div>
                <div>
                  <strong>Full Address:</strong>{" "}
                  {row.original.address?.localAddress}
                </div>
                <div>
                  <strong>Landmark:</strong> {row.original.address?.landmark}
                </div>
                {coordinates.latitude && coordinates.longitude && (
                  <a
                    href={`https://www.google.com/maps?q=${coordinates.latitude},${coordinates.longitude}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center text-blue-600 hover:underline"
                  >
                    <MapPin className="mr-1 h-4 w-4" />
                    Open in Google Maps
                  </a>
                )}
              </div>
            </DialogContent>
          </Dialog>
        );
      },
    },
    {
      accessorKey: "address.createdAt",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Created" />
      ),
      cell: ({ row }) => {
        const date = row.original.address?.createdAt;
        return <div>{date ? new Date(date).toLocaleDateString() : "N/A"}</div>;
      },
    },
    {
      id: "actions",
      enableHiding: false,
      cell: ({ row }) => {
        const scraphub = row.original;
        return (
          <div className="flex gap-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                <DropdownMenuItem asChild>
                  <Link href={`/manage-scraphubs/${scraphub.id}`}>
                    <ExternalLink className="mr-2 h-4 w-4" />
                    View
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => openSheet(scraphub.id)}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  className="text-red-600"
                  onClick={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                  }}
                >
                  <AlertWrapper
                    trigger={
                      <button
                        className="flex items-center gap-2"
                        onClick={(e) => {
                          e.stopPropagation();
                          e.preventDefault();
                        }}
                      >
                        <Trash className="size-4" /> Delete
                      </button>
                    }
                    onConfirm={() =>
                      deleteScraphub.mutate({ scraphubId: scraphub.id })
                    }
                    title="Are you sure you want to delete this scraphub?"
                    description="This action cannot be undone."
                  />
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        );
      },
    },
  ];

  const table = useReactTable({
    data: scraphubs,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  return (
    <div className="w-full">
      <div className="flex items-center justify-between py-4">
        <Input
          placeholder="Filter by name..."
          value={table.getColumn("name")?.getFilterValue() as string}
          onChange={(event) =>
            table.getColumn("name")?.setFilterValue(event.target.value)
          }
          className="max-w-sm"
        />
        <div className="flex gap-2">
          <ScraphubFormSheet />
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                Columns <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {table
                .getAllColumns()
                .filter((column) => column.getCanHide())
                .map((column) => {
                  return (
                    <DropdownMenuCheckboxItem
                      key={column.id}
                      className="capitalize"
                      checked={column.getIsVisible()}
                      onCheckedChange={(value) =>
                        column.toggleVisibility(!!value)
                      }
                    >
                      {column.id}
                    </DropdownMenuCheckboxItem>
                  );
                })}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id} className="whitespace-nowrap">
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext(),
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody className="relative text-start">
            {table.getRowModel().rows.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell
                      className={cn(
                        cell.column.id === "actions" &&
                          "sticky right-0 backdrop-blur-3xl",
                      )}
                      key={cell.id}
                    >
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-end space-x-2 py-4">
        <div className="flex-1 text-sm text-muted-foreground">
          {table.getFilteredSelectedRowModel().rows.length} of{" "}
          {table.getFilteredRowModel().rows.length} row(s) selected.
        </div>
        <div className="space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ScraphubsDataTable;
