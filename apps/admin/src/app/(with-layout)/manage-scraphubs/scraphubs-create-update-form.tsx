"use client";

import type { z } from "zod";
import { useEffect, useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  skipToken,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";
import { Loader2 } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

import { Button } from "@acme/ui/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@acme/ui/components/ui/form";
import GoogleAutocompleteInput from "@acme/ui/components/ui/google-autocomplete-input";
import { Input } from "@acme/ui/components/ui/input";

import { useSheet } from "~/hooks/use-sheet";
import { scraphubParamName } from "~/lib/constants";
import { ScraphubSchema } from "~/lib/zod-schema";
import { useTRPC } from "~/trpc/react";
import ScraphubMap from "./scraphub-map";

type ScraphubFormValues = z.infer<typeof ScraphubSchema>;

// Default coordinates (you can change this to your preferred default location)
const DEFAULT_COORDINATES = { latitude: 28.6139, longitude: 77.209 }; // Delhi, India

export function ScraphubsCreateUpdateForm() {
  const trpc = useTRPC();
  const trpcUtils = useQueryClient();
  const { paramValue: scraphubId, closeSheet } = useSheet(scraphubParamName);
  const isEditMode = !!scraphubId;

  const [autocompleteInput, setAutocompleteInput] = useState<string | null>(
    null,
  );

  const { data: scraphubData, isLoading } = useQuery(
    trpc.scraphub.getScraphubById.queryOptions(
      scraphubId ? { scraphubId: scraphubId } : skipToken,
    ),
  );

  const form = useForm<ScraphubFormValues>({
    resolver: zodResolver(ScraphubSchema),
    defaultValues: scraphubData
      ? {
          name: scraphubData.name,
          phoneNumber: scraphubData.phoneNumber ?? "",
          address: {
            display: scraphubData.address?.display ?? "",
            street: scraphubData.address?.street ?? "",
            city: scraphubData.address?.city ?? "",
            state: scraphubData.address?.state ?? "",
            country: scraphubData.address?.country ?? "",
            postalCode: scraphubData.address?.postalCode ?? "",
            coordinates:
              scraphubData.address?.coordinates ?? DEFAULT_COORDINATES,
            localAddress: scraphubData.address?.localAddress ?? "",
            landmark: scraphubData.address?.landmark ?? "",
          },
        }
      : {
          name: "",
          phoneNumber: "",
          address: {
            display: "",
            street: "",
            city: "",
            state: "",
            country: "",
            postalCode: "",
            coordinates: DEFAULT_COORDINATES,
            localAddress: "",
            landmark: "",
          },
        },
  });

  const createScraphub = useMutation(
    trpc.scraphub.createScraphub.mutationOptions({
      onSuccess: (opts) => {
        toast.success(opts.message);
        void trpcUtils.invalidateQueries({
          queryKey: trpc.scraphub.getAllScraphubs.queryKey(),
        });
        closeSheet();
      },
      onError: (error) => {
        toast.error(error.message);
      },
    }),
  );

  const updateScraphub = useMutation(
    trpc.scraphub.updateScraphub.mutationOptions({
      onSuccess: (opts) => {
        toast.success(opts.message);
        void trpcUtils.invalidateQueries({
          queryKey: trpc.scraphub.getAllScraphubs.queryKey(),
        });
        closeSheet();
      },
      onError: (error) => {
        toast.error(error.message);
      },
    }),
  );

  const onSubmit = (data: ScraphubFormValues) => {
    if (isEditMode) {
      updateScraphub.mutate({ ...data, scraphubId: scraphubId });
    } else {
      createScraphub.mutate(data);
    }
  };

  useEffect(() => {
    if (isEditMode && scraphubData) {
      form.reset({
        name: scraphubData.name,
        phoneNumber: scraphubData.phoneNumber ?? "",
        address: {
          display: scraphubData.address?.display ?? "",
          street: scraphubData.address?.street ?? "",
          city: scraphubData.address?.city ?? "",
          state: scraphubData.address?.state ?? "",
          country: scraphubData.address?.country ?? "",
          postalCode: scraphubData.address?.postalCode ?? "",
          coordinates: scraphubData.address?.coordinates ?? {
            latitude: 0,
            longitude: 0,
          },
          localAddress: scraphubData.address?.localAddress ?? "",
          landmark: scraphubData.address?.landmark ?? "",
        },
      });
    }
  }, [isEditMode, scraphubData, form]);

  const selectedLocationCoordinates = form.watch("address.coordinates");

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Scraphub Name</FormLabel>
              <FormControl>
                <Input placeholder="Enter scraphub name" {...field} />
              </FormControl>
              <FormDescription>The name of the scraphub.</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="phoneNumber"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Phone Number</FormLabel>
              <FormControl>
                <Input placeholder="Enter phone number" {...field} />
              </FormControl>
              <FormDescription>
                The contact phone number for the scraphub.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="space-y-4">
          <h3 className="text-lg font-medium">Address Details</h3>

          <GoogleAutocompleteInput
            showSearchIcon
            showAutoDetectLocationIcon
            placeholder="Search for location"
            initialValue={autocompleteInput ?? ""}
            showClearButton
            onInputChange={(value) => {
              setAutocompleteInput(value);
            }}
            onLocationSelect={(location) => {
              if (location.address?.display) {
                form.setValue("address.display", location.address.display);
              }
              if (location.address?.street) {
                form.setValue("address.street", location.address.street);
              }
              if (location.address?.city) {
                form.setValue("address.city", location.address.city);
              }
              if (location.address?.state) {
                form.setValue("address.state", location.address.state);
              }
              if (location.address?.country) {
                form.setValue("address.country", location.address.country);
              }
              if (location.address?.postalCode) {
                form.setValue(
                  "address.postalCode",
                  location.address.postalCode,
                );
              }
              form.setValue("address.coordinates", {
                latitude: location.latitude,
                longitude: location.longitude,
              });
            }}
            onUserLocationDetect={(location) => {
              if (location.address?.display) {
                form.setValue("address.display", location.address.display);
              }
              if (location.address?.street) {
                form.setValue("address.street", location.address.street);
              }
              if (location.address?.city) {
                form.setValue("address.city", location.address.city);
              }
              if (location.address?.state) {
                form.setValue("address.state", location.address.state);
              }
              if (location.address?.country) {
                form.setValue("address.country", location.address.country);
              }
              if (location.address?.postalCode) {
                form.setValue(
                  "address.postalCode",
                  location.address.postalCode,
                );
              }
              form.setValue("address.coordinates", {
                latitude: location.latitude,
                longitude: location.longitude,
              });
            }}
          />

          <ScraphubMap
            selectedLocationCoordinates={selectedLocationCoordinates}
          />

          <FormField
            control={form.control}
            name="address.localAddress"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Street/ Block number</FormLabel>
                <FormControl>
                  <Input placeholder="eg: Block Ab" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="address.landmark"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Landmark/ Road/ Area/ Locality</FormLabel>
                <FormControl>
                  <Input placeholder="eg: Nearby xyz." {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="flex gap-2">
          <Button
            type="submit"
            disabled={createScraphub.isPending || updateScraphub.isPending}
          >
            {createScraphub.isPending || updateScraphub.isPending ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {isEditMode ? "Updating..." : "Creating..."}
              </>
            ) : (
              <>{isEditMode ? "Update Scraphub" : "Create Scraphub"}</>
            )}
          </Button>
          <Button type="button" variant="outline" onClick={closeSheet}>
            Cancel
          </Button>
        </div>
      </form>
    </Form>
  );
}

export default ScraphubsCreateUpdateForm;
