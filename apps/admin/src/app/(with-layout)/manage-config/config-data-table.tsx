"use client";

import type { ColumnDef } from "@tanstack/react-table";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import {
  flexRender,
  getCoreRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { Edit, MoreHorizontal, Trash } from "lucide-react";
import { toast } from "sonner";

import { Button } from "@acme/ui/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@acme/ui/components/ui/dropdown-menu";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@acme/ui/components/ui/table";

import { useSheet } from "~/hooks/use-sheet";
import { configParamName } from "~/lib/constants";
import { lexicalJsonToPlainText } from "~/lib/utils-client";
import { useTRPC } from "~/trpc/react";
import ConfigFormSheet from "./config-form-sheet";

const formatKeyLabel = (key: string) =>
  key
    .replace(/_/g, " ")
    .toLowerCase()
    .replace(/\b\w/g, (l) => l.toUpperCase());

const ConfigDataTable = () => {
  const trpc = useTRPC();
  const trpcUtils = useQueryClient();
  const { openSheet } = useSheet(configParamName);

  const { data: configs = [] } = useQuery(
    trpc.config.getAllConfigs.queryOptions(),
  );

  const deleteConfig = useMutation(
    trpc.config.deleteConfig.mutationOptions({
      onSuccess: () => {
        toast.success("Config deleted successfully");
        void trpcUtils.invalidateQueries({
          queryKey: trpc.config.getAllConfigs.queryKey(),
        });
      },
      onError: (error) => {
        toast.error(error.message);
      },
    }),
  );

  const columns: ColumnDef<(typeof configs)[number]>[] = [
    {
      accessorKey: "key",
      header: "Config",
      cell: ({ row }) => (
        <span className="font-mono">{formatKeyLabel(row.getValue("key"))}</span>
      ),
    },
    {
      accessorKey: "value",
      header: "Config Value",
      cell: ({ row }) => {
        const key = row.original.key;
        const value = row.getValue("value");
        if (key === "TERMS_AND_CONDITIONS" || key === "PRIVACY_POLICY") {
          return (
            <span className="block max-w-[300px] truncate">
              {lexicalJsonToPlainText(value as string)}
            </span>
          );
        }
        return (
          <span className="block max-w-[300px] truncate">
            {value as string}
          </span>
        );
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button size="icon" variant="ghost" aria-label="Actions">
              <MoreHorizontal className="size-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => openSheet(row.original.id)}>
              <Edit className="mr-2 size-4" /> Edit
            </DropdownMenuItem>
            <DropdownMenuItem
              className="text-red-600 focus:text-red-600"
              onClick={() => deleteConfig.mutate({ id: row.original.id })}
            >
              <Trash className="mr-2 size-4" /> Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      ),
    },
  ];

  const table = useReactTable({
    data: configs,
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  return (
    <div>
      <div className="mb-4 flex justify-end">
        <ConfigFormSheet />
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {flexRender(
                      header.column.columnDef.header,
                      header.getContext(),
                    )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow key={row.id}>
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="text-center">
                  No configs found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};

export default ConfigDataTable;
