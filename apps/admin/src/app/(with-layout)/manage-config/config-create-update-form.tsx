"use client";

import { useEffect, useMemo } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { Loader2 } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";

// Import enum and validators
import { systemConfigurationEnum } from "@acme/db/schema";
import { Button } from "@acme/ui/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@acme/ui/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@acme/ui/components/ui/select";
import { emailSchema, phoneNumberSchema } from "@acme/validators";

import { RichTextInput } from "~/components/shared/rich-text-input";
import { useSheet } from "~/hooks/use-sheet";
import { configParamName } from "~/lib/constants";
import { useTRPC } from "~/trpc/react";
import { EmailInput } from "./email-input";
import { NumberInput } from "./number-input";
import { PhoneInput } from "./phone-input";

const keyOptions = systemConfigurationEnum.enumValues.map((key) => ({
  label: key
    .replace(/_/g, " ")
    .toLowerCase()
    .replace(/\b\w/g, (l) => l.toUpperCase()),
  value: key,
}));

// const configValueSchemaByKey = (
//   key: (typeof systemConfigurationEnum.enumValues)[number],
// ) => {
//   switch (key) {
//     case "CONTACT_EMAIL":
//     case "SUPPORT_EMAIL":
//       return emailSchema;
//     case "CONTACT_PHONE":
//     case "SUPPORT_PHONE":
//       return phoneNumberSchema;
//     case "TERMS_AND_CONDITIONS":
//     case "PRIVACY_POLICY":
//       return z.string().min(1, "Field is required");
//     default:
//       // Require at least 1 character, then transform to number
//       return z
//         .string()
//         .min(1, "Field is required")
//         .refine((val) => !isNaN(Number(val)), {
//           message: "Must be a valid number",
//         })
//         .transform((val) => Number(val));
//   }
// };

// const friendlyEmailSchema = emailSchema.or(
//   z.string().email("Please enter a valid email address."),
// );
// const friendlyPhoneSchema = phoneNumberSchema.or(
//   z.string().min(10, "Please enter a valid phone number."),
// );

const ConfigFormSchema = z
  .object({
    key: z.enum(systemConfigurationEnum.enumValues),
    value: z.string(),
  })
  .refine(
    (data) => {
      const value = data.value;
      const key = data.key;
      if (key === "CONTACT_EMAIL" || key === "SUPPORT_EMAIL") {
        return emailSchema.safeParse(value).success;
      }
      if (key === "CONTACT_PHONE" || key === "SUPPORT_PHONE") {
        return phoneNumberSchema.safeParse(value).success;
      }
      if (
        key === "TERMS_AND_CONDITIONS" ||
        key === "PRIVACY_POLICY" ||
        key === "ABOUT_US" ||
        key === "CANCELLATION_POLICY" ||
        key === "REFUND_POLICY"
      ) {
        return typeof value === "string" && value.length > 0;
      }
      // All others must be a valid number and required
      return z
        .string()
        .min(1, "This field is required.")
        .refine((val) => !isNaN(Number(val)), {
          message: "Please enter a valid number.",
        })
        .safeParse(value).success;
    },
    {
      path: ["value"],
    },
  );

type ConfigFormValues = z.infer<typeof ConfigFormSchema>;

interface Config {
  id: string;
  key: (typeof systemConfigurationEnum.enumValues)[number];
  value: string;
}

export function ConfigCreateUpdateForm() {
  const trpc = useTRPC();
  const trpcUtils = useQueryClient();
  const { paramValue: configId, closeSheet } = useSheet(configParamName);
  const isEditMode = !!configId;

  // Use getConfigById in edit mode, getAllConfigs otherwise
  const { data: editingConfigData, isLoading: isDataLoading } = useQuery(
    trpc.config.getConfigById.queryOptions(
      { id: configId ?? "" },
      { enabled: isEditMode },
    ),
  );

  // For edit mode, editingConfig is the single config; for create, undefined
  const editingConfig: Config | undefined = isEditMode
    ? ((editingConfigData as Config | null) ?? undefined)
    : undefined;

  // Fetch all configs to filter out used keys
  const { data: allConfigs, isLoading: isAllConfigsLoading } = useQuery(
    trpc.config.getAllConfigs.queryOptions(),
  );

  // Compute available keys for creation (exclude already used keys)
  const availableKeyOptions = useMemo(() => {
    if (isEditMode) return keyOptions;
    const usedKeys = new Set(
      (allConfigs ?? []).filter((c) => c.key !== null).map((c) => c.key),
    );
    return keyOptions.filter((opt) => !usedKeys.has(opt.value));
  }, [isEditMode, allConfigs]);

  const getSafeKey = (
    key: unknown,
  ): (typeof systemConfigurationEnum.enumValues)[number] => {
    return (
      typeof key === "string" &&
      (systemConfigurationEnum.enumValues as readonly string[]).includes(key)
        ? key
        : "RADIUS_TO_SHOW_KABADIWALA_NEW_ORDER"
    ) as (typeof systemConfigurationEnum.enumValues)[number];
  };

  const form = useForm<ConfigFormValues>({
    resolver: zodResolver(ConfigFormSchema),
    defaultValues: editingConfig
      ? {
          key: getSafeKey(editingConfig.key),
          value: editingConfig.value,
        }
      : { key: "RADIUS_TO_SHOW_KABADIWALA_NEW_ORDER", value: "" },
  });

  const createConfig = useMutation(
    trpc.config.createConfig.mutationOptions({
      onSuccess: () => {
        toast.success("Config created successfully");
        void trpcUtils.invalidateQueries({
          queryKey: trpc.config.getAllConfigs.queryKey(),
        });
        closeSheet();
      },
      onError: (error) => {
        toast.error(error.message);
      },
    }),
  );

  const updateConfig = useMutation(
    trpc.config.updateConfig.mutationOptions({
      onSuccess: () => {
        toast.success("Config updated successfully");
        void trpcUtils.invalidateQueries({
          queryKey: trpc.config.getAllConfigs.queryKey(),
        });
        closeSheet();
      },
      onError: (error) => {
        toast.error(error.message);
      },
    }),
  );

  const selectedKey = form.watch("key");

  const onSubmit = (data: ConfigFormValues) => {
    if (isEditMode && editingConfig) {
      updateConfig.mutate({ id: editingConfig.id, value: data.value });
    } else {
      createConfig.mutate({ key: data.key, value: data.value });
    }
  };

  useEffect(() => {
    if (isEditMode && editingConfig) {
      form.reset({
        key: editingConfig.key,
        value: editingConfig.value,
      });
    }
  }, [editingConfig, form, isEditMode]);

  if (isDataLoading || isAllConfigsLoading) {
    return <div>Loading...</div>;
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <FormField
          control={form.control}
          name="key"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Config</FormLabel>
              <Select
                value={field.value}
                onValueChange={(val) => {
                  field.onChange(val);
                  form.setValue("value", "");
                }}
                disabled={isEditMode}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select key" />
                </SelectTrigger>
                <SelectContent>
                  {availableKeyOptions.map((opt) => (
                    <SelectItem key={opt.value} value={opt.value}>
                      {opt.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="value"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Config Value</FormLabel>
              <FormControl>
                {selectedKey === "TERMS_AND_CONDITIONS" ||
                selectedKey === "PRIVACY_POLICY" ||
                selectedKey === "ABOUT_US" ||
                selectedKey === "CANCELLATION_POLICY" ||
                selectedKey === "REFUND_POLICY" ? (
                  <RichTextInput
                    value={field.value}
                    onChange={field.onChange}
                  />
                ) : selectedKey.endsWith("EMAIL") ? (
                  <EmailInput {...field} />
                ) : selectedKey.endsWith("PHONE") ? (
                  <PhoneInput {...field} />
                ) : (
                  <NumberInput {...field} />
                )}
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button
          type="submit"
          disabled={createConfig.isPending || updateConfig.isPending}
        >
          {(createConfig.isPending || updateConfig.isPending) && (
            <Loader2 className="mr-2 size-4 animate-spin" />
          )}
          {isEditMode ? "Update Config" : "Create Config"}
        </Button>
      </form>
    </Form>
  );
}
