import type { SearchParams } from "nuqs/server";
import { Suspense } from "react";

import { Separator } from "@acme/ui/components/ui/separator";

import DataTableSkeleton from "~/components/skeleton/data-table-skeleton";
import { loadConfigSearchParams } from "~/lib/nuqs-utils";
import { getQueryClient, HydrateClient, trpc } from "~/trpc/server";
import ConfigDataTable from "./config-data-table";

export const metadata = {
  title: "Config Management",
  description: "Manage system configuration key-value pairs.",
};
interface PageProps {
  searchParams: Promise<SearchParams>;
}
const ManageConfigPage = async ({ searchParams }: PageProps) => {
  const { config_id } = await loadConfigSearchParams(searchParams);
  const queryClient = getQueryClient();
  await queryClient.prefetchQuery(trpc.config.getAllConfigs.queryOptions());
  await queryClient.prefetchQuery(
    trpc.config.getConfigById.queryOptions({ id: config_id }),
  );

  return (
    <HydrateClient>
      <div>
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Config Management
            </h1>
            <p className="text-muted-foreground">
              Manage system configuration.
            </p>
          </div>
        </div>
        <Separator className="my-6" />
        <Suspense fallback={<DataTableSkeleton />}>
          <ConfigDataTable />
        </Suspense>
      </div>
    </HydrateClient>
  );
};

export default ManageConfigPage;
