"use client";

import { PlusIcon } from "lucide-react";

import { But<PERSON> } from "@acme/ui/components/ui/button";

import { GenericSheet } from "~/components/shared/generic-sheet";
import { useSheet } from "~/hooks/use-sheet";
import { configParamName } from "~/lib/constants";
import { ConfigCreateUpdateForm } from "./config-create-update-form";

const ConfigFormSheet = () => {
  const { isOpen, paramValue, openSheet, closeSheet } =
    useSheet(configParamName);

  return (
    <div>
      <Button onClick={() => openSheet()}>
        <PlusIcon className="size-4" />
        Create Config
      </Button>
      <GenericSheet
        isOpen={isOpen}
        onClose={closeSheet}
        title={paramValue ? "Edit Config" : "Create Config"}
        className="md:min-w-full md:max-w-full 2xl:max-w-[900px]"
      >
        <ConfigCreateUpdateForm />
      </GenericSheet>
    </div>
  );
};

export default ConfigFormSheet;
