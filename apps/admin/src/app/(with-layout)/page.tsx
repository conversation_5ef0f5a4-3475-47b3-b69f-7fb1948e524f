import { Suspense } from "react";

import { Skeleton } from "@acme/ui/components/ui/skeleton";

import { DashboardOverview } from "./dashboard-overview";
import StatsRangePicker from "./stats-range-picker";

// Next.js 15: accept async searchParams
interface DashboardPageProps {
  searchParams: Promise<{ "start-date"?: string; "end-date"?: string }>;
}

const DashboardPage = async ({ searchParams }: DashboardPageProps) => {
  const params = await searchParams;
  const startDate = params["start-date"];
  let endDate = params["end-date"];
  // If endDate is not provided, use today's date (dd-mm-yyyy)
  if (!endDate) {
    const today = new Date();
    const pad = (n: number) => n.toString().padStart(2, "0");
    endDate = `${pad(today.getDate())}-${pad(today.getMonth() + 1)}-${today.getFullYear()}`;
  }

  return (
    <div className="space-y-8 p-6">
      <div className="flex flex-row justify-between">
        <div>
          <h1 className="text-3xl font-bold">Dashboard</h1>
          <p className="text-muted-foreground">
            Overview of your platform's performance and statistics
          </p>
        </div>
        <div className="self-end">
          Filter Stats by Range
          <StatsRangePicker />
        </div>
      </div>
      <Suspense fallback={<DashboardLoadingSkeleton />}>
        {/* Pass startDate and endDate as props */}
        <DashboardOverview startDate={startDate} endDate={endDate} />
      </Suspense>
    </div>
  );
};

const DashboardLoadingSkeleton = () => (
  <div className="space-y-6">
    <div>
      <Skeleton className="mb-4 h-6 w-32" />
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <div key={i} className="space-y-2 rounded-lg border p-6">
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-8 w-16" />
            <Skeleton className="h-3 w-32" />
          </div>
        ))}
      </div>
    </div>
    <div>
      <Skeleton className="mb-4 h-6 w-32" />
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
        {Array.from({ length: 5 }).map((_, i) => (
          <div key={i} className="space-y-2 rounded-lg border p-6">
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-8 w-16" />
            <Skeleton className="h-3 w-32" />
          </div>
        ))}
      </div>
    </div>
  </div>
);

export default DashboardPage;
