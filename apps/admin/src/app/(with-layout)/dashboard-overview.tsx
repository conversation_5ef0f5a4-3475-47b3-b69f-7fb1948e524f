import {
  Calendar,
  CheckCircle,
  Clock,
  Loader2,
  MessageCircle,
  Package,
  ShoppingBag,
  Star,
  Users,
} from "lucide-react";

import { getQueryClient, trpc } from "~/trpc/server";
import { StatsCard } from "./stats-card";

// Accept startDate and endDate as props
interface DashboardOverviewProps {
  startDate?: string;
  endDate?: string;
}

export const DashboardOverview = async ({
  startDate,
  endDate,
}: DashboardOverviewProps) => {
  const queryClient = getQueryClient();

  // Pass startDate and endDate to all queries
  const stats = await queryClient.fetchQuery(
    trpc.dashboard.getStats.queryOptions({ startDate, endDate }),
  );
  // Support chat stats
  const supportChatStats = await queryClient.fetchQuery(
    trpc.dashboard.getSupportChatStats.queryOptions({ startDate, endDate }),
  );
  const sellerSupportStats = await queryClient.fetchQuery(
    trpc.dashboard.getSellerSupportChatStats.queryOptions({
      startDate,
      endDate,
    }),
  );
  const kabadiwalaSupportStats = await queryClient.fetchQuery(
    trpc.dashboard.getKabadiwalaSupportChatStats.queryOptions({
      startDate,
      endDate,
    }),
  );

  return (
    <div className="flex flex-col space-y-6">
      {/* Date Range Filter */} {/* Users Section */}
      <div>
        <h2 className="mb-4 text-lg font-semibold">Users Overview</h2>
        <div className="grid gap-4 md:grid-cols-2 xl:grid-cols-4">
          <StatsCard
            title="Total Sellers"
            value={stats.users.totalSellers}
            icon={<Users className="h-5 w-5" />}
            description="Registered sellers on platform"
          />
          <StatsCard
            title="Total Kabadiwalas"
            value={stats.users.totalKabadiwalas}
            icon={<Users className="h-5 w-5" />}
            description="Registered kabadiwalas on platform"
          />
          <StatsCard
            title="Verified Sellers"
            value={stats.users.verifiedSellers}
            icon={<CheckCircle className="h-5 w-5" />}
            description="Email & phone verified sellers"
            className="border-green-200 bg-green-50"
          />
          <StatsCard
            title="Verified Kabadiwalas"
            value={stats.users.verifiedKabadiwalas}
            icon={<CheckCircle className="h-5 w-5" />}
            description="Email & phone verified kabadiwalas"
            className="border-green-200 bg-green-50"
          />
        </div>
      </div>
      {/* Orders Section */}
      <div>
        <h2 className="mb-4 text-lg font-semibold">Orders Overview</h2>
        <div className="grid gap-4 md:grid-cols-2 xl:grid-cols-5">
          <StatsCard
            title="Active Orders"
            value={stats.orders.active}
            icon={<ShoppingBag className="h-5 w-5" />}
            description="Currently active orders"
            className="border-blue-200 bg-blue-50"
          />
          <StatsCard
            title="Pending Orders"
            value={stats.orders.pending}
            icon={<Clock className="h-5 w-5" />}
            description="Orders awaiting pickup"
            className="border-yellow-200 bg-yellow-50"
          />
          <StatsCard
            title="Completed Orders"
            value={stats.orders.completed}
            icon={<CheckCircle className="h-5 w-5" />}
            description="Successfully completed orders"
            className="border-green-200 bg-green-50"
          />
          <StatsCard
            title="Today's Orders"
            value={stats.orders.today}
            icon={<Calendar className="h-5 w-5" />}
            description="Orders placed today"
          />
          <StatsCard
            title="This Week"
            value={stats.orders.thisWeek}
            icon={<Calendar className="h-5 w-5" />}
            description="Orders this week"
          />
        </div>
      </div>
      {/* Categories Section */}
      <div>
        <h2 className="mb-4 text-lg font-semibold">Categories</h2>
        <div className="grid max-w-sm gap-4 md:grid-cols-2 lg:grid-cols-1">
          <StatsCard
            title="Active Categories"
            value={stats.categories.total}
            icon={<Package className="h-5 w-5" />}
            description="Available categories for orders"
            className="border-purple-200 bg-purple-50"
          />
        </div>
      </div>
      {/* Support Chats Section */}
      <div>
        <h2 className="mb-4 text-lg font-semibold">Support Chats Overview</h2>
        <div className="mb-6 grid gap-4 md:grid-cols-2 xl:grid-cols-3">
          <StatsCard
            title="Total Support Chats"
            value={supportChatStats.total}
            icon={<MessageCircle className="h-5 w-5" />}
            description="All support chats (seller + kabadiwala)"
          />
          <StatsCard
            title="Total Rated Chats"
            value={supportChatStats.totalRated}
            icon={<Star className="h-5 w-5" />}
            description="Chats with rating (excluding skipped)"
          />
          <StatsCard
            title="Average Rating"
            value={supportChatStats.averageRating.toFixed(2)}
            icon={<Star className="h-5 w-5 text-yellow-500" />}
            description="Average rating (excluding skipped)"
          />
        </div>
        <div>
          <h2 className="mb-4 text-lg font-semibold">Seller Support Chats</h2>
          <div className="grid gap-4 md:grid-cols-2 xl:grid-cols-4">
            <StatsCard
              title="Total Seller Chats"
              value={sellerSupportStats.total}
              icon={<MessageCircle className="h-5 w-5" />}
              description="Total seller support chats"
            />
            <StatsCard
              title="Average Rating"
              value={sellerSupportStats.averageRating.toFixed(2)}
              icon={<Star className="h-5 w-5 text-yellow-500" />}
              description="Average rating (excluding skipped)"
            />
            <StatsCard
              title="Closed Chats"
              value={sellerSupportStats.closed}
              icon={<CheckCircle className="h-5 w-5 text-green-500" />}
              description="Closed seller support chats"
            />
            <StatsCard
              title="Ongoing Chats"
              value={sellerSupportStats.ongoing}
              icon={<Loader2 className="h-5 w-5 animate-spin text-blue-500" />}
              description="Ongoing seller support chats"
            />
          </div>
        </div>
        <div>
          <h2 className="mb-4 text-lg font-semibold">
            Kabadiwala Support Chats
          </h2>
          <div className="grid gap-4 md:grid-cols-2 xl:grid-cols-4">
            <StatsCard
              title="Total Kabadiwala Chats"
              value={kabadiwalaSupportStats.total}
              icon={<MessageCircle className="h-5 w-5" />}
              description="Total kabadiwala support chats"
            />
            <StatsCard
              title="Average Rating"
              value={kabadiwalaSupportStats.averageRating.toFixed(2)}
              icon={<Star className="h-5 w-5 text-yellow-500" />}
              description="Average rating (excluding skipped)"
            />
            <StatsCard
              title="Closed Chats"
              value={kabadiwalaSupportStats.closed}
              icon={<CheckCircle className="h-5 w-5 text-green-500" />}
              description="Closed kabadiwala support chats"
            />
            <StatsCard
              title="Ongoing Chats"
              value={kabadiwalaSupportStats.ongoing}
              icon={<Loader2 className="h-5 w-5 animate-spin text-blue-500" />}
              description="Ongoing kabadiwala support chats"
            />
          </div>
        </div>
      </div>
    </div>
  );
};
