import "@acme/ui/globals.css";

import type { Metadata, Viewport } from "next";
import { Poppins } from "next/font/google";

// import localFont from "next/font/local";

import { GoogleMapsProvider } from "@acme/ui/context/google-maps-provider";

import SidebarProviderWrapper from "~/components/shared/sidebar-provider";
import { env } from "~/env";
import OneSignalProvider from "~/providers/onesignal-provider";
import { TRPCReactProvider } from "~/trpc/react";
import { HydrateClient } from "~/trpc/server";

export const metadata: Metadata = {
  title: "Scraplo Admin",
  description: "Scraplo admin pannel",
  openGraph: {
    title: "Scraplo Admin",
    description: "Scraplo admin pannel",
  },
  icons: [{ rel: "icon", url: "/favicon.png" }],
};

export const viewport: Viewport = {
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "white" },
    { media: "(prefers-color-scheme: dark)", color: "black" },
  ],
};

const poppins = Poppins({
  subsets: ["latin"],
  variable: "--font-poppins",
  display: "swap",
  weight: ["100", "200", "300", "400", "500", "600", "700", "800", "900"],
});

export default function RootLayout(props: { children: React.ReactNode }) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${poppins.className} min-h-screen bg-green-600 text-foreground antialiased`}
      >
        <TRPCReactProvider>
          <OneSignalProvider>
            <SidebarProviderWrapper>
              <GoogleMapsProvider apiKey={env.NEXT_PUBLIC_GOOGLE_MAPS_KEY}>
                <HydrateClient>{props.children}</HydrateClient>
              </GoogleMapsProvider>
            </SidebarProviderWrapper>
          </OneSignalProvider>
        </TRPCReactProvider>
      </body>
    </html>
  );
}
