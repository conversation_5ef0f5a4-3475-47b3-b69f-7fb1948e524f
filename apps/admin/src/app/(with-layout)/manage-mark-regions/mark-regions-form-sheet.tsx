"use client";

import { PlusIcon } from "lucide-react";

import { But<PERSON> } from "@acme/ui/components/ui/button";

import { GenericSheet } from "~/components/shared/generic-sheet";
import { useSheet } from "~/hooks/use-sheet";
import { markRegionParamName } from "~/lib/constants";
import MarkRegionsCreateUpdateForm from "./mark-regions-create-update-form";

const MarkRegionsFormSheet = () => {
  const { isOpen, paramValue, openSheet, closeSheet } =
    useSheet(markRegionParamName);

  return (
    <div>
      <Button onClick={() => openSheet()}>
        <PlusIcon className="size-4" />
        Create Region
      </Button>
      <GenericSheet
        isOpen={isOpen}
        onClose={closeSheet}
        title={paramValue ? "Edit Region" : "Create Region"}
        className="md:min-w-[600px] lg:min-w-[700px] 2xl:min-w-[800px]"
      >
        <MarkRegionsCreateUpdateForm />
      </GenericSheet>
    </div>
  );
};

export default MarkRegionsFormSheet;
