"use client";

import type { z } from "zod";
import { useEffect, useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  skipToken,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query";
import { useMap } from "@vis.gl/react-google-maps";
import { Loader2 } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

import DrawableGoogleMaps from "@acme/ui/components/google/drawable-google-maps";
import { Button } from "@acme/ui/components/ui/button";
import { Checkbox } from "@acme/ui/components/ui/checkbox";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@acme/ui/components/ui/form";
import GoogleAutocompleteInput from "@acme/ui/components/ui/google-autocomplete-input";
import { Input } from "@acme/ui/components/ui/input";

import { useSheet } from "~/hooks/use-sheet";
import { markRegionParamName } from "~/lib/constants";
import { MarkRegionSchema } from "~/lib/zod-schema";
import { useTRPC } from "~/trpc/react";

type MarkRegionFormValues = z.infer<typeof MarkRegionSchema>;

const MarkRegionsCreateUpdateForm = () => {
  const { closeSheet, paramValue } = useSheet(markRegionParamName);
  const isUpdateMode = !!paramValue;

  const map = useMap();
  const [searchMapCoords, setSearchMapCoords] = useState<{
    lat: number;
    lng: number;
  }>({ lat: 0, lng: 0 });

  const form = useForm<MarkRegionFormValues>({
    resolver: zodResolver(MarkRegionSchema),
    defaultValues: {
      name: "",
      isActive: true,
      markersLatLng: [],
    },
  });

  const trpc = useTRPC();
  const queryClient = useQueryClient();
  const { mutate: createRegion, isPending: isPendingCreateRegion } =
    useMutation(trpc.region.create.mutationOptions());

  const { mutate: updateRegion, isPending: isPendingUpdateRegion } =
    useMutation(trpc.region.update.mutationOptions());

  const { data: regionData, isLoading } = useQuery(
    trpc.region.byId.queryOptions(paramValue ? { id: paramValue } : skipToken, {
      enabled: paramValue ? true : false,
    }),
  );

  const markersLatLng = form.watch("markersLatLng");

  const onSubmit = (data: MarkRegionFormValues) => {
    if (isPendingCreateRegion || isPendingUpdateRegion) return;

    if (isUpdateMode && paramValue) {
      updateRegion(
        { ...data, id: paramValue },
        {
          onSuccess: (opts) => {
            void queryClient.invalidateQueries(trpc.region.all.queryOptions());
            toast.success(opts.message);
            closeSheet();
          },
          onError: (error) => {
            toast.error(error.message);
          },
        },
      );
    } else {
      createRegion(data, {
        onSuccess: (opts) => {
          void queryClient.invalidateQueries(trpc.region.all.queryOptions());
          toast.success(opts.message);
          closeSheet();
        },
        onError: (error) => {
          toast.error(error.message);
        },
      });
    }
  };

  useEffect(() => {
    if (regionData) {
      form.reset({
        name: regionData.name,
        isActive: regionData.isActive,
        markersLatLng: regionData.markers,
      });
    }
  }, [regionData, form]);

  useEffect(() => {
    if (!map) return;

    if (searchMapCoords.lat && searchMapCoords.lng) {
      map.moveCamera({
        center: {
          lat: searchMapCoords.lat,
          lng: searchMapCoords.lng,
        },
        zoom: 12,
      });
    }
  }, [map, searchMapCoords]);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="space-y-6">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Region Name</FormLabel>
                <FormControl>
                  <Input placeholder="Enter region name" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="isActive"
            render={({ field }) => (
              <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel>Active Region</FormLabel>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          <GoogleAutocompleteInput
            showSearchIcon
            showAutoDetectLocationIcon
            placeholder="Search for location"
            onLocationSelect={(location) => {
              setSearchMapCoords({
                lat: location.latitude,
                lng: location.longitude,
              });
            }}
            onUserLocationDetect={(location) => {
              setSearchMapCoords({
                lat: location.latitude,
                lng: location.longitude,
              });
            }}
          />

          <FormField
            control={form.control}
            name="markersLatLng"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Markers</FormLabel>
                <FormControl>
                  <div className="space-y-2">
                    <DrawableGoogleMaps
                      onOverlaysChange={(e) => {
                        field.onChange(e);
                      }}
                      initialPolygonCoords={regionData?.markers}
                    />
                    {markersLatLng.length > 0 && (
                      <p className="text-sm text-muted-foreground">
                        {markersLatLng.length} point
                        {markersLatLng.length !== 1 ? "s" : ""} added
                      </p>
                    )}
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="flex gap-2">
            <Button type="submit" disabled={form.formState.isSubmitting}>
              {form.formState.isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {isUpdateMode ? "Updating..." : "Creating..."}
                </>
              ) : isUpdateMode ? (
                "Update Mark Region"
              ) : (
                "Create Mark Region"
              )}
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                form.reset();
              }}
            >
              Reset
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default MarkRegionsCreateUpdateForm;
