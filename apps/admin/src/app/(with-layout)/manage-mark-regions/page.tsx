import type { SearchParams } from "nuqs/server";
import { Suspense } from "react";

import { Separator } from "@acme/ui/components/ui/separator";

import DataTableSkeleton from "~/components/skeleton/data-table-skeleton";
import { loadRegionSearchParams } from "~/lib/nuqs-utils";
import { getQueryClient, HydrateClient, trpc } from "~/trpc/server";
import RegionsDataTable from "./regions-data-table";

export const metadata = {
  title: "Regions Management",
  description: "Manage your website's regions",
};

interface ManageMarkRegionsProps {
  searchParams: Promise<SearchParams>;
}

const ManageMarkRegions = async ({ searchParams }: ManageMarkRegionsProps) => {
  const { region_id } = await loadRegionSearchParams(searchParams);
  const queryClient = getQueryClient();

  // Prefetch list data
  await queryClient.prefetchQuery(trpc.region.all.queryOptions());

  // Prefetch individual item data if ID exists
  if (region_id) {
    await queryClient.prefetchQuery(
      trpc.region.byId.queryOptions({ id: region_id }),
    );
  }

  return (
    <HydrateClient>
      <div>
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Regions Management
            </h1>
            <p className="text-muted-foreground">Manage Regions</p>
          </div>
        </div>
        <Separator className="my-6" />
        <Suspense fallback={<DataTableSkeleton />}>
          <RegionsDataTable />
        </Suspense>
      </div>
    </HydrateClient>
  );
};

export default ManageMarkRegions;
