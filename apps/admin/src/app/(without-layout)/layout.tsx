import "@acme/ui/globals.css";

import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON> } from "next/font/google";
import { redirect } from "next/navigation";

// import localFont from "next/font/local";

import { Toaster } from "@acme/ui/components/ui/sonner";

import { getSession } from "~/server/auth/get-session.rsc";
import { TRPCReactProvider } from "~/trpc/react";
import { HydrateClient } from "~/trpc/server";

export const metadata: Metadata = {
  title: "scraplo Admin",
  description: "scraplo admin pannel",
  openGraph: {
    title: "scraplo",
    description: "scraplo admin pannel",
  },
  icons: [{ rel: "icon", url: "/favicon.png" }],
};

const poppins = Poppins({
  subsets: ["latin"],
  variable: "--font-poppins",
  display: "swap",
  weight: ["100", "200", "300", "400", "500", "600", "700", "800", "900"],
});

export default async function RootLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  const data = await getSession();
  const user = data?.user;
  console.log("user in without ", user);

  const isAdmin = !!user;
  if (isAdmin) {
    redirect("/");
  }
  return (
    <html lang="en" className={`${poppins.className} `}>
      <body>
        <TRPCReactProvider>
          <HydrateClient>{children}</HydrateClient>
          <Toaster richColors />
        </TRPCReactProvider>
      </body>
    </html>
  );
}
