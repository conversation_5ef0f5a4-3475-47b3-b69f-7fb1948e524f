import { createLoader, parseAsString } from "nuqs/server";

import {
  adminParamName,
  blogParamName,
  categoryParamName,
  configParamName,
  employeeParamName,
  faqParamName,
  issueParamName,
  kabadiwalaParamName,
  markRegionParamName,
  orderFilterParamName,
  pricingByRegionParamName,
  scraphubParamName,
  sellerParamName,
  subCategoryParamName,
  testimonialParamName,
} from "./constants";

// Describe your search params, and reuse this in useQueryStates / createSerializer:
export const configSearchParams = {
  [configParamName]: parseAsString.withDefault(""),
};

// Category related search params
export const categorySearchParams = {
  [categoryParamName]: parseAsString.withDefault(""),
  [subCategoryParamName]: parseAsString.withDefault(""),
};

// User related search params
export const userSearchParams = {
  [adminParamName]: parseAsString.withDefault(""),
  [sellerParamName]: parseAsString.withDefault(""),
  [kabadiwalaParamName]: parseAsString.withDefault(""),
};

// Order related search params
export const orderSearchParams = {
  [orderFilterParamName]: parseAsString.withDefault(""),
};

// Content related search params
export const contentSearchParams = {
  [faqParamName]: parseAsString.withDefault(""),
  [blogParamName]: parseAsString.withDefault(""),
  [testimonialParamName]: parseAsString.withDefault(""),
};

// Issue related search params
export const issueSearchParams = {
  [issueParamName]: parseAsString.withDefault(""),
};

// Scraphub related search params
export const scraphubSearchParams = {
  [scraphubParamName]: parseAsString.withDefault(""),
};

// Employee related search params
export const employeeSearchParams = {
  [employeeParamName]: parseAsString.withDefault(""),
};

// Region and pricing related search params
export const regionSearchParams = {
  [markRegionParamName]: parseAsString.withDefault(""),
  [pricingByRegionParamName]: parseAsString.withDefault(""),
};

// All search params combined for comprehensive loading
export const allSearchParams = {
  ...configSearchParams,
  ...categorySearchParams,
  ...userSearchParams,
  ...orderSearchParams,
  ...contentSearchParams,
  ...issueSearchParams,
  ...scraphubSearchParams,
  ...employeeSearchParams,
  ...regionSearchParams,
};

// Create loader functions for each category
export const loadConfigSearchParams = createLoader(configSearchParams);
export const loadCategorySearchParams = createLoader(categorySearchParams);
export const loadUserSearchParams = createLoader(userSearchParams);
export const loadOrderSearchParams = createLoader(orderSearchParams);
export const loadContentSearchParams = createLoader(contentSearchParams);
export const loadIssueSearchParams = createLoader(issueSearchParams);
export const loadScraphubSearchParams = createLoader(scraphubSearchParams);
export const loadEmployeeSearchParams = createLoader(employeeSearchParams);
export const loadRegionSearchParams = createLoader(regionSearchParams);

// Main loader function that handles all search params
export const loadAllSearchParams = createLoader(allSearchParams);

// Legacy export for backward compatibility
export const loadSearchParams = loadConfigSearchParams;
