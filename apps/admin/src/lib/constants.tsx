import {
  IndianRupee,
  ListOrdered,
  MessageCircle,
  Recycle,
  Tags,
  Users,
} from "lucide-react";

export const REFETCH_TIME_INTERVAL_FOR_ORDER_DETAILS_PAGE = 10000; // 10 seconds
export const CHAT_REFETCH_TIME_INTERVAL = 10000; // 10 seconds

export const categoryParamName = "category_id";
export const subCategoryParamName = "add_sub_category";
export const adminParamName = "admin_id";
export const sellerParamName = "seller_id";
export const kabadiwalaParamName = "kabadiwala_id";
export const orderFilterParamName = "status";
export const faqParamName = "faq_id";
export const blogParamName = "blog_id";
export const testimonialParamName = "testimonial_id";
export const configParamName = "config_id";
export const issueParamName = "issue_id";
export const scraphubParamName = "scraphub_id";
export const markRegionParamName = "region_id";
export const pricingByRegionParamName = "region_category_pricing_id";
export const employeeParamName = "employee_id";

export const SIDEBAR_LINKS_1 = {
  navMain: [
    {
      title: "Content",
      url: "#",
      icon: Tags,
      isActive: true,
      items: [
        {
          title: "Blogs",
          url: "/manage-blogs",
        },
        {
          title: "Faqs",
          url: "/manage-faqs",
        },
        {
          title: "Categories",
          url: "/manage-categories",
        },
        {
          title: "Testimonials",
          url: "/manage-testimonials",
        },
        {
          title: "Config",
          url: "/manage-config",
        },
      ],
    },
    {
      title: "Orders",
      url: "#",
      icon: ListOrdered,
      items: [
        {
          title: "All Orders",
          url: "/manage-orders",
        },
      ],
    },
    {
      title: "Region & Pricing",
      url: "#",
      icon: IndianRupee,
      items: [
        {
          title: "Pricing",
          url: "/manage-pricing",
        },
        {
          title: "Mark Regions",
          url: "/manage-mark-regions",
        },
      ],
    },
    {
      title: "Users",
      url: "#",
      icon: Users,
      items: [
        {
          title: "Sellers",
          url: "/manage-sellers",
        },
        {
          title: "Kabadiwalas",
          url: "/manage-kabadiwalas",
        },
        {
          title: "Admins",
          url: "/manage-admins",
        },
        {
          title: "Delete Account Requests",
          url: "/manage-delete-account-requests",
        },
        {
          title: "Issues",
          url: "/manage-issues",
        },
      ],
    },
    {
      title: "Chats",
      url: "#",
      icon: MessageCircle,
      items: [
        {
          title: "Customer-Chats",
          url: "/manage-chats/customer",
        },
        {
          title: "Kabadiwala-Chats",
          url: "/manage-chats/kabadiwala",
        },
      ],
    },
    {
      title: "Scraphub",
      url: "#",
      icon: Recycle,
      items: [
        {
          title: "All Scraphubs",
          url: "/manage-scraphubs",
        },
      ],
    },
  ],
};
