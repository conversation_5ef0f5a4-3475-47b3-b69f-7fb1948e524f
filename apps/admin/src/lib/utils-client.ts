export interface LexicalNode {
  text?: string;
  children?: LexicalNode[];
}

export interface LexicalRoot {
  root: LexicalNode;
}

export function lexicalJsonToPlainText(json: string | LexicalRoot): string {
  try {
    const data: LexicalRoot =
      typeof json === "string" ? (JSON.parse(json) as LexicalRoot) : json;
    let text = "";
    const traverse = (node: LexicalNode): void => {
      if (node.text) text += node.text + " ";
      if (Array.isArray(node.children)) {
        node.children.forEach(traverse);
      }
    };
    traverse(data.root);
    return text.trim();
  } catch {
    return "";
  }
}
