import type { inferRouterOutputs } from "@trpc/server";

import type { RouterOutputs } from "~/server/api";
import type { AppRouter } from "~/server/api/root";

export type Status = "idle" | "loading" | "success" | "error";

export type OrderDetails = RouterOutputs["order"]["getOrderById"];

export interface DLAdvanceApiAddress {
  addressLine1: string;
  completeAddress: string;
  country: string;
  district: string;
  pin: string;
  state: string;
  type: string;
}

export interface DLAdvanceApiValidity {
  from: string;
  to: string;
}

export interface DLAdvanceApiStatusDetails {
  from: string;
  remarks: string;
  to: string;
}

export interface DLAdvanceApiVehicleCategoryDetail {
  cov: string;
  expiryDate: string;
  issueDate: string;
}

export interface DLAdvanceApiResult {
  user_address: DLAdvanceApiAddress[];
  user_blood_group: string;
  dl_number: string;
  user_dob: string;
  endorse_date: string;
  endorse_number: string;
  expiry_date: string;
  father_or_husband: string;
  issued_date: string;
  non_transport_validity: DLAdvanceApiValidity;
  state: string;
  status: string;
  status_details: DLAdvanceApiStatusDetails;
  transport_validity: DLAdvanceApiValidity;
  user_full_name: string;
  user_image: string;
  vehicle_category_details: DLAdvanceApiVehicleCategoryDetail[];
}

export interface DLAdvanceApiResponse {
  api_category: string;
  api_name: string;
  billable: boolean;
  txn_id: string;
  message: string;
  status: number;
  result: DLAdvanceApiResult | null;
  datetime: string;
}

// --- Support Conversation Types ---
export type KabadiwalaSupportConversation =
  inferRouterOutputs<AppRouter>["conversation"]["getAllKabadiwalaSupportConversations"][number];
export type CustomerSupportConversation =
  inferRouterOutputs<AppRouter>["conversation"]["getAllCustomerSupportConversations"][number];

export interface SupportMessage {
  id: string;
  senderId: string;
  content: string;
  // Add more fields if needed
}

export type PricingPeriod =
  RouterOutputs["pricing"]["getAllPricingPeriods"][number];
