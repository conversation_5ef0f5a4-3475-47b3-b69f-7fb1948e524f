# OneSignal Provider Component

This component provides OneSignal push notification integration for the admin application.

## Usage

### Basic Usage

Wrap your app with the OneSignalProvider in your layout:

```tsx
import OneSignalProvider from "~/components/shared/onesignal-provider";

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body>
        <OneSignalProvider>{children}</OneSignalProvider>
      </body>
    </html>
  );
}
```

### With Custom App ID

```tsx
<OneSignalProvider appId="your-custom-app-id">{children}</OneSignalProvider>
```

## Environment Variables

Add the following to your `.env` file:

```env
NEXT_PUBLIC_ONESIGNAL_ADMIN_APP_ID=your-onesignal-app-id
```

## Features

- Automatic initialization on client-side only
- Configurable app ID (via props or environment variable)
- Pre-configured notification button with custom text
- Localhost support for development
- Auto-registration and resubscription
- TypeScript support

## OneSignal Configuration

The component comes with sensible defaults:

- **Notify Button**: Enabled with custom text
- **Localhost Support**: Enabled for development
- **Auto Registration**: Enabled
- **Auto Resubscription**: Enabled

## Requirements

- `react-onesignal` package (already installed)
- OneSignal App ID from your OneSignal dashboard
