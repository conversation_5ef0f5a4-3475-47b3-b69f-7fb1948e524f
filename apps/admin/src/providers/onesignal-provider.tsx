"use client";

import { useEffect } from "react";
import OneSignal from "react-onesignal";

import { env } from "~/env";
import { useSession } from "~/server/auth/client";

export default function OneSignalProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  //   const trpc = useTRPC();
  //   const { data: defaultAdmin } = useQuery(
  //     trpc.admin.getDefaultAdmin.queryOptions(),
  //   );
  const { data } = useSession();
  useEffect(() => {
    // Ensure this code runs only on the client side
    if (data?.user) {
      if (typeof window !== "undefined") {
        OneSignal.init({
          appId: env.NEXT_PUBLIC_ONESIGNAL_ADMIN_APP_ID,
          //   safari_web_id: env.NEXT_PUBLIC_ONESIGNAL_ADMIN_APP_SAFARI_WEB_ID,
          notifyButton: {
            enable: true,
            prenotify: true,
            showCredit: false,
            text: {
              "tip.state.unsubscribed": "Subscribe to notifications",
              "tip.state.subscribed": "You are subscribed to notifications",
              "tip.state.blocked": "You have blocked notifications",
              "message.prenotify": "Click to subscribe to notifications",
              "message.action.subscribing": "Subscribing...",
              "message.action.subscribed": "Thanks for subscribing!",
              "message.action.resubscribed":
                "You are now subscribed to notifications",
              "message.action.unsubscribed":
                "You will no longer receive notifications",
              "dialog.main.title": "Manage your notification settings",
              "dialog.main.button.subscribe": "SUBSCRIBE",
              "dialog.main.button.unsubscribe": "UNSUBSCRIBE",
              "dialog.blocked.title": "Unblock Notifications",
              "dialog.blocked.message":
                "Follow these instructions to allow notifications:",
            },
          },
          allowLocalhostAsSecureOrigin: true,
          autoRegister: true,
          autoResubscribe: true,
        })
          .then(async () => {
            console.log("OneSignal initialized");
            await OneSignal.login(data.user.id);
            console.log("one signal logged in");
          })
          .catch(async (error) => {
            console.error("OneSignal initialization failed", error);
            await OneSignal.logout();
          });
      }
    }
  }, [data?.user]);

  return <>{children}</>;
}
