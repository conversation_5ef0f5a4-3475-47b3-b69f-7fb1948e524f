import { createEnv } from "@t3-oss/env-nextjs";
import { vercel } from "@t3-oss/env-nextjs/presets-zod";
import { z } from "zod";

import { env as mailEnv } from "@acme/mail/env";

export const env = createEnv({
  extends: [mailEnv, vercel()],
  shared: {
    NODE_ENV: z
      .enum(["development", "production", "test"])
      .default("development"),
  },
  /**
   * Specify your server-side environment variables schema here.
   * This way you can ensure the app isn't built with invalid env vars.
   */
  server: {
    POSTGRES_URL: z.string().url(),
    ADMIN_BETTER_AUTH_SECRET: z.string().nonempty(),
    UPLOADTHING_TOKEN: z.string().nonempty(),
    ONESIGNAL_ADMIN_KEY: z.string().nonempty(),
    RAZORPAY_KEY_ID: z.string().nonempty(),
    RAZORPAY_SECRET_KEY: z.string().nonempty(),
    RAZORPAYX_ACCOUNT_NUMBER: z.string().nonempty(),
    // RESEND_EMAIL_FROM: z.string().min(1),
    // RESEND_API_KEY: z.string().min(1),
    // BYPASS_RESEND_OTP: z
    //   .string()
    //   .optional()
    //   .transform((val) => val === "true"),
  },

  /**
   * Specify your client-side environment variables schema here.
   * For them to be exposed to the client, prefix them with `NEXT_PUBLIC_`.
   */
  client: {
    // NEXT_PUBLIC_CLIENTVAR: z.string(),
    NEXT_PUBLIC_ADMIN_BETTER_AUTH_URL: z.string().nonempty(),
    NEXT_PUBLIC_GOOGLE_MAPS_KEY: z.string().nonempty(),
    NEXT_PUBLIC_ONESIGNAL_ADMIN_APP_ID: z.string().nonempty(),
    NEXT_PUBLIC_ONESIGNAL_ADMIN_APP_SAFARI_WEB_ID: z.string().nonempty(),
  },
  /**
   * Destructure all variables from `process.env` to make sure they aren't tree-shaken away.
   */
  experimental__runtimeEnv: {
    NODE_ENV: process.env.NODE_ENV,
    NEXT_PUBLIC_ADMIN_BETTER_AUTH_URL:
      process.env.NEXT_PUBLIC_ADMIN_BETTER_AUTH_URL,
    NEXT_PUBLIC_GOOGLE_MAPS_KEY: process.env.NEXT_PUBLIC_GOOGLE_MAPS_KEY,
    NEXT_PUBLIC_ONESIGNAL_ADMIN_APP_ID:
      process.env.NEXT_PUBLIC_ONESIGNAL_ADMIN_APP_ID,
    NEXT_PUBLIC_ONESIGNAL_ADMIN_APP_SAFARI_WEB_ID:
      process.env.NEXT_PUBLIC_ONESIGNAL_ADMIN_APP_SAFARI_WEB_ID,
  },
  skipValidation:
    !!process.env.CI || process.env.npm_lifecycle_event === "lint",
});
