{"name": "@acme/tailwind-config", "version": "0.1.0", "private": true, "type": "module", "exports": {"./native": "./native.ts", "./web": "./web.ts", "./postcss.config": "./postcss.config.mjs"}, "license": "MIT", "scripts": {"clean": "git clean -xdf .cache .turbo node_modules", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "typecheck": "tsc --noEmit"}, "dependencies": {"postcss": "^8.5.3", "tailwindcss": "catalog:", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@acme/eslint-config": "workspace:*", "@acme/prettier-config": "workspace:*", "@acme/tsconfig": "workspace:*", "@types/node": "^22.14.1", "eslint": "catalog:", "prettier": "catalog:", "typescript": "catalog:"}, "prettier": "@acme/prettier-config"}