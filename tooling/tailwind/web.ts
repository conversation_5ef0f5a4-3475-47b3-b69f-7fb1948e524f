import type { Config } from "tailwindcss";
import animate from "tailwindcss-animate";

import base from "./base";

export default {
  presets: [base],
  content: [
    "./pages/**/*.{ts,tsx,jsx,js}",
    "./components/**/*.{ts,tsx,jsx,js}",
    "./app/**/*.{ts,tsx,jsx,js}",
    "./src/**/*.{ts,tsx,js,jsx}",
    "../../packages/ui/src/**/*.{ts,tsx,jsx,js}",
  ],
  theme: {
    container: {
      padding: {
        xs: "1.5rem",
        sm: "1.5rem",
        md: "2.5rem",
        lg: "3.125rem",
        xl: "5rem",
        "2xl": "5rem",
      },
    },
    extend: {
      screens: {
        xs: "320px",
        sm: "430px",
        md: "768px",
        lg: "1024px",
        xl: "1336px",
        "2xl": "1920px",
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        sidebar: {
          DEFAULT: "hsl(var(--sidebar-background))",
          foreground: "hsl(var(--sidebar-foreground))",
          primary: "hsl(var(--sidebar-primary))",
          "primary-foreground": "hsl(var(--sidebar-primary-foreground))",
          accent: "hsl(var(--sidebar-accent))",
          "accent-foreground": "hsl(var(--sidebar-accent-foreground))",
          border: "hsl(var(--sidebar-border))",
          ring: "hsl(var(--sidebar-ring))",
        },
        teal: {
          950: "#00191A",
          900: "#003333",
          850: "#004C4D",
          800: "#006666",
          750: "#007F80",
          700: "#009999",
          650: "#00B2B3",
          600: "#00CCCC",
          550: "#00E5E6",
          500: "#00FFFF",
          450: "#1AFFFF",
          400: "#33FFFF",
          350: "#4DFFFF",
          300: "#66FFFF",
          250: "#80FFFF",
          200: "#99FFFF",
          150: "#B3FFFF",
          100: "#CCFFFF",
          50: "#E5FFFF",
        },
        black: {
          900: "#0A0A0A",
          800: "#1F1F1F",
          700: "#333333",
          600: "#454545",
          500: "#666666",
          400: "#808080",
          300: "#999999",
          250: "#B3B3B3",
          200: "#CCCCCC",
          150: "#DEDEDE",
          100: "#E6E6E6",
          50: "#F2F2F2",
          0: "#F8F8F8",
          "00": "#FBFBFB",
        },
        yellow: {
          950: "#191500",
          900: "#322901",
          850: "#4B3E01",
          800: "#645302",
          750: "#7D6702",
          700: "#967C03",
          650: "#AF9103",
          600: "#C8A504",
          550: "#E2BA04",
          500: "#FBCF04",
          450: "#FBD41D",
          400: "#FBD837",
          350: "#FCDD50",
          300: "#FCE269",
          250: "#FDE782",
          200: "#FDEC9B",
          150: "#FEF1B4",
          100: "#FEF5CD",
          50: "#FFFAE6",
          0: "#FFFCF2",
          "00": "#FFFEF9",
        },
      },
      keyframes: {
        "accordion-down": {
          from: {
            height: "0",
          },
          to: {
            height: "var(--radix-accordion-content-height)",
          },
        },
        "accordion-up": {
          from: {
            height: "var(--radix-accordion-content-height)",
          },
          to: {
            height: "0",
          },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
      fontFamily: {
        jakarta: ["var(--font-jakarta)"],
        inter: ["var(--font-inter)"],
      },
    },
  },
  plugins: [animate],
} satisfies Config;
