{"name": "@acme/eslint-config", "private": true, "version": "0.3.0", "type": "module", "exports": {"./base": "./base.js", "./nextjs": "./nextjs.js", "./react": "./react.js"}, "scripts": {"clean": "git clean -xdf .cache .turbo node_modules", "format": "prettier --check . --ignore-path ../../.gitignore", "typecheck": "tsc --noEmit"}, "dependencies": {"@eslint/compat": "^1.2.8", "@next/eslint-plugin-next": "^15.2.3", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^6.0.0-rc.1", "eslint-plugin-turbo": "^2.5.1", "typescript-eslint": "^8.31.0"}, "devDependencies": {"@acme/prettier-config": "workspace:*", "@acme/tsconfig": "workspace:*", "eslint": "catalog:", "prettier": "catalog:", "typescript": "catalog:"}, "prettier": "@acme/prettier-config"}