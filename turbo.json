{"$schema": "https://turborepo.org/schema.json", "ui": "tui", "tasks": {"topo": {"dependsOn": ["^topo"]}, "build": {"dependsOn": ["^build"], "outputs": [".cache/tsbuildinfo.json", "dist/**"]}, "dev": {"dependsOn": ["^dev"], "cache": false, "persistent": false}, "format": {"outputs": [".cache/.prettiercache"], "outputLogs": "new-only"}, "lint": {"dependsOn": ["^topo", "^build"], "outputs": [".cache/.eslintcache"]}, "typecheck": {"dependsOn": ["^topo", "^build"], "outputs": [".cache/tsbuildinfo.json"]}, "clean": {"cache": false}, "//#clean": {"cache": false}, "push": {"cache": false, "interactive": true}, "studio": {"cache": false, "persistent": true}, "ui-add": {"cache": false, "interactive": true}}, "globalEnv": ["POSTGRES_URL", "AUTH_REDIRECT_PROXY_URL", "PORT"], "globalPassThroughEnv": ["NODE_ENV", "CI", "VERCEL", "VERCEL_ENV", "VERCEL_URL", "DATABASE_URL", "UPLOADTHING_TOKEN", "BYPASS_RESEND_OTP", "RESEND_EMAIL_FROM", "RESEND_API_KEY", "ADMIN_BETTER_AUTH_SECRET", "SELLER_BETTER_AUTH_SECRET", "KABADIWALA_BETTER_AUTH_SECRET", "RAZORPAY_KEY_ID", "RAZORPAY_SECRET_KEY", "ONE_SIGNAL_APP_ID", "ONE_SIGNAL_AUTH_KEY", "NEXT_PUBLIC_ONESIGNAL_ADMIN_APP_ID", "NEXT_PUBLIC_ONESIGNAL_ADMIN_APP_SAFARI_WEB_ID", "ENABLE_RAZORPAY_FUND_ACCOUNT_VERIFICATION", "GETSTREAM_APP_ID", "NEXT_PUBLIC_GETSTREAM_API_KEY", "GETSTREAM_API_SECRET", "NEXT_PUBLIC_ONESIGNAL_ADMIN_APP_ID", "NEXT_PUBLIC_ONESIGNAL_ADMIN_APP_SAFARI_WEB_ID", "ONESIGNAL_ADMIN_KEY", "NEXT_PUBLIC_ONESIGNAL_CUSTOMER_APP_ID", "NEXT_PUBLIC_ONESIGNAL_CUSTOMER_APP_SAFARI_WEB_ID", "ONESIGNAL_CUSTOMER_KEY", "NEXT_PUBLIC_ONESIGNAL_KABADIWALA_APP_ID", "ONESIGNAL_KABADIWALA_KEY", "NEXT_PUBLIC_POSTHOG_KEY", "NEXT_PUBLIC_POSTHOG_HOST", "SCRAPHUB_EMPLOYEE_BETTER_AUTH_SECRET", "RAZORPAYX_ACCOUNT_NUMBER", "npm_lifecycle_event"]}