packages:
  - apps/*
  - packages/*
  - tooling/*
catalog:
  '@auth/core': 0.37.2
  '@auth/drizzle-adapter': ~1.7.4
  date-fns: ^4.1.0
  next-auth: 5.0.0-beta.25
  eslint: ^9.25.1
  prettier: ^3.5.3
  tailwindcss: ^3.4.15
  typescript: ^5.8.3
  zod: ^3.24.3
  '@tanstack/react-query': ^5.69.0
  '@trpc/client': ^11.1.0
  '@trpc/tanstack-react-query': ^11.1.0
  '@trpc/server': ^11.1.0
catalogs:
  react19:
    react: 19.0.0
    react-dom: 19.0.0
    '@types/react': 19.0.12
    '@types/react-dom': 19.0.4
onlyBuiltDependencies:
  - '@firebase/util'
  - core-js
  - core-js-pure
  - esbuild
  - msgpackr-extract
  - protobufjs
  - sharp
