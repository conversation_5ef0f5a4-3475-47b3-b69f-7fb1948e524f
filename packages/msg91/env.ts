import { createEnv } from "@t3-oss/env-nextjs";
import { z } from "zod";

export const env = createEnv({
  server: {
    MESSAGE91_AUTH_KEY: z.string().min(1, "MESSAGE91_AUTH_KEY is required"),
    MESSAGE91_TEMPLATE_ID: z
      .string()
      .min(1, "MESSAGE91_TEMPLATE_ID is required"),
    MESSAGE91_OTP_EXPIRY: z.coerce.number().min(1).default(10),
    MESSAGE91_SENDER_ID: z
      .string()
      .min(1, "MESSAGE91_SENDER_ID is required")
      .default("RGSUPL"),
    DAILY_OTP_LIMIT: z.coerce.number().min(1).default(4),
  },
  client: {},
  experimental__runtimeEnv: {},
  skipValidation:
    !!process.env.CI || process.env.npm_lifecycle_event === "lint",
});
