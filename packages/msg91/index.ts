import axios from "axios";

import { env } from "./env";

// Export env for use in other packages

interface ISuccessResponse {
  request_id: string;
  type: "success";
}

interface IFailureResponse {
  message: string;
  type: "error";
}

type IResponse = ISuccessResponse | IFailureResponse;

interface ISendSms {
  receiversPhoneNumber: string;
  otp?: string;
}

export const sendSms = async ({ receiversPhoneNumber, otp }: ISendSms) => {
  // Test account sendSms
  if (receiversPhoneNumber === "**********") {
    return true;
  }
  try {
    const response = await axios.post<IResponse>(
      `https://control.msg91.com/api/v5/otp?otp_expiry=&template_id=${env.MESSAGE91_TEMPLATE_ID}&mobile=91${receiversPhoneNumber}&authkey=${env.MESSAGE91_AUTH_KEY}${otp && `&otp=${otp}`}&realTimeResponse=1`,
      {},
      {
        headers: {
          "Content-Type": "application/json",
        },
      },
    );
    console.log("response", response.data);
    if (response.data.type === "success") {
      console.log("Email OTP response:", response.data);
    }
    return response.data.type === "success";
    // return true;
  } catch (err) {
    console.error("Error while sending OTP", JSON.stringify(err));
    return false;
  }
};

export const retryOTP = async (receiversPhoneNumber: string) => {
  // Test account retryOTP
  if (receiversPhoneNumber === "**********") {
    return { success: true };
  }
  try {
    const response = await axios.get<IResponse>(
      `https://control.msg91.com/api/v5/otp/retry?authkey=${env.MESSAGE91_AUTH_KEY}&retrytype=&mobile=91${receiversPhoneNumber}`,
      {
        headers: {
          "Content-Type": "application/json",
        },
      },
    );
    console.log("response in msg 91 api--", response.data);

    if (response.data.type === "success") {
      return { success: true };
    } else {
      // Return error details for better error handling
      return {
        success: false,
        error: response.data.message || "Unknown error",
      };
    }
  } catch (err) {
    console.error("Error while retrying OTP", JSON.stringify(err));
    return {
      success: false,
      error: "Network error occurred while retrying OTP",
    };
  }
};

export const verifyOTP = async (otp: string, receiversPhoneNumber: string) => {
  if (receiversPhoneNumber === "**********") {
    return otp === "1238";
  }

  try {
    const response = await axios.get<{ type: "success" | "error" }>(
      `https://control.msg91.com/api/v5/otp/verify?mobile=91${receiversPhoneNumber}&otp=${otp}`,
      {
        headers: {
          "Content-Type": "application/json",
          authkey: env.MESSAGE91_AUTH_KEY,
        },
      },
    );
    console.log("response", response.data);
    return response.data.type === "success";
  } catch (err) {
    console.error("Error while sending OTP", JSON.stringify(err));
    return false;
  }
};
