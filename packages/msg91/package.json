{"name": "@acme/msg91", "version": "0.0.0", "license": "MIT", "main": "index.ts", "types": "index.ts", "scripts": {"lint": "eslint --fix"}, "dependencies": {"@t3-oss/env-core": "^0.13.0", "@t3-oss/env-nextjs": "^0.13.0", "axios": "^1.9.0", "zod": "catalog:"}, "devDependencies": {"@acme/tsconfig": "workspace:*", "eslint": "catalog:", "typescript": "catalog:"}, "exports": {".": "./index.ts", "./env": "./env.ts"}}