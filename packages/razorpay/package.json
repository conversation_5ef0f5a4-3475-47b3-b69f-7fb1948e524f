{"name": "@acme/razorpay-sdk", "version": "1.0.0", "description": "TypeScript SDK for Razorpay APIs (Contacts, Fund Accounts, and Payouts)", "main": "src/index.ts", "files": ["dist", "README.md"], "scripts": {"build": "tsc", "prepare": "npm run build", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["razorpay", "payments", "api", "typescript", "sdk"], "author": "", "license": "ISC", "dependencies": {"axios": "^1.9.0"}, "peerDependencies": {"typescript": "^5.0.0"}, "devDependencies": {"@types/node": "^22.14.1", "typescript": "catalog:"}}