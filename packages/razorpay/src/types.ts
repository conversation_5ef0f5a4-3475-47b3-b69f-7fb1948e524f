export interface RazorpayConfig {
  keyId: string;
  keySecret: string;
  baseURL?: string;
}

export interface RazorpayResponse<T> {
  data: T | null;
  isLoading: boolean;
  error: Error | null;
}

// Contact Types
export interface ContactRequest {
  name: string;
  email?: string;
  contact?: string;
  type: "employee" | "customer" | "vendor" | "self";
  reference_id?: string;
  notes?: Record<string, string | null>;
}

export interface ContactResponse extends ContactRequest {
  id: string;
  entity: string;
  active: boolean;
  created_at: number;
}

// Fund Account Types
export interface BankAccount {
  name: string;
  ifsc: string;
  bank_name: string;
  account_number: string;
  notes: unknown[];
}

export interface VPA {
  username: string;
  handle: string;
  address: string;
}

export interface Card {
  name: string;
  last4: string;
  network: string;
  type: string;
  issuer: string;
}

export interface FundAccountRequest {
  contact_id: string;
  account_type: "bank_account" | "vpa" | "card";
  bank_account?: BankAccount;
  vpa?: VPA;
  card?: Card;
}

export interface FundAccountResponse extends FundAccountRequest {
  id: string;
  entity: string;
  active: boolean;
  batch_id: string | null;
  created_at: number;
}

// Fund Account Update Types
export interface FundAccountUpdateRequest {
  active: boolean;
}

// Payout Types
export interface PayoutRequest {
  account_number: string;
  fund_account_id: string;
  amount: number;
  currency: string;
  mode: "IMPS" | "NEFT" | "RTGS";
  purpose: string;
  queue_if_low_balance?: boolean;
  reference_id?: string;
  narration?: string;
  notes?: Record<string, string>;
}

export interface ValidateFundAccount {
  account_number: string;
  fund_account: {
    id: string;
  };
  amount: number;
  currency?: string;
  notes?: Record<string, unknown>;
}

export interface ValidateFundAccountResponse {
  id: string;
  entity: string;
  fund_account: {
    id: string;
    entity: string;
    contact_id: string;
    account_type: string;
    bank_account: {
      ifsc: string;
      bank_name: string;
      name: string;
      account_number: string;
    };
    active: boolean;
  };
  batch_id?: string;
  created_at: number;
  status: "created" | "completed" | "failed";
  amount: number;
  currency?: string;
  notes?: Record<string, string>;
  results?: {
    account_status: "active" | "inactive" | "invalid";
    registered_name: string;
    created_at: number;
    utr: string;
  };
}

export interface PayoutResponse extends PayoutRequest {
  id: string;
  entity: string;
  status: string;
  utr?: string;
  fees: number;
  tax: number;
  created_at: number;
}

// Bill Types
export interface BillCustomer {
  name?: string;
  contact?: string;
  email?: string;
}

export interface BillLoyalty {
  points_earned?: number;
  points_redeemed?: number;
}

export interface BillEmployee {
  id: string;
  name?: string;
}

export interface BillAdditionalCharge {
  description: string;
  amount: number;
}

export interface BillDiscount {
  name: string;
  amount: number;
  percent: number;
}

export interface BillTax {
  name: string;
  percentage: number;
  amount: number;
}

export interface BillSubItem {
  name: string;
  quantity: number;
  unit_amount: number;
  total_amount: number;
  unit: string;
  employee_id?: string;
}

export interface BillLineItem {
  name: string;
  quantity: number;
  unit_amount: number;
  total_amount: number;
  unit: string;
  employee_id?: string;
  tags?: string[];
  sub_items?: BillSubItem[];
  taxes?: BillTax[];
}

export interface BillReceiptSummary {
  total_quantity: number;
  sub_total_amount: number;
  total_tax_amount: number;
  total_tax_percent: number;
  currency: string;
  net_payable_amount: number;
  additional_charges?: BillAdditionalCharge[];
  payment_status: "success" | "pending" | "failed";
  change_amount?: number;
  roundup_amount?: number;
  total_discount_percent?: number;
  total_discount_amount?: number;
  discounts?: BillDiscount[];
  used_wallet_amount?: number;
}

export interface BillPayment {
  amount: number;
  method: string;
  currency: string;
  reference?: string;
}

export interface BillEvent {
  name: string;
  date: number;
  venue?: string;
  description?: string;
}

export interface BillRequest {
  store_code?: string;
  business_type: "ecommerce" | "retail";
  business_category:
    | "events"
    | "food_and_beverages"
    | "retail_and_consumer_goods"
    | "other_services";
  customer?: BillCustomer;
  loyalty?: BillLoyalty;
  employee?: BillEmployee;
  receipt_timestamp: number;
  receipt_number: string;
  receipt_type: string;
  receipt_delivery: "digital" | "print" | "digital_and_print";
  tags?: string[];
  bar_code_number?: string;
  qr_code_number?: string;
  billing_pos_number?: string;
  pos_category?: string;
  order_number?: string;
  order_service_type?: "dine_in" | "take_away" | "delivery";
  delivery_status_url?: string;
  line_items?: BillLineItem[];
  receipt_summary: BillReceiptSummary;
  taxes?: BillTax[];
  payments?: BillPayment[];
  event?: BillEvent;
}

export interface BillResponse extends BillRequest {
  id: string;
  entity: string;
  created_at: number;
  status: string;
  bill_url?: string;
}

// For Backward Compatibility
export type Contact = ContactRequest;
export type FundAccount = FundAccountRequest;
export type Payout = PayoutRequest;
