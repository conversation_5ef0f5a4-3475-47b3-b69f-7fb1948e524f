import { RazorpayClient } from "./client";
import {
  BillRequest,
  BillResponse,
  ContactRequest,
  ContactResponse,
  FundAccountRequest,
  FundAccountResponse,
  FundAccountUpdateRequest,
  PayoutRequest,
  PayoutResponse,
  RazorpayConfig,
  RazorpayResponse,
  ValidateFundAccount,
  ValidateFundAccountResponse,
} from "./types";

export class Razorpay {
  private client: RazorpayClient;

  constructor(config: RazorpayConfig) {
    this.client = new RazorpayClient(config);
  }

  // Contacts API
  async createContact(
    contact: ContactRequest,
  ): Promise<RazorpayResponse<ContactResponse>> {
    try {
      const data = await this.client.request<ContactResponse>(
        "POST",
        "/contacts",
        contact,
      );
      return { data, isLoading: false, error: null };
    } catch (error) {
      return { data: null, isLoading: false, error: error as Error };
    }
  }

  async getContact(
    contactId: string,
  ): Promise<RazorpayResponse<ContactResponse>> {
    try {
      const data = await this.client.request<ContactResponse>(
        "GET",
        `/contacts/${contactId}`,
      );
      return { data, isLoading: false, error: null };
    } catch (error) {
      return { data: null, isLoading: false, error: error as Error };
    }
  }

  async listContacts(options?: {
    from?: number;
    to?: number;
    count?: number;
    skip?: number;
  }): Promise<
    RazorpayResponse<{
      entity: string;
      count: number;
      items: ContactResponse[];
    }>
  > {
    try {
      const data = await this.client.request<{
        entity: string;
        count: number;
        items: ContactResponse[];
      }>("GET", "/contacts", undefined, { params: options });
      return { data, isLoading: false, error: null };
    } catch (error) {
      return { data: null, isLoading: false, error: error as Error };
    }
  }

  async updateContact(
    contactId: string,
    updates: Partial<ContactRequest>,
  ): Promise<RazorpayResponse<ContactResponse>> {
    try {
      const data = await this.client.request<ContactResponse>(
        "PATCH",
        `/contacts/${contactId}`,
        updates,
      );
      return { data, isLoading: false, error: null };
    } catch (error) {
      return { data: null, isLoading: false, error: error as Error };
    }
  }

  // Fund Accounts API
  async createFundAccount(
    fundAccount: FundAccountRequest,
  ): Promise<RazorpayResponse<FundAccountResponse>> {
    try {
      const data = await this.client.request<FundAccountResponse>(
        "POST",
        "/fund_accounts",
        fundAccount,
      );
      return { data, isLoading: false, error: null };
    } catch (error) {
      return { data: null, isLoading: false, error: error as Error };
    }
  }

  async getFundAccount(
    fundAccountId: string,
  ): Promise<RazorpayResponse<FundAccountResponse>> {
    try {
      const data = await this.client.request<FundAccountResponse>(
        "GET",
        `/fund_accounts/${fundAccountId}`,
      );
      return { data, isLoading: false, error: null };
    } catch (error) {
      return { data: null, isLoading: false, error: error as Error };
    }
  }

  async listFundAccounts(contactId?: string): Promise<
    RazorpayResponse<{
      entity: string;
      count: number;
      items: FundAccountResponse[];
    }>
  > {
    try {
      const params = contactId ? { contact_id: contactId } : undefined;
      const data = await this.client.request<{
        entity: string;
        count: number;
        items: FundAccountResponse[];
      }>("GET", "/fund_accounts", undefined, { params });
      return { data, isLoading: false, error: null };
    } catch (error) {
      return { data: null, isLoading: false, error: error as Error };
    }
  }

  async deactivateFundAccount(
    fundAccountId: string,
  ): Promise<RazorpayResponse<FundAccountResponse>> {
    try {
      const updateData: FundAccountUpdateRequest = { active: false };
      const data = await this.client.request<FundAccountResponse>(
        "PATCH",
        `/fund_accounts/${fundAccountId}`,
        updateData,
      );
      return { data, isLoading: false, error: null };
    } catch (error) {
      return { data: null, isLoading: false, error: error as Error };
    }
  }

  async reactivateFundAccount(
    fundAccountId: string,
  ): Promise<RazorpayResponse<FundAccountResponse>> {
    try {
      const updateData: FundAccountUpdateRequest = { active: true };
      const data = await this.client.request<FundAccountResponse>(
        "PATCH",
        `/fund_accounts/${fundAccountId}`,
        updateData,
      );
      return { data, isLoading: false, error: null };
    } catch (error) {
      return { data: null, isLoading: false, error: error as Error };
    }
  }

  // Payouts API
  async createPayout(
    payout: PayoutRequest,
  ): Promise<RazorpayResponse<PayoutResponse>> {
    try {
      const data = await this.client.request<PayoutResponse>(
        "POST",
        "/payouts",
        payout,
      );
      return { data, isLoading: false, error: null };
    } catch (error) {
      return { data: null, isLoading: false, error: error as Error };
    }
  }

  // Bills API
  async createBill(bill: BillRequest): Promise<RazorpayResponse<BillResponse>> {
    try {
      const data = await this.client.request<BillResponse>(
        "POST",
        "/bills",
        bill,
      );
      return { data, isLoading: false, error: null };
    } catch (error) {
      return { data: null, isLoading: false, error: error as Error };
    }
  }

  async validateFundAccount(
    options: ValidateFundAccount,
  ): Promise<RazorpayResponse<ValidateFundAccountResponse>> {
    try {
      const data = await this.client.request<ValidateFundAccountResponse>(
        "POST",
        "/fund_accounts/validations",
        options,
      );
      return { data, isLoading: false, error: null };
    } catch (error) {
      return { data: null, isLoading: false, error: error as Error };
    }
  }

  async getPayout(payoutId: string): Promise<RazorpayResponse<PayoutResponse>> {
    try {
      const data = await this.client.request<PayoutResponse>(
        "GET",
        `/payouts/${payoutId}`,
      );
      return { data, isLoading: false, error: null };
    } catch (error) {
      return { data: null, isLoading: false, error: error as Error };
    }
  }

  async listPayouts(options?: {
    from?: number;
    to?: number;
    count?: number;
    skip?: number;
    fund_account_id?: string;
  }): Promise<
    RazorpayResponse<{ entity: string; count: number; items: PayoutResponse[] }>
  > {
    try {
      const data = await this.client.request<{
        entity: string;
        count: number;
        items: PayoutResponse[];
      }>("GET", "/payouts", undefined, { params: options });
      return { data, isLoading: false, error: null };
    } catch (error) {
      return { data: null, isLoading: false, error: error as Error };
    }
  }
}

export default Razorpay;
