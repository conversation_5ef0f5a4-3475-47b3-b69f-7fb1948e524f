import axios, { AxiosInstance, AxiosRequestConfig, Method } from "axios";
import { RazorpayConfig } from "./types";

export class RazorpayClient {
  private axiosInstance: AxiosInstance;
  private baseURL: string;

  constructor(config: RazorpayConfig) {
    this.baseURL = config.baseURL || "https://api.razorpay.com/v1";

    // Create base64 encoded auth string
    const auth = Buffer.from(`${config.keyId}:${config.keySecret}`).toString(
      "base64",
    );

    // Create axios instance with auth headers
    this.axiosInstance = axios.create({
      baseURL: this.baseURL,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Basic ${auth}`,
      },
    });
  }

  /**
   * Make a request to the Razorpay API
   * @param method HTTP method
   * @param endpoint API endpoint
   * @param data Request data (for POST, PUT, PATCH)
   * @param config Additional axios config
   * @returns Promise with response data
   */
  async request<T>(
    method: Method,
    endpoint: string,
    data?: any,
    config?: AxiosRequestConfig,
  ): Promise<T> {
    try {
      const response = await this.axiosInstance({
        method,
        url: endpoint,
        data,
        ...config,
      });

      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error) && error.response) {
        // Extract the error message from Razorpay response
        const message =
          error.response.data?.error?.description || error.message;
        const razorpayError = new Error(`Razorpay API Error: ${message}`);
        // Add the original error data for consumers
        (razorpayError as any).originalError = error.response.data;
        (razorpayError as any).razorpayError = true;
        throw razorpayError;
      }
      throw error;
    }
  }
}
