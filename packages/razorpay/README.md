# Razorpay TypeScript SDK

A TypeScript SDK for integrating with Razorpay APIs, focusing on Contacts, Fund Accounts, and Payouts.

## Installation

```bash
npm install @your-org/razorpay
# or
yarn add @your-org/razorpay
# or
pnpm add @your-org/razorpay
```

## Usage

### Initialize the client

```typescript
import { Razorpay } from "@your-org/razorpay";

const razorpay = new Razorpay({
  keyId: "YOUR_RAZORPAY_KEY_ID",
  keySecret: "YOUR_RAZORPAY_KEY_SECRET",
});
```

### Working with Contacts

```typescript
// Create a contact
const { data, isLoading, error } = await razorpay.createContact({
  name: "<PERSON>",
  email: "<EMAIL>",
  type: "customer",
});

// Get a contact
const contact = await razorpay.getContact("cont_123456789");

// List contacts
const contacts = await razorpay.listContacts({ count: 10 });

// Update a contact
const updatedContact = await razorpay.updateContact("cont_123456789", {
  name: "<PERSON>",
  email: "<EMAIL>",
});
```

### Working with Fund Accounts

```typescript
// Create a bank account fund account
const bankAccount = await razorpay.createFundAccount({
  contact_id: "cont_123456789",
  account_type: "bank_account",
  bank_account: {
    name: "John Doe",
    ifsc: "HDFC0000001",
    account_number: "**************",
  },
});

// Create a VPA fund account
const vpaAccount = await razorpay.createFundAccount({
  contact_id: "cont_123456789",
  account_type: "vpa",
  vpa: {
    address: "john@upi",
  },
});

// Get a fund account
const fundAccount = await razorpay.getFundAccount("fa_123456789");

// List fund accounts for a contact
const fundAccounts = await razorpay.listFundAccounts("cont_123456789");
```

### Working with Payouts

```typescript
// Create a payout
const payout = await razorpay.createPayout({
  account_number: "acc_123456789",
  fund_account_id: "fa_123456789",
  amount: 10000, // ₹100.00
  currency: "INR",
  mode: "IMPS",
  purpose: "refund",
});

// Get a payout
const payoutDetails = await razorpay.getPayout("pout_123456789");

// List payouts
const payouts = await razorpay.listPayouts({ count: 10 });

// List payouts for a specific fund account
const fundAccountPayouts = await razorpay.listPayouts({
  fund_account_id: "fa_123456789",
});
```

## Types

The SDK provides TypeScript type definitions for all the API requests and responses to ensure type safety.

## Error Handling

All methods return a consistent response structure with `data`, `isLoading`, and `error` properties, making it easy to handle errors in your application.

```typescript
const { data, error } = await razorpay.createContact({
  name: "John Doe",
  email: "<EMAIL>",
  type: "customer",
});

if (error) {
  console.error("Error creating contact:", error.message);
} else {
  console.log("Contact created:", data);
}
```

## License

ISC
