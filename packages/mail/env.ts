import { createEnv } from "@t3-oss/env-nextjs";
import { z } from "zod";

export const env = createEnv({
  server: {
    RESEND_EMAIL_FROM: z.string().min(1),
    RESEND_API_KEY: z.string().min(1),
    BYPASS_RESEND_OTP: z.enum(["true", "false"]).default("false"),
  },
  client: {},
  experimental__runtimeEnv: {},
  skipValidation:
    !!process.env.CI || process.env.npm_lifecycle_event === "lint",
});
