import { createEnv } from "@t3-oss/env-nextjs";
import { z } from "zod";

export const env = createEnv({
  server: {
    NODE_ENV: z.enum(["development", "production"]).optional(),
    KABADIWALA_BETTER_AUTH_SECRET: z.string().nonempty(),
    UPLOADTHING_TOKEN: z.string().nonempty(),
    GETSTREAM_API_KEY: z.string().nonempty(),
    GETSTREAM_API_SECRET: z.string().nonempty(),
  },
  client: {
    NEXT_PUBLIC_KABADIWALA_BETTER_AUTH_URL: z.string().nonempty(),
  },
  experimental__runtimeEnv: {
    NEXT_PUBLIC_KABADIWALA_BETTER_AUTH_URL:
      process.env.NEXT_PUBLIC_KABADIWALA_BETTER_AUTH_URL,
  },
  skipValidation:
    !!process.env.CI || process.env.npm_lifecycle_event === "lint",
});
