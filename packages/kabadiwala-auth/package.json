{"name": "@acme/kabadiwala-auth", "version": "0.1.0", "private": true, "license": "MIT", "type": "module", "exports": {".": {"default": "./src/index.ts"}, "./middleware": "./src/middleware.ts", "./client": "./src/client.ts", "./env": "./env.ts", "./constants": "./src/constant.ts"}, "scripts": {"clean": "git clean -xdf .cache .turbo dist node_modules", "format": "prettier --check . --ignore-path ../../.gitignore", "generate": "pnpx @better-auth/cli generate --output ../db/src/auth-schema.ts", "lint": "eslint", "typecheck": "tsc --noEmit", "with-env": "dotenv -e ../../.env --"}, "prettier": "@acme/prettier-config", "dependencies": {"@acme/db": "workspace:*", "@acme/msg91": "workspace:*", "@acme/validators": "workspace:*", "@auth/core": "0.34.2", "@auth/drizzle-adapter": "1.4.2", "@better-auth/expo": "1.2.10", "@stream-io/node-sdk": "^0.4.25", "@t3-oss/env-nextjs": "^0.13.0", "better-auth": "^1.2.10", "next": "^15.2.3", "react": "catalog:react19", "react-dom": "catalog:react19", "zod": "catalog:"}, "devDependencies": {"@acme/eslint-config": "workspace:*", "@acme/prettier-config": "workspace:*", "@acme/tsconfig": "workspace:*", "eslint": "catalog:", "prettier": "catalog:", "typescript": "catalog:"}}