import type { BetterAuthOptions } from "better-auth";
import { betterAuth } from "better-auth";
import { drizzleAdapter } from "better-auth/adapters/drizzle";
import { bearer, customSession, phoneNumber } from "better-auth/plugins";

import { eq } from "@acme/db";
import { db } from "@acme/db/client";
import * as schema from "@acme/db/schema";
import { sendSms } from "@acme/msg91";
import { tryCatch } from "@acme/validators/utils";

import { env } from "../env";
import {
  GET_STREAM_USER_AUTH_TOKEN_EXPIRATION_SECONDS,
  OTP_EXPIRATION_TIME_IN_SECONDS,
} from "./constant";
import { streamClient } from "./utils";

export const config = {
  database: drizzleAdapter(db, {
    provider: "pg",
    schema: schema,
  }),
  user: {
    modelName: "kabadiwala",
  },
  session: {
    modelName: "session",
    fields: {
      userId: "kabadiwalaId",
    },
  },
  account: {
    modelName: "account",
    fields: {
      userId: "kabadiwalaId",
    },
  },
  emailAndPassword: {
    enabled: true,
  },
  secret: env.KABADIWALA_BETTER_AUTH_SECRET,
  baseURL: env.NEXT_PUBLIC_KABADIWALA_BETTER_AUTH_URL,
  plugins: [
    bearer(),

    customSession(async ({ user, session }) => {
      const { data: kabadiwalaRecord, err: kabadiwalaErr } = await tryCatch(
        db.query.kabadiwala.findFirst({
          where: eq(schema.kabadiwala.id, user.id),
          columns: {
            isBlocked: true,
            onboardingCompleted: true,
          },
        }),
      );

      if (kabadiwalaErr || !kabadiwalaRecord) {
        return Promise.reject(new Error("User not found"));
      }

      if (kabadiwalaRecord.isBlocked) {
        return Promise.reject(
          new Error(
            "Your account is blocked, Please contact support to unblock your account.",
          ),
        );
      }

      const authToken = streamClient.generateUserToken({
        user_id: user.id,
        validity_in_seconds: GET_STREAM_USER_AUTH_TOKEN_EXPIRATION_SECONDS,
      });

      return {
        user: {
          ...user,
          onboardingCompleted: kabadiwalaRecord.onboardingCompleted,
          streamToken: authToken,
        },
        session,
      };
    }),
    phoneNumber({
      sendOTP: async ({ phoneNumber, code }) => {
        const { err: sendOtpError } = await tryCatch(
          sendSms({
            receiversPhoneNumber: phoneNumber,
            otp: code,
          }),
        );

        if (sendOtpError) {
          return Promise.reject(sendOtpError);
        }

        return Promise.resolve();
      },
      expiresIn: OTP_EXPIRATION_TIME_IN_SECONDS,
      signUpOnVerification: {
        // Actually the better-auth doesn't work without an email address so we need to provide a dummy email address
        // for the user. This is a workaround until we can use the email and password auth

        // NOTE: This is recommended by better-auth docs itself

        getTempEmail: (phoneNumber) => {
          return `${phoneNumber}@scraplo-dummy.com`;
        },
        getTempName: (phoneNumber) => {
          return phoneNumber;
        },
      },
      async callbackOnVerification(data) {
        // creating get stream user
        const userId = data.user.id;
        try {
          await streamClient.upsertUsers([
            {
              id: userId,
              name: data.user.name,
            },
          ]);
        } catch (error) {
          console.error("Error creating stream user:", error);
        }
      },
    }),
  ],
  trustedOrigins: ["exp://", "https://*.expo.dev", "localhost", "127.0.0.1"],
} satisfies BetterAuthOptions;

export const auth = betterAuth(config);
export type Session = typeof auth.$Infer.Session;
