import {
  customSessionClient,
  phoneNumberClient,
} from "better-auth/client/plugins";
import { createAuthClient } from "better-auth/react";

import type { auth } from "./auth";
import { env } from "../env";

export const authClient = createAuthClient({
  plugins: [phoneNumberClient(), customSessionClient<typeof auth>()],
  baseURL: env.NEXT_PUBLIC_KABADIWALA_BETTER_AUTH_URL,
});

export const { useSession } = authClient;
