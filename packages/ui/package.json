{"name": "@acme/ui", "version": "0.0.0", "private": true, "exports": {"./globals.css": "./src/globals.css", "./editor": "./src/rich-editor/editor.tsx", "./lib/*": "./src/lib/*.ts", "./cn": "./src/lib/cn.ts", "./components/*": ["./src/components/*.tsx", "./src/components/*.ts"], "./context/*": ["./src/context/google-maps-provider.tsx", "./src/context/google-map-api-provider.tsx"], "./hooks/*": ["./src/hooks/*.tsx", "./src/hooks/*.ts"], "./types/*": ["./src/lib/types.ts"]}, "scripts": {"lint": "eslint .", "ui:add": "pnpm dlx shadcn@2.3.0 add"}, "prettier": "@acme/prettier-config", "dependencies": {"@googlemaps/js-api-loader": "^1.16.8", "@hookform/resolvers": "^4.1.3", "@lexical/code": "^0.18.0", "@lexical/link": "^0.18.0", "@lexical/list": "^0.18.0", "@lexical/markdown": "^0.18.0", "@lexical/react": "^0.18.0", "@lexical/rich-text": "^0.18.0", "@lexical/selection": "^0.18.0", "@lexical/table": "^0.18.0", "@lexical/utils": "^0.18.0", "@radix-ui/react-accordion": "^1.2.4", "@radix-ui/react-alert-dialog": "^1.1.7", "@radix-ui/react-aspect-ratio": "^1.1.3", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-context-menu": "^2.2.7", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-hover-card": "^1.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-menubar": "^1.1.7", "@radix-ui/react-navigation-menu": "^1.2.6", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-progress": "^1.1.3", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.2.7", "@radix-ui/react-toggle": "^1.1.3", "@radix-ui/react-toggle-group": "^1.1.3", "@radix-ui/react-tooltip": "^1.2.0", "@uidotdev/usehooks": "^2.4.1", "@vis.gl/react-google-maps": "^1.5.2", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.1.1", "date-fns": "catalog:", "embla-carousel-react": "^8.6.0", "input-otp": "^1.4.2", "lexical": "^0.18.0", "lodash-es": "^4.17.21", "lucide-react": "^0.477.0", "motion": "^12.4.10", "next-themes": "^0.4.4", "react": "catalog:react19", "react-colorful": "^5.6.1", "react-day-picker": "^9", "react-hook-form": "^7.54.2", "react-resizable-panels": "^2.1.7", "react-use-measure": "^2.1.7", "recharts": "^2.15.2", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tailwindcss": "catalog:", "tailwindcss-animate": "^1.0.7", "timescape": "^0.7.1", "vaul": "^1.1.2", "zod": "^3.24.1"}, "devDependencies": {"@acme/eslint-config": "workspace:*", "@acme/prettier-config": "workspace:*", "@acme/tailwind-config": "workspace:*", "@acme/tsconfig": "workspace:*", "@types/lodash-es": "^4.17.12", "@types/react": "catalog:react19", "eslint": "^9.21.0", "prettier": "^3.3.3", "react": "catalog:react19", "typescript": "catalog:", "zod": "catalog:"}, "peerDependencies": {"react": "catalog:react19", "zod": "^3.24.1"}}