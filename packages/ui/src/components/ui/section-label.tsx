import React from "react";

import { cn } from "@acme/ui/lib/utils";

const SectionLabel: React.FC<{
  children: React.ReactNode;
  className?: string;
}> = ({ children, className }) => {
  return (
    <>
      <div
        className={cn(
          "text-secondary-800 flex w-fit items-center gap-2.5 rounded-lg bg-[#fef9f6] px-4 py-2 text-sm font-semibold tracking-[1%] md:text-base",
          className,
        )}
      >
        {children}
      </div>
    </>
  );
};

export default SectionLabel;
