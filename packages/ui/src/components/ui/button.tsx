import type { VariantProps } from "class-variance-authority";
import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva } from "class-variance-authority";

import { cn } from "@acme/ui/lib/utils";

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 xl:p-[14px] [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
  {
    variants: {
      variant: {
        default:
          "border-[1.2px] border-transparent bg-teal-550 font-jakarta text-sm tracking-[0.14px] text-black-900 hover:bg-black-800 hover:text-teal-50 active:border-teal-50 active:bg-teal-850 active:text-black-0 md:text-base md:tracking-[0.16px] lg:text-lg lg:tracking-[0.18px]",
        disabled:
          "bg-black-100 font-jakarta text-sm tracking-[0.14px] text-black-600 md:text-base md:tracking-[0.16px] lg:text-lg lg:tracking-[0.18px]",
        loading:
          "bg-black-250 font-jakarta text-sm tracking-[0.14px] text-black-700 md:text-base md:tracking-[0.16px] lg:text-lg lg:tracking-[0.18px]",
        outline:
          "border-[1.2px] border-black-250 bg-transparent font-jakarta text-sm tracking-[0.14px] text-black-700 hover:border-black-300 hover:bg-black-50 active:border-black-150 active:bg-black-100 active:text-black-600 md:text-base md:tracking-[0.16px] lg:text-lg lg:tracking-[0.18px]",
        ghost:
          "border-0 bg-transparent font-jakarta text-sm tracking-[0.14px] text-black-700 hover:bg-black-50 hover:text-black-800 active:bg-black-100 active:text-black-600 md:text-base md:tracking-[0.16px] lg:text-lg lg:tracking-[0.18px]",
        destructive:
          "border-[1.2px] border-transparent bg-red-500 font-jakarta text-sm tracking-[0.14px] text-white hover:bg-red-600 active:bg-red-700 md:text-base md:tracking-[0.16px] lg:text-lg lg:tracking-[0.18px]",
      },
      size: {
        default: "rounded-lg px-3 py-[14px]",
        sm: "h-8 rounded-md px-3 text-xs",
        lg: "h-10 rounded-md px-8",
        icon: "h-9 w-9",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  },
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button";
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    );
  },
);
Button.displayName = "Button";

export { Button, buttonVariants };
