/* eslint-disable @typescript-eslint/prefer-promise-reject-errors */
"use client";

import * as React from "react";
import { useCallback, useEffect, useRef, useState } from "react";
import { Locate, Search, X } from "lucide-react";

import { But<PERSON> } from "@acme/ui/components/ui/button";
import { Input } from "@acme/ui/components/ui/input";
import { useGoogleMaps } from "@acme/ui/context/google-maps-provider";
import { cn } from "@acme/ui/lib/utils";

import type {
  GeographicPoint,
  GoogleAutocompleteInputProps,
} from "../../lib/types";
import { parseGoogleAddressComponents } from "../../lib/utils";
import { Spinner } from "./spinner";

/**
 * Google Autocomplete Input Component
 *
 * A fully-featured location search component with Google Maps Places Autocomplete.
 *
 * Features:
 * - Location search with Google Places API
 * - Geolocation detection
 * - Keyboard navigation
 * - Custom styling options
 * - Accessibility support
 * - Detailed location information
 *
 * @example
 * // Basic usage
 * <GoogleAutocompleteInput
 *   onLocationSelect={(location) => console.log(location)}
 * />
 *
 * @example
 * // Advanced usage with all options
 * <GoogleAutocompleteInput
 *   initialValue="New Delhi"
 *   placeholder="Search for a location"
 *   onLocationSelect={handleLocationSelect}
 *   onInputChange={handleInputChange}
 *   onUserLocationDetect={handleUserLocation}
 *   onError={handleError}
 *   isDisabled={false}
 *   showSearchIcon={true}
 *   showAutoDetectLocationIcon={true}
 *   showClearButton={true}
 *   countryRestrictions={["in", "us"]}
 *   debounceMs={500}
 *   maxSuggestions={5}
 *   searchBounds={{
 *     point1: { lat: 28.5, lng: 77.0 },
 *     point2: { lat: 28.7, lng: 77.5 }
 *   }}
 *   className="w-full"
 *   inputClassName="rounded-lg"
 *   suggestionsClassName="rounded-lg"
 *   ariaLabel="Search for locations"
 *   renderSuggestion={(suggestion, isActive) => (
 *     <div className={isActive ? "bg-blue-100" : ""}>
 *       {suggestion.structured_formatting.main_text}
 *     </div>
 *   )}
 * />
 */
const GoogleAutocompleteInput = React.forwardRef<
  HTMLInputElement,
  GoogleAutocompleteInputProps
>(
  (
    {
      className,
      inputClassName,
      suggestionsClassName,
      placeholder = "Enter location",
      onLocationSelect,
      initialValue = "",
      onInputChange,
      isDisabled = false,
      searchBounds,
      showSearchIcon = false,
      showAutoDetectLocationIcon = false,
      showClearButton = true,
      onUserLocationDetect,
      countryRestrictions = ["in"],
      debounceMs = 300,
      maxSuggestions = 5,
      ariaLabel = "Enter an address for autocomplete suggestions",
      onError,
      renderSuggestion,
    },
    ref,
  ) => {
    // State management
    const [query, setQuery] = useState(initialValue);
    const [suggestions, setSuggestions] = useState<
      google.maps.places.AutocompletePrediction[]
    >([]);
    const [showSuggestions, setShowSuggestions] = useState(false);
    const [hasSelectedLocation, setHasSelectedLocation] = useState(false);
    const [activeIndex, setActiveIndex] = useState<number>(-1);
    const [isLoadingLocation, setIsLoadingLocation] = useState(false);
    const [isLoadingSuggestions, setIsLoadingSuggestions] = useState(false);
    const [errorMessage, setErrorMessage] = useState<string | null>(null);

    const { isLoaded, loadError } = useGoogleMaps();
    const inputRef = useRef<HTMLInputElement>(null);
    const suggestionsRef = useRef<HTMLUListElement>(null);
    const forwardedRef = (ref ?? inputRef) as React.RefObject<HTMLInputElement>;

    // Handle API errors
    useEffect(() => {
      if (loadError && onError) {
        onError(new Error("Google Maps API failed to load"));
      }
    }, [loadError, onError]);

    // Create bounds from provided points
    const createBoundsFromPoints = useCallback(() => {
      if (!searchBounds) return null;

      const { point1, point2, point3, point4 } = searchBounds;
      if (!point1 || !point2) return null;

      const points = [point1, point2, point3, point4].filter(
        Boolean,
      ) as GeographicPoint[];

      const lngs = points.map((p) => p.lng);
      const lats = points.map((p) => p.lat);

      return {
        west: Math.min(...lngs),
        east: Math.max(...lngs),
        south: Math.min(...lats),
        north: Math.max(...lats),
      };
    }, [searchBounds]);

    // Fetch place predictions with better error handling
    const fetchPlacePredictions = useCallback(async () => {
      if (!isLoaded || !query) {
        setSuggestions([]);
        return;
      }

      try {
        setIsLoadingSuggestions(true);
        setErrorMessage(null);

        const service = new google.maps.places.AutocompleteService();
        const bounds = createBoundsFromPoints();

        const request: google.maps.places.AutocompletionRequest = {
          input: query,
          componentRestrictions: { country: countryRestrictions },
        };

        if (bounds) {
          request.locationRestriction = bounds;
        }

        return new Promise<void>((resolve, reject) => {
          service.getPlacePredictions(request, (predictions, status) => {
            setIsLoadingSuggestions(false);

            if (
              status === google.maps.places.PlacesServiceStatus.OK &&
              predictions
            ) {
              const limitedSuggestions = predictions.slice(0, maxSuggestions);
              setSuggestions(limitedSuggestions);
              setShowSuggestions(true);
              setActiveIndex(-1);
              resolve();
            } else {
              setSuggestions([]);
              setShowSuggestions(false);

              if (
                status === google.maps.places.PlacesServiceStatus.ZERO_RESULTS
              ) {
                // This is an expected case, not an error
                resolve();
              } else {
                const error = new Error(`Places API error: ${status}`);
                setErrorMessage("Failed to load suggestions");
                onError?.(error);
                reject(error);
              }
            }
          });
        });
      } catch (error) {
        setIsLoadingSuggestions(false);
        setErrorMessage("Failed to load suggestions");
        onError?.(error instanceof Error ? error : new Error(String(error)));
        return Promise.reject(error);
      }
    }, [
      query,
      isLoaded,
      createBoundsFromPoints,
      countryRestrictions,
      maxSuggestions,
      onError,
    ]);

    // Debounced search
    useEffect(() => {
      if (!query) {
        setSuggestions([]);
        setShowSuggestions(false);
        return;
      }

      if (hasSelectedLocation) return;

      const delayDebounceFn = setTimeout(() => {
        fetchPlacePredictions();
      }, debounceMs);

      return () => clearTimeout(delayDebounceFn);
    }, [query, hasSelectedLocation, debounceMs]);

    // Select a place with enhanced error handling
    const selectPlace = async (
      suggestion: google.maps.places.AutocompletePrediction,
    ) => {
      if (!isLoaded) return;

      try {
        const placesService = new google.maps.places.PlacesService(
          document.createElement("div"),
        );

        return new Promise<void>((resolve, reject) => {
          placesService.getDetails(
            {
              placeId: suggestion.place_id,
              fields: [
                "address_components",
                "geometry",
                "formatted_address",
                "place_id",
              ],
            },
            async (place, status) => {
              if (
                status === google.maps.places.PlacesServiceStatus.OK &&
                place?.geometry?.location
              ) {
                const location = place.geometry.location;
                const lat = location.lat();
                const lng = location.lng();

                // Use Google Maps Geocoder for reverse geocode
                const geocoder = new google.maps.Geocoder();
                geocoder.geocode(
                  { location: { lat, lng } },
                  (results, geoStatus) => {
                    if (
                      geoStatus === google.maps.GeocoderStatus.OK &&
                      results?.[0]
                    ) {
                      const addressComponents = results[0].address_components;
                      const formatted_address = results[0].formatted_address;
                      const address = parseGoogleAddressComponents(
                        addressComponents,
                        formatted_address,
                      );
                      const placeId = suggestion.place_id;
                      onLocationSelect({
                        latitude: lat,
                        longitude: lng,
                        address,
                        googleAddressComponent: addressComponents,
                        district: address.district,
                        googlePlaceId: placeId,
                      });
                      setQuery(formatted_address || "");
                      onInputChange?.(formatted_address || "");
                      setSuggestions([]);
                      setShowSuggestions(false);
                      setHasSelectedLocation(true);
                      forwardedRef.current.focus();
                      resolve();
                    } else {
                      setErrorMessage(
                        "Failed to get address from Google Geocoder",
                      );
                      onError?.(
                        new Error("Failed to get address from Google Geocoder"),
                      );
                      reject(
                        new Error("Failed to get address from Google Geocoder"),
                      );
                    }
                  },
                );
              } else {
                const error = new Error(`Places API details error: ${status}`);
                setErrorMessage("Failed to get location details");
                onError?.(error);
                reject(error);
              }
            },
          );
        });
      } catch (error) {
        setErrorMessage("Failed to select location");
        onError?.(error instanceof Error ? error : new Error(String(error)));
      }
    };

    // Get user's current location with Google Geocoding
    const getUserLocation = async () => {
      if (!onUserLocationDetect) return;

      try {
        setIsLoadingLocation(true);
        setErrorMessage(null);

        if (!navigator.geolocation) {
          throw new Error("Geolocation is not supported by your browser");
        }

        const position = await new Promise<GeolocationPosition>(
          (resolve, reject) => {
            navigator.geolocation.getCurrentPosition(resolve, reject, {
              enableHighAccuracy: true,
              timeout: 10000,
              maximumAge: 0,
            });
          },
        );

        const { latitude, longitude } = position.coords;

        if (isLoaded) {
          const geocoder = new google.maps.Geocoder();
          geocoder.geocode(
            { location: { lat: latitude, lng: longitude } },
            (results, geoStatus) => {
              if (geoStatus === google.maps.GeocoderStatus.OK && results?.[0]) {
                const addressComponents = results[0].address_components;
                const formatted_address = results[0].formatted_address;
                const address = parseGoogleAddressComponents(
                  addressComponents,
                  formatted_address,
                );
                const googlePlaceId = results[0]?.place_id;
                onUserLocationDetect({
                  latitude,
                  longitude,
                  address,
                  googleAddressComponent: addressComponents,
                  district: address.district,
                  googlePlaceId,
                });
                setQuery(formatted_address ?? "");
                onInputChange?.(formatted_address ?? "");
                setHasSelectedLocation(true);
              } else {
                setErrorMessage("Failed to get address from Google Geocoder");
                onError?.(
                  new Error("Failed to get address from Google Geocoder"),
                );
              }
              setIsLoadingLocation(false);
            },
          );
        }
      } catch (error) {
        setErrorMessage(
          error instanceof Error
            ? error.message
            : "Failed to get your location",
        );
        onError?.(error instanceof Error ? error : new Error(String(error)));
        setIsLoadingLocation(false);
      }
    };

    // Clear the input and reset state
    const handleClear = () => {
      setQuery("");
      onInputChange?.("");
      setHasSelectedLocation(false);
      setSuggestions([]);
      setShowSuggestions(false);
      setActiveIndex(-1);
      forwardedRef.current?.focus();
    };

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const newQuery = e.target.value;
      setQuery(newQuery);
      onInputChange?.(newQuery);

      if (hasSelectedLocation) {
        setHasSelectedLocation(false);
      }

      if (errorMessage) {
        setErrorMessage(null);
      }
    };

    const handleInputFocus = () => {
      if (!hasSelectedLocation && query && suggestions.length > 0) {
        setShowSuggestions(true);
      }
    };

    // Keyboard navigation handler
    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
      if (!showSuggestions || suggestions.length === 0) return;

      switch (e.key) {
        case "ArrowDown":
          e.preventDefault();
          setActiveIndex((prevIndex) =>
            prevIndex < suggestions.length - 1 ? prevIndex + 1 : prevIndex,
          );
          break;
        case "ArrowUp":
          e.preventDefault();
          setActiveIndex((prevIndex) => (prevIndex > 0 ? prevIndex - 1 : -1));
          break;
        case "Enter":
          if (activeIndex >= 0 && activeIndex < suggestions.length) {
            e.preventDefault();
            if (!suggestions[activeIndex]) return;
            void selectPlace(suggestions[activeIndex]);
          }
          break;
        case "Escape":
          e.preventDefault();
          setShowSuggestions(false);
          setActiveIndex(-1);
          break;
      }
    };

    // Effect to scroll active suggestion into view
    useEffect(() => {
      if (activeIndex >= 0 && suggestionsRef.current) {
        const activeElement = suggestionsRef.current.children[
          activeIndex
        ] as HTMLLIElement;
        activeElement.scrollIntoView({ block: "nearest" });
      }
    }, [activeIndex]);

    // Loading state when Google Maps is not yet loaded
    if (!isLoaded) {
      return (
        <div className="mb-3 h-12 animate-pulse rounded-md border bg-gray-200 p-3 pl-10 pr-10 text-sm md:pl-10 md:pr-10 xl:p-3.5 xl:pl-10 xl:pr-10"></div>
      );
    }

    // Default suggestion renderer
    const defaultRenderSuggestion = (
      suggestion: google.maps.places.AutocompletePrediction,
      isActive: boolean,
    ) => (
      <div className={cn("flex flex-col", isActive && "")}>
        <span className="font-jakarta text-sm font-medium lg:text-base">
          {suggestion.structured_formatting.main_text}
        </span>
        <span className="font-jakarta text-xs text-gray-500 lg:text-sm">
          {suggestion.structured_formatting.secondary_text}
        </span>
      </div>
    );

    return (
      <div className={cn("relative w-full", className)}>
        <div className="relative">
          <Input
            ref={forwardedRef}
            placeholder={placeholder}
            className={cn(
              "pl-10 pr-10 md:pl-10 md:pr-10 xl:pl-10 xl:pr-10",
              errorMessage && "border-red-500",
              inputClassName,
            )}
            value={initialValue || query}
            onChange={handleInputChange}
            onFocus={handleInputFocus}
            onKeyDown={handleKeyDown}
            onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
            aria-label={ariaLabel}
            aria-invalid={!!errorMessage}
            aria-describedby={errorMessage ? "location-error" : undefined}
            disabled={isDisabled || isLoadingLocation}
            aria-autocomplete="list"
            role="combobox"
            aria-expanded={showSuggestions}
            aria-controls="location-suggestions"
            aria-activedescendant={
              activeIndex >= 0
                ? `location-suggestion-${activeIndex}`
                : undefined
            }
          />

          {/* Icons and buttons */}
          <div className="absolute inset-y-0 left-0 flex items-center pl-3">
            {showSearchIcon && !isLoadingSuggestions && (
              <Search
                className="size-4 text-gray-500 lg:size-5"
                aria-hidden="true"
              />
            )}
            {isLoadingSuggestions && (
              <Spinner
                className="size-4 text-gray-500 lg:size-5"
                aria-label="Loading suggestions"
              />
            )}
          </div>

          <div className="absolute inset-y-0 right-0 flex items-center gap-1 rounded-r-lg pr-3 backdrop-blur-sm">
            {query && showClearButton && (
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="size-6 p-0"
                onClick={handleClear}
                aria-label="Clear input"
              >
                <X className="size-4" />
              </Button>
            )}

            {showAutoDetectLocationIcon && (
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="size-6 p-0"
                onClick={getUserLocation}
                disabled={isLoadingLocation}
                aria-label="Detect your location"
              >
                {isLoadingLocation ? (
                  <Spinner className="size-4" />
                ) : (
                  <Locate className="size-4" />
                )}
              </Button>
            )}
          </div>
        </div>

        {/* Error message */}
        {errorMessage && (
          <div id="location-error" className="mt-1 text-sm text-red-500">
            {errorMessage}
          </div>
        )}

        {/* Suggestions dropdown */}
        {showSuggestions && suggestions.length > 0 && (
          <ul
            id="location-suggestions"
            ref={suggestionsRef}
            className={cn(
              "absolute z-50 mt-1 max-h-60 w-full overflow-y-auto rounded-xl border border-gray-300 bg-white py-1 shadow-lg",
              suggestionsClassName,
            )}
            role="listbox"
          >
            {suggestions.map((suggestion, index) => (
              <li
                id={`location-suggestion-${index}`}
                key={suggestion.place_id}
                className={cn("cursor-pointer px-4 py-2 hover:bg-yellow-50")}
                onMouseDown={() => selectPlace(suggestion)}
                onMouseEnter={() => setActiveIndex(index)}
                role="option"
                aria-selected={index === activeIndex}
              >
                {renderSuggestion
                  ? renderSuggestion(suggestion, index === activeIndex)
                  : defaultRenderSuggestion(suggestion, index === activeIndex)}
              </li>
            ))}
          </ul>
        )}
      </div>
    );
  },
);

GoogleAutocompleteInput.displayName = "GoogleAutocompleteInput";

export default React.memo(GoogleAutocompleteInput);
