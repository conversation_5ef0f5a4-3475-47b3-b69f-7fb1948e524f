"use client";

import { useState } from "react";
import { Check, Copy } from "lucide-react";
import { toast } from "sonner";

import { But<PERSON> } from "./button";

export function CopyToClipboardButton({ text }: { text: string }) {
  const [copied, setCopied] = useState(false);
  return (
    <Button
      size="sm"
      variant="outline"
      className={
        copied
          ? "bg-green-600 px-2 py-1 text-white hover:bg-green-700"
          : "border-fluidpe-teal text-fluidpe-teal hover:bg-fluidpe-light-teal px-2 py-1"
      }
      onClick={async () => {
        try {
          await navigator.clipboard.writeText(text);
          setCopied(true);
          toast.success("Copied to clipboard!");
          setTimeout(() => setCopied(false), 2000);
        } catch (e) {
          console.error(e);
          toast.error("Failed to copy.");
        }
      }}
      disabled={copied}
    >
      {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
    </Button>
  );
}
