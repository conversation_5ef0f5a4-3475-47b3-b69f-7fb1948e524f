"use client";

import * as React from "react";
import { ChevronDownIcon } from "lucide-react";

import { cn } from "@acme/ui/lib/cn";

import { Button } from "./button";
import { Calendar } from "./calendar";
import { Popover, PopoverContent, PopoverTrigger } from "./popover";

export interface DateRange {
  from: Date;
  to: Date | undefined;
}

export interface DateRangePickerProps {
  onUpdate?: (values: { range: DateRange }) => void;
  initialDateFrom?: Date | string;
  initialDateTo?: Date | string;
  align?: "start" | "center" | "end";
  locale?: string;
  className?: string;
}

const formatDate = (date: Date, locale = "en-us"): string => {
  return date.toLocaleDateString(locale, {
    month: "short",
    day: "numeric",
    year: "numeric",
  });
};

const getDateAdjustedForTimezone = (dateInput: Date | string): Date => {
  if (typeof dateInput === "string") {
    const parts = dateInput.split("-").map((part) => Number.parseInt(part, 10));
    return new Date(Number(parts[0]), Number(parts[1]) - 1, parts[2]);
  }
  return dateInput;
};

export const DateRangePicker: React.FC<DateRangePickerProps> = ({
  initialDateFrom = new Date(new Date().setHours(0, 0, 0, 0)),
  initialDateTo,
  onUpdate,
  align = "center",
  locale = "en-US",
  className,
}) => {
  const [isOpen, setIsOpen] = React.useState(false);
  const [range, setRange] = React.useState<DateRange>({
    from: getDateAdjustedForTimezone(initialDateFrom),
    to: initialDateTo
      ? getDateAdjustedForTimezone(initialDateTo)
      : getDateAdjustedForTimezone(initialDateFrom),
  });

  const openedRangeRef = React.useRef<DateRange>(range);

  const [calendarMonths, setCalendarMonths] = React.useState<[Date, Date]>([
    new Date(),
    new Date(new Date().setMonth(new Date().getMonth() + 1)),
  ]);

  const areRangesEqual = (a?: DateRange, b?: DateRange): boolean => {
    if (!a || !b) return a === b;
    return (
      a.from.getTime() === b.from.getTime() &&
      (!a.to || !b.to || a.to.getTime() === b.to.getTime())
    );
  };

  React.useEffect(() => {
    if (isOpen) {
      openedRangeRef.current = range;
    }
  }, [isOpen, range]);

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            "w-fit justify-start text-nowrap text-left text-[11px] font-normal",
            className,
          )}
        >
          {formatDate(range.from, locale)}
          {range.to && (
            <>
              <ChevronDownIcon className="mx-2 h-4 w-4" />
              {formatDate(range.to, locale)}
            </>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align={align} sideOffset={4}>
        <div className="flex flex-col gap-4 lg:flex-row">
          {/* Calendar Section */}
          <div className="">
            <div className="hidden p-4 lg:flex">
              {/* Two calendars side by side for desktop */}

              <Calendar
                mode="range"
                selected={range}
                onSelect={(newRange) =>
                  newRange && setRange(newRange as DateRange)
                }
                numberOfMonths={2}
                className="rounded-md border"
                disabled={{
                  after: new Date(),
                }}
                showOutsideDays={false}
              />
            </div>

            {/* Single calendar for mobile */}
            <div className="lg:hidden">
              <Calendar
                mode="range"
                selected={range}
                onSelect={(newRange) =>
                  newRange && setRange(newRange as DateRange)
                }
                className="rounded-md border"
                disabled={{
                  after: new Date(),
                }}
                showOutsideDays={false}
              />
            </div>
          </div>
        </div>

        {/* Footer Actions */}
        <div className="flex items-center justify-end gap-2 border-t p-4">
          <Button
            variant="ghost"
            onClick={() => {
              setIsOpen(false);
            }}
          >
            Cancel
          </Button>
          <Button
            onClick={() => {
              console.log("clicking update with ranges", range);

              onUpdate?.({ range });
              console.log(
                " open range ref current is ",
                openedRangeRef.current,
              );
              console.log(
                " are the ranges equal ",
                areRangesEqual(range, openedRangeRef.current),
              );

              if (!areRangesEqual(range, openedRangeRef.current)) {
                console.log("running on update");
                console.log("on update function is ", JSON.stringify(onUpdate));
              }
              setIsOpen(false);
            }}
          >
            Apply
          </Button>
        </div>
      </PopoverContent>
    </Popover>
  );
};

DateRangePicker.displayName = "DateRangePicker";
