import * as React from "react";

import { cn } from "@acme/ui/lib/utils";

const Input = React.forwardRef<HTMLInputElement, React.ComponentProps<"input">>(
  ({ className, type, ...props }, ref) => {
    return (
      <input
        type={type}
        className={cn(
          "flex w-full rounded-lg border-[1.2px] border-black-100 bg-black-0 px-4 py-[14px] text-base text-black-700 shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-black-400 focus-visible:bg-white focus-visible:outline-none focus-visible:ring-[1.2px] focus-visible:ring-teal-750 focus-visible:placeholder:text-transparent disabled:cursor-not-allowed disabled:opacity-50",
          className,
        )}
        ref={ref}
        {...props}
      />
    );
  },
);
Input.displayName = "Input";

export { Input };
