import { Skeleton } from "@acme/ui/components/ui/skeleton";
import { cn } from "@acme/ui/lib/utils";

interface BlogCardSkeletonProps {
  imagePosition?: "top" | "left" | "none";
  className?: string;
}

const BlogCardSkeleton = ({
  imagePosition = "none",
  className,
}: BlogCardSkeletonProps) => {
  return (
    <div
      className={cn(
        "border-text-200 bg-text-00 flex overflow-hidden rounded-2xl border-2",
        imagePosition === "left"
          ? "flex-row gap-4"
          : "flex-col gap-[18px] lg:gap-5",
        className,
      )}
    >
      {/* image skeleton */}
      {imagePosition !== "none" && (
        <Skeleton
          className={cn(
            "relative",
            imagePosition === "top" &&
              "aspect-[332/221] w-full md:aspect-[369/221] lg:aspect-[502/206] xl:aspect-[718/300]",
            imagePosition === "left" &&
              "aspect-[141/204] min-w-[141px] lg:aspect-[175/234] lg:min-w-[175px]",
          )}
        />
      )}

      {/* content skeleton */}
      <div className="flex flex-1 flex-col gap-[14px] lg:gap-4">
        <div
          className={cn(
            "flex flex-col gap-1.5 px-4 lg:gap-2.5 lg:px-4",
            imagePosition === "none" && "p-4 pb-0",
            imagePosition === "left" && "pb-0 pl-0 pt-4",
          )}
        >
          {/* tags skeleton */}
          <div className="flex gap-2">
            <Skeleton className="h-6 w-16" />
            <Skeleton className="h-6 w-16" />
            <Skeleton className="h-6 w-16" />
          </div>

          <div className="flex flex-col gap-0.5 lg:gap-1">
            {/* title skeleton */}
            <Skeleton className="h-7 w-full" />
            {/* description skeleton */}
            <Skeleton className="h-10 w-full" />
          </div>
        </div>

        <div className="bg-text-200 h-[1.5px]" />

        <div className="flex items-center justify-between px-4 pb-4 lg:px-4">
          <Skeleton className="h-4 w-24" />
          <Skeleton className="h-6 w-6" />
        </div>
      </div>
    </div>
  );
};

export default BlogCardSkeleton;
