import { useEffect, useReducer, useRef } from "react";
import { useMap } from "@vis.gl/react-google-maps";

import { DrawingActionKind, isPolygon } from "./types";
import reducer, {
  useDrawingManagerEvents,
  useOverlaySnapshots,
} from "./undo-redo";

interface Props {
  drawingManager: google.maps.drawing.DrawingManager | null;
  onOverlaysChange?: (
    overlays: { latitude: number; longitude: number }[],
  ) => void;
  initialPolygonCoords?: { latitude: number; longitude: number }[];
}

export const UndoRedoControl = ({
  drawingManager,
  onOverlaysChange,
  initialPolygonCoords,
}: Props) => {
  const map = useMap();

  const [state, dispatch] = useReducer(reducer, {
    past: [],
    now: [],
    future: [],
  });

  // We need this ref to prevent infinite loops in certain cases.
  const overlaysShouldUpdateRef = useRef<boolean>(false);
  const initialPolygonLoadedRef = useRef<boolean>(false);

  useDrawingManagerEvents(drawingManager, overlaysShouldUpdateRef, dispatch);
  useOverlaySnapshots(map, state, overlaysShouldUpdateRef);

  // Load initial polygon if provided
  useEffect(() => {
    if (
      !map ||
      !initialPolygonCoords ||
      initialPolygonCoords.length === 0 ||
      initialPolygonLoadedRef.current
    ) {
      return;
    }

    // Convert coordinates to Google Maps LatLng objects
    const polygonPath = initialPolygonCoords.map(
      (coord) => new google.maps.LatLng(coord.latitude, coord.longitude),
    );

    // Create polygon overlay
    const polygon = new google.maps.Polygon({
      paths: polygonPath,
      editable: true,
      draggable: true,
      map: map,
    });

    // Add event listeners for polygon changes
    const updateListener = () => {
      if (overlaysShouldUpdateRef.current) {
        dispatch({ type: DrawingActionKind.UPDATE_OVERLAYS });
      }
    };

    polygon.addListener("mouseup", updateListener);

    // Dispatch action to add polygon to state
    dispatch({
      type: DrawingActionKind.SET_OVERLAY,
      payload: {
        type: google.maps.drawing.OverlayType.POLYGON,
        overlay: polygon,
      },
    });

    initialPolygonLoadedRef.current = true;

    return () => {
      google.maps.event.clearInstanceListeners(polygon);
    };
  }, [map, initialPolygonCoords, dispatch]);

  // Reset the loaded flag when initial coords change
  useEffect(() => {
    initialPolygonLoadedRef.current = false;
  }, [initialPolygonCoords]);

  // Extract polygon coordinates and notify parent component
  useEffect(() => {
    if (!onOverlaysChange) return;

    const polygonPoints: { latitude: number; longitude: number }[] = [];

    for (const overlay of state.now) {
      if (isPolygon(overlay.geometry) && overlay.snapshot.path) {
        for (const latLng of overlay.snapshot.path) {
          polygonPoints.push({
            latitude: latLng.lat(),
            longitude: latLng.lng(),
          });
        }
        break; // Only take the first polygon for now
      }
    }

    onOverlaysChange(polygonPoints);
  }, [state.now, onOverlaysChange]);

  return (
    <div className="drawing-history">
      <button
        onClick={(e) => {
          e.stopPropagation();
          dispatch({ type: DrawingActionKind.UNDO });
        }}
        disabled={!state.past.length}
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          height="24"
          viewBox="960 960 960 960"
          width="24"
        >
          <path d="M280-200v-80h284q63 0 109.5-40T720-420q0-60-46.5-100T564-560H312l104 104-56 56-200-200 200-200 56 56-104 104h252q97 0 166.5 63T800-420q0 94-69.5 157T564-200H280Z" />
        </svg>
      </button>
      <button
        onClick={(e) => {
          e.stopPropagation();
          dispatch({ type: DrawingActionKind.REDO });
        }}
        disabled={!state.future.length}
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          height="24"
          viewBox="http://www.w3.org/2000/svg"
          width="24"
        >
          <path d="M396-200q-97 0-166.5-63T160-420q0-94 69.5-157T396-640h252L544-744l56-56 200 200-200 200-56-56 104-104H396q-63 0-109.5 40T240-420q0 60 46.5 100T396-280h284v80H396Z" />
        </svg>
      </button>
    </div>
  );
};
