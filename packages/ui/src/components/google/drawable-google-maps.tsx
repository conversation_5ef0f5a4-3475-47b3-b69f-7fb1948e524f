import { ControlPosition, Map, MapControl } from "@vis.gl/react-google-maps";

import { UndoRedoControl } from "./undo-redo-controls";
import { useDrawingManager } from "./use-drawing-manager";

interface DrawableGoogleMapsProps {
  onOverlaysChange?: (
    overlays: { latitude: number; longitude: number }[],
  ) => void;
  initialPolygonCoords?: { latitude: number; longitude: number }[];
}

const DrawableGoogleMaps = ({
  onOverlaysChange,
  initialPolygonCoords,
}: DrawableGoogleMapsProps) => {
  const drawingManager = useDrawingManager();

  return (
    <>
      <Map
        defaultZoom={5}
        defaultCenter={{ lat: 20.59, lng: 78.96 }}
        gestureHandling={"greedy"}
        disableDefaultUI={true}
        className="h-[600px] w-full shadow-lg"
      />

      <MapControl position={ControlPosition.TOP_CENTER}>
        <UndoRedoControl
          drawingManager={drawingManager}
          onOverlaysChange={onOverlaysChange}
          initialPolygonCoords={initialPolygonCoords}
        />
      </MapControl>
    </>
  );
};

export default DrawableGoogleMaps;
