import { useEffect, useState } from "react";
import { useMap, useMapsLibrary } from "@vis.gl/react-google-maps";

export function useDrawingManager(
  initialValue: google.maps.drawing.DrawingManager | null = null,
) {
  const map = useMap();
  const drawing = useMapsLibrary("drawing");

  const [drawingManager, setDrawingManager] =
    useState<google.maps.drawing.DrawingManager | null>(initialValue);

  useEffect(() => {
    if (!map || !drawing) return;

    const newDrawingManager = new drawing.DrawingManager({
      map,
      drawingMode: google.maps.drawing.OverlayType.POLYGON,
      drawingControl: true,
      drawingControlOptions: {
        position: google.maps.ControlPosition.TOP_CENTER,
        drawingModes: [
          //   google.maps.drawing.OverlayType.MARKER,
          //   google.maps.drawing.OverlayType.CIRCLE,
          google.maps.drawing.OverlayType.POLYGON,
          //   google.maps.drawing.OverlayType.POLYLINE,
          //   google.maps.drawing.OverlayType.RECTANGLE,
        ],
      },
      //   markerOptions: {

      //     draggable: true,
      //   },
      //   circleOptions: {
      //     editable: true,
      //   },
      polygonOptions: {
        editable: true,
        draggable: true,
      },
      //   rectangleOptions: {
      //     editable: true,
      //     draggable: true,
      //   },
      //   polylineOptions: {
      //     editable: true,
      //     draggable: true,
      //   },
    });

    setDrawingManager(newDrawingManager);

    return () => {
      newDrawingManager.setMap(null);
    };
  }, [drawing, map]);

  return drawingManager;
}
