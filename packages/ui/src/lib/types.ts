export interface GoogleReverseGeocodeAddressComponent {
  long_name: string;
  short_name: string;
  types: string[];
}

export interface GoogleReverseGeocodeLatLng {
  lat: number;
  lng: number;
}

export interface GoogleReverseGeocodeBounds {
  northeast: GoogleReverseGeocodeLatLng;
  southwest: GoogleReverseGeocodeLatLng;
}

export interface GoogleReverseGeocodeGeometry {
  bounds?: GoogleReverseGeocodeBounds;
  location: GoogleReverseGeocodeLatLng;
  location_type: string;
  viewport: GoogleReverseGeocodeBounds;
}

export interface GoogleReverseGeocodeNavigationPoint {
  location: {
    latitude: number;
    longitude: number;
  };
  restricted_travel_modes: string[];
}

export interface GoogleReverseGeocodeResult {
  address_components: GoogleReverseGeocodeAddressComponent[];
  formatted_address: string;
  geometry: GoogleReverseGeocodeGeometry;
  navigation_points?: GoogleReverseGeocodeNavigationPoint[];
  place_id: string;
  types: string[];
}
export interface GeographicPoint {
  lat: number;
  lng: number;
}

export interface GeographicBounds {
  northeast: GeographicPoint;
  southwest: GeographicPoint;
}

export interface AddressComponent {
  short_name: string;
  long_name: string;
  types: string[];
  postcode_localities?: string[];
}

export interface Address {
  display: string;
  street: string | null;
  city: string | null;
  state: string | null;
  country: string | null;
  postalCode: string | null;
  district: string | null;
}

export interface DetectedLocation {
  latitude: number;
  longitude: number;
  address: Address | null;
  googleAddressComponent?: GoogleReverseGeocodeAddressComponent[];
  district?: string | null;
  googlePlaceId?: string;
}

export interface SearchBounds {
  point1?: GeographicPoint;
  point2?: GeographicPoint;
  point3?: GeographicPoint;
  point4?: GeographicPoint;
}

export interface GoogleAutocompleteInputProps {
  className?: string;
  inputClassName?: string;
  suggestionsClassName?: string;
  placeholder?: string;
  onLocationSelect: (location: DetectedLocation) => void;
  initialValue?: string;
  onInputChange?: (value: string) => void;
  isDisabled?: boolean;
  onUserLocationDetect?: (location: DetectedLocation) => void;
  searchBounds?: SearchBounds;
  showSearchIcon?: boolean;
  showAutoDetectLocationIcon?: boolean;
  showClearButton?: boolean;
  countryRestrictions?: string[]; // Add support for multiple countries
  debounceMs?: number; // Allow customizing debounce timing
  maxSuggestions?: number; // Limit number of suggestions
  ariaLabel?: string; // For better accessibility
  onError?: (error: Error) => void; // Error handling
  renderSuggestion?: (
    suggestion: google.maps.places.AutocompletePrediction,
    isActive: boolean,
  ) => React.ReactNode; // Custom suggestion rendering
}
