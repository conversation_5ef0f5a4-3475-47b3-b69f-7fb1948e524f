import type { ClassValue } from "clsx";
import { clsx } from "clsx";
import { twMerge } from "tailwind-merge";

import type { Address, GoogleReverseGeocodeAddressComponent } from "./types";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * Parses Google address components into our Address type, including district.
 * @param components GoogleReverseGeocodeAddressComponent[]
 * @returns Address
 */
export function parseGoogleAddressComponents(
  components: GoogleReverseGeocodeAddressComponent[],
  formatted_address: string,
): Address {
  let street: string | null = null;
  let city: string | null = null;
  let state: string | null = null;
  let country: string | null = null;
  let postalCode: string | null = null;
  let district: string | null = null;

  for (const comp of components) {
    if (comp.types.includes("route")) {
      street = comp.long_name;
    }
    if (comp.types.includes("locality")) {
      city = comp.long_name;
    }
    if (comp.types.includes("administrative_area_level_3")) {
      district = comp.long_name;
    }
    // Only set admin_level_2 if admin_level_3 hasn't been set
    if (comp.types.includes("administrative_area_level_2") && !district) {
      district = comp.long_name;
    }
    if (comp.types.includes("administrative_area_level_1")) {
      state = comp.long_name;
    }
    if (comp.types.includes("country")) {
      country = comp.long_name;
    }
    if (comp.types.includes("postal_code")) {
      postalCode = comp.long_name;
    }
  }

  return {
    display: formatted_address,
    street,
    city,
    state,
    country,
    postalCode,
    district,
  };
}
