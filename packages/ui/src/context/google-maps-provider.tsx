"use client";

import React, { createContext, useContext, useEffect, useState } from "react";
import { Loader } from "@googlemaps/js-api-loader";
import { APIProvider } from "@vis.gl/react-google-maps";

// Create a context for Google Maps
interface GoogleMapsContextType {
  isLoaded: boolean;
  loadError: Error | null;
}

const GoogleMapsContext = createContext<GoogleMapsContextType>({
  isLoaded: false,
  loadError: null,
});

export const GoogleMapsProvider: React.FC<{
  children: React.ReactNode;
  apiKey: string;
}> = ({ children, apiKey }) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [loadError, setLoadError] = useState<Error | null>(null);

  useEffect(() => {
    const loader = new Loader({
      apiKey: apiKey,
      version: "weekly",
      libraries: ["places", "geometry"],
    });

    loader
      .load()
      .then(() => {
        setIsLoaded(true);
      })
      .catch((error) => {
        console.error("Error loading Google Maps", error);
        setLoadError(error);
      });
  }, []);

  return (
    <GoogleMapsContext.Provider value={{ isLoaded, loadError }}>
      <APIProvider apiKey={apiKey}>{children}</APIProvider>
    </GoogleMapsContext.Provider>
  );
};

// Custom hook to use Google Maps context
export const useGoogleMaps = () => useContext(GoogleMapsContext);
