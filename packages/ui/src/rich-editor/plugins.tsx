import { useState } from "react";
import { CheckListPlugin } from "@lexical/react/LexicalCheckListPlugin";
import { ClickableLinkPlugin } from "@lexical/react/LexicalClickableLinkPlugin";
import { LexicalErrorBoundary } from "@lexical/react/LexicalErrorBoundary";
import { HistoryPlugin } from "@lexical/react/LexicalHistoryPlugin";
import { LinkPlugin as LexicalLinkPlugin } from "@lexical/react/LexicalLinkPlugin";
import { ListPlugin } from "@lexical/react/LexicalListPlugin";
import { RichTextPlugin } from "@lexical/react/LexicalRichTextPlugin";
import { TablePlugin } from "@lexical/react/LexicalTablePlugin";

import { ContentEditable } from "./editor-ui/content-editable";
import { ImagesPlugin } from "./plugins/images-plugin";
import { AutoLinkPlugin } from "./plugins/link/auto";
import { TableActionMenuPlugin } from "./plugins/table/action-menu";
import { TableCellResizerPlugin } from "./plugins/table/cell-resizer";
import { TableHoverActionsPlugin } from "./plugins/table/hover-action";
import { AlignmentToolbarPlugin } from "./plugins/toolbar/alignment-picker";
import { BlockFormatDropDown } from "./plugins/toolbar/block-format-toolbar-plugin";
import { FormatBulletedList } from "./plugins/toolbar/block-format/format-bulleted-list";
import { FormatCheckList } from "./plugins/toolbar/block-format/format-check-list";
import { FormatHeading } from "./plugins/toolbar/block-format/format-heading";
import { FormatNumberedList } from "./plugins/toolbar/block-format/format-numbered-list";
import { FormatParagraph } from "./plugins/toolbar/block-format/format-paragraph";
import { FormatQuote } from "./plugins/toolbar/block-format/format-quote";
import { BlockInsertPlugin } from "./plugins/toolbar/block-insert";
import { ClearFormattingToolbarPlugin } from "./plugins/toolbar/clear-formatting-toolbar-plugin";
import { FloatingLinkEditorPlugin } from "./plugins/toolbar/floating-link";
import { FloatingTextFormatToolbarPlugin } from "./plugins/toolbar/floating-text";
import { FontFormatToolbarPlugin } from "./plugins/toolbar/font-format-toolbar-plugin";
import { FontSizeToolbarPlugin } from "./plugins/toolbar/font-size-toolbar-plugin";
import { HistoryToolbarPlugin } from "./plugins/toolbar/history-toolbar-plugin";
import { InsertImage } from "./plugins/toolbar/insert-image";
import { LinkToolbarPlugin } from "./plugins/toolbar/link-toolbar-plugin";
import { SubSuperToolbarPlugin } from "./plugins/toolbar/subsuper-toolbar-plugin";
import { InsertTable } from "./plugins/toolbar/table-insert";
import { ToolbarPlugin } from "./plugins/toolbar/toolbar-plugin";
import { validateUrl } from "./utils/url";

export function Plugins() {
  const [floatingAnchorElem, setFloatingAnchorElem] =
    useState<HTMLDivElement | null>(null);

  const onRef = (_floatingAnchorElem: HTMLDivElement) => {
    if (_floatingAnchorElem !== null) {
      setFloatingAnchorElem(_floatingAnchorElem);
    }
  };

  return (
    <div className="hide-scrollbar relative max-h-[60dvh] rounded-md border">
      {/* Sticky toolbar at the top of the editor area */}
      <div className="sticky top-0 z-10 bg-white">
        <ToolbarPlugin>
          {() => (
            <div className="hide-scrollbar flex h-10 items-center gap-2 overflow-x-auto border-b px-2 py-3">
              <HistoryToolbarPlugin />
              <BlockFormatDropDown>
                <FormatParagraph />
                <FormatHeading levels={["h1", "h2", "h3"]} />
                <FormatNumberedList />
                <FormatBulletedList />
                <FormatCheckList />
                <FormatQuote />
              </BlockFormatDropDown>
              <FontSizeToolbarPlugin />
              <FontFormatToolbarPlugin format="bold" />
              <FontFormatToolbarPlugin format="italic" />
              <FontFormatToolbarPlugin format="underline" />
              <FontFormatToolbarPlugin format="strikethrough" />
              <FontFormatToolbarPlugin format="code" />
              <LinkToolbarPlugin />
              <BlockInsertPlugin>
                <InsertTable />
                <InsertImage />
              </BlockInsertPlugin>
              {/* <TablePlugin /> */}
              <SubSuperToolbarPlugin />
              <ClearFormattingToolbarPlugin />
              <AlignmentToolbarPlugin />
            </div>
          )}
        </ToolbarPlugin>
      </div>
      {/* Scrollable content area below toolbar, height = 60dvh - 40px (toolbar) */}
      <div className="hide-scrollbar h-[calc(60dvh-40px)] max-h-[60dvh] overflow-auto">
        <div className="relative min-h-[100px]">
          <RichTextPlugin
            contentEditable={
              <div className="">
                <div className="" ref={onRef}>
                  <ContentEditable placeholder={"Start typing ..."} />
                </div>
              </div>
            }
            ErrorBoundary={LexicalErrorBoundary}
          />
          <ImagesPlugin />
          <TablePlugin />
          <TableActionMenuPlugin anchorElem={floatingAnchorElem} />
          <TableCellResizerPlugin />
          <TableHoverActionsPlugin anchorElem={floatingAnchorElem} />
          <FloatingTextFormatToolbarPlugin anchorElem={floatingAnchorElem} />
          <HistoryPlugin />
          <ListPlugin />
          <CheckListPlugin />
          <ClickableLinkPlugin />
          <AutoLinkPlugin />
          <LexicalLinkPlugin validateUrl={validateUrl} />
          <FloatingLinkEditorPlugin anchorElem={floatingAnchorElem} />
          {/* editor plugins */}
        </div>
      </div>
      {/* actions plugins */}
    </div>
  );
}
