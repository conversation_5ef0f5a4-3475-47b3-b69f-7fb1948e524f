import type { ElementFormatType } from "lexical";
import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
import { FORMAT_ELEMENT_COMMAND } from "lexical";
import {
  AlignCenter,
  AlignCenterIcon,
  AlignJustifyIcon,
  AlignLeft,
  AlignLeftIcon,
  AlignRight,
  AlignRightIcon,
} from "lucide-react";

import { Button } from "@acme/ui/components/ui/button";

import { ComponentPickerOption } from "./component-picker";

export function AlignmentPickerPlugin({
  alignment,
}: {
  alignment: "left" | "center" | "right" | "justify";
}) {
  return new ComponentPickerOption(`Align ${alignment}`, {
    icon: <AlignIcons alignment={alignment} />,
    keywords: ["align", "justify", alignment],
    onSelect: (_, editor) =>
      editor.dispatchCommand(FORMAT_ELEMENT_COMMAND, alignment),
  });
}

function AlignIcons({
  alignment,
}: {
  alignment: "left" | "center" | "right" | "justify";
}) {
  switch (alignment) {
    case "left":
      return <AlignLeftIcon className="size-4" />;
    case "center":
      return <AlignCenterIcon className="size-4" />;
    case "right":
      return <AlignRightIcon className="size-4" />;
    case "justify":
      return <AlignJustifyIcon className="size-4" />;
  }
}

export function AlignmentToolbarPlugin() {
  const [editor] = useLexicalComposerContext();

  const formatElement = (format: ElementFormatType) => {
    editor.dispatchCommand(FORMAT_ELEMENT_COMMAND, format);
  };

  return (
    <div className="flex items-center gap-1">
      <Button
        className="m-0 p-0"
        type="button"
        onClick={() => formatElement("left")}
        variant={"outline"}
        aria-label="Align Left"
        size="icon"
      >
        <AlignLeft className="h-4 w-4" />
      </Button>
      <Button
        className="m-0 p-0"
        type="button"
        onClick={() => formatElement("center")}
        variant={"outline"}
        aria-label="Align Center"
        size="icon"
      >
        <AlignCenter className="h-4 w-4" />
      </Button>
      <Button
        className="m-0 p-0"
        type="button"
        onClick={() => formatElement("right")}
        variant={"outline"}
        aria-label="Align Right"
        size="icon"
      >
        <AlignRight className="h-4 w-4" />
      </Button>
    </div>
  );
}
