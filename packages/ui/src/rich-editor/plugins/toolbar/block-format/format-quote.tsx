import { $createQuoteNode } from "@lexical/rich-text";
import { $setBlocksType } from "@lexical/selection";
import { $getSelection, $isRangeSelection, $isTextNode } from "lexical";

import { SelectItem } from "@acme/ui/components/ui/select";

import { useToolbarContext } from "../../../context/toolbar-context";
import { blockTypeToBlockName } from "./block-format-data";

const BLOCK_FORMAT_VALUE = "quote";

export function FormatQuote() {
  const { activeEditor, blockType } = useToolbarContext();

  const formatQuote = () => {
    if (blockType !== "quote") {
      activeEditor.update(() => {
        const selection = $getSelection();
        if ($isRangeSelection(selection) && !selection.isCollapsed()) {
          // Wrap selected text in quotes
          const nodes = selection.getNodes();
          nodes.forEach((node) => {
            if ($isTextNode(node)) {
              const textContent = node.getTextContent();
              // Only wrap if not already wrapped
              if (!textContent.startsWith("“") && !textContent.endsWith("”")) {
                node.setTextContent(`“${textContent}”`);
              }
            }
          });
        }
        $setBlocksType(selection, () => $createQuoteNode());
      });
    }
  };

  return (
    <SelectItem value="quote" onPointerDown={formatQuote}>
      <div className="flex items-center gap-1 font-normal">
        {blockTypeToBlockName[BLOCK_FORMAT_VALUE]?.icon}
        {blockTypeToBlockName[BLOCK_FORMAT_VALUE]?.label}
      </div>
    </SelectItem>
  );
}
