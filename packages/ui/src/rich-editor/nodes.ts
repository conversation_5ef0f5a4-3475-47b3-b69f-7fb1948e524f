import { AutoLinkNode, LinkNode } from "@lexical/link";
import { ListItemNode, ListNode } from "@lexical/list";
import { HeadingNode, QuoteNode } from "@lexical/rich-text";
import { TableCellNode, TableNode, TableRowNode } from "@lexical/table";
import {
  type Klass,
  type LexicalNode,
  type LexicalNodeReplacement,
  ParagraphNode,
  TextNode,
} from "lexical";
import { ImageNode } from "./nodes/image-node";

export const nodes: ReadonlyArray<Klass<LexicalNode> | LexicalNodeReplacement> =
  [
    TableNode,
    TableRowNode,
    TableCellNode,
    HeadingNode,
    ParagraphNode,
    TextNode,
    QuoteNode,
    ImageNode,
    ListNode,
    ListItemNode,
    LinkNode,
    AutoLinkNode,
  ];
