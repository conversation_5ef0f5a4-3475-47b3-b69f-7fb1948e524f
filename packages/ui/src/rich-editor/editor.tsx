"use client";

import type { InitialConfigType } from "@lexical/react/LexicalComposer";
import type { EditorState, SerializedEditorState } from "lexical";
import { LexicalComposer } from "@lexical/react/LexicalComposer";
import { LexicalErrorBoundary } from "@lexical/react/LexicalErrorBoundary";
import { OnChangePlugin } from "@lexical/react/LexicalOnChangePlugin";
import { RichTextPlugin } from "@lexical/react/LexicalRichTextPlugin";

import { TooltipProvider } from "@acme/ui/components/ui/tooltip";
import { cn } from "@acme/ui/lib/utils";

import { ContentEditable } from "../rich-editor/editor-ui/content-editable";
import { FloatingLinkContext } from "./context/floating-link-context";
import { nodes } from "./nodes";
import { Plugins } from "./plugins";
import { editorTheme } from "./themes/editor-theme";

const editorConfig: InitialConfigType = {
  namespace: "Editor",
  theme: editorTheme,
  nodes,
  onError: (error: Error) => {
    console.error(error);
  },
  //   editorState
};

export function Editor({
  editorState,
  editorSerializedState,
  onChange,
  onSerializedChange,
  editable = true,
  className,
}: {
  editorState?: EditorState;
  editorSerializedState?: SerializedEditorState | string;
  onChange?: (editorState: EditorState) => void;
  onSerializedChange?: (editorSerializedState: SerializedEditorState) => void;
  editable?: boolean;
  className?: string;
}) {
  // Convert serialized state to string format for LexicalComposer
  let initialEditorStateString: string | undefined;

  if (editorState) {
    // If we have an actual EditorState object
    initialEditorStateString = JSON.stringify(editorState.toJSON());
  } else if (editorSerializedState) {
    // If we have a serialized state (either as string or object)
    if (typeof editorSerializedState === "string" && editorSerializedState) {
      try {
        // Make sure it's valid JSON before using it
        JSON.parse(editorSerializedState);
        initialEditorStateString = editorSerializedState;
      } catch (e) {
        console.error("Invalid serialized editor state:", e);
        initialEditorStateString = undefined;
      }
    } else if (typeof editorSerializedState === "object") {
      // If it's already an object, stringify it
      initialEditorStateString = JSON.stringify(editorSerializedState);
    }
  }

  // Create a stable key for the editor to force remounting when content changes
  const editorKey = "editor-instance";

  // Create the config object with conditional editorState
  const initialConfig: InitialConfigType = {
    ...editorConfig,
    editable,
    ...(initialEditorStateString
      ? { editorState: initialEditorStateString }
      : {}),
  };

  return (
    <div
      className={cn(
        "w-full rounded-lg",
        editable
          ? "hide-scrollbar h-fit overflow-scroll border bg-background shadow"
          : "",
        className,
      )}
    >
      <LexicalComposer key={editorKey} initialConfig={initialConfig}>
        {editable ? (
          <TooltipProvider>
            <FloatingLinkContext>
              <Plugins />
            </FloatingLinkContext>
          </TooltipProvider>
        ) : (
          <div className="relative">
            <RichTextPlugin
              contentEditable={
                <ContentEditable
                  className="p-2 focus:outline-none"
                  placeholder=""
                />
              }
              ErrorBoundary={LexicalErrorBoundary}
            />
          </div>
        )}
        {onChange && (
          <OnChangePlugin
            ignoreSelectionChange={true}
            onChange={(editorState) => {
              onChange?.(editorState);
              onSerializedChange?.(editorState.toJSON());
            }}
          />
        )}
      </LexicalComposer>
    </div>
  );
}
