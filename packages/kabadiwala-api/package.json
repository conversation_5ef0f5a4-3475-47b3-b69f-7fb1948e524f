{"name": "@acme/kabadiwala-api", "version": "0.1.0", "private": true, "license": "MIT", "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "default": "./src/index.ts"}, "./env": "./env.ts", "./types": "./src/types.ts"}, "scripts": {"clean": "git clean -xdf .cache .turbo dist node_modules", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "prettier": "@acme/prettier-config", "dependencies": {"@acme/core": "workspace:*", "@acme/db": "workspace:*", "@acme/kabadiwala-auth": "workspace:*", "@acme/msg91": "workspace:*", "@acme/onesignal": "workspace:*", "@acme/razorpay-sdk": "workspace:*", "@acme/validators": "workspace:*", "@onesignal/node-onesignal": "5.0.0-alpha-02", "@stream-io/node-sdk": "^0.4.25", "@trpc/server": "catalog:", "date-fns": "catalog:", "jose": "^6.0.10", "razorpay": "^2.9.6", "superjson": "2.2.2", "uploadthing": "^7.6.0", "zod": "catalog:"}, "devDependencies": {"@acme/eslint-config": "workspace:*", "@acme/prettier-config": "workspace:*", "@acme/tsconfig": "workspace:*", "eslint": "catalog:", "prettier": "catalog:", "typescript": "catalog:"}}