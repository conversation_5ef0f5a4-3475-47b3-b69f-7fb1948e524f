import { StreamClient } from "@stream-io/node-sdk";
import { createRemoteJWKSet, jwtVerify } from "jose";
import Razorpay from "razorpay";
import { UTApi } from "uploadthing/server";

import { getOneSignalClient } from "@acme/onesignal";
import { OneSignalUserType } from "@acme/onesignal/src/types";
import { Razorpay as RazorpayClient } from "@acme/razorpay-sdk";

import { env } from "../env";

let JWKS: ReturnType<typeof createRemoteJWKSet> | null = null;

export const utapi = new UTApi({
  token: env.UPLOADTHING_TOKEN,
});

export async function validateToken(token: string) {
  try {
    if (!JWKS) {
      const baseUrl = env.NEXT_PUBLIC_KABADIWALA_BETTER_AUTH_URL;
      JWKS = createRemoteJWKSet(new URL(`${baseUrl}/api/auth/jwks`));
    }

    const { payload } = await jwtVerify(token, JWKS, {
      issuer: env.NEXT_PUBLIC_KABADIWALA_BETTER_AUTH_URL,
      audience: env.NEXT_PUBLIC_KABADIWALA_BETTER_AUTH_URL,
    });

    if (!payload.sub) {
      throw new Error("Invalid token: missing subject claim");
    }

    return payload;
  } catch (error) {
    console.error("Token validation failed:", error);
    throw error;
  }
}

export const razorpay = new Razorpay({
  key_id: env.RAZORPAY_KEY_ID,
  key_secret: env.RAZORPAY_SECRET_KEY,
});

export const razorpayClientPackage = new RazorpayClient({
  keyId: env.RAZORPAY_KEY_ID,
  keySecret: env.RAZORPAY_SECRET_KEY,
});

export const streamClient = new StreamClient(
  env.GETSTREAM_API_KEY,
  env.GETSTREAM_API_SECRET,
);

export const onesignalSellerClient = getOneSignalClient(
  OneSignalUserType.SELLER,
);

export const onesignalAdminClient = getOneSignalClient(OneSignalUserType.ADMIN);
