import { authRouter } from "./router/auth";
import { callRouter } from "./router/call";
import { categoryRouter } from "./router/category";
import { conversationRouter } from "./router/conversation";
import { notificationRouter } from "./router/notification";
import { onboardingRouter } from "./router/onboarding";
import { orderRouter } from "./router/order";
import { paymentRouter } from "./router/payment";
import { supportRouter } from "./router/support";
import { userRouter } from "./router/user";
import { utilsRouter } from "./router/utils";
import { walletRouter } from "./router/wallet";
import { createTRPCRouter } from "./trpc";

export const appRouter = createTRPCRouter({
  auth: authRouter,
  user: userRouter,
  onboarding: onboardingRouter,
  order: orderRouter,
  payment: paymentRouter,
  wallet: walletRouter,
  utils: utilsRouter,
  streamCall: callRouter,
  category: categoryRouter,
  conversation: conversationRouter,
  support: supportRouter,
  notification: notificationRouter,
});

// export type definition of API
export type AppRouter = typeof appRouter;
