import type { TRP<PERSON>outerRecord } from "@trpc/server";
import { TRPCError } from "@trpc/server";
import { z } from "zod";

import { tryCatch } from "@acme/validators/utils";

import { protectedProcedure } from "../trpc";
import { streamClient } from "../utils";

export const callRouter = {
  createCall: protectedProcedure
    .input(
      z.object({
        orderId: z.string(),
        recipientId: z.string(),
        callerId: z.string(),
      }),
    )
    .query(async ({ input }) => {
      const uniqueCallId = `${input.orderId}-${Date.now()}`.slice(0, 64);
      const call = streamClient.video.call("default", uniqueCallId);

      const { err: callerErr } = await tryCatch(
        streamClient.upsertUsers([
          {
            id: input.callerId,
          },
        ]),
      );

      if (callerErr) {
        console.error("Caller upsert error:", callerErr);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: `Failed to create the call.`,
        });
      }

      const { err: recipientErr } = await tryCatch(
        streamClient.upsertUsers([
          {
            id: input.recipientId,
          },
        ]),
      );

      if (recipientErr) {
        console.error("Recipient upsert error:", recipientErr);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: `Failed to create the call.`,
        });
      }

      const { err: callErr } = await tryCatch(
        call.create({
          data: {
            created_by: {
              id: input.callerId,
              name: "Caller",
            },
            video: false,
            members: [
              { user_id: input.callerId },
              { user_id: input.recipientId },
            ],
            settings_override: {
              video: {
                enabled: false,
                target_resolution: {
                  height: 360,
                  width: 640,
                  bitrate: 1000,
                },
                camera_default_on: false,
                access_request_enabled: false,
              },
              audio: {
                mic_default_on: true,
                default_device: "speaker",
              },
              thumbnails: {
                enabled: true,
              },
              limits: {
                max_participants: 2,
                max_duration_seconds: 3600,
              },
              ring: {
                incoming_call_timeout_ms: 30000,
                missed_call_timeout_ms: 60000,
                auto_cancel_timeout_ms: 60000,
              },
              session: {
                inactivity_timeout_seconds: 300,
              },
              screensharing: {
                enabled: false,
                access_request_enabled: false,
              },
            },
          },
        }),
      );

      if (callErr) {
        console.error("Call creation error:", callErr);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: `Failed to create the call.`,
        });
      }

      return {
        callId: call.id,
      };
    }),
} satisfies TRPCRouterRecord;
