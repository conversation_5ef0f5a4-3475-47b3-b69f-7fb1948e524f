import type { TRPCRouterRecord } from "@trpc/server";
import { TRPCError } from "@trpc/server";

import { desc, eq } from "@acme/db";
import { notification } from "@acme/db/schema";
import { tryCatch } from "@acme/validators/utils";

import { protectedProcedure } from "../trpc";

export const notificationRouter = {
  getAll: protectedProcedure.query(async ({ ctx }) => {
    const { data: notifications, err: notificationError } = await tryCatch(
      ctx.db.query.notification.findMany({
        where: eq(notification.kabadiwalaId, ctx.session.user.id),
        orderBy: desc(notification.createdAt),
      }),
    );

    if (notificationError) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch notifications",
      });
    }

    return notifications;
  }),
} satisfies TRPCRouterRecord;
