import type { TRP<PERSON>outerRecord } from "@trpc/server";
import { z } from "zod";

import {
  getCategoryById,
  getSubCategoriesWithOrWithoutParent,
  getTopLevelCategories,
  searchCategoryByName,
} from "@acme/core";

import { protectedProcedure } from "../trpc";

export const categoryRouter = {
  getTopLevelCategories: protectedProcedure
    .input(z.object({ limit: z.number().min(1).optional() }))
    .query(async ({ input }) => {
      const data = await getTopLevelCategories(input.limit);
      return data;
    }),

  getSubCategoriesWithOrWithoutParent: protectedProcedure
    .input(z.object({ id: z.string(), withParent: z.boolean().optional() }))
    .query(async ({ input }) => {
      const data = await getSubCategoriesWithOrWithoutParent(
        input.id,
        input.withParent,
      );
      return data;
    }),

  getCategoryById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ input }) => {
      const category = await getCategoryById(input.id);
      return { category };
    }),

  searchCategoryByName: protectedProcedure
    .input(
      z.object({
        name: z.string().min(1, "Name is required"),
        limit: z.number().min(1).optional(),
      }),
    )
    .query(async ({ input }) => {
      const categories = await searchCategoryByName(input.name, input.limit);
      return categories;
    }),
} satisfies TRPCRouterRecord;
