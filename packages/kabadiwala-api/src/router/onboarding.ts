import type { TRPCRouterRecord } from "@trpc/server";
import { TRPCError } from "@trpc/server";
import axios from "axios";
import { format } from "date-fns";

import { and, eq, not } from "@acme/db";
import {
  kabadiwala,
  kabadiwalaAddress,
  kabadiwalaMedia,
  OnBoardingEnum,
  vehicle,
} from "@acme/db/schema";
import {
  KabadiwalaOnboardingStepFiveSchema,
  KabadiwalaOnboardingStepFourSchema,
  KabadiwalaOnboardingStepOneSchema,
  KabadiwalaOnboardingStepSixSchema,
  KabadiwalaOnboardingStepThreeSchema,
  KabadiwalaOnboardingStepTwoSchema,
} from "@acme/validators/kabadiwala";
import { tryCatch } from "@acme/validators/utils";

import type { DLAdvanceApiResponse } from "../types";
import { env } from "../../env";
import { protectedProcedure } from "../trpc";

export const onboardingRouter = {
  getOnboardingAndActiveAccDetails: protectedProcedure.query(
    async ({ ctx }) => {
      const { data: kabadiwalaDetails, err: kabadiwalaErr } = await tryCatch(
        ctx.db.query.kabadiwala.findFirst({
          where: eq(kabadiwala.id, ctx.session.user.id),
          columns: {
            onboarding: true,
            isBlocked: true,
            onboardingCompleted: true,
          },
        }),
      );

      if (kabadiwalaErr || !kabadiwalaDetails) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch Kabadiwala onboarding details",
        });
      }

      return {
        lastStepOnBoardingLeftAt: kabadiwalaDetails.onboarding,
        onBoardingCompleted: kabadiwalaDetails.onboardingCompleted,
        isActive:
          kabadiwalaDetails.isBlocked === null ||
          kabadiwalaDetails.isBlocked === false,
      };
    },
  ),

  getStep_1Details: protectedProcedure.query(async ({ ctx }) => {
    const { data, err } = await tryCatch(
      ctx.db
        .select({
          name: kabadiwala.name,
          email: kabadiwala.email,
          image: kabadiwala.image,
          imageKey: kabadiwala.imageFileKey,
        })
        .from(kabadiwala)
        .where(eq(kabadiwala.id, ctx.session.user.id)),
    );
    const kabadiwalaDetails = data?.at(0);

    if (err || !kabadiwalaDetails) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch Kabadiwala onboarding step 1 details",
      });
    }

    return {
      fullName: kabadiwalaDetails.name,
      email: kabadiwalaDetails.email,
      imageUrl: kabadiwalaDetails.image,
      imageKey: kabadiwalaDetails.imageKey,
    };
  }),

  step_1: protectedProcedure
    .input(KabadiwalaOnboardingStepOneSchema)
    .mutation(async ({ ctx, input }) => {
      if (input.email) {
        const { data, err } = await tryCatch(
          ctx.db
            .select()
            .from(kabadiwala)
            .where(
              and(
                eq(kabadiwala.email, input.email),
                not(eq(kabadiwala.id, ctx.session.user.id)),
              ),
            ),
        );

        if (err) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: "Kabadiwala onboarding step 1 failed",
          });
        }

        if (data.length > 0) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Kabadiwala already exists",
            cause: "Kabadiwala already exists",
          });
        }
      }

      const { err } = await tryCatch(
        ctx.db
          .update(kabadiwala)
          .set({
            name: input.fullName,
            email: input.email,
            image: input.imageUrl,
            imageFileKey: input.imageFileKey,
            onboarding: OnBoardingEnum.enumValues[1], // STEP_2
          })
          .where(eq(kabadiwala.id, ctx.session.user.id)),
      );

      if (err) {
        if (err instanceof TRPCError) {
          throw err;
        }

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Kabadiwala onboarding step 1 failed",
        });
      }

      return {
        message: "Kabadiwala onboarding step 1 completed",
        subMessage:
          "Your profile has been updated successfully. Let's continue to the next step.",
        nextStep: OnBoardingEnum.enumValues[1],
      };
    }),

  getStep_2Details: protectedProcedure.query(async ({ ctx }) => {
    const { data, err } = await tryCatch(
      ctx.db.query.vehicle.findFirst({
        where: eq(vehicle.kabadiwalaId, ctx.session.user.id),
      }),
    );

    if (err) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch Kabadiwala onboarding step 2 details",
      });
    }

    return {
      vehicle: data,
    };
  }),

  step_2: protectedProcedure
    .input(KabadiwalaOnboardingStepTwoSchema)
    .mutation(async ({ ctx, input }) => {
      if (input.vehicleNumber && input.vehicleNumber.length > 0) {
        // Check if a vehicle with the same number already exists for this kabadiwala
        const { data: existingVehicle, err: existingErr } = await tryCatch(
          ctx.db.query.vehicle.findFirst({
            where: and(
              eq(vehicle.vehicleNumber, input.vehicleNumber),
              not(eq(kabadiwala.id, ctx.session.user.id)),
            ),
          }),
        );

        if (existingErr) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: "Failed to check existing vehicles",
          });
        }

        if (existingVehicle) {
          if (existingVehicle.kabadiwalaId === ctx.session.user.id) {
            // If the vehicle belongs to the same kabadiwala, no conflict
            return {
              message: "Kabadiwala onboarding step 2 completed",
              subMessage:
                "Your vehicle details have been updated successfully.",
              nextStep: OnBoardingEnum.enumValues[2],
            };
          }

          throw new TRPCError({
            code: "CONFLICT",
            message: "A vehicle with this number already exists",
          });
        }
      }

      const { data: existingVehicle, err: existingErr } = await tryCatch(
        ctx.db.query.vehicle.findFirst({
          where: and(
            eq(vehicle.kabadiwalaId, ctx.session.user.id),
            eq(vehicle.isActive, true),
          ),
        }),
      );

      if (existingErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to check existing vehicles",
        });
      }

      if (existingVehicle) {
        const { err: vehicleUpdateError } = await tryCatch(
          ctx.db
            .update(vehicle)
            .set({
              vehicleType: input.vehicleType,
              vehicleNumber:
                input.vehicleNumber?.length === 0 ? null : input.vehicleNumber,
            })
            .where(eq(vehicle.id, existingVehicle.id)),
        );

        if (vehicleUpdateError) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: "Failed to update vehicle details",
          });
        }

        return {
          message: "Kabadiwala onboarding step 2 completed",
          subMessage: "Your vehicle details have been updated successfully.",
          nextStep: OnBoardingEnum.enumValues[2],
        };
      }

      const { err } = await tryCatch(
        ctx.db
          .insert(vehicle)
          .values({
            kabadiwalaId: ctx.session.user.id,
            vehicleType: input.vehicleType,
            vehicleNumber:
              input.vehicleNumber?.length === 0 ? null : input.vehicleNumber,
            isActive: true, // Default to active because this is the 1st vehicle is being added
          })
          .returning(),
      );

      if (err) {
        console.error("Error creating vehicle:", err);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create vehicle",
        });
      }

      return {
        message: "Kabadiwala onboarding step 2 completed",
        subMessage: "Your vehicle details have been saved successfully.",
        nextStep: OnBoardingEnum.enumValues[2],
      };
    }),

  getStep_3Details: protectedProcedure.query(async ({ ctx }) => {
    const { data: kabadiwalaDetails, err } = await tryCatch(
      ctx.db.query.kabadiwala.findFirst({
        where: eq(kabadiwala.id, ctx.session.user.id),
        columns: {
          frontOfAdharCardMediaId: true,
          backOfAdharCardMediaId: true,
          id: true,
          adharCardNumber: true,
        },
        with: {
          frontAdharCardMedia: {
            columns: { mediaUrl: true, fileKey: true },
          },
          backAdharCardMedia: {
            columns: { mediaUrl: true, fileKey: true },
          },
        },
      }),
    );

    if (err || !kabadiwalaDetails) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch Kabadiwala onboarding step 3 details",
      });
    }

    const frontMediaUrl = kabadiwalaDetails.frontAdharCardMedia?.mediaUrl;
    const frontMediaKey = kabadiwalaDetails.frontAdharCardMedia?.fileKey;
    const backMediaUrl = kabadiwalaDetails.backAdharCardMedia?.mediaUrl;
    const backMediaKey = kabadiwalaDetails.backAdharCardMedia?.fileKey;

    return {
      adharCardNumber: kabadiwalaDetails.adharCardNumber,
      frontOfAdharCardImageUrl: frontMediaUrl,
      backOfAdharCardImageUrl: backMediaUrl,
      frontOfAdharCardFileKey: frontMediaKey,
      backOfAdharCardFileKey: backMediaKey,
      frontOfAdharCardMediaId: kabadiwalaDetails.frontOfAdharCardMediaId,
      backOfAdharCardMediaId: kabadiwalaDetails.backOfAdharCardMediaId,
    };
  }),

  step_3: protectedProcedure
    .input(KabadiwalaOnboardingStepThreeSchema)
    .mutation(async ({ ctx, input }) => {
      const userId = ctx.session.user.id;
      let frontMediaId = null;
      let backMediaId = null;

      // Create media entry for front Aadhar card
      if (input.frontOfAdharCardImageUrl) {
        const { data: frontMedia, err: frontMediaErr } = await tryCatch(
          ctx.db
            .insert(kabadiwalaMedia)
            .values({
              kabadiwalaId: userId,
              mediaUrl: input.frontOfAdharCardImageUrl,
              mediaType: "IMAGE",
              fileName: "front-aadhar-card",
              mimeType: "image/jpeg",
              fileKey: input.frontOfAdharCardFileKey,
            })
            .returning(),
        );

        if (frontMediaErr || !frontMedia.length) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: "Failed to save front Aadhar card image",
          });
        }

        frontMediaId = frontMedia[0]?.id;
      }

      // Create media entry for back Aadhar card
      if (input.backOfAdharCardImageUrl) {
        const { data: backMedia, err: backMediaErr } = await tryCatch(
          ctx.db
            .insert(kabadiwalaMedia)
            .values({
              kabadiwalaId: userId,
              mediaUrl: input.backOfAdharCardImageUrl,
              mediaType: "IMAGE",
              fileName: "back-aadhar-card",
              mimeType: "image/jpeg",
              fileKey: input.backOfAdharCardFileKey,
            })
            .returning(),
        );

        if (backMediaErr || !backMedia.length) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: "Failed to save back Aadhar card image",
          });
        }

        backMediaId = backMedia[0]?.id;
      }

      const { err } = await tryCatch(
        ctx.db
          .update(kabadiwala)
          .set({
            frontOfAdharCardMediaId: frontMediaId,
            backOfAdharCardMediaId: backMediaId,
            adharCardNumber: input.adharCardNumber,
            onboarding: "STEP_4", // STEP_4
          })
          .where(eq(kabadiwala.id, userId)),
      );

      if (err) {
        if (err instanceof TRPCError) {
          throw err;
        }

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Kabadiwala onboarding step 3 failed",
        });
      }

      return {
        message: "Kabadiwala onboarding step 3 completed",
        subMessage: "Your Aadhar card details have been saved successfully.",
        nextStep: OnBoardingEnum.enumValues[3],
      };
    }),

  step_4: protectedProcedure
    .input(KabadiwalaOnboardingStepFourSchema)
    .mutation(async ({ ctx, input }) => {
      const userId = ctx.session.user.id;

      // Verify vehicle number using external API
      const { data: verificationData, err: verificationErr } = await tryCatch(
        axios.post<DLAdvanceApiResponse>(
          "https://dl-advance.befisc.com",
          {
            dl_no: input.dlNumber,
            dob: format(input.dob, "dd-MM-yyyy"),
          },
          {
            headers: {
              authkey: env.DL_VERIFICATION_API_KEY,
              "Content-Type": "application/json",
            },
          },
        ),
      );

      if (verificationErr || verificationData.status !== 200) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Invalid vehicle number or verification failed",
        });
      }

      if (verificationData.data.status !== 1) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: verificationData.data.message,
        });
      }

      const { err } = await tryCatch(
        ctx.db
          .update(kabadiwala)
          .set({
            dlVerificationResponse: verificationData.data.result,
            onboarding: OnBoardingEnum.enumValues[3], // STEP_4
          })
          .where(eq(kabadiwala.id, userId)),
      );

      if (err) {
        if (err instanceof TRPCError) {
          throw err;
        }

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Kabadiwala onboarding step 3 failed",
        });
      }

      return {
        message: "Kabadiwala onboarding step 3 completed",
        subMessage:
          "Your Driving License details have been saved successfully.",
        nextStep: OnBoardingEnum.enumValues[3],
      };
    }),

  skipStep4: protectedProcedure.mutation(async ({ ctx }) => {
    const userId = ctx.session.user.id;

    const { err } = await tryCatch(
      ctx.db
        .update(kabadiwala)
        .set({
          dlVerificationResponse: null,
          onboarding: "STEP_5",
        })
        .where(eq(kabadiwala.id, userId)),
    );

    if (err) {
      if (err instanceof TRPCError) {
        throw err;
      }

      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Kabadiwala onboarding step 3 failed",
      });
    }

    return {
      message: "Kabadiwala onboarding step 3 skipped",
      subMessage:
        "You can update your Driving License details later in your profile.",
      nextStep: "STEP_5",
    };
  }),

  getStep_5Details: protectedProcedure.query(async ({ ctx }) => {
    const { data: kabadiwalaDetails, err } = await tryCatch(
      ctx.db.query.kabadiwala.findFirst({
        where: eq(kabadiwala.id, ctx.session.user.id),
        columns: {
          policeVerificationMediaId: true,
          id: true,
        },
        with: {
          policeVerificationMedia: {
            columns: { mediaUrl: true, fileKey: true },
          },
        },
      }),
    );

    if (err || !kabadiwalaDetails) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch Kabadiwala onboarding step 4 details",
      });
    }

    const mediaUrl = kabadiwalaDetails.policeVerificationMedia?.mediaUrl;
    const fileKey = kabadiwalaDetails.policeVerificationMedia?.fileKey;

    return {
      policeVerificationDocumentUrl: mediaUrl,
      policeVerificationDocumentFileKey: fileKey,
      policeVerificationMediaId: kabadiwalaDetails.policeVerificationMediaId,
    };
  }),

  step_5: protectedProcedure
    .input(KabadiwalaOnboardingStepFiveSchema)
    .mutation(async ({ ctx, input }) => {
      const userId = ctx.session.user.id;
      let mediaId = null;

      // Create media entry for police verification document
      if (input.policeVerificationDocumentUrl) {
        const { data: documentMedia, err: documentMediaErr } = await tryCatch(
          ctx.db
            .insert(kabadiwalaMedia)
            .values({
              kabadiwalaId: userId,
              mediaUrl: input.policeVerificationDocumentUrl,
              mediaType: "IMAGE",
              fileName: "police-verification",
              mimeType: "image/jpeg",
              fileKey: input.policeVerificationDocumentFileKey, // Save the file key
            })
            .returning(),
        );

        if (documentMediaErr || !documentMedia.length) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: "Failed to save police verification document",
          });
        }

        mediaId = documentMedia[0]?.id;
      }

      const { err } = await tryCatch(
        ctx.db
          .update(kabadiwala)
          .set({
            policeVerificationMediaId: mediaId,
            onboarding: "STEP_6",
          })
          .where(eq(kabadiwala.id, userId)),
      );

      if (err) {
        if (err instanceof TRPCError) {
          throw err;
        }

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Kabadiwala onboarding step 4 failed",
        });
      }

      return {
        message: "Kabadiwala onboarding step 4 completed",
        subMessage:
          "Your police verification document has been saved successfully.",
        nextStep: "STEP_6",
      };
    }),

  getStep_6Details: protectedProcedure.query(async ({ ctx }) => {
    const { data, err } = await tryCatch(
      ctx.db.query.kabadiwala.findFirst({
        where: eq(kabadiwala.id, ctx.session.user.id),
        with: {
          addresses: {
            where: eq(kabadiwalaAddress.isDefault, true),
            limit: 1,
          },
        },
      }),
    );

    if (err || !data) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch Kabadiwala onboarding step 5 details",
      });
    }

    // Return the default address if exists
    const defaultAddress = data.addresses[0];

    return {
      address: defaultAddress
        ? {
            name: defaultAddress.name,
            street: defaultAddress.street,
            city: defaultAddress.city,
            state: defaultAddress.state,
            country: defaultAddress.country,
            postalCode: defaultAddress.postalCode,
            coordinates: defaultAddress.coordinates,
            addressType: defaultAddress.addressType,
            localAddress: defaultAddress.localAddress,
            display: defaultAddress.display,
            landmark: defaultAddress.landmark,
          }
        : undefined,
    };
  }),

  step_6: protectedProcedure
    .input(KabadiwalaOnboardingStepSixSchema)
    .mutation(async ({ ctx, input }) => {
      const userId = ctx.session.user.id;

      const { err } = await tryCatch(
        ctx.db.transaction(async (tx) => {
          await tx.insert(kabadiwalaAddress).values({
            name: input.address.name,
            addressType: input.address.addressType,
            display: input.address.display,
            street: input.address.street,
            city: input.address.city,
            state: input.address.state,
            country: input.address.country,
            postalCode: input.address.postalCode,
            coordinates: input.address.coordinates,
            localAddress: input.address.localAddress,
            landmark: input.address.landmark,
            isDefault: true, // Set as default address
            kabadiwalaId: userId,
          });

          await tx
            .update(kabadiwala)
            .set({
              onboardingCompleted: true,
              onboarding: "STEP_6",
            })
            .where(eq(kabadiwala.id, userId));
        }),
      );

      if (err) {
        if (err instanceof TRPCError) {
          throw err;
        }

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Kabadiwala onboarding step 5 failed",
        });
      }

      return {
        message: "Kabadiwala onboarding completed",
        subMessage:
          "Your profile is now complete. You can start using the app.",
        nextStep: null, // No next step as this is the final step
      };
    }),
} satisfies TRPCRouterRecord;
