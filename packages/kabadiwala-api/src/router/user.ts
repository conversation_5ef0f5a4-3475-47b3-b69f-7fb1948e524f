import type { TRPCRouterRecord } from "@trpc/server";
import { TRPCError } from "@trpc/server";
import { z } from "zod";

import { and, eq, not, or } from "@acme/db";
import {
  kabadiwala,
  kabadiwalaAddress,
  kabadiwalaLocationHistory,
  kabadiwalaMedia,
  vehicle,
} from "@acme/db/schema";
import {
  EditProfileSchema,
  LocationUpdateSchema,
  UpdateWorkHoursSchema,
  VehicleSchema,
} from "@acme/validators/kabadiwala";
import { tryCatch } from "@acme/validators/utils";

import { getKabadiwalaOnDuty } from "../../../core/src/kabadiwala";
import { protectedProcedure } from "../trpc";

export const userRouter = {
  getProfile: protectedProcedure.query(async ({ ctx }) => {
    const { data, err } = await tryCatch(
      ctx.db.query.kabadiwala.findFirst({
        where: eq(kabadiwala.id, ctx.session.user.id),
        columns: {
          id: true,
          name: true,
          phoneNumber: true,
          email: true,
          image: true,
          imageFileKey: true,
          emailVerified: true,
          phoneNumberVerified: true,
          averageRating: true,
        },
        with: {
          addresses: {
            where: eq(kabadiwalaAddress.isDefault, true),
            limit: 1,
          },
          vehicles: {
            limit: 3,
          },
        },
      }),
    );

    if (err || !data) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Kabadiwala not found",
      });
    }

    return data;
  }),

  getDocumentDetails: protectedProcedure.query(async ({ ctx }) => {
    const kabadiwalaData = await ctx.db.query.kabadiwala.findFirst({
      where: eq(kabadiwala.id, ctx.session.user.id),
      with: {
        frontAdharCardMedia: {
          columns: {
            id: true,
            fileName: true,
            mediaUrl: true,
            fileKey: true,
          },
        },
        backAdharCardMedia: {
          columns: {
            id: true,
            fileName: true,
            mediaUrl: true,
            fileKey: true,
          },
        },
        policeVerificationMedia: {
          columns: {
            id: true,
            fileName: true,
            mediaUrl: true,
            fileKey: true,
          },
        },
      },
    });

    if (!kabadiwalaData) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Kabadiwala not found",
      });
    }

    return {
      frontAdharCard: kabadiwalaData.frontAdharCardMedia
        ? {
            id: kabadiwalaData.frontAdharCardMedia.id,
            fileName: kabadiwalaData.frontAdharCardMedia.fileName,
            mediaUrl: kabadiwalaData.frontAdharCardMedia.mediaUrl,
            fileKey: kabadiwalaData.frontAdharCardMedia.fileKey,
          }
        : null,
      backAdharCard: kabadiwalaData.backAdharCardMedia
        ? {
            id: kabadiwalaData.backAdharCardMedia.id,
            fileName: kabadiwalaData.backAdharCardMedia.fileName,
            mediaUrl: kabadiwalaData.backAdharCardMedia.mediaUrl,
            fileKey: kabadiwalaData.backAdharCardMedia.fileKey,
          }
        : null,
      policeVerificationDocument: kabadiwalaData.policeVerificationMedia
        ? {
            id: kabadiwalaData.policeVerificationMedia.id,
            fileName: kabadiwalaData.policeVerificationMedia.fileName,
            mediaUrl: kabadiwalaData.policeVerificationMedia.mediaUrl,
            fileKey: kabadiwalaData.policeVerificationMedia.fileKey,
          }
        : null,
    };
  }),

  getDutyStatus: protectedProcedure.query(async ({ ctx }) => {
    const kabadiwalaData = await ctx.db.query.kabadiwala.findFirst({
      where: eq(kabadiwala.id, ctx.session.user.id),
      columns: {
        isOnDuty: true,
        workHoursMode: true,
        scheduleHours: true,
      },
    });

    if (!kabadiwalaData) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Kabadiwala not found",
      });
    }

    const onDuty = getKabadiwalaOnDuty({
      isOnDuty: kabadiwalaData.isOnDuty,
      workHoursMode: kabadiwalaData.workHoursMode,
      scheduleHours: kabadiwalaData.scheduleHours,
    });

    return {
      isOnDuty: onDuty,
    };
  }),

  getWalletBalance: protectedProcedure.query(async ({ ctx }) => {
    const { data, err } = await tryCatch(
      ctx.db.query.kabadiwala.findFirst({
        where: eq(kabadiwala.id, ctx.session.user.id),
        columns: { walletBalance: true },
      }),
    );

    if (err || !data) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Kabadiwala not found",
      });
    }

    return { walletBalance: Number(data.walletBalance).toFixed(2) };
  }),

  getVehicles: protectedProcedure.query(async ({ ctx }) => {
    const { data, err } = await tryCatch(
      ctx.db.query.vehicle.findMany({
        where: eq(vehicle.kabadiwalaId, ctx.session.user.id),
        orderBy: (vehicle, { desc }) => [desc(vehicle.isActive)],
      }),
    );

    if (err) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch vehicles",
      });
    }

    return { vehicles: data };
  }),

  getSettingsInformation: protectedProcedure.query(async ({ ctx }) => {
    const kabadiwalaData = await ctx.db.query.kabadiwala.findFirst({
      where: eq(kabadiwala.id, ctx.session.user.id),
      columns: {
        generalNotificationPermission: true,
        chatNotificationPermission: true,
      },
    });
    if (!kabadiwalaData) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Kabadiwala not found",
      });
    }
    return {
      generalNotificationPermission:
        kabadiwalaData.generalNotificationPermission,
      chatNotificationPermission: kabadiwalaData.chatNotificationPermission,
    };
  }),

  getWorkHoursSettings: protectedProcedure.query(async ({ ctx }) => {
    const { data, err } = await tryCatch(
      ctx.db.query.kabadiwala.findFirst({
        where: eq(kabadiwala.id, ctx.session.user.id),
        columns: {
          workHoursMode: true,
          scheduleHours: true,
        },
      }),
    );

    if (err || !data) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Kabadiwala not found",
      });
    }

    return {
      workHoursMode: data.workHoursMode ?? "MANUAL",
      scheduleHours: data.scheduleHours,
    };
  }),

  toggleDuty: protectedProcedure
    .input(z.object({ onDuty: z.boolean() }))
    .mutation(async ({ ctx, input }) => {
      // First check if kabadiwala is in MANUAL mode
      const kabadiwalaData = await ctx.db.query.kabadiwala.findFirst({
        where: eq(kabadiwala.id, ctx.session.user.id),
        columns: { workHoursMode: true, scheduleHours: true },
      });

      if (!kabadiwalaData) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Kabadiwala not found",
        });
      }

      // If in AUTOMATIC mode, prevent manual toggling
      if (kabadiwalaData.workHoursMode === "AUTOMATIC") {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message:
            "Cannot manually toggle duty in automatic mode. Please switch to manual mode first.",
        });
      }

      const { err } = await tryCatch(
        ctx.db
          .update(kabadiwala)
          .set({
            isOnDuty: input.onDuty,
          })
          .where(eq(kabadiwala.id, ctx.session.user.id)),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update duty status",
        });
      }

      return { message: "Duty status updated successfully" };
    }),

  createVehicle: protectedProcedure
    .input(VehicleSchema)
    .mutation(async ({ ctx, input }) => {
      if (!input.vehicleNumber) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Vehicle number is required",
        });
      }

      // Check if a vehicle with the same number already exists for this kabadiwala
      const { data: existingVehicle, err: existingErr } = await tryCatch(
        ctx.db.query.vehicle.findFirst({
          where: and(eq(vehicle.vehicleNumber, input.vehicleNumber)),
        }),
      );

      if (existingErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to check existing vehicles",
        });
      }

      if (existingVehicle) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "A vehicle with this number already exists",
        });
      }

      const { data, err } = await tryCatch(
        ctx.db
          .insert(vehicle)
          .values({
            kabadiwalaId: ctx.session.user.id,
            vehicleType: input.vehicleType,
            vehicleNumber: input.vehicleNumber,
            isActive: false, // Default to inactive
          })
          .returning(),
      );

      if (err || !data[0]) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create vehicle",
        });
      }

      return { message: "Vehicle created successfully", vehicle: data[0] };
    }),

  updateProfile: protectedProcedure
    .input(EditProfileSchema)
    .mutation(async ({ ctx, input }) => {
      // check for existing kabadiwala with the same phone number or email
      const { data: existingUser, err: existingUserError } = await tryCatch(
        ctx.db.query.kabadiwala.findFirst({
          where: and(
            or(
              eq(kabadiwala.phoneNumber, input.phoneNumber),
              eq(kabadiwala.email, input.email),
            ),
            not(eq(kabadiwala.id, ctx.session.user.id)), // Exclude current user
          ),
        }),
      );

      if (existingUserError) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to check existing kabadiwala",
        });
      }

      if (existingUser) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "User with this phone number or email already exists",
        });
      }

      const { data, err } = await tryCatch(
        ctx.db
          .update(kabadiwala)
          .set({
            name: input.fullName,
            phoneNumber: input.phoneNumber,
            email: input.email,
            image: input.imageUrl,
            imageFileKey: input.imageFileKey,
          })
          .where(eq(kabadiwala.id, ctx.session.user.id))
          .returning(),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update profile",
        });
      }

      return { message: "Profile updated successfully", user: data[0] };
    }),

  updateDocuments: protectedProcedure
    .input(
      z.object({
        mediaId: z.string(),
        fileUrl: z.string(),
        fileKey: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { err } = await tryCatch(
        ctx.db
          .update(kabadiwalaMedia)
          .set({
            mediaUrl: input.fileUrl,
            fileKey: input.fileKey,
          })
          .where(eq(kabadiwalaMedia.id, input.mediaId)),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update front Adhar card",
        });
      }

      return { message: "Document updated successfully" };
    }),

  updateVehicle: protectedProcedure
    .input(VehicleSchema.extend({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      if (!input.vehicleNumber) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Vehicle number is required",
        });
      }

      // check whether the vehicle number already exists for another vehicle
      const { data: vehicleWithSameNumber, err: vehicleWithSameNumberError } =
        await tryCatch(
          ctx.db.query.vehicle.findFirst({
            where: and(eq(vehicle.vehicleNumber, input.vehicleNumber)),
          }),
        );

      if (vehicleWithSameNumberError) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to check existing vehicles",
        });
      }

      if (vehicleWithSameNumber && vehicleWithSameNumber.id !== input.id) {
        throw new TRPCError({
          code: "CONFLICT",
          message: "A vehicle with this number already exists",
        });
      }

      // verify the vehicle belongs to this kabadiwala
      const { data: existingVehicle, err: existingErr } = await tryCatch(
        ctx.db.query.vehicle.findFirst({
          where: and(
            eq(vehicle.id, input.id),
            eq(vehicle.kabadiwalaId, ctx.session.user.id),
          ),
        }),
      );

      if (existingErr || !existingVehicle) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message:
            "Vehicle not found or you don't have permission to update it",
        });
      }

      const { err } = await tryCatch(
        ctx.db
          .update(vehicle)
          .set({
            vehicleNumber: input.vehicleNumber,
            vehicleType: input.vehicleType,
          })
          .where(
            and(
              eq(vehicle.id, input.id),
              eq(vehicle.kabadiwalaId, ctx.session.user.id),
            ),
          ),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update vehicle information",
        });
      }

      return { message: "Vehicle information updated successfully" };
    }),

  updateWorkHoursSettings: protectedProcedure
    .input(UpdateWorkHoursSchema)
    .mutation(async ({ ctx, input }) => {
      const { err } = await tryCatch(
        ctx.db
          .update(kabadiwala)
          .set({
            workHoursMode: input.mode,
            scheduleHours: input.scheduleHours,
          })
          .where(eq(kabadiwala.id, ctx.session.user.id)),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update work hours settings",
        });
      }

      return { message: "Work hours settings updated successfully" };
    }),

  setDefaultVehicle: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // Verify this vehicle belongs to the current user
      const { data: existingVehicle, err: existingErr } = await tryCatch(
        ctx.db.query.vehicle.findFirst({
          where: and(
            eq(vehicle.id, input.id),
            eq(vehicle.kabadiwalaId, ctx.session.user.id),
          ),
        }),
      );

      if (existingErr || !existingVehicle) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message:
            "Vehicle not found or you don't have permission to modify it",
        });
      }

      // First, set all vehicles to inactive
      await tryCatch(
        ctx.db
          .update(vehicle)
          .set({ isActive: false })
          .where(eq(vehicle.kabadiwalaId, ctx.session.user.id)),
      );

      // Then set the selected one to active
      const { err } = await tryCatch(
        ctx.db
          .update(vehicle)
          .set({ isActive: true })
          .where(eq(vehicle.id, input.id)),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to set default vehicle",
        });
      }

      return { message: "Default vehicle set successfully" };
    }),

  deleteVehicle: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // First verify the vehicle belongs to this kabadiwala
      const { data: existingVehicle, err: existingErr } = await tryCatch(
        ctx.db.query.vehicle.findFirst({
          where: and(
            eq(vehicle.id, input.id),
            eq(vehicle.kabadiwalaId, ctx.session.user.id),
          ),
        }),
      );

      if (existingErr || !existingVehicle) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message:
            "Vehicle not found or you don't have permission to delete it",
        });
      }

      const { err } = await tryCatch(
        ctx.db
          .delete(vehicle)
          .where(
            and(
              eq(vehicle.id, input.id),
              eq(vehicle.kabadiwalaId, ctx.session.user.id),
            ),
          ),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to delete vehicle",
        });
      }

      return { message: "Vehicle deleted successfully" };
    }),

  updateSettings: protectedProcedure
    .input(
      z.object({
        generalNotificationPermission: z.boolean().optional(),
        chatNotificationPermission: z.boolean().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const updateData: Record<string, boolean> = {};

      if (input.generalNotificationPermission !== undefined) {
        updateData.generalNotificationPermission =
          input.generalNotificationPermission;
      }

      if (input.chatNotificationPermission !== undefined) {
        updateData.chatNotificationPermission =
          input.chatNotificationPermission;
      }

      const { err } = await tryCatch(
        ctx.db
          .update(kabadiwala)
          .set(updateData)
          .where(eq(kabadiwala.id, ctx.session.user.id)),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update settings",
        });
      }

      return { message: "Settings updated successfully" };
    }),

  updateLiveLocationWithOrWithoutOrderId: protectedProcedure
    .input(LocationUpdateSchema)
    .mutation(async ({ ctx, input }) => {
      const { err: liveLocationUpdateError } = await tryCatch(
        ctx.db
          .update(kabadiwala)
          .set({
            liveLocationCoordinate: input.coordinates,
          })
          .where(eq(kabadiwala.id, ctx.session.user.id)),
      );

      if (liveLocationUpdateError) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update live location",
        });
      }

      // maintain user location history
      const { err: locationHistoryError } = await tryCatch(
        ctx.db.insert(kabadiwalaLocationHistory).values({
          kabadiwalaId: ctx.session.user.id,
          coordinates: input.coordinates,
          orderId: input.orderId ?? null,
        }),
      );

      if (locationHistoryError) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update location history",
        });
      }

      return {
        success: true,
      };
    }),
} satisfies TRPCRouterRecord;
