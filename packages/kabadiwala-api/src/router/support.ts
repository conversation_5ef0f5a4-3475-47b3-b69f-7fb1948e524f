import type { TRPCRouterRecord } from "@trpc/server";
import { TRPCError } from "@trpc/server";
import { isNull } from "drizzle-orm";
import { z } from "zod";

import { and, eq } from "@acme/db";
import {
  admin,
  kabadiwalaSupportConversation,
  kabadiwalaSupportMessage,
  order,
} from "@acme/db/schema";
import { tryCatch } from "@acme/validators/utils";

import { protectedProcedure } from "../trpc";
import { onesignalAdminClient } from "../utils";

export const supportRouter = {
  getConversation: protectedProcedure
    .input(
      z.object({
        orderId: z.string().optional(),
      }),
    )
    .query(async ({ ctx, input }) => {
      // Find or create conversation for this kabadiwala
      const whereClause = input.orderId
        ? and(
            eq(kabadiwalaSupportConversation.kabadiwalaId, ctx.session.user.id),
            eq(kabadiwalaSupportConversation.orderId, input.orderId),
          )
        : and(
            eq(kabadiwalaSupportConversation.kabadiwalaId, ctx.session.user.id),
            isNull(kabadiwalaSupportConversation.orderId),
          );

      const { data: existingConversations, err: fetchError } = await tryCatch(
        ctx.db.query.kabadiwalaSupportConversation.findMany({
          where: whereClause,
          with: {
            messages: {
              orderBy: (messages, { asc }) => [asc(messages.createdAt)],
            },
          },
          orderBy: (conversations, { desc }) => [desc(conversations.createdAt)],
        }),
      );

      if (fetchError) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch conversation",
        });
      }

      // Return the most recent conversation if it exists
      if (existingConversations.length > 0) {
        return existingConversations[0];
      }

      // If no conversation exists, return null
      return null;
    }),

  sendMessage: protectedProcedure
    .input(
      z.object({
        orderId: z.string().optional(),
        message: z.string().min(1, "Message cannot be empty"),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // Validate order if orderId is provided
      if (input.orderId) {
        const { data: orderData, err: orderErr } = await tryCatch(
          ctx.db.query.order.findFirst({
            where: eq(order.id, input.orderId),
            columns: {
              id: true,
            },
          }),
        );

        if (orderErr || !orderData) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Order not found",
          });
        }
      }

      // Find existing conversation or create a new one
      const whereClause = input.orderId
        ? and(
            eq(kabadiwalaSupportConversation.kabadiwalaId, ctx.session.user.id),
            eq(kabadiwalaSupportConversation.orderId, input.orderId),
            eq(kabadiwalaSupportConversation.isOpen, true),
          )
        : and(
            eq(kabadiwalaSupportConversation.kabadiwalaId, ctx.session.user.id),
            isNull(kabadiwalaSupportConversation.orderId),
            eq(kabadiwalaSupportConversation.isOpen, true),
          );

      const { data: existingConversation } = await tryCatch(
        ctx.db.query.kabadiwalaSupportConversation.findFirst({
          where: whereClause,
          with: {
            kabadiwala: {
              columns: {
                id: true,
                name: true,
              },
            },
          },
        }),
      );

      let conversationId: string | undefined;

      if (existingConversation) {
        conversationId = existingConversation.id;
      } else {
        // Create a new conversation
        const { data: newConversation, err: createErr } = await tryCatch(
          ctx.db
            .insert(kabadiwalaSupportConversation)
            .values({
              kabadiwalaId: ctx.session.user.id,
              orderId: input.orderId ?? null,
              isOpen: true,
              createdAt: new Date(),
              updatedAt: new Date(),
            })
            .returning(),
        );

        if (createErr || newConversation.length === 0) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: "Failed to create conversation",
          });
        }

        conversationId = newConversation[0]?.id;
      }

      if (conversationId === undefined) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Conversation ID is undefined",
        });
      }

      // Add the message
      const { err: messageErr } = await tryCatch(
        ctx.db.insert(kabadiwalaSupportMessage).values({
          conversationId: conversationId,
          senderId: ctx.session.user.id,
          senderType: "KABADIWALA",
          content: input.message,
          createdAt: new Date(),
          isRead: false,
        }),
      );

      if (messageErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to send message",
        });
      }

      const { data: defaultAdmin, err: defaultAdminErr } = await tryCatch(
        ctx.db.query.admin.findFirst({
          where: eq(admin.email, "<EMAIL>"),
        }),
      );

      if (defaultAdminErr) {
        console.error("Default admin not found.");
      }

      if (defaultAdmin?.id) {
        await tryCatch(
          onesignalAdminClient.sendNotification({
            title: "You have a new support message.",
            message: `You have a new message from ${existingConversation?.kabadiwala.name}`,
            externalIds: [defaultAdmin.id],
          }),
        );
      }

      return { success: true };
    }),

  rateSupportConversation: protectedProcedure
    .input(
      z.object({
        conversationId: z.string().nonempty(),
        rating: z.number().min(1).max(5).nullable(),
        reviewText: z.string().max(500).optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // Fetch the conversation
      const { data: convo, err } = await tryCatch(
        ctx.db.query.kabadiwalaSupportConversation.findFirst({
          where: eq(kabadiwalaSupportConversation.id, input.conversationId),
        }),
      );
      if (err || !convo) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Conversation not found",
        });
      }
      if (convo.isOpen) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Conversation must be closed before rating.",
        });
      }
      if (convo.isRated) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Conversation already rated.",
        });
      }
      // Update rating, reviewText, and isRated
      const { err: updateErr } = await tryCatch(
        ctx.db
          .update(kabadiwalaSupportConversation)
          .set({
            rating: input.rating?.toString() ?? null,
            reviewText: input.reviewText ?? null,
            isRated: true,
          })
          .where(eq(kabadiwalaSupportConversation.id, input.conversationId)),
      );

      if (updateErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update rating",
        });
      }

      return { success: true };
    }),

  closeSupportConversation: protectedProcedure
    .input(
      z.object({
        conversationId: z.string().nonempty(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // Fetch the conversation to verify ownership
      const { data: convo, err } = await tryCatch(
        ctx.db.query.kabadiwalaSupportConversation.findFirst({
          where: and(
            eq(kabadiwalaSupportConversation.id, input.conversationId),
            eq(kabadiwalaSupportConversation.kabadiwalaId, ctx.session.user.id),
          ),
        }),
      );

      if (err || !convo) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Conversation not found",
        });
      }

      if (!convo.isOpen) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Conversation is already closed",
        });
      }

      // Close the conversation
      const { err: updateErr } = await tryCatch(
        ctx.db
          .update(kabadiwalaSupportConversation)
          .set({
            isOpen: false,
            updatedAt: new Date(),
          })
          .where(eq(kabadiwalaSupportConversation.id, input.conversationId)),
      );

      if (updateErr) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to close conversation",
        });
      }

      return { success: true };
    }),
} satisfies TRPCRouterRecord;
