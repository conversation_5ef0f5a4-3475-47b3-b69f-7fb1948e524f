import type { TRPCRouterRecord } from "@trpc/server";
import { TRPCError } from "@trpc/server";
import { z } from "zod";

import { and, desc, eq, gte, lt } from "@acme/db";
import {
  kabadiwala,
  kabadiwalaPaymentTransaction,
  systemConfiguration,
} from "@acme/db/schema";
import { tryCatch } from "@acme/validators/utils";

import { protectedProcedure } from "../trpc";

export const walletRouter = {
  getTransactionHistory: protectedProcedure
    .input(
      z.object({
        limit: z.number().min(1).max(50).default(10),
        cursor: z.string().optional().nullable(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const { limit, cursor } = input;

      const whereConditions = [
        eq(kabadiwalaPaymentTransaction.kabadiwalaId, ctx.session.user.id),
      ];

      if (cursor) {
        whereConditions.push(lt(kabadiwalaPaymentTransaction.id, cursor));
      }

      const { data, err } = await tryCatch(
        ctx.db.query.kabadiwalaPaymentTransaction.findMany({
          where: and(...whereConditions),
          orderBy: desc(kabadiwalaPaymentTransaction.createdAt),
          limit: limit + 1, // Fetch one extra to determine if there's a next page
        }),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch transaction history",
        });
      }

      const hasNextPage = data.length > limit;
      const history = hasNextPage ? data.slice(0, -1) : data;
      const nextCursor = hasNextPage
        ? history[history.length - 1]?.id
        : undefined;

      return {
        history,
        nextCursor,
        hasNextPage,
      };
    }),

  getEarningsHistory: protectedProcedure
    .input(
      z.object({
        limit: z.number().min(1).max(50).default(10),
        cursor: z.string().optional().nullable(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const { limit, cursor } = input;

      const whereConditions = [
        eq(kabadiwalaPaymentTransaction.kabadiwalaId, ctx.session.user.id),
        eq(kabadiwalaPaymentTransaction.transactionFor, "EARNINGS"),
      ];

      if (cursor) {
        whereConditions.push(lt(kabadiwalaPaymentTransaction.id, cursor));
      }

      const { data, err } = await tryCatch(
        ctx.db.query.kabadiwalaPaymentTransaction.findMany({
          where: and(...whereConditions),
          orderBy: desc(kabadiwalaPaymentTransaction.createdAt),
          limit: limit + 1, // Fetch one extra to determine if there's a next page
        }),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch earnings history",
        });
      }

      const hasNextPage = data.length > limit;
      const earnings = hasNextPage ? data.slice(0, -1) : data;
      const nextCursor = hasNextPage
        ? earnings[earnings.length - 1]?.id
        : undefined;

      return {
        earnings,
        nextCursor,
        hasNextPage,
      };
    }),

  getTotalEarning: protectedProcedure.query(async ({ ctx }) => {
    const { data, err } = await tryCatch(
      ctx.db.query.kabadiwalaPaymentTransaction.findMany({
        where: and(
          eq(kabadiwalaPaymentTransaction.kabadiwalaId, ctx.session.user.id),
          eq(kabadiwalaPaymentTransaction.transactionFor, "EARNINGS"),
        ),
      }),
    );

    if (err) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch total earnings",
      });
    }

    let totalEarnings = 0;
    data.forEach((transaction) => {
      totalEarnings += Number(transaction.amount);
    });

    return {
      totalEarnings,
    };
  }),

  getMinimumWalletBalanceNeededToAddMoney: protectedProcedure.query(
    async ({ ctx }) => {
      const { data, err } = await tryCatch(
        ctx.db.query.kabadiwala.findFirst({
          where: eq(kabadiwala.id, ctx.session.user.id),
          columns: {
            walletBalance: true,
          },
        }),
      );

      if (err || !data?.walletBalance) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch kabadiwala wallet balance",
        });
      }

      const { data: walletConfig, err: fetchWalletConfigError } =
        await tryCatch(
          ctx.db.query.systemConfiguration.findFirst({
            where: eq(
              systemConfiguration.key,
              "MINIMUM_WALLET_BALANCE_TO_ACCEPT_ORDER_BY_KABADIWALA",
            ),
          }),
        );

      if (fetchWalletConfigError || !walletConfig) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch minimum wallet balance",
        });
      }

      const currentBalance = Number(data.walletBalance);
      const minimumBalanceConfig = Number(walletConfig.value);

      if (currentBalance < 0) {
        const minimumBalance = Math.abs(currentBalance) + minimumBalanceConfig;
        return {
          minimumBalance,
        };
      } else {
        return {
          minimumBalance: minimumBalanceConfig,
        };
      }
    },
  ),

  getTodayEarnings: protectedProcedure.query(async ({ ctx }) => {
    const today = new Date();
    const startOfDay = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate(),
    );
    const endOfDay = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate() + 1,
    );

    const { data, err } = await tryCatch(
      ctx.db.query.kabadiwalaPaymentTransaction.findMany({
        where: and(
          eq(kabadiwalaPaymentTransaction.kabadiwalaId, ctx.session.user.id),
          eq(kabadiwalaPaymentTransaction.transactionFor, "EARNINGS"),
          gte(kabadiwalaPaymentTransaction.createdAt, startOfDay),
          lt(kabadiwalaPaymentTransaction.createdAt, endOfDay),
        ),
      }),
    );

    if (err) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch today's earnings",
      });
    }

    let todayEarnings = 0;
    data.forEach((transaction) => {
      todayEarnings += Number(transaction.amount);
    });

    return {
      todayEarnings,
      transactionCount: data.length,
    };
  }),
} satisfies TRPCRouterRecord;
