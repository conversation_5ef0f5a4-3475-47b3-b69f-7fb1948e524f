import crypto from "crypto";
import { TRPCError } from "@trpc/server";
import { z } from "zod";

import type { BillResponse } from "@acme/razorpay-sdk";
import { eq } from "@acme/db";
import {
  kabadiwala,
  kabadiwalaPaymentTransaction,
  order,
} from "@acme/db/schema";
import {
  AddMoneySchema,
  VerifyPaymentSchema,
} from "@acme/validators/kabadiwala";
import { tryCatch } from "@acme/validators/utils";

import { env } from "../../env";
import { createTRPCRouter, protectedProcedure } from "../trpc";
import { razorpay, razorpayClientPackage } from "../utils";

export const paymentRouter = createTRPCRouter({
  createOrder: protectedProcedure
    .input(AddMoneySchema)
    .mutation(async ({ ctx, input }) => {
      const options = {
        amount: input.amount * 100, // amount in paise
        currency: input.currency,
        receipt: `receipt_order_${Date.now()}`,
      };

      const { data, err } = await tryCatch(razorpay.orders.create(options));
      const order = data;
      if (err || !order) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create order",
        });
      }

      const { data: insertData, err: insertErr } = await tryCatch(
        ctx.db
          .insert(kabadiwalaPaymentTransaction)
          .values({
            kabadiwalaId: ctx.session.user.id,
            amount: input.amount.toString(),
            razorpayOrderId: data.id,
            status: "PENDING",
            currency: options.currency,
            transactionFor: "WALLET_TOPUP",
            transactionType: "CREDIT",
          })
          .returning(),
      );

      const transactionId = insertData?.[0]?.id;

      if (insertErr || !transactionId) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to insert payment transaction",
        });
      }

      return {
        orderId: order.id,
        transactionId: transactionId,
        name: "Add Money",
        amount: Number(order.amount),
        currency: order.currency,
        description: order.description,
      };
    }),

  verifyPayment: protectedProcedure
    .input(VerifyPaymentSchema)
    .mutation(async ({ ctx, input }) => {
      const {
        razorpayOrderId,
        razorpayPaymentId,
        razorpaySignature,
        transactionId,
      } = input;
      const generated_signature = crypto
        .createHmac("sha256", env.RAZORPAY_SECRET_KEY)
        .update(`${razorpayOrderId}|${razorpayPaymentId}`)
        .digest("hex");

      if (generated_signature !== razorpaySignature) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Payment verification failed",
        });
      }

      const { data: prevBalance, err: walletBalanceFetchErr } = await tryCatch(
        ctx.db.query.kabadiwala.findFirst({
          where: eq(kabadiwala.id, ctx.session.user.id),
          columns: { walletBalance: true },
        }),
      );

      const walletBalance = prevBalance?.walletBalance;
      if (
        walletBalanceFetchErr ||
        walletBalance === null ||
        walletBalance === undefined
      ) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch wallet balance",
        });
      }

      const { data: razorpayPaymentInfo, err } = await tryCatch(
        razorpay.payments.fetch(input.razorpayPaymentId),
      );

      if (err || !razorpayPaymentInfo.amount || !razorpayPaymentInfo.currency) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch payment information",
        });
      }

      const amountPaid = Number(razorpayPaymentInfo.amount) / 100; // Amount in rupees

      const totalAmountToBeUpdated = String(Number(walletBalance) + amountPaid);

      const { err: txnError } = await tryCatch(
        ctx.db.transaction(async (tx) => {
          const paymentUpdate = await tx
            .update(kabadiwalaPaymentTransaction)
            .set({
              status: "COMPLETED",
              razorpayPaymentId: razorpayPaymentInfo.id,
            })
            .where(eq(kabadiwalaPaymentTransaction.id, transactionId))
            .returning();

          if (!paymentUpdate.length) {
            throw new Error("Failed to update payment transaction");
          }

          const walletUpdate = await tx
            .update(kabadiwala)
            .set({
              walletBalance: totalAmountToBeUpdated,
            })
            .where(eq(kabadiwala.id, ctx.session.user.id))
            .returning();

          if (!walletUpdate.length) {
            throw new Error("Failed to update wallet balance");
          }
        }),
      );

      if (txnError) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Payment verification failed: " + txnError.message,
        });
      }

      return {
        success: true,
        message: "Payment verified successfully",
      };
    }),

  viewBill: protectedProcedure
    .input(z.object({ orderId: z.string().nonempty() }))
    .mutation(async ({ ctx, input }) => {
      const { data: orderDetails, err: fetchOrderError } = await tryCatch(
        ctx.db.query.order.findFirst({
          where: eq(order.id, input.orderId),
        }),
      );

      if (
        fetchOrderError ||
        !orderDetails?.securityFeeAmount ||
        !orderDetails.gstPercentage ||
        !orderDetails.gstAmount ||
        !orderDetails.totalServiceChargeWithGst
      ) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Order not found",
        });
      }

      if (orderDetails.kabadiwalaId !== ctx.session.user.id) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You do not have permission to view this order",
        });
      }

      if (orderDetails.status !== "COMPLETED") {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Order is not completed yet",
        });
      }

      const billData = orderDetails.billToKabadiwala
        ? (orderDetails.billToKabadiwala as BillResponse)
        : null;

      if (billData?.bill_url) {
        return {
          billUrl: billData.bill_url,
        };
      }

      // Creating razorpay bill
      const { data, error } = await razorpayClientPackage.createBill({
        business_category: "other_services",
        business_type: "ecommerce",
        receipt_delivery: "digital",
        receipt_number: input.orderId,
        receipt_summary: {
          currency: "INR",
          total_quantity: 1,
          net_payable_amount: Number(orderDetails.securityFeeAmount),
          payment_status: "success",
          total_tax_percent: Number(orderDetails.gstPercentage),
          total_tax_amount: Number(orderDetails.gstAmount),
          sub_total_amount: Number(orderDetails.totalServiceChargeWithGst),
        },
        receipt_timestamp: Math.floor(new Date().getTime() / 1000),
        receipt_type: "order_confirmation",
      });

      if (error) {
        console.error("Razorpay bill creation error:", error);
      }

      const { err: orderBillDataUpdateErr } = await tryCatch(
        ctx.db.update(order).set({
          billToKabadiwala: data,
        }),
      );

      if (orderBillDataUpdateErr) {
        console.error(
          "Failed to update order with bill data:",
          orderBillDataUpdateErr,
        );
      }

      if (!data?.bill_url) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to generate or getting bill",
        });
      }

      return {
        billUrl: data.bill_url,
      };
    }),
});
