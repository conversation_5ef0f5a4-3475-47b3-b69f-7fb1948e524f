import type { TRPCRouterRecord } from "@trpc/server";
import { TRPCError } from "@trpc/server";
import { z } from "zod";

import {
  calculateDistance,
  checkPermissionsForOrderActionKabadiwala,
  getKabadiwalaOnDuty,
  getOrderEstimatedAmount,
  getOrderTotalAmountWithBreakdown,
  getSystemConfig,
} from "@acme/core";
import { and, desc, eq, isNull, lt, notExists } from "@acme/db";
import {
  conversation,
  customerPaymentStatusEnum,
  customerPaymentTransaction,
  customerTransactionForEnum,
  customerTransactionTypeEnum,
  kabadiwala,
  kabadiwalaPaymentTransaction,
  notification,
  order,
  orderItem,
  orderStatusEnum,
  rejectedOrders,
  reviews,
  scraphub,
  seller,
} from "@acme/db/schema";
import { KabadiwalaArrivalVerificationSchema } from "@acme/validators/kabadiwala";
import { kabadiwalaToSellerNotificationMessages } from "@acme/validators/notification";
import { tryCatch } from "@acme/validators/utils";

import { protectedProcedure } from "../trpc";
import { onesignalSellerClient, razorpayClientPackage } from "../utils";

export const orderRouter = {
  getOrderById: protectedProcedure
    .input(z.object({ orderId: z.string().nonempty() }))
    .query(async ({ ctx, input }) => {
      const { data: orderDetails, err: fetchOrderError } = await tryCatch(
        ctx.db.query.order.findFirst({
          where: eq(order.id, input.orderId),
          columns: {
            pickupOtp: false,
          },
          with: {
            items: {
              with: {
                category: {
                  with: {
                    parent: true,
                  },
                },
              },
            },
            address: true,
            seller: {
              columns: {
                id: true,
                fullName: true,
                phoneNumber: true,
              },
            },
            kabadiwala: {
              columns: {
                id: true,
                name: true,
                phoneNumber: true,
              },
            },
          },
        }),
      );

      if (fetchOrderError || !orderDetails) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Order not found",
        });
      }

      return orderDetails;
    }),

  getKabadiwalaReviewByOrderId: protectedProcedure
    .input(z.object({ orderId: z.string() }))
    .query(async ({ ctx, input }) => {
      const { data: review, err } = await tryCatch(
        ctx.db.query.reviews.findFirst({
          where: and(
            eq(reviews.orderId, input.orderId),
            eq(reviews.kabadiwalaId, ctx.session.user.id),
            eq(reviews.reviewFor, "KABADIWALA"),
          ),
        }),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch review",
        });
      }

      return review;
    }),

  getSellerReviewByOrderId: protectedProcedure
    .input(z.object({ orderId: z.string(), sellerId: z.string() }))
    .query(async ({ ctx, input }) => {
      const { data: review, err } = await tryCatch(
        ctx.db.query.reviews.findFirst({
          where: and(
            eq(reviews.orderId, input.orderId),
            eq(reviews.sellerId, input.sellerId),
            eq(reviews.reviewFor, "SELLER"),
          ),
        }),
      );

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch review",
        });
      }

      return review ?? null;
    }),

  getNearbyOrder: protectedProcedure.query(async ({ ctx }) => {
    const { data: kabadiwalaDetails, err: fetchKabadiwalaError } =
      await tryCatch(
        ctx.db.query.kabadiwala.findFirst({
          where: eq(kabadiwala.id, ctx.session.user.id),
          columns: {
            isOnDuty: true,
            workHoursMode: true,
            scheduleHours: true,
            liveLocationCoordinate: true,
          },
        }),
      );

    if (fetchKabadiwalaError || !kabadiwalaDetails) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch kabadiwala details",
      });
    }

    const isKabadiwalaOnDuty = getKabadiwalaOnDuty(kabadiwalaDetails);

    if (isKabadiwalaOnDuty === false) {
      return null;
    }

    const { data: alreadyActiveOrder, err: fetchActiveOrderError } =
      await tryCatch(
        ctx.db.query.order.findFirst({
          where: and(
            eq(order.kabadiwalaId, ctx.session.user.id),
            eq(order.status, orderStatusEnum.enumValues["2"]), // "ACTIVE" status
          ),
        }),
      );

    if (fetchActiveOrderError) {
      return null;
    }

    if (alreadyActiveOrder) {
      return null;
    }

    const orderRadius = await getSystemConfig({
      key: "RADIUS_TO_SHOW_KABADIWALA_NEW_ORDER",
    });

    const finalOrderRadius = Number(orderRadius ?? 5); // this is in KM

    const { data: nearybyOrder, err: fetchNearbyOrderError } = await tryCatch(
      ctx.db.query.order.findFirst({
        where: and(
          eq(order.status, orderStatusEnum.enumValues["2"]),
          notExists(
            ctx.db
              .select()
              .from(rejectedOrders)
              .where(
                and(
                  eq(rejectedOrders.orderId, order.id),
                  eq(rejectedOrders.kabadiwalaId, ctx.session.user.id),
                ),
              ),
          ),
        ),
        with: {
          address: true,
        },
      }),
    );

    if (fetchNearbyOrderError) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch nearby order",
      });
    }

    if (!nearybyOrder) {
      return null;
    }

    const orderCoordinate = nearybyOrder.address.coordinates;
    const kabadiwalaCoordinate = kabadiwalaDetails.liveLocationCoordinate;

    if (!orderCoordinate || !kabadiwalaCoordinate) {
      return null;
    }

    const distance = calculateDistance(
      kabadiwalaCoordinate.latitude,
      kabadiwalaCoordinate.longitude,
      orderCoordinate.latitude,
      orderCoordinate.longitude,
    );

    // Check if order is within the specified radius
    if (distance > finalOrderRadius) {
      return null;
    }

    return nearybyOrder;
  }),

  getActiveOrder: protectedProcedure.query(async ({ ctx }) => {
    const { data: orderDetails, err: fetchOrderError } = await tryCatch(
      ctx.db.query.order.findFirst({
        where: and(
          eq(order.kabadiwalaId, ctx.session.user.id),
          eq(order.status, "ACTIVE"),
          isNull(order.paymentCompletedAt),
        ),
        columns: {
          pickupOtp: false,
        },
        with: {
          seller: {
            columns: {
              id: true,
              fullName: true,
              phoneNumber: true,
            },
          },
          items: {
            with: {
              category: {
                with: {
                  parent: {
                    columns: {
                      name: true,
                    },
                  },
                },
              },
            },
          },
          address: true,
        },
      }),
    );

    if (fetchOrderError) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch active order",
      });
    }

    return orderDetails;
  }),

  getEstimatedOrderTotalAmount: protectedProcedure
    .input(z.object({ orderId: z.string().nonempty() }))
    .query(async ({ ctx, input }) => {
      const { data: orderDetails, err: fetchOrderError } = await tryCatch(
        ctx.db.query.order.findFirst({
          where: eq(order.id, input.orderId),
          with: {
            items: {
              with: {
                category: true,
              },
            },
          },
        }),
      );

      if (fetchOrderError || !orderDetails) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch order details",
        });
      }

      const totalAmount = orderDetails.items.reduce((acc, item) => {
        const rate = item.category.rate;
        const quantity = parseFloat(item.quantity) || 0;
        return acc + Number(rate) * quantity;
      }, 0);

      return totalAmount;
    }),

  getOrderHistory: protectedProcedure
    .input(
      z.object({
        limit: z.number().min(1).max(100).default(10),
        cursor: z.string().optional().nullable(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const whereConditions = [eq(order.kabadiwalaId, ctx.session.user.id)];

      if (input.cursor) {
        whereConditions.push(lt(order.id, input.cursor));
      }

      const { data, err: fetchOrderError } = await tryCatch(
        ctx.db.query.order.findMany({
          where: and(...whereConditions),
          orderBy: desc(order.createdAt),
          limit: input.limit + 1,
          columns: {
            pickupOtp: false,
          },
          with: {
            seller: {
              columns: {
                id: true,
                fullName: true,
                phoneNumber: true,
              },
            },
            items: {
              with: {
                category: {
                  with: {
                    parent: {
                      columns: {
                        name: true,
                      },
                    },
                  },
                },
              },
            },
            address: true,
          },
        }),
      );

      if (fetchOrderError) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch order history",
        });
      }

      const hasNextPage = data.length > input.limit;
      const orders = hasNextPage ? data.slice(0, -1) : data;
      const nextCursor = hasNextPage
        ? orders[orders.length - 1]?.id
        : undefined;

      return {
        orders,
        nextCursor,
        hasNextPage,
      };
    }),

  requestCustomerApproval: protectedProcedure
    .input(
      z.object({
        orderId: z.string().nonempty(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { data: orderDetails, err: fetchOrderError } = await tryCatch(
        ctx.db.query.order.findFirst({
          where: eq(order.id, input.orderId),
          columns: {
            kabadiwalaId: true,
            status: true,
            sellerId: true,
          },
        }),
      );

      if (fetchOrderError || !orderDetails) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch order details",
        });
      }

      checkPermissionsForOrderActionKabadiwala({
        kabadiwalaId: ctx.session.user.id,
        orderDetails: {
          kabadiwalaId: orderDetails.kabadiwalaId,
          status: orderDetails.status,
        },
      });

      const { err: requestError } = await tryCatch(
        ctx.db
          .update(order)
          .set({
            orderApprovalStatus: "REQUESTED_CONFIRMATION_BY_KABADIWALA",
          })
          .where(eq(order.id, input.orderId)),
      );

      if (requestError) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to request customer approval",
        });
      }

      // Notification for customer approval requested
      const approvalMsg =
        kabadiwalaToSellerNotificationMessages.customerApprovalRequested({
          orderId: input.orderId,
        });
      await tryCatch(
        ctx.db.insert(notification).values({
          title: approvalMsg.title,
          content: approvalMsg.description,
          sellerId: orderDetails.sellerId,
        }),
      );
      await tryCatch(
        onesignalSellerClient.sendNotification({
          title: approvalMsg.title,
          message: approvalMsg.description,
          // eslint-disable-next-line turbo/no-undeclared-env-vars
          url: `${process.env.NEXT_PUBLIC_SELLER_BETTER_AUTH_URL}/profile/orders/${input.orderId}`,
          externalIds: [orderDetails.sellerId],
        }),
      );

      return {
        message: "Customer approval requested successfully",
      };
    }),

  addUpdateItemInOrder: protectedProcedure
    .input(
      z.object({
        categoryId: z.string().nonempty(),
        orderId: z.string().nonempty(),
        quantity: z.number().min(1, "Quantity must be at least 1"),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { data: orderDetails, err: fetchOrderError } = await tryCatch(
        ctx.db.query.order.findFirst({
          where: eq(order.id, input.orderId),
          with: {
            items: {
              with: {
                category: true,
              },
            },
            seller: true,
          },
        }),
      );

      if (fetchOrderError || !orderDetails) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch order details",
        });
      }

      checkPermissionsForOrderActionKabadiwala({
        kabadiwalaId: ctx.session.user.id,
        orderDetails: {
          kabadiwalaId: orderDetails.kabadiwalaId,
          status: orderDetails.status,
        },
      });

      const existingItem = orderDetails.items.find(
        (item) => item.categoryId === input.categoryId,
      );
      const categoryName = existingItem?.category.name ?? "Item";
      let notificationMsg;
      let resultMsg;
      if (existingItem) {
        const { err: updateError } = await tryCatch(
          ctx.db
            .update(orderItem)
            .set({
              quantity: String(input.quantity),
            })
            .where(
              and(
                eq(orderItem.orderId, input.orderId),
                eq(orderItem.categoryId, input.categoryId),
              ),
            ),
        );

        if (updateError) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: "Failed to update item in order",
          });
        }
        notificationMsg =
          kabadiwalaToSellerNotificationMessages.itemUpdatedInOrder({
            orderId: input.orderId,
            categoryName,
          });
        resultMsg = "Item updated in order successfully";
      } else {
        const { err: addItemError } = await tryCatch(
          ctx.db.insert(orderItem).values({
            orderId: input.orderId,
            categoryId: input.categoryId,
            quantity: String(input.quantity),
            rate: "0",
          }),
        );

        if (addItemError) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: "Failed to add item to order",
          });
        }
        notificationMsg =
          kabadiwalaToSellerNotificationMessages.itemAddedToOrder({
            orderId: input.orderId,
            categoryName,
          });
        resultMsg = "Item added to order successfully";
      }
      await tryCatch(
        ctx.db.insert(notification).values({
          title: notificationMsg.title,
          content: notificationMsg.description,
          sellerId: orderDetails.seller.id,
        }),
      );
      await tryCatch(
        onesignalSellerClient.sendNotification({
          title: notificationMsg.title,
          message: notificationMsg.description,
          // eslint-disable-next-line turbo/no-undeclared-env-vars
          url: `${process.env.NEXT_PUBLIC_SELLER_BETTER_AUTH_URL}/profile/orders/${input.orderId}`,
          externalIds: [orderDetails.seller.id],
        }),
      );
      return {
        message: resultMsg,
      };
    }),

  removeItemFromOrder: protectedProcedure
    .input(
      z.object({
        orderId: z.string().nonempty(),
        categoryId: z.string().nonempty(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // Fetch order and category name for notification
      const { data: orderDetails, err: fetchOrderError } = await tryCatch(
        ctx.db.query.order.findFirst({
          where: eq(order.id, input.orderId),
          with: {
            items: {
              with: {
                category: true,
              },
            },
            seller: true,
          },
        }),
      );

      if (fetchOrderError || !orderDetails) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Something went wrong.",
        });
      }

      checkPermissionsForOrderActionKabadiwala({
        kabadiwalaId: ctx.session.user.id,
        orderDetails: {
          kabadiwalaId: orderDetails.kabadiwalaId,
          status: orderDetails.status,
        },
      });

      const removedItem = orderDetails.items.find(
        (item) => item.categoryId === input.categoryId,
      );
      const categoryName = removedItem?.category.name ?? "Item";
      const sellerId = orderDetails.seller.id;
      const { err: deleteError } = await tryCatch(
        ctx.db
          .delete(orderItem)
          .where(
            and(
              eq(orderItem.orderId, input.orderId),
              eq(orderItem.categoryId, input.categoryId),
            ),
          ),
      );

      if (deleteError) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to remove item from order",
        });
      }
      // Notification for item removed
      const notificationMsg =
        kabadiwalaToSellerNotificationMessages.itemRemovedFromOrder({
          orderId: input.orderId,
          categoryName,
        });
      if (sellerId) {
        await tryCatch(
          ctx.db.insert(notification).values({
            title: notificationMsg.title,
            content: notificationMsg.description,
            sellerId,
          }),
        );
        await tryCatch(
          onesignalSellerClient.sendNotification({
            title: notificationMsg.title,
            message: notificationMsg.description,
            // eslint-disable-next-line turbo/no-undeclared-env-vars
            url: `${process.env.NEXT_PUBLIC_SELLER_BETTER_AUTH_URL}/profile/orders/${input.orderId}`,
            externalIds: [sellerId],
          }),
        );
      }
      return {
        message: "Item removed from order successfully",
      };
    }),

  acceptOrder: protectedProcedure
    .input(z.object({ orderId: z.string() }))
    .mutation(async ({ ctx, input }) => {
      // before checking for anything, we need to check weither the user has sufficient balance to process this request.
      const { data: kabadiwalaDetails, err: kabadiwalaError } = await tryCatch(
        ctx.db.query.kabadiwala.findFirst({
          where: eq(kabadiwala.id, ctx.session.user.id),
          columns: {
            walletBalance: true,
            liveLocationCoordinate: true,
          },
        }),
      );

      if (kabadiwalaError || !kabadiwalaDetails?.walletBalance) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch kabadiwala wallet balance",
        });
      }

      const minWalletBalance = await getSystemConfig({
        key: "MINIMUM_WALLET_BALANCE_TO_ACCEPT_ORDER_BY_KABADIWALA",
      });

      const minBalanceRequired = Number(minWalletBalance ?? 1000);
      const kabadiwalaWalletBalance = Number(kabadiwalaDetails.walletBalance);

      if (kabadiwalaWalletBalance < minBalanceRequired) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: `You need to have at least ₹${minBalanceRequired} in your wallet to accept an order.`,
        });
      }

      const estimatedAmount = await getOrderEstimatedAmount(input.orderId);

      if (estimatedAmount > kabadiwalaWalletBalance) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Insufficient balance to accept this order.",
        });
      }

      const { data: alreadyRejected, err: fetchRejectedError } = await tryCatch(
        ctx.db.query.rejectedOrders.findFirst({
          where: and(
            eq(rejectedOrders.orderId, input.orderId),
            eq(rejectedOrders.kabadiwalaId, ctx.session.user.id),
          ),
        }),
      );

      if (fetchRejectedError) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to check if order is already rejected",
        });
      }

      if (alreadyRejected) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "You have already rejected this order.",
        });
      }

      const { data: orderAssigned, err: transactionError } = await tryCatch(
        ctx.db.transaction(async (tx) => {
          // validate the kabadiwala must have not already an active order
          const activeOrder = await tx
            .select()
            .from(order)
            .where(
              and(
                eq(order.kabadiwalaId, ctx.session.user.id),
                eq(order.status, "ACTIVE"),
              ),
            );

          if (activeOrder.length > 0) {
            throw new TRPCError({
              code: "BAD_REQUEST",
              message:
                "You already have an active order, You cannot accept another order.",
            });
          }

          // check weither is the order is already accepted by someone else
          const alreadyAcceptedOrder = await tx
            .select()
            .from(order)
            .where(eq(order.id, input.orderId));

          const acceptedOrder = alreadyAcceptedOrder[0];

          if (!acceptedOrder) {
            throw new TRPCError({
              code: "NOT_FOUND",
              message: "Order not found",
            });
          }

          // If the order is already accepted by someone else, throw an error
          if (acceptedOrder.status === "ACTIVE" || acceptedOrder.kabadiwalaId) {
            throw new TRPCError({
              code: "BAD_REQUEST",
              message: "Order is already accepted by someone else",
            });
          }

          // Create a conversation for the order
          await tx.insert(conversation).values({
            orderId: acceptedOrder.id,
          });

          // Accept the order
          const acceptOrderResult = await tx
            .update(order)
            .set({
              kabadiwalaId: ctx.session.user.id,
              status: "ACTIVE",
            })
            .where(eq(order.id, input.orderId))
            .returning();

          return acceptOrderResult[0];
        }),
      );

      if (transactionError) {
        // Re-throw TRPC errors as-is
        if (transactionError instanceof TRPCError) {
          throw transactionError;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to accept order",
        });
      }

      if (!orderAssigned) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to accept order",
        });
      }

      // notify user about the order acceptance
      const message =
        kabadiwalaToSellerNotificationMessages.orderAcceptedByAgent({
          agentName: ctx.session.user.name,
          orderId: orderAssigned.id,
        });

      const { err: notificationDbRecordCreationErr } = await tryCatch(
        ctx.db.insert(notification).values({
          title: message.title,
          content: message.description,
          sellerId: orderAssigned.sellerId,
        }),
      );

      if (notificationDbRecordCreationErr) {
        console.error("[notification]: ", notificationDbRecordCreationErr);
      }

      const { err: notificationErr } = await tryCatch(
        onesignalSellerClient.sendNotification({
          title: message.title,
          message: message.description,
          // eslint-disable-next-line turbo/no-undeclared-env-vars
          url: `${process.env.NEXT_PUBLIC_SELLER_BETTER_AUTH_URL}/profile/orders/${orderAssigned.id}`,
          externalIds: [orderAssigned.sellerId],
        }),
      );

      if (notificationErr) {
        console.error("[onesignal]: ", notificationErr);
      }

      return {
        message: "Congratulations! You have accepted the order.",
      };
    }),

  rejectOrder: protectedProcedure
    .input(
      z.object({
        orderId: z.string().nonempty(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { data: orderDetails, err: fetchOrderError } = await tryCatch(
        ctx.db.query.order.findFirst({
          where: eq(order.id, input.orderId),
          columns: {
            kabadiwalaId: true,
            status: true,
            sellerId: true,
          },
        }),
      );

      if (fetchOrderError || !orderDetails) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch order details",
        });
      }

      const { data: rejectedOrderDetails, err: fetchRejectedOrderError } =
        await tryCatch(
          ctx.db.query.rejectedOrders.findFirst({
            where: and(
              eq(rejectedOrders.kabadiwalaId, ctx.session.user.id),
              eq(rejectedOrders.orderId, input.orderId),
            ),
          }),
        );

      if (fetchRejectedOrderError) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to check if order is already rejected",
        });
      }

      if (rejectedOrderDetails) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "You have already rejected this order.",
        });
      }

      const { err: rejectError } = await tryCatch(
        ctx.db.insert(rejectedOrders).values({
          orderId: input.orderId,
          kabadiwalaId: ctx.session.user.id,
        }),
      );

      if (rejectError) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to reject order",
        });
      }
      // Notification for order rejected
      const notificationMsg =
        kabadiwalaToSellerNotificationMessages.orderRejected({
          orderId: input.orderId,
        });
      await tryCatch(
        ctx.db.insert(notification).values({
          title: notificationMsg.title,
          content: notificationMsg.description,
          sellerId: orderDetails.sellerId,
        }),
      );
      await tryCatch(
        onesignalSellerClient.sendNotification({
          title: notificationMsg.title,
          message: notificationMsg.description,
          // eslint-disable-next-line turbo/no-undeclared-env-vars
          url: `${process.env.NEXT_PUBLIC_SELLER_BETTER_AUTH_URL}/profile/orders/${input.orderId}`,
          externalIds: [orderDetails.sellerId],
        }),
      );
      return {
        message: "Order rejected successfully",
      };
    }),

  verifyOrderOtp: protectedProcedure
    .input(
      KabadiwalaArrivalVerificationSchema.extend({
        orderId: z.string().nonempty(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { data: orderData, err: orderError } = await tryCatch(
        ctx.db.query.order.findFirst({
          where: eq(order.id, input.orderId),
          columns: {
            status: true,
            sellerId: true,
            pickupOtp: true,
            kabadiwalaId: true,
          },
        }),
      );

      if (orderError || !orderData) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Order not found",
        });
      }

      checkPermissionsForOrderActionKabadiwala({
        kabadiwalaId: ctx.session.user.id,
        orderDetails: {
          kabadiwalaId: orderData.kabadiwalaId,
          status: orderData.status,
        },
      });

      if (orderData.pickupOtp !== input.otp) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Invalid OTP",
        });
      }

      // Update the pickup otp status to 'Verified'
      const { err: updateError } = await tryCatch(
        ctx.db.update(order).set({
          pickupOtpStatus: "VERIFIED",
        }),
      );

      if (updateError) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update order",
        });
      }
      // Notification for OTP verified
      const notificationMsg =
        kabadiwalaToSellerNotificationMessages.otpVerified({
          orderId: input.orderId,
        });
      await tryCatch(
        ctx.db.insert(notification).values({
          title: notificationMsg.title,
          content: notificationMsg.description,
          sellerId: orderData.sellerId,
        }),
      );
      await tryCatch(
        onesignalSellerClient.sendNotification({
          title: notificationMsg.title,
          message: notificationMsg.description,
          // eslint-disable-next-line turbo/no-undeclared-env-vars
          url: `${process.env.NEXT_PUBLIC_SELLER_BETTER_AUTH_URL}/profile/orders/${input.orderId}`,
          externalIds: [orderData.sellerId],
        }),
      );
      return {
        message: "OTP verified successfully",
        subMessage: "Please upload your selfie to verify your identity.",
      };
    }),

  verifySelfie: protectedProcedure
    .input(
      z.object({
        selfieImageUrl: z.string().url(),
        selfieFileKey: z.string(),
        orderId: z.string().nonempty(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // Fetch order for sellerId
      const { data: orderData, err: orderError } = await tryCatch(
        ctx.db.query.order.findFirst({
          where: eq(order.id, input.orderId),
          columns: {
            status: true,
            sellerId: true,
            kabadiwalaId: true,
          },
        }),
      );

      if (orderError || !orderData) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Order details not found or Something went wrong",
        });
      }

      checkPermissionsForOrderActionKabadiwala({
        kabadiwalaId: ctx.session.user.id,
        orderDetails: {
          kabadiwalaId: orderData.kabadiwalaId,
          status: orderData.status,
        },
      });

      const { err: updateError } = await tryCatch(
        ctx.db.update(order).set({
          kabadiwalaSelfiImageUrl: input.selfieImageUrl,
          kabadiwalaSelfiImageFileKey: input.selfieFileKey,
        }),
      );

      if (updateError) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to verify selfie",
        });
      }

      return {
        message: "Selfie verified successfully",
      };
    }),

  getFinalOrderTotalAmountWithBreakdown: protectedProcedure
    .input(
      z.object({
        orderId: z.string().nonempty(),
      }),
    )
    .query(async ({ ctx, input }) => {
      const res = await getOrderTotalAmountWithBreakdown(
        input.orderId,
        ctx.session.user.id,
      );

      return res;
    }),

  processPayment: protectedProcedure
    .input(
      z.object({
        orderId: z.string().nonempty(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const breakdown = await getOrderTotalAmountWithBreakdown(
        input.orderId,
        ctx.session.user.id,
      );

      console.log("[breakdown]: ", breakdown);

      const { data: paymentData, err: paymentError } = await tryCatch(
        ctx.db.transaction(async (tx) => {
          // Update kabadiwala wallet balance
          await tx
            .update(kabadiwala)
            .set({
              walletBalance: String(breakdown.balanceAfterPayment),
            })
            .where(eq(kabadiwala.id, ctx.session.user.id));

          // Update order with payment details
          await tx
            .update(order)
            .set({
              totalAmount: String(breakdown.amountToPayCustomer),
              securityFeePercentage: String(breakdown.securityFeePercentage),
              securityFeeAmount: String(breakdown.securityFeeAmount),
              gstPercentage: String(breakdown.gstPercent),
              gstAmount: String(breakdown.gstAmount),
              totalServiceChargeWithGst: String(
                breakdown.amountWithoutServiceFee,
              ),
              totalServiceChargeWithSecurityFee: String(
                breakdown.amountToPayCustomer,
              ),
              amountToPayCustomer: String(breakdown.amountToPayCustomer),
              kabadiwalaWalletBalanceBeforePayment: String(
                breakdown.kabadiwalaWalletBalance,
              ),
              kabadiwalaWalletBalanceAfterPayment: String(
                breakdown.balanceAfterPayment,
              ),
              paymentStatus: "COMPLETED",
              paymentCompletedAt: new Date(),
              status: "COMPLETED",
              completedAt: new Date(),
            })
            .where(eq(order.id, input.orderId));

          // Create payment transaction record for security fee (DEBIT)
          await tx
            .insert(kabadiwalaPaymentTransaction)
            .values({
              kabadiwalaId: ctx.session.user.id,
              amount: String(breakdown.securityFeeAmount),
              status: "COMPLETED",
              orderId: input.orderId,
              transactionType: "DEBIT",
              transactionFor: "ORDER_PAYMENT",
            })
            .returning();

          const [transactionData] = await tx
            .insert(kabadiwalaPaymentTransaction)
            .values({
              kabadiwalaId: ctx.session.user.id,
              amount: String(breakdown.amountToPayCustomer),
              status: "COMPLETED",
              orderId: input.orderId,
              transactionType: "DEBIT",
              transactionFor: "ORDER_PAYMENT",
            })
            .returning();

          // TODO: Razorypay actual payment to seller pending.

          // Create customer payment transaction record (CREDIT)
          const orderDetails = await tx
            .select({ sellerId: order.sellerId })
            .from(order)
            .where(eq(order.id, input.orderId));
          const sellerId = orderDetails[0]?.sellerId;
          if (sellerId) {
            await tx.insert(customerPaymentTransaction).values({
              customerId: sellerId,
              amount: String(breakdown.amountToPayCustomer),
              status: customerPaymentStatusEnum.enumValues[1], // "COMPLETED"
              orderId: input.orderId,
              transactionType: customerTransactionTypeEnum.enumValues[0], // "CREDIT"
              transactionFor: customerTransactionForEnum.enumValues[0], // "ORDER_PAYMENT"
              createdAt: new Date(),
              updatedAt: new Date(),
            });
          }

          return { success: true, transactionId: transactionData?.id };
        }),
      );

      if (paymentError) {
        console.error("[paymentError]: ", paymentError);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to process payment",
        });
      }

      // Creating razorpay bill
      const { data, error } = await razorpayClientPackage.createBill({
        business_category: "other_services",
        business_type: "ecommerce",
        receipt_delivery: "digital",
        receipt_number: input.orderId,
        receipt_summary: {
          currency: "INR",
          total_quantity: 1,
          net_payable_amount: breakdown.amountWithoutServiceFee, // payout to customer + security fee
          payment_status: "success",
          total_tax_percent: Number(breakdown.gstPercent ?? 0),
          total_tax_amount: breakdown.gstAmount,
          additional_charges: [
            {
              amount: breakdown.securityFeeAmount,
              description: "Security Fee",
            },
          ],
          sub_total_amount: breakdown.amountToPayCustomer, // payout to customer + security fee
        },
        receipt_timestamp: Math.floor(new Date().getTime() / 1000),
        receipt_type: "order_confirmation",
      });

      if (error) {
        console.error("Razorpay bill creation error:", error);
      }

      const { err: orderBillDataUpdateErr } = await tryCatch(
        ctx.db.update(order).set({
          billToKabadiwala: data,
        }),
      );

      if (orderBillDataUpdateErr) {
        console.error(
          "Failed to update order with bill data:",
          orderBillDataUpdateErr,
        );
      }

      // Notification for payment processed
      const { data: orderData } = await tryCatch(
        ctx.db.query.order.findFirst({
          where: eq(order.id, input.orderId),
          columns: {
            sellerId: true,
          },
        }),
      );
      if (orderData?.sellerId) {
        const notificationMsg =
          kabadiwalaToSellerNotificationMessages.paymentProcessed({
            orderId: input.orderId,
          });
        await tryCatch(
          ctx.db.insert(notification).values({
            title: notificationMsg.title,
            content: notificationMsg.description,
            sellerId: orderData.sellerId,
          }),
        );
        await tryCatch(
          onesignalSellerClient.sendNotification({
            title: notificationMsg.title,
            message: notificationMsg.description,
            // eslint-disable-next-line turbo/no-undeclared-env-vars
            url: `${process.env.NEXT_PUBLIC_SELLER_BETTER_AUTH_URL}/profile/orders/${input.orderId}`,
            externalIds: [orderData.sellerId],
          }),
        );
      }
      return {
        message: "Payment completed successfully",
        transactionId: paymentData.transactionId,
      };
    }),

  getPaymentDetailsById: protectedProcedure
    .input(
      z.object({
        paymentId: z.string().nonempty(),
      }),
    )
    .query(async ({ ctx, input }) => {
      console.log("Fetching payment details for paymentId:", input.paymentId);

      const { data: paymentDetails, err: fetchPaymentError } = await tryCatch(
        ctx.db.query.kabadiwalaPaymentTransaction.findFirst({
          where: and(
            eq(kabadiwalaPaymentTransaction.id, input.paymentId),
            eq(kabadiwalaPaymentTransaction.kabadiwalaId, ctx.session.user.id),
          ),
          with: {
            order: {
              columns: {
                id: true,
                amountToPayCustomer: true,
                status: true,
                paymentCompletedAt: true,
                sellerId: true,
              },
            },
          },
        }),
      );

      if (fetchPaymentError || !paymentDetails) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch payment details",
        });
      }

      return paymentDetails;
    }),

  giveReviewToSeller: protectedProcedure
    .input(
      z.object({
        orderId: z.string(),
        rating: z.number().min(1).max(5),
        review: z.string().max(500).optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // 1. Fetch order and check eligibility
      const { data: orderData, err: orderErr } = await tryCatch(
        ctx.db.query.order.findFirst({
          where: eq(order.id, input.orderId),
          with: {
            reviews: {
              where: and(
                eq(reviews.reviewFor, "SELLER"),
                eq(reviews.sellerId, ctx.session.user.id),
              ),
            },
          },
        }),
      );

      if (orderErr || !orderData) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Order not found",
        });
      }

      if (orderData.kabadiwalaId !== ctx.session.user.id) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You dont have permission to review this order",
        });
      }

      if (orderData.status !== "COMPLETED") {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "You can only review completed orders",
        });
      }

      const sellerReview = orderData.reviews.at(0);

      if (sellerReview) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "You have already reviewed this order",
        });
      }

      // 2. Insert review
      const { err: reviewErr } = await tryCatch(
        ctx.db.insert(reviews).values({
          kabadiwalaId: orderData.kabadiwalaId,
          sellerId: orderData.sellerId,
          orderId: input.orderId,
          rating: String(input.rating),
          review: input.review,
          reviewFor: "SELLER",
        }),
      );

      if (reviewErr) {
        console.error("Failed to create review:", reviewErr);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create review",
        });
      }

      const { data: allReviews } = await tryCatch(
        ctx.db.query.reviews.findMany({
          where: eq(reviews.sellerId, orderData.sellerId),
          columns: { rating: true },
        }),
      );

      const ratings = allReviews?.map((r) => Number(r.rating)) ?? [];

      const avg =
        ratings.length > 0
          ? ratings.reduce((a, b) => a + b, 0) / ratings.length
          : input.rating;

      await tryCatch(
        ctx.db
          .update(seller)
          .set({ averageRating: String(avg) })
          .where(eq(seller.id, orderData.sellerId)),
      );

      return { message: "Review submitted successfully" };
    }),

  getScraphubAddress: protectedProcedure.query(async ({ ctx }) => {
    const { data: kabadiwalaScraphubId, err: kabadiwalaError } = await tryCatch(
      ctx.db.query.kabadiwala.findFirst({
        where: eq(kabadiwala.id, ctx.session.user.id),
        columns: {
          scraphubId: true,
        },
      }),
    );

    if (kabadiwalaError || !kabadiwalaScraphubId?.scraphubId) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch Kabadiwala Scraphub ID",
      });
    }

    const { data: scraphubDetails, err: fetchError } = await tryCatch(
      ctx.db.query.scraphub.findFirst({
        where: eq(scraphub.id, kabadiwalaScraphubId.scraphubId),
        with: {
          address: true,
        },
      }),
    );

    if (fetchError || !scraphubDetails) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch Scraphub details",
      });
    }
    return { ...scraphubDetails.address };
  }),
} satisfies TRPCRouterRecord;
