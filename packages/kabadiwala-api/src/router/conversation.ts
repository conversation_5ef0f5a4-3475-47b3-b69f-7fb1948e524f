import type { TRPCRouterRecord } from "@trpc/server";
import { TRPCError } from "@trpc/server";
import { isNull } from "drizzle-orm";
import { z } from "zod";

import { and, eq } from "@acme/db";
import {
  conversation,
  kabadiwalaSupportConversation,
  kabadiwalaSupportMessage,
  message,
  order,
} from "@acme/db/schema";
import { tryCatch } from "@acme/validators/utils";

import { protectedProcedure } from "../trpc";
import { onesignalAdminClient } from "../utils";

export const conversationRouter = {
  getMessages: protectedProcedure
    .input(z.object({ orderId: z.string().nonempty() }))
    .query(async ({ ctx, input }) => {
      const { data: convoDetails, err: fetchMessagesError } = await tryCatch(
        ctx.db.query.conversation.findFirst({
          where: eq(conversation.orderId, input.orderId),
          with: {
            messages: true,
          },
        }),
      );

      if (fetchMessagesError) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch messages",
        });
      }

      return convoDetails?.messages ?? [];
    }),

  sendMessageToCustomer: protectedProcedure
    .input(
      z.object({
        orderId: z.string().nonempty(),
        message: z.string().min(1, "Message cannot be empty"),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { data: orderDetails, err: fetchOrderError } = await tryCatch(
        ctx.db.query.order.findFirst({
          where: eq(order.id, input.orderId),
          columns: {
            status: true,
            kabadiwalaId: true,
          },
          with: {
            conversation: {
              columns: {
                id: true,
              },
            },
          },
        }),
      );

      if (fetchOrderError || !orderDetails) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch order details",
        });
      }

      if (orderDetails.kabadiwalaId !== ctx.session.user.id) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "You are not authorized to send messages for this order",
        });
      }

      if (orderDetails.status !== "ACTIVE") {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "You can only send messages for active orders",
        });
      }

      const { err: messageError } = await tryCatch(
        ctx.db.insert(message).values({
          content: input.message,
          senderId: ctx.session.user.id,
          conversationId: orderDetails.conversation.id,
        }),
      );

      if (messageError) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to send message",
        });
      }

      return true;
    }),

  getSupportConversation: protectedProcedure
    .input(z.object({ orderId: z.string().optional() }))
    .query(async ({ ctx, input }) => {
      // Find latest conversation for this kabadiwala and orderId (or null for help/support)
      const whereClause = input.orderId
        ? and(
            eq(kabadiwalaSupportConversation.kabadiwalaId, ctx.session.user.id),
            eq(kabadiwalaSupportConversation.orderId, input.orderId),
          )
        : and(
            eq(kabadiwalaSupportConversation.kabadiwalaId, ctx.session.user.id),
            isNull(kabadiwalaSupportConversation.orderId),
          );
      const { data: convos, err } = await tryCatch(
        ctx.db.query.kabadiwalaSupportConversation.findMany({
          where: whereClause,
          with: {
            messages: true,
          },
          orderBy: (c, { desc }) => [desc(c.createdAt)],
        }),
      );
      if (err)
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to fetch conversation",
        });
      const convo = convos.length > 0 ? convos[0] : null;
      return convo;
    }),

  sendSupportMessage: protectedProcedure
    .input(
      z.object({
        orderId: z.string().optional(),
        content: z.string().trim().nonempty(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // Find open conversation for this kabadiwala and orderId (or null for help/support)
      const whereClause = input.orderId
        ? and(
            eq(kabadiwalaSupportConversation.kabadiwalaId, ctx.session.user.id),
            eq(kabadiwalaSupportConversation.orderId, input.orderId),
            eq(kabadiwalaSupportConversation.isOpen, true),
          )
        : and(
            eq(kabadiwalaSupportConversation.kabadiwalaId, ctx.session.user.id),
            isNull(kabadiwalaSupportConversation.orderId),
            eq(kabadiwalaSupportConversation.isOpen, true),
          );
      let { data: convo } = await tryCatch(
        ctx.db.query.kabadiwalaSupportConversation.findFirst({
          where: whereClause,
        }),
      );
      let isNewConversation = false;
      if (!convo) {
        // Create new conversation only when sending a message
        const [created] = await ctx.db
          .insert(kabadiwalaSupportConversation)
          .values({
            kabadiwalaId: ctx.session.user.id,
            orderId: input.orderId ?? null,
            isOpen: true,
            createdAt: new Date(),
            updatedAt: new Date(),
          })
          .returning();
        convo = created ? { ...created } : null;
        isNewConversation = true;
      }
      if (!convo?.isOpen)
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "This conversation is closed.",
        });
      // Insert message
      await ctx.db.insert(kabadiwalaSupportMessage).values({
        conversationId: convo.id,
        senderId: ctx.session.user.id,
        senderType: "KABADIWALA",
        content: input.content,
        createdAt: new Date(),
        isRead: false,
      });
      // Fetch all admin IDs
      const { data: admins, err: adminErr } = await tryCatch(
        ctx.db.query.admin.findMany({ columns: { id: true } }),
      );
      if (!adminErr && admins && admins.length > 0) {
        const adminIds = admins.map((a) => a.id);
        await tryCatch(
          onesignalAdminClient.sendNotification({
            title: isNewConversation
              ? "New Support Ticket"
              : "New Support Message",
            message: isNewConversation
              ? "A new support ticket has been opened by a kabadiwala."
              : input.content,
            externalIds: adminIds,
          }),
        );
      }
      return { success: true };
    }),

  rateSupportConversation: protectedProcedure
    .input(
      z.object({
        conversationId: z.string().nonempty(),
        rating: z.number().min(1).max(5).nullable(),
        reviewText: z.string().max(500).optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      // Fetch the conversation
      const { data: convo, err } = await tryCatch(
        ctx.db.query.kabadiwalaSupportConversation.findFirst({
          where: eq(kabadiwalaSupportConversation.id, input.conversationId),
        }),
      );
      if (err || !convo) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Conversation not found",
        });
      }
      if (convo.isOpen) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Conversation must be closed before rating.",
        });
      }
      if (convo.isRated) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Conversation already rated.",
        });
      }
      // Update rating, reviewText, and isRated
      await ctx.db
        .update(kabadiwalaSupportConversation)
        .set({
          rating: input.rating?.toString() ?? null,
          reviewText: input.reviewText ?? null,
          isRated: true,
        })
        .where(eq(kabadiwalaSupportConversation.id, input.conversationId));
      return { success: true };
    }),
} satisfies TRPCRouterRecord;
