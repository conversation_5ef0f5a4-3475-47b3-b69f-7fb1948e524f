import type { TRP<PERSON>outerRecord } from "@trpc/server";
import { TRPCError } from "@trpc/server";
import { z } from "zod";

import { eq } from "@acme/db";
import { kabadiwala } from "@acme/db/schema";
import { tryCatch } from "@acme/validators/utils";

import { protectedProcedure } from "../trpc";
import { utapi } from "../utils";

// This router is used to handle utility functions that are not specific to any particular feature
export const utilsRouter = {
  deleteUploadthingFileUsingFileKey: protectedProcedure
    .input(z.object({ fileKey: z.string() }))
    .mutation(async ({ input }) => {
      const { err } = await tryCatch(utapi.deleteFiles(input.fileKey));

      if (err) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to delete file",
        });
      }
      return {
        success: true,
        message: "File deleted successfully",
      };
    }),

  getSystemConfigData: protectedProcedure.query(async ({ ctx }) => {
    const { data, err } = await tryCatch(
      ctx.db.query.systemConfiguration.findMany(),
    );

    if (err) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch system configuration data",
      });
    }

    return data;
  }),

  getAppStartRequiredData: protectedProcedure.query(async ({ ctx }) => {
    const { data: kabadiwalaDetails, err: kabadiwalaErr } = await tryCatch(
      ctx.db.query.kabadiwala.findFirst({
        where: eq(kabadiwala.id, ctx.session.user.id),
        columns: {
          onboarding: true,
          isBlocked: true,
          onboardingCompleted: true,
        },
      }),
    );

    if (kabadiwalaErr || !kabadiwalaDetails) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch Kabadiwala onboarding details",
      });
    }

    const accountActive =
      kabadiwalaDetails.isBlocked === null
        ? false
        : kabadiwalaDetails.isBlocked === true
          ? false
          : true;

    return {
      lastStepOnBoardingLeftAt: kabadiwalaDetails.onboarding,
      onBoardingCompleted: kabadiwalaDetails.onboardingCompleted,
      accountActive: accountActive,
      isBlocked: kabadiwalaDetails.isBlocked ? true : false,
    };
  }),
} satisfies TRPCRouterRecord;
