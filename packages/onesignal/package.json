{"name": "@acme/onesignal", "version": "1.0.0", "description": "TypeScript SDK for Notifications", "main": "src/index.ts", "files": ["dist", "README.md"], "scripts": {"build": "tsc", "prepare": "npm run build", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "dependencies": {"@onesignal/node-onesignal": "5.0.0-alpha-02", "axios": "^1.9.0"}, "peerDependencies": {"typescript": "^5.0.0"}, "devDependencies": {"@types/node": "^22.14.1", "typescript": "catalog:"}}