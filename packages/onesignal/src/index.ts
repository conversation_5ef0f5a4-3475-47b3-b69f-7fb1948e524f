import {
  createConfiguration,
  DefaultApi,
  Notification,
} from "@onesignal/node-onesignal";

import {
  NotificationPayload,
  OneSignalConfig,
  OneSignalUserType,
} from "./types";

const getConfig = (userType: OneSignalUserType): OneSignalConfig => {
  switch (userType) {
    case OneSignalUserType.SELLER:
      return {
        appId: process.env.NEXT_PUBLIC_ONESIGNAL_CUSTOMER_APP_ID!,
        apiKey: process.env.ONESIGNAL_CUSTOMER_KEY!,
      };
    case OneSignalUserType.KABADIWALA:
      return {
        appId: process.env.NEXT_PUBLIC_ONESIGNAL_KABADIWALA_APP_ID!,
        apiKey: process.env.ONESIGNAL_KABADIWALA_KEY!,
      };
    case OneSignalUserType.ADMIN:
      return {
        appId: process.env.NEXT_PUBLIC_ONESIGNAL_ADMIN_APP_ID!,
        apiKey: process.env.ONESIGNAL_ADMIN_KEY!,
      };

    default:
      throw new Error("Invalid OneSignal user type");
  }
};

class OneSignalClient {
  private config: OneSignalConfig;
  private client: DefaultApi;

  constructor(config: OneSignalConfig) {
    this.config = config;
    const configuration = createConfiguration({
      restApiKey: config.apiKey,
    });
    this.client = new DefaultApi(configuration);
  }

  async sendNotification(payload: NotificationPayload) {
    const notification = new Notification();
    notification.app_id = this.config.appId;
    notification.target_channel = "push";
    notification.include_aliases = { external_id: payload.externalIds };
    notification.headings = payload.headings || { en: payload.title };
    notification.contents = payload.contents || { en: payload.message };
    if (payload.data) notification.data = payload.data;
    if (payload.url) notification.url = payload.url;
    if (payload.name) notification.name = payload.name;
    // Add any additional fields from payload
    for (const key of Object.keys(payload)) {
      if (!(key in notification)) {
        // @ts-ignore
        notification[key] = payload[key];
      }
    }
    try {
      const response = await this.client.createNotification(notification);
      return response;
    } catch (error: any) {
      throw error.body || error;
    }
  }
}

// Factory to get a OneSignalClient for a user type
export function getOneSignalClient(
  userType: OneSignalUserType,
): OneSignalClient {
  const config = getConfig(userType);
  if (!config.appId || !config.apiKey) {
    throw new Error(`Missing OneSignal credentials for ${userType}`);
  }
  return new OneSignalClient(config);
}

// Example usage:
// const client = getOneSignalClient(OneSignalUserType.SELLER);
// await client.sendNotification({
//   title: 'Order Placed',
//   message: 'Your order has been placed!',
//   externalIds: ['seller123'],
// });
