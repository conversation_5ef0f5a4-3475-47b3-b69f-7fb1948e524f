export enum OneSignalUserType {
  SELLER = "seller",
  KABADIWALA = "kabadiwala",
  ADMIN = "admin",
}

export interface OneSignalConfig {
  appId: string;
  apiKey: string;
}

export interface NotificationPayload {
  title: string;
  message: string;
  externalIds: string[];
  data?: Record<string, any>;
  headings?: Record<string, string>;
  contents?: Record<string, string>;
  url?: string;
  [key: string]: any;
}
