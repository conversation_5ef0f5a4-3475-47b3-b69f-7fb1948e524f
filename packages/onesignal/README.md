# OneSignal Notification Helper

This package provides a reusable helper for sending push notifications via OneSignal for multiple apps (e.g., seller and kabadiwala) with minimal boilerplate.

## Installation

```
pnpm add @yourorg/onesignal
```

## Environment Variables

Set the following environment variables in your app:

```
ONE_SIGNAL_SELLER_APP_ID=your_seller_app_id
ONE_SIGNAL_SELLER_API_KEY=your_seller_api_key
ONE_SIGNAL_KABADIWALA_APP_ID=your_kabadiwala_app_id
ONE_SIGNAL_KABADIWALA_API_KEY=your_kabadiwala_api_key
```

## Usage

```ts
import { getOneSignalClient, OneSignalUserType } from "@yourorg/onesignal";

const client = getOneSignalClient(OneSignalUserType.SELLER); // or KABADIWALA
await client.sendNotification({
  title: "Order Placed",
  message: "Your order has been placed!",
  externalIds: ["seller123"], // external_id(s) to notify
  data: { orderId: "123" }, // optional custom data
  url: "https://yourapp.com/orders/123", // optional
});
```

## API

### getOneSignalClient(userType: OneSignalUserType): OneSignalClient

Returns a client instance for the specified user type (SELLER or KABADIWALA).

### OneSignalClient.sendNotification(payload: NotificationPayload)

Sends a push notification. See `NotificationPayload` type for options.

---

- Supports custom headings, contents, data, and url fields.
- Throws if required env vars are missing.
- Uses OneSignal REST API under the hood.
