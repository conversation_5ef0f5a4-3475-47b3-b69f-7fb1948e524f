{"name": "@acme/core", "version": "0.1.0", "private": true, "license": "MIT", "type": "module", "exports": {".": "./src/index.ts"}, "scripts": {"clean": "git clean -xdf .cache .turbo dist node_modules", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "prettier": "@acme/prettier-config", "dependencies": {"@acme/db": "workspace:*", "@acme/validators": "workspace:*", "date-fns": "catalog:", "zod": "catalog:"}, "devDependencies": {"@acme/eslint-config": "workspace:*", "@acme/prettier-config": "workspace:*", "@acme/tsconfig": "workspace:*", "eslint": "catalog:", "prettier": "catalog:", "typescript": "catalog:"}}