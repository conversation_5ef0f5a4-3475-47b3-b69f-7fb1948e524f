import { eq } from "@acme/db";
import { db } from "@acme/db/client";
import { kabadiwala, order } from "@acme/db/schema";
import { tryCatch } from "@acme/validators/utils";

import { getSystemConfig } from "./config";
import { checkPermissionsForOrderActionKabadiwala } from "./kabadiwala";

export const getOrderTotalAmountWithBreakdown = async (
  orderId: string,
  kabadiwalaId: string,
) => {
  const { data: orderData, err: orderError } = await tryCatch(
    db.query.order.findFirst({
      where: eq(order.id, orderId),
      with: {
        items: {
          with: {
            category: true,
          },
        },
      },
    }),
  );

  if (orderError || !orderData) {
    throw new Error("Order not found");
  }

  checkPermissionsForOrderActionKabadiwala({
    kabadiwalaId,
    orderDetails: {
      kabadiwalaId: orderData.kabadiwalaId,
      status: orderData.status,
    },
  });

  // Calculate base amount (sum of all items)
  const estimatedAmount = orderData.items.reduce((acc, item) => {
    const rate = item.category.rate;
    const quantity = parseFloat(item.quantity) || 0;
    return acc + Number(rate) * quantity;
  }, 0);

  // Get service charge percentage from system configuration
  const securityFeePercentage = await getSystemConfig({
    key: "SECURITY_FEE_PERCENTAGE",
    defaultValue: "20",
  });
  const minOrderValue = await getSystemConfig({
    key: "MINIMUM_ORDER_VALUE",
    defaultValue: "500",
  });
  const gstPercent = await getSystemConfig({
    key: "GST_PERCENTAGE",
    defaultValue: "18",
  });
  const pickupCharge = await getSystemConfig({
    key: "PICKUP_CHARGE",
    defaultValue: "60",
  });

  const isBelowMinOrder = Number(estimatedAmount) < Number(minOrderValue);

  //calculate total payout without service charge
  // let gstAmount = 0;
  let totalCharges = 0;
  let payoutToCustomer = estimatedAmount;

  if (isBelowMinOrder) {
    // gstAmount =
    //   Math.round(((Number(pickupCharge) * Number(gstPercent)) / 100) * 100) /
    //   100;
    totalCharges = Number(pickupCharge); //+ gstAmount;
    payoutToCustomer =
      Math.round((Number(estimatedAmount) - totalCharges) * 100) / 100;
  }

  //   const securityFeePercentage = Number(serviceChargeConfig?.value ?? 20);

  // Calculate final payout amount
  const securityFeeAmount =
    (payoutToCustomer * Number(securityFeePercentage)) / 100;

  // Get current kabadiwala wallet balance
  const { data: kabadiwalaDetails } = await tryCatch(
    db.query.kabadiwala.findFirst({
      where: eq(kabadiwala.id, kabadiwalaId),
      columns: { walletBalance: true },
    }),
  );

  const currentWalletBalance = Number(kabadiwalaDetails?.walletBalance ?? 0);
  const balanceAfterPayment =
    currentWalletBalance - payoutToCustomer - securityFeeAmount;

  return {
    estimatedAmount: parseFloat(estimatedAmount.toFixed(2)),
    securityFeePercentage,
    securityFeeAmount: parseFloat(securityFeeAmount.toFixed(2)),
    amountToPayCustomer: parseFloat(payoutToCustomer.toFixed(2)),
    kabadiwalaWalletBalance: parseFloat(currentWalletBalance.toFixed(2)),
    balanceAfterPayment: parseFloat(balanceAfterPayment.toFixed(2)),
    pickupCharge: isBelowMinOrder ? pickupCharge : 0,
    gstPercent: isBelowMinOrder ? gstPercent : 0,
    gstAmount: 0,
    amountWithoutServiceFee: payoutToCustomer,
    canProceedWithPayment: balanceAfterPayment >= 0,
    totalAmount: parseFloat((payoutToCustomer + securityFeeAmount).toFixed(2)),
    items: orderData.items.map((item) => ({
      name: item.category.name,
      quantity: item.quantity,
      rate: Number(item.category.rate),
      total: Number(item.category.rate) * Number(item.quantity),
    })),
  };
};

export const getOrderEstimatedAmount = async (orderId: string) => {
  const { data: orderData, err: orderError } = await tryCatch(
    db.query.order.findFirst({
      where: eq(order.id, orderId),
      with: {
        items: {
          with: {
            category: true,
          },
        },
      },
    }),
  );

  if (orderError || !orderData) {
    throw new Error("Order not found");
  }

  const estimatedAmount = orderData.items.reduce((acc, item) => {
    const rate = item.category.rate;
    const quantity = parseFloat(item.quantity) || 0;
    return acc + Number(rate) * quantity;
  }, 0);

  return estimatedAmount;
};
