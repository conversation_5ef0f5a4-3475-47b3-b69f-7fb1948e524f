import type { orderStatusEnum } from "@acme/db/schema";

export const checkOrderAccessPermission = (
  order: { sellerId: string; kabadiwalaId: string },
  userId: string,
) => {
  if (order.sellerId !== userId && order.kabadiwalaId !== userId) {
    throw new Error("You do not have permission to access this conversation");
  }
};

export const checkOrderActiveStatus = (
  orderStatus: (typeof orderStatusEnum.enumValues)[number] | null,
) => {
  if (orderStatus === null || orderStatus !== "ACTIVE") {
    throw new Error(
      "You cannot access this conversation as the order is not active",
    );
  }
};

export const checkSendMessagePermission = (
  order: { sellerId: string; kabadiwalaId: string | null },
  userId: string,
) => {
  if (order.sellerId !== userId && order.kabadiwalaId !== userId) {
    throw new Error("You do not have permission to send a message");
  }
};

export const checkSupportConversationOwnership = (
  conversation: { customerId: string },
  userId: string,
) => {
  if (conversation.customerId !== userId) {
    throw new Error("You do not have permission to close this conversation");
  }
};
