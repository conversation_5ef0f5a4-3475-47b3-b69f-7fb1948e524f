import { and, asc, eq, ilike, isNull } from "@acme/db";
import { db } from "@acme/db/client";
import { category } from "@acme/db/schema";
import { tryCatch } from "@acme/validators/utils";

export const getTopLevelCategories = async (limit?: number) => {
  const { data, err } = await tryCatch(
    db.query.category.findMany({
      where: isNull(category.parentId),
      orderBy: asc(category.createdAt),
      ...(limit ? { limit } : {}),
    }),
  );

  if (err) {
    throw new Error(`Failed to fetch top-level categories: ${err.message}`);
  }

  return data;
};

export const getSubCategoriesWithOrWithoutParent = async (
  id: string,
  withParent?: boolean,
) => {
  const { data, err } = await tryCatch(
    db.query.category.findFirst({
      where: and(eq(category.id, id), eq(category.isActive, true)),
      columns: {
        compensationKabadiwalaRate: false,
        compensationRecyclerRate: false,
      },
      with: {
        ...(withParent
          ? {
              parent: {
                columns: {
                  compensationKabadiwalaRate: false,
                  compensationRecyclerRate: false,
                },
              },
            }
          : {}),

        children: {
          with: {
            children: {
              where: and(
                eq(category.isActive, true),
                isNull(category.deletedAt),
              ),
              columns: {
                compensationKabadiwalaRate: false,
                compensationRecyclerRate: false,
              },
              with: {
                children: {
                  where: and(
                    eq(category.isActive, true),
                    isNull(category.deletedAt),
                  ),
                  columns: {
                    compensationKabadiwalaRate: false,
                    compensationRecyclerRate: false,
                  },
                },
              },
            },
          },
        },
      },
    }),
  );

  if (err) {
    throw new Error(`Failed to fetch subcategories: ${err.message}`);
  }

  return {
    parent: data?.parent ?? data,
    subCategories: data?.children,
  };
};

export const getCategoryById = async (id: string) => {
  const { data, err } = await tryCatch(
    db.query.category.findFirst({
      where: and(eq(category.id, id)),
      columns: {
        compensationKabadiwalaRate: false,
        compensationRecyclerRate: false,
      },
    }),
  );

  if (err) {
    throw new Error(`Failed to fetch category: ${err.message}`);
  }

  return data;
};

export const searchCategoryByName = async (name: string, limit?: number) => {
  const { data, err } = await tryCatch(
    db.query.category.findMany({
      where: and(
        ilike(category.name, `%${name}%`),
        isNull(category.parentId),
        eq(category.isActive, true),
      ),
      ...(limit ? { limit } : {}),
    }),
  );

  if (err) {
    throw new Error(`Failed to search categories: ${err.message}`);
  }

  return data;
};
