import type { kabadiwala, order } from "@acme/db/schema";

export const getKabadiwalaOnDuty = (
  kabadiwalaDetails: Pick<
    typeof kabadiwala.$inferSelect,
    "workHoursMode" | "isOnDuty" | "scheduleHours"
  >,
): boolean => {
  let isKabadiwalaOnDuty = false;

  if (kabadiwalaDetails.workHoursMode === "MANUAL") {
    isKabadiwalaOnDuty = kabadiwalaDetails.isOnDuty;
  } else {
    if (!kabadiwalaDetails.scheduleHours) {
      return false;
    }

    const now = new Date();
    const currentDay = now
      .toLocaleDateString("en-US", { weekday: "long" })
      .toLowerCase() as keyof typeof kabadiwalaDetails.scheduleHours;
    const currentTime = now.toTimeString().slice(0, 5);

    const daySchedule = kabadiwalaDetails.scheduleHours[currentDay];

    if (!daySchedule.isAvailable) {
      isKabadiwalaOnDuty = false;
    }

    const isWithinTimeSlot = daySchedule.timeSlots.some(
      (slot) => currentTime >= slot.startTime && currentTime <= slot.endTime,
    );

    isKabadiwalaOnDuty = isWithinTimeSlot;
  }

  return isKabadiwalaOnDuty;
};

export const checkPermissionsForOrderActionKabadiwala = ({
  kabadiwalaId,
  orderDetails,
}: {
  kabadiwalaId: string;
  orderDetails: Pick<typeof order.$inferSelect, "status" | "kabadiwalaId">;
}) => {
  console.log(
    "Checking permissions for Kabadiwala action on order:",
    orderDetails,
  );
  console.log("Kabadiwala ID:", kabadiwalaId);

  if (orderDetails.kabadiwalaId !== kabadiwalaId) {
    throw new Error(
      "You do not have permission to perform this action on this order.",
    );
  }

  if (orderDetails.status !== "ACTIVE") {
    throw new Error("Order is not in correct state to process this request.");
  }
};
