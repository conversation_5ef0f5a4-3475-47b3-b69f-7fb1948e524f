import type { categoryRateTypeEnum } from "@acme/db/schema";
import { eq } from "@acme/db";
import { db } from "@acme/db/client";
import { pricingHistory, regionCategoryPricing } from "@acme/db/schema";

interface RegionPricing {
  categoryId: string;
  rate: string;
  compensationKabadiwalaRate?: string;
  compensationRecyclerRate?: string;
}

interface CategoryPricing {
  id: string;
  rate: number;
  rateType: (typeof categoryRateTypeEnum.enumValues)[number];
  compensationKabadiwalaRate: number;
  compensationRecyclerRate: number;
}

interface PricingHistoryParams {
  regionId: string;
  effectiveFromDate: Date;
  expiresAt: Date;
}

interface PricingHistoryWithCategories extends PricingHistoryParams {
  regionCategoryPricing: CategoryPricing[];
}

interface RegionPricingWithHistory extends RegionPricing {
  pricingHistoryId: string;
}

// Get current pricing history for a region
export const getCurrentPricingHistoryByRegion = async (regionId: string) => {
  const now = new Date();
  return await db.query.pricingHistory.findFirst({
    where: (history, { eq, and, gte, lte }) =>
      and(
        eq(history.regionId, regionId),
        lte(history.effectiveFromDate, now),
        gte(history.expiresAt, now),
      ),
    orderBy: (history, { desc }) => [desc(history.effectiveFromDate)],
  });
};

// Create pricing history entry
export const createRegionPricingPeriod = async (
  params: PricingHistoryParams,
) => {
  // Check if region exists
  const regionExists = await db.query.regions.findFirst({
    where: (regions, { eq }) => eq(regions.id, params.regionId),
  });

  if (!regionExists) {
    throw new Error("Region not found");
  }

  // Check for overlapping pricing history
  const overlappingHistory = await db.query.pricingHistory.findFirst({
    where: (history, { eq, and, or, lte, gte }) =>
      and(
        eq(history.regionId, params.regionId),
        or(
          and(
            lte(history.effectiveFromDate, params.effectiveFromDate),
            gte(history.expiresAt, params.effectiveFromDate),
          ),
          and(
            lte(history.effectiveFromDate, params.expiresAt),
            gte(history.expiresAt, params.expiresAt),
          ),
          and(
            gte(history.effectiveFromDate, params.effectiveFromDate),
            lte(history.expiresAt, params.expiresAt),
          ),
        ),
      ),
  });

  if (overlappingHistory) {
    throw new Error("Overlapping pricing history exists for this region");
  }

  const [newPricingHistory] = await db
    .insert(pricingHistory)
    .values({
      regionId: params.regionId,
      effectiveFromDate: params.effectiveFromDate,
      expiresAt: params.expiresAt,
    })
    .returning();

  return newPricingHistory;
};

// Create pricing period with category pricing
export const createRegionPricingPeriodWithCategoryPricing = async (
  params: PricingHistoryWithCategories,
) => {
  // Check if region exists
  const regionExists = await db.query.regions.findFirst({
    where: (regions, { eq }) => eq(regions.id, params.regionId),
  });

  if (!regionExists) {
    throw new Error("Region not found");
  }

  // Check for overlapping pricing history
  const overlappingHistory = await db.query.pricingHistory.findFirst({
    where: (history, { eq, and, or, lte, gte }) =>
      and(
        eq(history.regionId, params.regionId),
        or(
          and(
            lte(history.effectiveFromDate, params.effectiveFromDate),
            gte(history.expiresAt, params.effectiveFromDate),
          ),
          and(
            lte(history.effectiveFromDate, params.expiresAt),
            gte(history.expiresAt, params.expiresAt),
          ),
          and(
            gte(history.effectiveFromDate, params.effectiveFromDate),
            lte(history.expiresAt, params.expiresAt),
          ),
        ),
      ),
  });

  if (overlappingHistory) {
    throw new Error("Overlapping pricing history exists for this region");
  }

  const transactionRes = await db.transaction(async (tx) => {
    // Create pricing history entry
    const [newPricingHistory] = await tx
      .insert(pricingHistory)
      .values({
        regionId: params.regionId,
        effectiveFromDate: params.effectiveFromDate,
        expiresAt: params.expiresAt,
      })
      .returning();

    if (!newPricingHistory) {
      throw new Error("Failed to create pricing history");
    }

    // Create category pricing entries
    const categoryPricingEntries = params.regionCategoryPricing.map(
      (category) => ({
        pricingHistoryId: newPricingHistory.id,
        categoryId: category.id,
        rate: String(category.rate),
        compensationKabadiwalaRate: String(category.compensationKabadiwalaRate),
        compensationRecyclerRate: String(category.compensationRecyclerRate),
      }),
    );

    if (categoryPricingEntries.length > 0) {
      await tx.insert(regionCategoryPricing).values(categoryPricingEntries);
    }

    return { message: "Pricing period created successfully" };
  });

  return transactionRes;
};

// Get rate by region and category (considering active pricing history)
export const getCategoryRateForRegion = async (
  regionId: string,
  categoryId: string,
) => {
  // Get active pricing history
  const activePricingHistory = await getCurrentPricingHistoryByRegion(regionId);

  if (!activePricingHistory) {
    // Fallback to default category rate
    const categoryData = await db.query.category.findFirst({
      where: (category, { eq }) => eq(category.id, categoryId),
      columns: {
        rate: true,
      },
    });

    if (!categoryData) {
      throw new Error("Category not found");
    }

    return {
      rate: categoryData.rate,
      source: "category" as const,
      regionPricingId: null,
      compensationKabadiwalaRate: null,
      compensationRecyclerRate: null,
    };
  }

  // Get region-specific pricing
  const regionPricing = await db.query.regionCategoryPricing.findFirst({
    where: (pricing, { eq, and }) =>
      and(
        eq(pricing.pricingHistoryId, activePricingHistory.id),
        eq(pricing.categoryId, categoryId),
      ),
  });

  if (regionPricing) {
    return {
      rate: regionPricing.rate,
      source: "region" as const,
      regionPricingId: regionPricing.id,
      compensationKabadiwalaRate: regionPricing.compensationKabadiwalaRate,
      compensationRecyclerRate: regionPricing.compensationRecyclerRate,
    };
  }

  // Fallback to default category rate
  const categoryData = await db.query.category.findFirst({
    where: (category, { eq }) => eq(category.id, categoryId),
    columns: {
      rate: true,
    },
  });

  if (!categoryData) {
    throw new Error("Category not found");
  }

  return {
    rate: categoryData.rate,
    source: "category" as const,
    regionPricingId: null,
    compensationKabadiwalaRate: null,
    compensationRecyclerRate: null,
  };
};

// Get all pricing for a region (considering active pricing history)
export const getAllCategoryPricingForRegion = async (regionId: string) => {
  const activePricingHistory = await getCurrentPricingHistoryByRegion(regionId);

  if (!activePricingHistory) {
    return [];
  }

  return await db.query.regionCategoryPricing.findMany({
    where: (pricing, { eq }) =>
      eq(pricing.pricingHistoryId, activePricingHistory.id),
    with: {
      category: {
        columns: {
          id: true,
          name: true,
          image: true,
        },
      },
      pricingHistory: {
        with: {
          region: {
            columns: {
              id: true,
              name: true,
            },
          },
        },
      },
    },
  });
};

export const getPricingHistory = async (pricingId: string) => {
  return await db.query.pricingHistory.findFirst({
    where: (history, { eq }) => eq(history.id, pricingId),
    with: {
      region: {
        columns: {
          id: true,
          name: true,
        },
      },
      regionCategoryPricing: {
        with: {
          category: {
            columns: {
              id: true,
              name: true,
              rate: true,
              rateType: true,
              compensationKabadiwalaRate: true,
              compensationRecyclerRate: true,
            },
          },
        },
      },
    },
    orderBy: (history, { desc }) => [desc(history.effectiveFromDate)],
  });
};

// Create region category pricing
export const setCategoryPricingForRegion = async (
  params: RegionPricingWithHistory,
) => {
  // Check if pricing history exists
  const pricingHistoryExists = await db.query.pricingHistory.findFirst({
    where: (history, { eq }) => eq(history.id, params.pricingHistoryId),
  });

  if (!pricingHistoryExists) {
    throw new Error("Pricing history not found");
  }

  // Check if category exists
  const categoryExists = await db.query.category.findFirst({
    where: (category, { eq }) => eq(category.id, params.categoryId),
  });

  if (!categoryExists) {
    throw new Error("Category not found");
  }

  // Check if pricing for this category already exists in this pricing history
  const existingPricing = await db.query.regionCategoryPricing.findFirst({
    where: (pricing, { eq, and }) =>
      and(
        eq(pricing.pricingHistoryId, params.pricingHistoryId),
        eq(pricing.categoryId, params.categoryId),
      ),
  });

  if (existingPricing) {
    throw new Error(
      "Pricing already exists for this category in this pricing history",
    );
  }

  const [newPricing] = await db
    .insert(regionCategoryPricing)
    .values({
      pricingHistoryId: params.pricingHistoryId,
      categoryId: params.categoryId,
      rate: params.rate,
      compensationKabadiwalaRate: params.compensationKabadiwalaRate ?? "0",
      compensationRecyclerRate: params.compensationRecyclerRate ?? "0",
    })
    .returning();

  return newPricing;
};

// Get all region pricing with optional filters
export const searchCategoryPricing = async (
  regionId?: string,
  categoryId?: string,
  includeExpired = false,
) => {
  const now = new Date();

  // First, get the pricing history IDs that match our criteria
  let validPricingHistoryIds: string[] = [];

  if (regionId || !includeExpired) {
    const pricingHistories = await db.query.pricingHistory.findMany({
      where: (history, { eq, and, lte, gte }) => {
        const conditions = [];
        if (regionId) conditions.push(eq(history.regionId, regionId));
        if (!includeExpired) {
          conditions.push(
            and(
              lte(history.effectiveFromDate, now),
              gte(history.expiresAt, now),
            ),
          );
        }
        return conditions.length > 0 ? and(...conditions) : undefined;
      },
      columns: { id: true },
    });

    validPricingHistoryIds = pricingHistories.map((h) => h.id);

    // If no valid pricing histories found, return empty array
    if (validPricingHistoryIds.length === 0) {
      return [];
    }
  }

  return await db.query.regionCategoryPricing.findMany({
    where: (pricing, { eq, and, inArray }) => {
      const conditions = [];
      if (categoryId) conditions.push(eq(pricing.categoryId, categoryId));
      if (validPricingHistoryIds.length > 0) {
        conditions.push(
          inArray(pricing.pricingHistoryId, validPricingHistoryIds),
        );
      }
      return conditions.length > 0 ? and(...conditions) : undefined;
    },
    with: {
      category: {
        columns: {
          id: true,
          name: true,
        },
      },
      pricingHistory: {
        with: {
          region: {
            columns: {
              id: true,
              name: true,
            },
          },
        },
      },
    },
    orderBy: (pricing, { desc }) => [desc(pricing.createdAt)],
  });
};
// Get category pricing by ID
export const getCategoryPricingById = async (id: string) => {
  const pricing = await db.query.regionCategoryPricing.findFirst({
    where: (pricing, { eq }) => eq(pricing.id, id),
    with: {
      category: {
        columns: {
          id: true,
          name: true,
        },
      },
      pricingHistory: {
        with: {
          region: {
            columns: {
              id: true,
              name: true,
            },
          },
        },
      },
    },
  });

  if (!pricing) {
    throw new Error("Pricing not found");
  }

  return pricing;
};

// Update category pricing
export const updateCategoryPricing = async (
  id: string,
  params: Partial<RegionPricing>,
) => {
  const existingPricing = await db.query.regionCategoryPricing.findFirst({
    where: (pricing, { eq }) => eq(pricing.id, id),
  });

  if (!existingPricing) {
    throw new Error("Pricing not found");
  }

  const [updatedPricing] = await db
    .update(regionCategoryPricing)
    .set({
      rate: params.rate ?? existingPricing.rate,
      compensationKabadiwalaRate:
        params.compensationKabadiwalaRate ??
        existingPricing.compensationKabadiwalaRate,
      compensationRecyclerRate:
        params.compensationRecyclerRate ??
        existingPricing.compensationRecyclerRate,
    })
    .where(eq(regionCategoryPricing.id, id))
    .returning();

  return updatedPricing;
};

// Delete category pricing
export const deleteCategoryPricing = async (id: string) => {
  const existingPricing = await db.query.regionCategoryPricing.findFirst({
    where: (pricing, { eq }) => eq(pricing.id, id),
  });

  if (!existingPricing) {
    throw new Error("Pricing not found");
  }

  await db
    .delete(regionCategoryPricing)
    .where(eq(regionCategoryPricing.id, id));

  return { message: "Pricing deleted successfully" };
};

// Get pricing history for a region
export const getAllPricingPeriodsForRegion = async (regionId: string) => {
  return await db.query.pricingHistory.findMany({
    where: (history, { eq }) => eq(history.regionId, regionId),
    with: {
      region: {
        columns: {
          id: true,
          name: true,
        },
      },
      regionCategoryPricing: {
        with: {
          category: {
            columns: {
              id: true,
              name: true,
            },
          },
        },
      },
    },
    orderBy: (history, { desc }) => [desc(history.effectiveFromDate)],
  });
};

// Get all pricing periods
export const getAllPricingPeriods = async () => {
  return await db.query.pricingHistory.findMany({
    with: {
      region: {
        columns: {
          id: true,
          name: true,
        },
      },
      regionCategoryPricing: {
        with: {
          category: {
            columns: {
              id: true,
              name: true,
            },
          },
        },
      },
    },
    orderBy: (history, { desc }) => [desc(history.effectiveFromDate)],
  });
};

// Update pricing period
export const updatePricingPeriod = async (
  id: string,
  params: Partial<PricingHistoryParams>,
) => {
  const existingHistory = await db.query.pricingHistory.findFirst({
    where: (history, { eq }) => eq(history.id, id),
  });

  if (!existingHistory) {
    throw new Error("Pricing history not found");
  }

  // Check for overlapping pricing history if dates are being updated
  if (params.effectiveFromDate || params.expiresAt) {
    const newEffectiveFrom =
      params.effectiveFromDate ?? existingHistory.effectiveFromDate;
    const newExpiresAt = params.expiresAt ?? existingHistory.expiresAt;

    const overlappingHistory = await db.query.pricingHistory.findFirst({
      where: (history, { eq, and, or, lte, gte, ne }) =>
        and(
          eq(history.regionId, existingHistory.regionId),
          ne(history.id, id), // Exclude current record
          or(
            and(
              lte(history.effectiveFromDate, newEffectiveFrom),
              gte(history.expiresAt, newEffectiveFrom),
            ),
            and(
              lte(history.effectiveFromDate, newExpiresAt),
              gte(history.expiresAt, newExpiresAt),
            ),
            and(
              gte(history.effectiveFromDate, newEffectiveFrom),
              lte(history.expiresAt, newExpiresAt),
            ),
          ),
        ),
    });

    if (overlappingHistory) {
      throw new Error("Updated dates would create overlapping pricing history");
    }
  }

  const [updatedHistory] = await db
    .update(pricingHistory)
    .set({
      effectiveFromDate:
        params.effectiveFromDate ?? existingHistory.effectiveFromDate,
      expiresAt: params.expiresAt ?? existingHistory.expiresAt,
    })
    .where(eq(pricingHistory.id, id))
    .returning();

  return updatedHistory;
};

// Update pricing period with category pricing
export const updatePricingPeriodWithCategoryPricing = async (
  id: string,
  params: PricingHistoryWithCategories,
) => {
  const existingHistory = await db.query.pricingHistory.findFirst({
    where: (history, { eq }) => eq(history.id, id),
  });

  if (!existingHistory) {
    throw new Error("Pricing history not found");
  }

  // Update pricing history
  const updatedHistory = await updatePricingPeriod(id, {
    effectiveFromDate: params.effectiveFromDate,
    expiresAt: params.expiresAt,
  });

  if (!updatedHistory) {
    throw new Error("Failed to update pricing history");
  }

  // Update category pricing if provided
  if (params.regionCategoryPricing.length > 0) {
    await Promise.allSettled(
      params.regionCategoryPricing.map(async (category) => {
        await db
          .update(regionCategoryPricing)
          .set({
            rate: String(category.rate),
            compensationKabadiwalaRate: String(
              category.compensationKabadiwalaRate,
            ),
            compensationRecyclerRate: String(category.compensationRecyclerRate),
          })
          .where(eq(regionCategoryPricing.categoryId, category.id));
      }),
    );
  }

  return updatedHistory;
};

// Delete pricing period (will cascade delete related pricing)
export const deletePricingPeriod = async (id: string) => {
  const existingHistory = await db.query.pricingHistory.findFirst({
    where: (history, { eq }) => eq(history.id, id),
  });

  if (!existingHistory) {
    throw new Error("Pricing history not found");
  }

  await db.delete(pricingHistory).where(eq(pricingHistory.id, id));

  return { message: "Pricing history deleted successfully" };
};
