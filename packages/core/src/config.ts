import type { systemConfigurationEnum } from "@acme/db/schema";
import { eq } from "@acme/db";
import { db } from "@acme/db/client";
import { systemConfiguration } from "@acme/db/schema";
import { tryCatch } from "@acme/validators/utils";

export const getSystemConfig = async ({
  key,
  defaultValue,
}: {
  key: (typeof systemConfigurationEnum.enumValues)[number];
  defaultValue?: string;
}) => {
  const { data, err } = await tryCatch(
    db.query.systemConfiguration.findFirst({
      where: eq(systemConfiguration.key, key),
      columns: {
        value: true,
      },
    }),
  );

  if (err) {
    console.error("Error fetching system configuration:", err);
    return null;
  }

  return data?.value ?? defaultValue ?? null;
};
