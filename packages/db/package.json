{"name": "@acme/db", "version": "0.1.0", "private": true, "type": "module", "exports": {".": {"default": "./src/index.ts"}, "./client": {"default": "./src/client.ts"}, "./schema": {"default": "./src/schema.ts"}}, "license": "MIT", "scripts": {"clean": "git clean -xdf .cache .turbo dist node_modules", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "push": "pnpm with-env drizzle-kit push", "studio": "pnpm with-env drizzle-kit studio", "typecheck": "tsc --noEmit --emitDeclarationOnly false", "with-env": "dotenv -e ../../.env --", "seed:admin": "pnpm with-env pnpx tsx ./src/seed/admin.ts", "seed:faq": "pnpm with-env pnpx tsx ./src/seed/faq.ts"}, "dependencies": {"@paralleldrive/cuid2": "^2.2.2", "drizzle-orm": "^0.41.0", "drizzle-zod": "^0.7.1", "pg": "^8.15.6", "postgres": "^3.4.5", "zod": "catalog:"}, "devDependencies": {"@acme/eslint-config": "workspace:*", "@acme/prettier-config": "workspace:*", "@acme/tsconfig": "workspace:*", "dotenv-cli": "^8.0.0", "drizzle-kit": "^0.31.0", "eslint": "catalog:", "prettier": "catalog:", "typescript": "catalog:"}, "prettier": "@acme/prettier-config"}