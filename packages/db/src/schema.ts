import type { AnyPgColumn } from "drizzle-orm/pg-core";
import { createId } from "@paralleldrive/cuid2";
import { relations } from "drizzle-orm";
import {
  boolean,
  json,
  jsonb,
  numeric,
  pgEnum,
  pgTable,
  text,
  timestamp,
  varchar,
} from "drizzle-orm/pg-core";

import type { GoogleReverseGeocodeAddressComponent } from ".";
import { scraphub, scraphubEmployee } from "./schema";

export * from "./schemas/scraphub";

export const OnBoardingEnum = pgEnum("OnBoardingEnum", [
  "STEP_1",
  "STEP_2",
  "STEP_3",
  "STEP_4",
  "STEP_5",
  "STEP_6",
]);

export const admin = pgTable("admin", {
  id: varchar("id", { length: 128 })
    .$defaultFn(() => createId())
    .primaryKey(),
  name: text("name").notNull(),
  email: text("email").notNull().unique(),
  emailVerified: boolean("emailVerified").notNull(),
  image: text("image"),
  createdAt: timestamp("createdAt").notNull(),
  updatedAt: timestamp("updatedAt").notNull(),
});

export const adminSession = pgTable("adminSession", {
  id: varchar("id", { length: 128 })
    .$defaultFn(() => createId())
    .primaryKey(),
  expiresAt: timestamp("expiresAt").notNull(),
  token: text("token").notNull().unique(),
  createdAt: timestamp("createdAt").notNull(),
  updatedAt: timestamp("updatedAt").notNull(),
  ipAddress: text("ipAddress"),
  userAgent: text("userAgent"),
  adminId: text("adminId")
    .notNull()
    .references(() => admin.id, { onDelete: "cascade" }),
});

export const adminAccount = pgTable("adminAccount", {
  id: varchar("id", { length: 128 })
    .$defaultFn(() => createId())
    .primaryKey(),
  accountId: text("accountId").notNull(),
  providerId: text("providerId").notNull(),
  adminId: text("adminId")
    .notNull()
    .references(() => admin.id, { onDelete: "cascade" }),
  accessToken: text("accessToken"),
  refreshToken: text("refreshToken"),
  idToken: text("idToken"),
  accessTokenExpiresAt: timestamp("accessTokenExpiresAt"),
  refreshTokenExpiresAt: timestamp("refreshTokenExpiresAt"),
  scope: text("scope"),
  password: text("password"),
  createdAt: timestamp("createdAt").notNull(),
  updatedAt: timestamp("updatedAt").notNull(),
});

export const adminVerification = pgTable("adminVerification", {
  id: varchar("id", { length: 128 })
    .$defaultFn(() => createId())
    .primaryKey(),
  identifier: text("identifier").notNull(),
  value: text("value").notNull(),
  expiresAt: timestamp("expiresAt").notNull(),
  createdAt: timestamp("createdAt"),
  updatedAt: timestamp("updatedAt"),
});

export const vehicleTypeEnum = pgEnum("vehicleTypeEnum", [
  "BICYCLE",
  "CYCLE_RICKSHAW",
  "HANDCART",
  "PUSHCART",
  "MOTORCYCLE_WITH_SIDECAR",
  "THREE_WHEELER_CARGO_VEHICLE",
  "MINI_TRUCK",
  "PICKUP_TRUCK",
  "FLATBED_TRUCK",
  "TIPPER_TRUCK",
  "DUMP_TRUCK",
  "GRAPPLE_TRUCK",
  "GARBAGE_TRUCK",
  "TOW_TRUCK",
  "BOX_TRUCK",
  "VAN",
]);

export const workHoursModeEnum = pgEnum("workHoursModeEnum", [
  "AUTOMATIC",
  "MANUAL",
]);

export const kabadiwala = pgTable("kabadiwala", {
  id: varchar("id", { length: 128 })
    .$defaultFn(() => createId())
    .primaryKey(),
  name: text("name").notNull(),
  email: text("email").notNull().unique(),
  emailVerified: boolean("emailVerified").notNull(),
  image: text("image"),
  imageFileKey: text("imageFileKey"),
  isOnDuty: boolean("isOnDuty").default(false).notNull(),
  walletBalance: numeric("walletBalance").default("0"),
  generalNotificationPermission: boolean(
    "generalNotificationPermission",
  ).default(true),
  chatNotificationPermission: boolean("chatNotificationPermission").default(
    true,
  ),
  onboardingCompleted: boolean("onboardingCompleted").default(false),
  onboarding: OnBoardingEnum("onboarding").default("STEP_1"),
  adharCardNumber: text("adharCardNumber").unique(),
  frontOfAdharCardMediaId: text("frontOfAdharCardMediaId"),
  backOfAdharCardMediaId: text("backOfAdharCardMediaId"),
  policeVerificationMediaId: text("policeVerificationMediaId"),
  whatsappConsent: boolean("whatsappConsent").default(false),
  liveLocationCoordinate: json("liveLocationCoordinates").$type<{
    latitude: number;
    longitude: number;
  }>(),
  ccAuthToken: text("ccAuthToken"),
  createdAt: timestamp("createdAt").notNull(),
  updatedAt: timestamp("updatedAt").notNull(),
  phoneNumber: text("phoneNumber").unique(),
  phoneNumberVerified: boolean("phoneNumberVerified"),
  dlVerificationResponse: json("dlVerificationResponse"),
  isBlocked: boolean("isBlocked"),
  reason: text("reason"),
  scraphubId: varchar("scraphubId", { length: 128 }).references(
    () => scraphub.id,
    { onDelete: "cascade" },
  ),

  workHoursMode: workHoursModeEnum("workHoursMode").default("MANUAL"),
  scheduleHours: json("scheduleHours").$type<{
    sunday: {
      isAvailable: boolean;
      timeSlots: { startTime: string; endTime: string }[];
    };
    monday: {
      isAvailable: boolean;
      timeSlots: { startTime: string; endTime: string }[];
    };
    tuesday: {
      isAvailable: boolean;
      timeSlots: { startTime: string; endTime: string }[];
    };
    wednesday: {
      isAvailable: boolean;
      timeSlots: { startTime: string; endTime: string }[];
    };
    thursday: {
      isAvailable: boolean;
      timeSlots: { startTime: string; endTime: string }[];
    };
    friday: {
      isAvailable: boolean;
      timeSlots: { startTime: string; endTime: string }[];
    };
    saturday: {
      isAvailable: boolean;
      timeSlots: { startTime: string; endTime: string }[];
    };
  }>(),
  violationCount: numeric("violationCount").default("0"),
  issueCount: numeric("issueCount").default("0"),
  lastViolationAt: timestamp("lastViolationAt", {
    mode: "date",
    withTimezone: true,
  }),
  averageRating: numeric("averageRating").default("0"),
});

export const kabadiwalaLocationHistory = pgTable("kabadiwalaLocationHistory", {
  id: varchar("id", { length: 128 })
    .$defaultFn(() => createId())
    .primaryKey(),
  kabadiwalaId: text("kabadiwalaId")
    .notNull()
    .references(() => kabadiwala.id, { onDelete: "cascade" }),
  coordinates: json("coordinates")
    .$type<{
      latitude: number;
      longitude: number;
    }>()
    .notNull(),
  orderId: text("orderId").references(() => order.id, {
    onDelete: "cascade",
  }),
  createdAt: timestamp("createdAt", { mode: "date", withTimezone: true })
    .defaultNow()
    .notNull(),
  updatedAt: timestamp("updatedAt", {
    mode: "date",
    withTimezone: true,
  }).$onUpdate(() => new Date()),
});

export const kabadiwalaLocationHistoryRelations = relations(
  kabadiwalaLocationHistory,
  ({ one }) => ({
    kabadiwala: one(kabadiwala, {
      fields: [kabadiwalaLocationHistory.kabadiwalaId],
      references: [kabadiwala.id],
    }),
  }),
);

export const kabadiwalaPaymentStatusEnum = pgEnum(
  "kabadiwalaPaymentStatusEnum",
  ["PENDING", "COMPLETED", "FAILED"],
);

export const kabadiwalaTransactionTypeEnum = pgEnum(
  "kabadiwalaTransactionTypeEnum",
  ["CREDIT", "DEBIT"],
);

export const kabadiwalaTransactionForEnum = pgEnum(
  "kabadiwalaTransactionForEnum",
  ["ORDER_PAYMENT", "WALLET_TOPUP", "WALLET_WITHDRAWAL", "EARNINGS", "OTHER"],
);

export const kabadiwalaPaymentTransaction = pgTable(
  "kabadiwalaPaymentTransaction",
  {
    id: varchar("id", { length: 128 })
      .$defaultFn(() => createId())
      .primaryKey(),
    kabadiwalaId: text("kabadiwalaId")
      .notNull()
      .references(() => kabadiwala.id, { onDelete: "cascade" }),
    amount: numeric("amount").notNull(),
    razorpayOrderId: text("razorpayOrderId").unique(),
    razorpayPaymentId: text("razorpayPaymentId").unique(),
    currency: text("currency").default("INR"),
    status: kabadiwalaPaymentStatusEnum("status").notNull(),
    transactionType: kabadiwalaTransactionTypeEnum("transactionType").notNull(),
    transactionFor: kabadiwalaTransactionForEnum("transactionFor").notNull(),
    orderId: text("orderId").references(() => order.id, {
      onDelete: "restrict",
    }),
    createdAt: timestamp("createdAt").defaultNow().notNull(),
    updatedAt: timestamp("updatedAt", {
      mode: "date",
      withTimezone: true,
    }).$onUpdate(() => new Date()),
  },
);

export const reviewForEnum = pgEnum("reviewForEnum", ["KABADIWALA", "SELLER"]);

export const reviews = pgTable("reviews", {
  id: varchar("id", { length: 128 })
    .$defaultFn(() => createId())
    .primaryKey(),
  reviewFor: reviewForEnum("reviewFor").notNull(), // KABADIWALA or SELLER
  orderId: varchar("orderId", { length: 128 })
    .notNull()
    .references(() => order.id, { onDelete: "cascade" }),
  sellerId: varchar("sellerId", { length: 128 }).references(() => seller.id, {
    onDelete: "cascade",
  }),
  kabadiwalaId: varchar("kabadiwalaId", { length: 128 }).references(
    () => kabadiwala.id,
    { onDelete: "cascade" },
  ),
  rating: numeric("rating").notNull(), // 1-5, validate in app logic
  review: varchar("review", { length: 500 }), // optional, max 500 chars
  createdAt: timestamp("createdAt", { mode: "date", withTimezone: true })
    .defaultNow()
    .notNull(),
  updatedAt: timestamp("updatedAt", { mode: "date", withTimezone: true })
    .$onUpdate(() => new Date())
    .notNull(),
});

export const reviewsRelations = relations(reviews, ({ one }) => ({
  order: one(order, {
    fields: [reviews.orderId],
    references: [order.id],
  }),
}));

// Customer equivalents for payment transaction and enums
export const customerPaymentStatusEnum = pgEnum("customerPaymentStatusEnum", [
  "PENDING",
  "COMPLETED",
  "FAILED",
]);

export const customerTransactionTypeEnum = pgEnum(
  "customerTransactionTypeEnum",
  ["CREDIT", "DEBIT"],
);

export const customerTransactionForEnum = pgEnum("customerTransactionForEnum", [
  "ORDER_PAYMENT",
  "WALLET_TOPUP",
  "WALLET_WITHDRAWAL",
  "OTHER",
]);

export const customerPaymentTransaction = pgTable(
  "customerPaymentTransaction",
  {
    id: varchar("id", { length: 128 })
      .$defaultFn(() => createId())
      .primaryKey(),
    customerId: text("customerId")
      .notNull()
      .references(() => seller.id, { onDelete: "cascade" }),
    amount: numeric("amount").notNull(),
    razorpayOrderId: text("razorpayOrderId").unique(),
    razorpayPaymentId: text("razorpayPaymentId").unique(),
    currency: text("currency").default("INR"),
    status: customerPaymentStatusEnum("status").notNull(),
    transactionType: customerTransactionTypeEnum("transactionType").notNull(),
    transactionFor: customerTransactionForEnum("transactionFor").notNull(),
    orderId: text("orderId").references(() => order.id, {
      onDelete: "restrict",
    }),
    createdAt: timestamp("createdAt").defaultNow().notNull(),
    updatedAt: timestamp("updatedAt", {
      mode: "date",
      withTimezone: true,
    }).$onUpdate(() => new Date()),
  },
);

export const mediaTypeEnum = pgEnum("mediaTypeEnum", [
  "IMAGE",
  "VIDEO",
  "AUDIO",
  "DOCUMENT",
  "OTHER",
]);

export const kabadiwalaMedia = pgTable("kabadiwalaMedia", {
  id: varchar("id", { length: 128 })
    .$defaultFn(() => createId())
    .primaryKey(),
  kabadiwalaId: text("kabadiwalaId").notNull(),
  mediaUrl: text("mediaUrl").notNull(),
  mediaType: mediaTypeEnum("mediaType").notNull(),
  fileName: text("fileName").notNull(),
  mimeType: text("mimeType"),
  fileKey: text("fileKey"),
  createdAt: timestamp("createdAt").defaultNow(),
  updatedAt: timestamp("updatedAt").defaultNow(),
});

export const vehicle = pgTable("vehicle", {
  id: varchar("id", { length: 128 })
    .$defaultFn(() => createId())
    .primaryKey(),
  kabadiwalaId: varchar("kabadiwalaId", { length: 128 })
    .references(() => kabadiwala.id, { onDelete: "cascade" })
    .notNull(),
  vehicleType: vehicleTypeEnum("vehicleType").notNull(),
  vehicleNumber: text("vehicleNumber").unique(),
  isActive: boolean("isActive").default(true),
  createdAt: timestamp("createdAt", { mode: "date", withTimezone: true })
    .defaultNow()
    .notNull(),
  updatedAt: timestamp("updatedAt", {
    mode: "date",
    withTimezone: true,
  }).$onUpdate(() => new Date()),
});

export const kabadiwalaRelations = relations(kabadiwala, ({ one, many }) => ({
  frontAdharCardMedia: one(kabadiwalaMedia, {
    fields: [kabadiwala.frontOfAdharCardMediaId],
    references: [kabadiwalaMedia.id],
  }),
  backAdharCardMedia: one(kabadiwalaMedia, {
    fields: [kabadiwala.backOfAdharCardMediaId],
    references: [kabadiwalaMedia.id],
  }),
  policeVerificationMedia: one(kabadiwalaMedia, {
    fields: [kabadiwala.policeVerificationMediaId],
    references: [kabadiwalaMedia.id],
  }),
  scraphub: one(scraphub, {
    fields: [kabadiwala.scraphubId],
    references: [scraphub.id],
  }),
  media: many(kabadiwalaMedia),
  addresses: many(kabadiwalaAddress),
  vehicles: many(vehicle),
  transactions: many(kabadiwalaPaymentTransaction),
  locationHistory: many(kabadiwalaLocationHistory),
  rejectedOrders: many(rejectedOrders),
}));

export const kabadiwalaPaymentTransactionRelations = relations(
  kabadiwalaPaymentTransaction,
  ({ one }) => ({
    kabadiwala: one(kabadiwala, {
      fields: [kabadiwalaPaymentTransaction.kabadiwalaId],
      references: [kabadiwala.id],
    }),
    order: one(order, {
      fields: [kabadiwalaPaymentTransaction.orderId],
      references: [order.id],
    }),
  }),
);

export const kabadiwalaMediaRelations = relations(
  kabadiwalaMedia,
  ({ one }) => ({
    kabadiwala: one(kabadiwala, {
      fields: [kabadiwalaMedia.kabadiwalaId],
      references: [kabadiwala.id],
    }),
  }),
);

export const vehicleRelations = relations(vehicle, ({ one }) => ({
  kabadiwala: one(kabadiwala, {
    fields: [vehicle.kabadiwalaId],
    references: [kabadiwala.id],
  }),
}));

export const session = pgTable("session", {
  id: varchar("id", { length: 128 })
    .$defaultFn(() => createId())
    .primaryKey(),
  expiresAt: timestamp("expiresAt").notNull(),
  token: text("token").notNull().unique(),
  createdAt: timestamp("createdAt").notNull(),
  updatedAt: timestamp("updatedAt").notNull(),
  ipAddress: text("ipAddress"),
  userAgent: text("userAgent"),
  kabadiwalaId: text("kabadiwalaId")
    .notNull()
    .references(() => kabadiwala.id, { onDelete: "cascade" }),
});

export const account = pgTable("account", {
  id: varchar("id", { length: 128 })
    .$defaultFn(() => createId())
    .primaryKey(),
  accountId: text("accountId").notNull(),
  providerId: text("providerId").notNull(),
  kabadiwalaId: text("kabadiwalaId")
    .notNull()
    .references(() => kabadiwala.id, { onDelete: "cascade" }),
  accessToken: text("accessToken"),
  refreshToken: text("refreshToken"),
  idToken: text("idToken"),
  accessTokenExpiresAt: timestamp("accessTokenExpiresAt"),
  refreshTokenExpiresAt: timestamp("refreshTokenExpiresAt"),
  scope: text("scope"),
  password: text("password"),
  createdAt: timestamp("createdAt").notNull(),
  updatedAt: timestamp("updatedAt").notNull(),
});

export const verification = pgTable("verification", {
  id: varchar("id", { length: 128 })
    .$defaultFn(() => createId())
    .primaryKey(),
  identifier: text("identifier").notNull(),
  value: text("value").notNull(),
  expiresAt: timestamp("expiresAt").notNull(),
  createdAt: timestamp("createdAt"),
  updatedAt: timestamp("updatedAt"),
  dailyOtpAttemptsCount: numeric("dailyOtpAttemptsCount")
    .default("0")
    .notNull(),
});

export const seller = pgTable("seller", {
  id: varchar("id", { length: 128 })
    .$defaultFn(() => createId())
    .primaryKey(),
  fullName: text("fullName").notNull(),
  email: text("email").notNull().unique(),
  emailVerified: boolean("emailVerified").notNull(),
  image: text("image"),
  isPaymentVerified: boolean("isPaymentVerified").default(false),
  phoneNumber: text("phoneNumber").unique(),
  phoneNumberVerified: boolean("phoneNumberVerified"),
  razorpayContactId: text("razorpayContactId").unique(),
  razorpayFundAccountId: text("razorpayFundAccountId").unique(),
  razorpayFundValidationResponseId: text(
    "razorpayFundValidationResponseId",
  ).unique(), // if this is present, it means the fund account is validated
  onBoardingStep: OnBoardingEnum("onBoardingStep").default("STEP_1"),
  onBoardingCompleted: boolean("onBoardingCompleted").default(false),
  ccAuthToken: text("ccAuthToken"),
  createdAt: timestamp("createdAt").notNull(),
  updatedAt: timestamp("updatedAt").notNull(),
  isBlocked: boolean("isBlocked").default(false).notNull(),
  reason: text("reason"),
  averageRating: numeric("averageRating").default("0"),
});

export const sellerSession = pgTable("sellerSession", {
  id: varchar("id", { length: 128 })
    .$defaultFn(() => createId())
    .primaryKey(),
  expiresAt: timestamp("expiresAt").notNull(),
  token: text("token").notNull().unique(),
  createdAt: timestamp("createdAt").notNull(),
  updatedAt: timestamp("updatedAt").notNull(),
  ipAddress: text("ipAddress"),
  userAgent: text("userAgent"),
  sellerId: text("sellerId")
    .notNull()
    .references(() => seller.id, { onDelete: "cascade" }),
});

export const sellerAccount = pgTable("sellerAccount", {
  id: varchar("id", { length: 128 })
    .$defaultFn(() => createId())
    .primaryKey(),
  accountId: text("accountId").notNull(),
  providerId: text("providerId").notNull(),
  sellerId: text("sellerId")
    .notNull()
    .references(() => seller.id, { onDelete: "cascade" }),
  accessToken: text("accessToken"),
  refreshToken: text("refreshToken"),
  idToken: text("idToken"),
  accessTokenExpiresAt: timestamp("accessTokenExpiresAt"),
  refreshTokenExpiresAt: timestamp("refreshTokenExpiresAt"),
  scope: text("scope"),
  password: text("password"),
  createdAt: timestamp("createdAt").notNull(),
  updatedAt: timestamp("updatedAt").notNull(),
});

export const sellerVerification = pgTable("sellerVerification", {
  id: varchar("id", { length: 128 })
    .$defaultFn(() => createId())
    .primaryKey(),
  identifier: text("identifier").notNull(),
  value: text("value").notNull(),
  expiresAt: timestamp("expiresAt").notNull(),
  createdAt: timestamp("createdAt"),
  updatedAt: timestamp("updatedAt"),
  dailyOtpAttemptsCount: numeric("dailyOtpAttemptsCount")
    .default("0")
    .notNull(),
});

export const categoryRateTypeEnum = pgEnum("categoryRateTypeEnum", [
  "PER_KG",
  "PER_ITEM",
]);

export const category = pgTable("category", {
  id: varchar("id", { length: 128 })
    .$defaultFn(() => createId())
    .primaryKey(),
  name: text("name").notNull(),
  rate: numeric("rate").notNull(), // ie: price
  rateType: categoryRateTypeEnum("rateType"),
  compensationKabadiwalaRate: numeric("compensationKabadiwalaRate")
    .notNull()
    .default("0"), // ie: price
  compensationRecyclerRate: numeric("compensationRecylerRate")
    .notNull()
    .default("0"), // ie: price
  image: text("image").notNull(),
  description: text("description"),
  isActive: boolean("isActive").default(true),
  parentId: varchar("parentId", { length: 128 }).references(
    (): AnyPgColumn => category.id, // Self-reference
    { onDelete: "restrict" },
  ),
  deletedAt: timestamp("deletedAt", { mode: "date", withTimezone: true }),
  createdAt: timestamp("createdAt", { mode: "date", withTimezone: true })
    .defaultNow()
    .notNull(),
  updatedAt: timestamp("updatedAt", { mode: "date", withTimezone: true }),
  tag: varchar("tag", { length: 128 }), // nullable, only for admin, not unique at DB level
});

export const categoryRelations = relations(category, ({ one, many }) => ({
  parent: one(category, {
    fields: [category.parentId],
    references: [category.id],
    relationName: "categoryHierarchy",
  }),
  children: many(category, {
    relationName: "categoryHierarchy",
  }),
  regionPricing: many(regionCategoryPricing),
}));

export const addressTypeEnum = pgEnum("addressTypeEnum", [
  "HOME",
  "WORK",
  "OTHER",
]);

export const kabadiwalaAddress = pgTable("kabadiwalaAddress", {
  id: varchar("id", { length: 128 })
    .$defaultFn(() => createId())
    .primaryKey(),
  name: text("name").notNull(),
  addressType: addressTypeEnum("addressType").default("HOME"),
  display: text("display").notNull(),
  street: text("street"),
  city: text("city"),
  state: text("state"),
  country: text("country"),
  postalCode: text("postalCode"),
  coordinates: json("coordinates").$type<{
    latitude: number;
    longitude: number;
  }>(),
  localAddress: text("localAddress").notNull(),
  landmark: text("landmark").notNull(),
  isDefault: boolean("isDefault").default(false),
  kabadiwalaId: varchar("kabadiwalaId", { length: 128 })
    .references(() => kabadiwala.id, { onDelete: "cascade" })
    .notNull(),
  createdAt: timestamp("createdAt", { mode: "date", withTimezone: true })
    .defaultNow()
    .notNull(),
  updatedAt: timestamp("updatedAt", {
    mode: "date",
    withTimezone: true,
  }).$onUpdate(() => new Date()),
});

export const address = pgTable("address", {
  id: varchar("id", { length: 128 })
    .$defaultFn(() => createId())
    .primaryKey(),
  name: text("name").notNull(),
  addressType: addressTypeEnum("addressType").default("HOME"),
  display: text("display").notNull(),
  street: text("street"),
  city: text("city"),
  state: text("state"),
  country: text("country"),
  postalCode: text("postalCode"),
  district: text("district"),
  googlePlaceId: text("googlePlaceId"),
  googleAddressComponent: jsonb("googleAddressComponent").$type<
    GoogleReverseGeocodeAddressComponent[]
  >(),

  coordinates: json("coordinates").$type<{
    latitude: number;
    longitude: number;
  }>(),
  localAddress: text("localAddress").notNull(),
  landmark: text("landmark").notNull(),
  isDefault: boolean("isDefault").default(false),
  regionId: varchar("regionId", { length: 128 }).references(() => regions.id),
  sellerId: varchar("sellerId", { length: 128 })
    .references(() => seller.id, { onDelete: "cascade" })
    .notNull(),
  createdAt: timestamp("createdAt", { mode: "date", withTimezone: true })
    .defaultNow()
    .notNull(),
  updatedAt: timestamp("updatedAt", {
    mode: "date",
    withTimezone: true,
  }).$onUpdate(() => new Date()),
});

export const sellerRelations = relations(seller, ({ many }) => ({
  addresses: many(address),
  orders: many(order),
}));

export const addressRelations = relations(address, ({ one }) => ({
  seller: one(seller, {
    fields: [address.sellerId],
    references: [seller.id],
  }),
  region: one(regions, {
    fields: [address.regionId],
    references: [regions.id],
  }),
}));

export const kabadiwalaAddressRelations = relations(
  kabadiwalaAddress,
  ({ one }) => ({
    kabadiwala: one(kabadiwala, {
      fields: [kabadiwalaAddress.kabadiwalaId],
      references: [kabadiwala.id],
    }),
  }),
);

export const orderStatusEnum = pgEnum("orderStatusEnum", [
  "CART",
  "ACTIVE",
  "PENDING",
  "COMPLETED",
  "CANCELLED",
]);

export const afterCompletedStatusEnum = pgEnum("afterCompletedStatusEnum", [
  "IN_SCRAPHUB",
  "IN_RECYCLEHUB",
]);

export const pickupOtpStatusEnum = pgEnum("pickupOtpStatusEnum", [
  "PENDING",
  "VERIFIED",
]);

export const orderApprovalStatusEnum = pgEnum("orderApprovalStatusEnum", [
  "REQUESTED_CONFIRMATION_BY_KABADIWALA",
  "REQUESTED_RE_REVIEW_BY_SELLER",
  "REQUEST_APPROVED_BY_SELLER",
  "REQUEST_REJECTED_BY_SELLER",
]);

export const paymentStatusEnum = pgEnum("paymentStatusEnum", [
  "PENDING",
  "COMPLETED",
  "FAILED",
]);

export const order = pgTable("order", {
  id: varchar("id", { length: 128 })
    .$defaultFn(() => createId())
    .primaryKey(),
  sellerId: varchar("sellerId", { length: 128 })
    .references(() => seller.id, { onDelete: "cascade" })
    .notNull(),
  status: orderStatusEnum("status").default("CART"),
  totalAmount: numeric("totalAmount"),
  orderApprovalStatus: orderApprovalStatusEnum("orderApprovalStatus"),
  earnings: numeric("earnings"),
  pickupOtp: numeric("pickupOtp"),
  pickupOtpStatus: pickupOtpStatusEnum("pickupOtpStatus").default("PENDING"),
  kabadiwalaSelfiImageUrl: text("kabadiwalaSelfiImageUrl"),
  kabadiwalaSelfiImageFileKey: text("kabadiwalaSelfiImageFileKey"),
  addressId: varchar("addressId", { length: 128 })
    .references(() => address.id)
    .notNull(),
  createdAt: timestamp("createdAt", { mode: "date", withTimezone: true })
    .defaultNow()
    .notNull(),
  updatedAt: timestamp("updatedAt", {
    mode: "date",
    withTimezone: true,
  }).$onUpdate(() => new Date()),
  completedAt: timestamp("completedAt", { mode: "date", withTimezone: true }),
  kabadiwalaId: varchar("kabadiwalaId", { length: 128 }).references(
    () => kabadiwala.id,
  ),
  afterCompletedStatus: afterCompletedStatusEnum("afterCompletedStatus"),

  securityFeePercentage: numeric("securityFeePercentage"),
  securityFeeAmount: numeric("securityFeeAmount"),
  gstPercentage: numeric("gstPercentage"), // GST on service charge
  gstAmount: numeric("gstAmount"), // calculated GST
  totalServiceChargeWithGst: numeric("totalServiceChargeWithGst"),
  totalServiceChargeWithSecurityFee: numeric(
    "totalServiceChargeWithSecurityFee",
  ),
  handlingCharge: numeric("handlingCharge"),
  amountToPayCustomer: numeric("amountToPayCustomer"), // final amount kabadiwala pays to customer
  kabadiwalaWalletBalanceBeforePayment: numeric(
    "kabadiwalaWalletBalanceBeforePayment",
  ),
  kabadiwalaWalletBalanceAfterPayment: numeric(
    "kabadiwalaWalletBalanceAfterPayment",
  ),

  // Payment status
  paymentCompletedAt: timestamp("paymentCompletedAt", {
    mode: "date",
    withTimezone: true,
  }),
  paymentStatus: paymentStatusEnum("paymentStatus").default("PENDING"),
  billToKabadiwala: jsonb("billToKabadiwala"),
  payoutMethodId: text("payoutMethodId"), // Razorpay fund account ID for payout

  scraphubEmployeeId: varchar("scraphubEmployeeId", { length: 128 }).references(
    () => scraphubEmployee.id,
    { onDelete: "cascade" },
  ),
  kabadiwalaEarningsAfterDeliveringOrderAtScraphub: numeric(
    "kabadiwalaEarningsAfterDeliveringOrderAtScraphub",
  ),
  kabadiwalaPaymentRecievedAtScraphubWithoutEarnings: numeric(
    "kabadiwalaPaymentRecievedAtScraphubWithoutEarnings",
  ),
  scraphubPaymentAt: timestamp("scraphubPaymentAt", {
    mode: "date",
    withTimezone: true,
  }),
});

export const orderItem = pgTable("orderItem", {
  id: varchar("id", { length: 128 })
    .$defaultFn(() => createId())
    .primaryKey(),
  orderId: varchar("orderId", { length: 128 })
    .references(() => order.id, { onDelete: "cascade" })
    .notNull(),
  categoryId: varchar("categoryId", { length: 128 })
    .references(() => category.id)
    .notNull(),
  quantity: numeric("quantity").notNull(),
  rate: numeric("rate").notNull(),
  quantityAtScraphub: numeric("quantityAtScraphub"),
  createdAt: timestamp("createdAt", { mode: "date", withTimezone: true })
    .defaultNow()
    .notNull(),
  updatedAt: timestamp("updatedAt", {
    mode: "date",
    withTimezone: true,
  }).$onUpdate(() => new Date()),
});

export const orderRelations = relations(order, ({ one, many }) => ({
  seller: one(seller, {
    fields: [order.sellerId],
    references: [seller.id],
  }),
  address: one(address, {
    fields: [order.addressId],
    references: [address.id],
  }),
  items: many(orderItem),
  kabadiwala: one(kabadiwala, {
    fields: [order.kabadiwalaId],
    references: [kabadiwala.id],
  }),
  conversation: one(conversation, {
    fields: [order.id],
    references: [conversation.orderId],
    relationName: "orderToConversation",
  }),
  scraphubEmployee: one(scraphubEmployee, {
    fields: [order.scraphubEmployeeId],
    references: [scraphubEmployee.id],
  }),
  rejections: many(rejectedOrders),
  reviews: many(reviews),
}));

export const rejectedOrders = pgTable("rejectedOrders", {
  id: varchar("id", { length: 128 })
    .$defaultFn(() => createId())
    .primaryKey(),
  orderId: varchar("orderId", { length: 128 })
    .references(() => order.id, { onDelete: "cascade" })
    .notNull(),
  kabadiwalaId: varchar("kabadiwalaId", { length: 128 })
    .references(() => kabadiwala.id, { onDelete: "cascade" })
    .notNull(),
  reason: text("reason"),
  createdAt: timestamp("createdAt", { mode: "date", withTimezone: true })
    .defaultNow()
    .notNull(),
});

export const rejectedOrdersRelations = relations(rejectedOrders, ({ one }) => ({
  order: one(order, {
    fields: [rejectedOrders.orderId],
    references: [order.id],
  }),
  kabadiwala: one(kabadiwala, {
    fields: [rejectedOrders.kabadiwalaId],
    references: [kabadiwala.id],
  }),
}));

export const orderItemRelations = relations(orderItem, ({ one }) => ({
  order: one(order, {
    fields: [orderItem.orderId],
    references: [order.id],
  }),
  category: one(category, {
    fields: [orderItem.categoryId],
    references: [category.id],
  }),
}));

export const message = pgTable("message", {
  id: varchar("id", { length: 128 })
    .$defaultFn(() => createId())
    .primaryKey(),
  content: text("content").notNull(),
  conversationId: varchar("conversationId", { length: 128 })
    .references(() => conversation.id, { onDelete: "cascade" })
    .notNull(),
  senderId: varchar("senderId", { length: 128 }).notNull(),
  isRead: boolean("isRead").default(false),
  createdAt: timestamp("createdAt", { mode: "date", withTimezone: true })
    .defaultNow()
    .notNull(),
  updatedAt: timestamp("updatedAt", {
    mode: "date",
    withTimezone: true,
  }).$onUpdate(() => new Date()),
});

export const conversation = pgTable("conversation", {
  id: varchar("id", { length: 128 })
    .$defaultFn(() => createId())
    .primaryKey(),
  orderId: varchar("orderId", { length: 128 })
    .notNull()
    .references(() => order.id, {
      onDelete: "cascade",
    })
    .unique(), // Add unique constraint to ensure one-to-one relationship
  createdAt: timestamp("createdAt", { mode: "date", withTimezone: true })
    .defaultNow()
    .notNull(),
  updatedAt: timestamp("updatedAt", {
    mode: "date",
    withTimezone: true,
  }).$onUpdate(() => new Date()),
});

export const conversationRelations = relations(
  conversation,
  ({ one, many }) => ({
    order: one(order, {
      fields: [conversation.orderId],
      references: [order.id],
      relationName: "orderToConversation",
    }),
    messages: many(message),
  }),
);

export const messageRelations = relations(message, ({ one }) => ({
  conversation: one(conversation, {
    fields: [message.conversationId],
    references: [conversation.id],
  }),
}));

export const faqForEnum = pgEnum("faqForEnum", [
  "KABADIWALA",
  "SELLER",
  "LANDING_PAGE",
]);
//add landing type single no parent
export const question = pgTable("question", {
  id: varchar("id", { length: 128 })
    .$defaultFn(() => createId())
    .primaryKey(),
  content: text("content").notNull(),
  questionFor: faqForEnum("questionFor").notNull(),
  order: numeric("order").default("0").notNull(),
  parentId: varchar("parentId", { length: 128 }).references(
    (): AnyPgColumn => question.id, // Self-reference for hierarchical questions
    { onDelete: "cascade" },
  ),
  createdAt: timestamp("createdAt", { mode: "date", withTimezone: true })
    .defaultNow()
    .notNull(),
  updatedAt: timestamp("updatedAt", {
    mode: "date",
    withTimezone: true,
  })
    .$onUpdate(() => new Date())
    .notNull(),
});

export const answer = pgTable("answer", {
  id: varchar("id", { length: 128 })
    .$defaultFn(() => createId())
    .primaryKey(),
  content: text("content").notNull(),
  questionId: varchar("questionId", { length: 128 })
    .references(() => question.id, { onDelete: "cascade" })
    .notNull(),
  createdAt: timestamp("createdAt", { mode: "date", withTimezone: true })
    .defaultNow()
    .notNull(),
  updatedAt: timestamp("updatedAt", {
    mode: "date",
    withTimezone: true,
  })
    .$onUpdate(() => new Date())
    .notNull(),
});

export const questionRelations = relations(question, ({ one, many }) => ({
  parent: one(question, {
    fields: [question.parentId],
    references: [question.id],
    relationName: "questionHierarchy",
  }),
  children: many(question, {
    relationName: "questionHierarchy",
  }),
  answers: many(answer),
}));

export const answerRelations = relations(answer, ({ one }) => ({
  question: one(question, {
    fields: [answer.questionId],
    references: [question.id],
  }),
}));

export const deleteAccountRequest = pgTable("deleteAccountRequest", {
  id: varchar("id", { length: 128 })
    .$defaultFn(() => createId())
    .primaryKey(),
  userId: varchar("userId", { length: 128 })
    .references(() => seller.id, { onDelete: "cascade" })
    .notNull(),
  reason: text("reason").notNull(),
  createdAt: timestamp("createdAt", { mode: "date", withTimezone: true })
    .defaultNow()
    .notNull(),
});

export const deleteAccountRequestRelations = relations(
  deleteAccountRequest,
  ({ one }) => ({
    user: one(seller, {
      fields: [deleteAccountRequest.userId],
      references: [seller.id],
    }),
  }),
);

export const blogStatusEnum = pgEnum("blogStatusEnum", [
  "DRAFT",
  "PUBLISHED",
  "ARCHIVED",
]);

export const blogs = pgTable("blogs", {
  id: varchar("id", { length: 128 })
    .$defaultFn(() => createId())
    .primaryKey(),
  title: text("title").notNull(),
  slug: text("slug").notNull().unique(),
  excerpt: text("excerpt"),
  content: text("content").notNull(),
  banner: text("banner").notNull(),
  status: blogStatusEnum("status").default("DRAFT"),
  isActive: boolean("isActive").default(true),
  isFeatured: boolean("isFeatured").default(false),
  tags: jsonb("tags").$type<string[]>().default([]),
  publishedAt: timestamp("publishedAt", { mode: "date", withTimezone: true }),
  createdAt: timestamp("createdAt", { mode: "date", withTimezone: true })
    .defaultNow()
    .notNull(),
  updatedAt: timestamp("updatedAt", {
    mode: "date",
    withTimezone: true,
  }).$onUpdate(() => new Date()),
});

export const testimonials = pgTable("testimonials", {
  id: varchar("id", { length: 128 })
    .$defaultFn(() => createId())
    .primaryKey(),
  title: text("title").notNull(),
  message: text("message").notNull(),
  stars: numeric("stars").default("5"),
  userImage: text("userImage"),
  userName: text("userName"),
  userDesignation: text("userDesignation"),
  userLocation: text("userLocation"),
  createdAt: timestamp("createdAt", { mode: "date", withTimezone: true })
    .defaultNow()
    .notNull(),
  updatedAt: timestamp("updatedAt", {
    mode: "date",
    withTimezone: true,
  }).$onUpdate(() => new Date()),
});

export const systemConfigurationEnum = pgEnum("systemConfigurationEnum", [
  "RADIUS_TO_SHOW_KABADIWALA_NEW_ORDER",
  "MINIMUM_WALLET_BALANCE_TO_ACCEPT_ORDER_BY_KABADIWALA",
  "PICKUP_CHARGE",
  "HANDLING_CHARGE",
  "SECURITY_FEE_PERCENTAGE",
  "MINIMUM_ORDER_VALUE",
  "GST_PERCENTAGE",
  "CONTACT_EMAIL",
  "CONTACT_PHONE",
  "SUPPORT_EMAIL",
  "SUPPORT_PHONE",
  "TERMS_AND_CONDITIONS",
  "PRIVACY_POLICY",
  "ABOUT_US",
  "CANCELLATION_POLICY",
  "REFUND_POLICY",
  "FIXED_KABADIWALA_EARNING",
]);

export const systemConfiguration = pgTable("systemConfiguration", {
  id: varchar("id", { length: 128 })
    .$defaultFn(() => createId())
    .primaryKey(),
  key: systemConfigurationEnum("key").unique(),
  value: text("value"),
  createdAt: timestamp("createdAt", { mode: "date", withTimezone: true })
    .defaultNow()
    .notNull(),
  updatedAt: timestamp("updatedAt", {
    mode: "date",
    withTimezone: true,
  }).$onUpdate(() => new Date()),
});

// --- Customer-Admin Support Conversation ---
export const customerSupportConversation = pgTable(
  "customerSupportConversation",
  {
    id: varchar("id", { length: 128 })
      .$defaultFn(() => createId())
      .primaryKey(),
    customerId: varchar("customerId", { length: 128 }).notNull(),
    orderId: varchar("orderId", { length: 128 }), // nullable for help/support
    isOpen: boolean("isOpen").default(true).notNull(),
    closedBy: varchar("closedBy", { length: 128 }), // adminId who closed, nullable
    closedAt: timestamp("closedAt", { mode: "date", withTimezone: true }),
    rating: numeric("rating"), // nullable, 1-5 or null if skipped
    reviewText: text("reviewText"), // optional review text
    isRated: boolean("isRated").default(false).notNull(),
    createdAt: timestamp("createdAt", { mode: "date", withTimezone: true })
      .defaultNow()
      .notNull(),
    updatedAt: timestamp("updatedAt", {
      mode: "date",
      withTimezone: true,
    }).$onUpdate(() => new Date()),
  },
);

export const customerSupportMessage = pgTable("customerSupportMessage", {
  id: varchar("id", { length: 128 })
    .$defaultFn(() => createId())
    .primaryKey(),
  conversationId: varchar("conversationId", { length: 128 })
    .notNull()
    .references(() => customerSupportConversation.id, { onDelete: "cascade" }),
  senderId: varchar("senderId", { length: 128 }).notNull(), // customer or admin id
  senderType: varchar("senderType", { length: 16 }).notNull(), // "CUSTOMER" or "ADMIN"
  content: text("content").notNull(),
  createdAt: timestamp("createdAt", { mode: "date", withTimezone: true })
    .defaultNow()
    .notNull(),
  isRead: boolean("isRead").default(false),
});

export const customerSupportConversationRelations = relations(
  customerSupportConversation,
  ({ one, many }) => ({
    customer: one(seller, {
      fields: [customerSupportConversation.customerId],
      references: [seller.id],
    }),
    order: one(order, {
      fields: [customerSupportConversation.orderId],
      references: [order.id],
    }),
    messages: many(customerSupportMessage),
  }),
);

export const customerSupportMessageRelations = relations(
  customerSupportMessage,
  ({ one }) => ({
    conversation: one(customerSupportConversation, {
      fields: [customerSupportMessage.conversationId],
      references: [customerSupportConversation.id],
    }),
  }),
);

// --- Kabadiwala-Admin Support Conversation ---
export const kabadiwalaSupportConversation = pgTable(
  "kabadiwalaSupportConversation",
  {
    id: varchar("id", { length: 128 })
      .$defaultFn(() => createId())
      .primaryKey(),
    kabadiwalaId: varchar("kabadiwalaId", { length: 128 }).notNull(),
    orderId: varchar("orderId", { length: 128 }), // nullable for help/support
    isOpen: boolean("isOpen").default(true).notNull(),
    closedBy: varchar("closedBy", { length: 128 }), // adminId who closed, nullable
    closedAt: timestamp("closedAt", { mode: "date", withTimezone: true }),
    rating: numeric("rating"), // nullable, 1-5 or null if skipped
    reviewText: text("reviewText"), // optional review text
    isRated: boolean("isRated").default(false).notNull(),
    createdAt: timestamp("createdAt", { mode: "date", withTimezone: true })
      .defaultNow()
      .notNull(),
    updatedAt: timestamp("updatedAt", {
      mode: "date",
      withTimezone: true,
    }).$onUpdate(() => new Date()),
  },
);

export const kabadiwalaSupportMessage = pgTable("kabadiwalaSupportMessage", {
  id: varchar("id", { length: 128 })
    .$defaultFn(() => createId())
    .primaryKey(),
  conversationId: varchar("conversationId", { length: 128 })
    .notNull()
    .references(() => kabadiwalaSupportConversation.id, {
      onDelete: "cascade",
    }),
  senderId: varchar("senderId", { length: 128 }).notNull(), // kabadiwala or admin id
  senderType: varchar("senderType", { length: 16 }).notNull(), // "KABADIWALA" or "ADMIN"
  content: text("content").notNull(),
  createdAt: timestamp("createdAt", { mode: "date", withTimezone: true })
    .defaultNow()
    .notNull(),
  isRead: boolean("isRead").default(false),
});

export const kabadiwalaSupportConversationRelations = relations(
  kabadiwalaSupportConversation,
  ({ one, many }) => ({
    kabadiwala: one(kabadiwala, {
      fields: [kabadiwalaSupportConversation.kabadiwalaId],
      references: [kabadiwala.id],
    }),
    order: one(order, {
      fields: [kabadiwalaSupportConversation.orderId],
      references: [order.id],
    }),
    messages: many(kabadiwalaSupportMessage),
  }),
);

export const kabadiwalaSupportMessageRelations = relations(
  kabadiwalaSupportMessage,
  ({ one }) => ({
    conversation: one(kabadiwalaSupportConversation, {
      fields: [kabadiwalaSupportMessage.conversationId],
      references: [kabadiwalaSupportConversation.id],
    }),
  }),
);

export const notification = pgTable("notification", {
  id: varchar("id", { length: 128 })
    .$defaultFn(() => createId())
    .primaryKey(),
  sellerId: varchar("sellerId", { length: 128 }).references(() => seller.id, {
    onDelete: "cascade",
  }),
  kabadiwalaId: varchar("kabadiwalaId", { length: 128 }).references(
    () => kabadiwala.id,
    { onDelete: "cascade" },
  ),
  title: text("title").notNull(),
  content: text("content").notNull(),
  metadata: json("metadata").$type<Record<string, unknown>>().default({}),
  createdAt: timestamp("createdAt", { mode: "date", withTimezone: true })
    .defaultNow()
    .notNull(),
  updatedAt: timestamp("updatedAt", {
    mode: "date",
    withTimezone: true,
  }).$onUpdate(() => new Date()),
});

export const issueStatusEnum = pgEnum("issueStatusEnum", [
  "OPEN", // Created by customer, pending admin action
  "ACKNOWLEDGED", // Admin has acknowledged, customer awarded, strike given
  "CLOSED", // Admin closed (valid or invalid, but no strike/award)
  "REJECTED", // Admin rejected as false/spite
]);

export const issue = pgTable("issue", {
  id: varchar("id", { length: 128 })
    .$defaultFn(() => createId())
    .primaryKey(),
  orderId: varchar("orderId", { length: 128 })
    .notNull()
    .references(() => order.id, { onDelete: "cascade" }),
  kabadiwalaId: varchar("kabadiwalaId", { length: 128 })
    .notNull()
    .references(() => kabadiwala.id, { onDelete: "cascade" }),
  customerId: varchar("customerId", { length: 128 })
    .notNull()
    .references(() => seller.id, { onDelete: "cascade" }),
  description: text("description").notNull(),
  imageUrls: jsonb("imageUrls").$type<string[]>().default([]), // up to 4 URLs
  status: issueStatusEnum("status").default("OPEN").notNull(),
  adminId: varchar("adminId", { length: 128 }), // nullable, set if admin acts
  acknowledgedAt: timestamp("acknowledgedAt", {
    mode: "date",
    withTimezone: true,
  }),
  closedAt: timestamp("closedAt", { mode: "date", withTimezone: true }),
  createdAt: timestamp("createdAt", { mode: "date", withTimezone: true })
    .defaultNow()
    .notNull(),
  updatedAt: timestamp("updatedAt", {
    mode: "date",
    withTimezone: true,
  }).$onUpdate(() => new Date()),
});

export const issueRelations = relations(issue, ({ one }) => ({
  order: one(order, {
    fields: [issue.orderId],
    references: [order.id],
  }),
  kabadiwala: one(kabadiwala, {
    fields: [issue.kabadiwalaId],
    references: [kabadiwala.id],
  }),
  customer: one(seller, {
    fields: [issue.customerId],
    references: [seller.id],
  }),
}));

export const regions = pgTable("regions", {
  id: varchar("id", { length: 128 })
    .$defaultFn(() => createId())
    .primaryKey(),
  name: varchar("name", { length: 128 }).notNull(),
  markers: jsonb("markers")
    .$type<{ latitude: number; longitude: number }[]>()
    .notNull()
    .default([]),
  isActive: boolean("isActive").default(true).notNull(),
  createdAt: timestamp("createdAt", { mode: "date", withTimezone: true })
    .defaultNow()
    .notNull(),
  updatedAt: timestamp("updatedAt", {
    mode: "date",
    withTimezone: true,
  }).$onUpdate(() => new Date()),
});

export const regionsRelations = relations(regions, ({ many }) => ({
  addresses: many(address),
}));

export const pricingHistory = pgTable("pricingHistory", {
  id: varchar("id", { length: 128 })
    .$defaultFn(() => createId())
    .primaryKey(),
  regionId: varchar("regionId", { length: 128 })
    .notNull()
    .references(() => regions.id, {
      onDelete: "cascade",
    }),
  effectiveFromDate: timestamp("effectiveFrom", {
    mode: "date",
    withTimezone: true,
  }).notNull(),
  expiresAt: timestamp("expiresAt", {
    mode: "date",
    withTimezone: true,
  }).notNull(),
  createdAt: timestamp("createdAt", { mode: "date", withTimezone: true })
    .defaultNow()
    .notNull(),
  updatedAt: timestamp("updatedAt", {
    mode: "date",
    withTimezone: true,
  }).$onUpdate(() => new Date()),
});

export const pricingHistoryRelations = relations(
  pricingHistory,
  ({ one, many }) => ({
    region: one(regions, {
      fields: [pricingHistory.regionId],
      references: [regions.id],
    }),
    regionCategoryPricing: many(regionCategoryPricing),
  }),
);

export const regionCategoryPricing = pgTable("regionCategoryPricing", {
  id: varchar("id", { length: 128 })
    .$defaultFn(() => createId())
    .primaryKey(),
  pricingHistoryId: varchar("pricingHistoryId", { length: 128 })
    .references(() => pricingHistory.id, { onDelete: "cascade" })
    .notNull(),
  categoryId: varchar("categoryId", { length: 128 })
    .references(() => category.id, { onDelete: "cascade" })
    .notNull(),
  rate: numeric("rate").notNull(),
  compensationKabadiwalaRate: numeric("compensationKabadiwalaRate")
    .notNull()
    .default("0"), // ie: price
  compensationRecyclerRate: numeric("compensationRecyclerRate")
    .notNull()
    .default("0"), // ie: price
  createdAt: timestamp("createdAt", { mode: "date", withTimezone: true })
    .defaultNow()
    .notNull(),
  updatedAt: timestamp("updatedAt", {
    mode: "date",
    withTimezone: true,
  }).$onUpdate(() => new Date()),
});

export const regionCategoryPricingRelations = relations(
  regionCategoryPricing,
  ({ one }) => ({
    category: one(category, {
      fields: [regionCategoryPricing.categoryId],
      references: [category.id],
    }),
    pricingHistory: one(pricingHistory, {
      fields: [regionCategoryPricing.pricingHistoryId],
      references: [pricingHistory.id],
    }),
  }),
);
