import { inArray } from "drizzle-orm";

import { db } from "../client";
import { answer, question } from "../schema";
import {
  kabadiwalaAnswer,
  sellerAnswers as sellerAnswerArray,
} from "./data/answer";
import {
  kabadiwalaQuestion,
  sellerQuestion as sellerQuestionArray,
} from "./data/question";

/**
 * Deletes all SELLER questions and their answers, then inserts new SELLER questions and answers from data files.
 */
export async function seedSellersFAQs() {
  try {
    await db.transaction(async (tx) => {
      // Delete all SELLER answers (must delete answers first due to FK)
      const sellerQuestionIds = sellerQuestionArray.map((q) => q.id);
      if (sellerQuestionIds.length > 0) {
        await tx
          .delete(answer)
          .where(inArray(answer.questionId, sellerQuestionIds));
        await tx
          .delete(question)
          .where(inArray(question.id, sellerQuestionIds));
      }
      // Insert questions
      if (sellerQuestionArray.length > 0) {
        await tx.insert(question).values(sellerQuestionArray);
      }
      // Insert answers (only those whose questionId is in SELLER questions)
      const sellerAnswers = sellerAnswerArray.filter((a) =>
        sellerQuestionIds.includes(a.questionId),
      );
      if (sellerAnswers.length > 0) {
        await tx.insert(answer).values(sellerAnswers);
      }
    });
    console.log("Seller FAQs seeded successfully.");
  } catch (error) {
    console.error("Error seeding seller FAQs:", error);
    throw error;
  }
}

/**
 * Deletes all SELLER questions and their answers, then inserts new SELLER questions and answers from data files.
 */
export async function seedKabadiwalaFAQs() {
  try {
    await db.transaction(async (tx) => {
      // Delete all SELLER answers (must delete answers first due to FK)
      const kabadiwalaQuestionId = kabadiwalaQuestion.map((q) => q.id);
      if (kabadiwalaQuestionId.length > 0) {
        await tx
          .delete(answer)
          .where(inArray(answer.questionId, kabadiwalaQuestionId));
        await tx
          .delete(question)
          .where(inArray(question.id, kabadiwalaQuestionId));
      }
      // Insert questions
      if (sellerQuestionArray.length > 0) {
        await tx.insert(question).values(kabadiwalaQuestion);
      }
      // Insert answers (only those whose questionId is in SELLER questions)
      const sellerAnswers = kabadiwalaAnswer.filter((a) =>
        kabadiwalaQuestionId.includes(a.questionId),
      );
      if (sellerAnswers.length > 0) {
        await tx.insert(answer).values(kabadiwalaAnswer);
      }
    });
    console.log("Kabadiwala FAQs seeded successfully.");
  } catch (error) {
    console.error("Error seeding kabadiwala FAQs:", error);
    throw error;
  }
}

async function main() {
  // CLI runner
  await seedSellersFAQs()
    .then(() => {
      console.log("[seller faqs seeding completed]");
    })
    .catch((e) => {
      console.log("[seller faqs seeding error]", e);
    });

  // CLI runner
  await seedKabadiwalaFAQs()
    .then(() => {
      console.log("[kabadiwala faqs seeding completed]");
    })
    .catch((e) => {
      console.log("[kabadiwala faqs seeding error]", e);
    });
  process.exit(0);
}
main();
