import { db } from "../client";
import { category, categoryRateTypeEnum } from "../schema";

async function main() {
  try {
    // Check if categories already exist
    const existingCategory = await db.query.category.findFirst();

    if (existingCategory) {
      console.log("Categories already exist, skipping seed.");
      return;
    }

    // Define parent categories
    const parentCategories = [
      {
        name: "Household",
        rate: "0",
        rateType: categoryRateTypeEnum.enumValues[0], // "PER_KG"
        image:
          "https://images.pexels.com/photos/450021/pexels-photo-450021.jpeg", // Household items
        description: "Items commonly found in households",
      },
      {
        name: "Electronics",
        rate: "0",
        rateType: categoryRateTypeEnum.enumValues[0], // "PER_KG"
        image:
          "https://images.pexels.com/photos/267614/pexels-photo-267614.jpeg", // Electronics
        description: "Electronic devices and components",
      },
      {
        name: "Paper",
        rate: "0",
        rateType: categoryRateTypeEnum.enumValues[0], // "PER_KG"
        image:
          "https://images.pexels.com/photos/267614/pexels-photo-267614.jpeg", // Paper
        description: "Paper materials and printed items",
      },
      {
        name: "Metals",
        rate: "0",
        rateType: categoryRateTypeEnum.enumValues[0], // "PER_KG"
        image:
          "https://images.pexels.com/photos/267614/pexels-photo-267614.jpeg", // Metals
        description: "Various metal scraps",
      },
    ];

    // Insert parent categories and collect their IDs
    const parentIds: Record<string, string> = {};

    for (const parentCat of parentCategories) {
      const [insertedParent] = await db
        .insert(category)
        .values({
          name: parentCat.name,
          rate: parentCat.rate,
          rateType: parentCat.rateType,
          image: parentCat.image,
          description: parentCat.description,
          isActive: true,
          compensationKabadiwalaRate: "0",
          compensationRecyclerRate: "0",
          createdAt: new Date(),
          updatedAt: new Date(),
        })
        .returning();

      if (insertedParent) {
        parentIds[parentCat.name] = insertedParent.id;
        console.log(`Created parent category: ${parentCat.name}`);
      }
    }

    // Define child categories with their respective parents
    const childCategories = [
      // Household children
      {
        name: "Furniture",
        rate: "15",
        rateType: categoryRateTypeEnum.enumValues[0], // "PER_KG"
        image:
          "https://images.pexels.com/photos/450021/pexels-photo-450021.jpeg", // Furniture
        description: "Used furniture items",
        parentId: parentIds.Household,
      },
      {
        name: "Kitchen Items",
        rate: "10",
        rateType: categoryRateTypeEnum.enumValues[0], // "PER_KG"
        image:
          "https://images.pexels.com/photos/450021/pexels-photo-450021.jpeg", // Kitchen Items
        description: "Used kitchen utensils and appliances",
        parentId: parentIds.Household,
      },
      // Electronics children
      {
        name: "Computers",
        rate: "30",
        rateType: categoryRateTypeEnum.enumValues[0], // "PER_KG"
        image:
          "https://images.pexels.com/photos/267614/pexels-photo-267614.jpeg", // Computers
        description: "Old computers and parts",
        parentId: parentIds.Electronics,
      },
      {
        name: "Fans",
        rate: "20",
        rateType: categoryRateTypeEnum.enumValues[1], // "PER_ITEM"
        image:
          "https://images.pexels.com/photos/267614/pexels-photo-267614.jpeg", // Fans
        description: "Used ceiling and table fans",
        parentId: parentIds.Electronics,
      },
      {
        name: "Small Appliances",
        rate: "25",
        rateType: categoryRateTypeEnum.enumValues[0], // "PER_KG"
        image:
          "https://images.pexels.com/photos/267614/pexels-photo-267614.jpeg", // Small Appliances
        description: "Used small electronic appliances",
        parentId: parentIds.Electronics,
      },
      // Paper children
      {
        name: "Newspapers",
        rate: "8",
        rateType: categoryRateTypeEnum.enumValues[0], // "PER_KG"
        image:
          "https://images.pexels.com/photos/267614/pexels-photo-267614.jpeg", // Newspapers
        description: "Old newspapers",
        parentId: parentIds.Paper,
      },
      {
        name: "Magazines",
        rate: "7",
        rateType: categoryRateTypeEnum.enumValues[0], // "PER_KG"
        image:
          "https://images.pexels.com/photos/267614/pexels-photo-267614.jpeg", // Magazines
        description: "Old magazines and catalogs",
        parentId: parentIds.Paper,
      },
      {
        name: "Books",
        rate: "10",
        rateType: categoryRateTypeEnum.enumValues[0], // "PER_KG"
        image:
          "https://images.pexels.com/photos/267614/pexels-photo-267614.jpeg", // Books
        description: "Used books",
        parentId: parentIds.Paper,
      },
      // Metals children
      {
        name: "Iron",
        rate: "18",
        rateType: categoryRateTypeEnum.enumValues[0], // "PER_KG"
        image:
          "https://images.pexels.com/photos/267614/pexels-photo-267614.jpeg", // Iron
        description: "Iron scrap",
        parentId: parentIds.Metals,
      },
      {
        name: "Aluminum",
        rate: "40",
        rateType: categoryRateTypeEnum.enumValues[0], // "PER_KG"
        image:
          "https://images.pexels.com/photos/267614/pexels-photo-267614.jpeg", // Aluminum
        description: "Aluminum scrap",
        parentId: parentIds.Metals,
      },
      {
        name: "Copper",
        rate: "100",
        rateType: categoryRateTypeEnum.enumValues[0], // "PER_KG"
        image:
          "https://images.pexels.com/photos/267614/pexels-photo-267614.jpeg", // Copper
        description: "Copper scrap",
        parentId: parentIds.Metals,
      },
    ];

    // Insert child categories
    for (const childCat of childCategories) {
      const [insertedChild] = await db
        .insert(category)
        .values({
          name: childCat.name,
          rate: childCat.rate,
          rateType: childCat.rateType,
          image: childCat.image,
          description: childCat.description,
          parentId: childCat.parentId,
          isActive: true,
          compensationKabadiwalaRate: childCat.rate,
          compensationRecyclerRate: childCat.rate,
          createdAt: new Date(),
          updatedAt: new Date(),
        })
        .returning();

      if (insertedChild) {
        console.log(`Created child category: ${childCat.name}`);
      }
    }

    console.log("Category seeding completed successfully");
  } catch (error) {
    console.error("Error seeding categories:", error);
    throw error;
  }
}

main()
  .then(() => {
    console.log("Categories seeding completed successfully.");
    process.exit(0);
  })
  .catch((e) => {
    console.error("Error during categories seeding:", e);
    process.exit(1);
  });
