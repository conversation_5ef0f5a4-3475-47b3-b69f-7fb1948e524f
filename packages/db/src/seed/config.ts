import { db } from "../client";
import { systemConfiguration, systemConfigurationEnum } from "../schema";

async function main() {
  try {
    // Check if any config already exists
    const existingConfig = await db.query.systemConfiguration.findFirst();
    if (existingConfig) {
      console.log("System configuration already exists, skipping seed.");
      return;
    }

    // Default values for each config key
    const configDefaults: Record<
      (typeof systemConfigurationEnum.enumValues)[number],
      string
    > = {
      RADIUS_TO_SHOW_KABADIWALA_NEW_ORDER: "5", // in km
      MINIMUM_WALLET_BALANCE_TO_ACCEPT_ORDER_BY_KABADIWALA: "1000",
      SECURITY_FEE_PERCENTAGE: "20",
      GST_PERCENTAGE: "18",
      CONTACT_EMAIL: "<EMAIL>",
      CONTACT_PHONE: "+911234567890",
      SUPPORT_EMAIL: "<EMAIL>",
      SUPPORT_PHONE: "+919876543210",
      TERMS_AND_CONDITIONS: "<p>Default terms and conditions...</p>",
      PRIVACY_POLICY: "<p>Default privacy policy...</p>",
    };

    for (const key of systemConfigurationEnum.enumValues) {
      await db.insert(systemConfiguration).values({
        key,
        value: configDefaults[key],
        createdAt: new Date(),
        updatedAt: new Date(),
      });
      console.log(`Seeded config: ${key}`);
    }

    console.log("System configuration seeding completed successfully.");
  } catch (error) {
    console.error("Error seeding system configuration:", error);
    throw error;
  }
}

main()
  .then(() => {
    console.log("Config seeding completed successfully.");
    process.exit(0);
  })
  .catch((e) => {
    console.error("Error during config seeding:", e);
    process.exit(1);
  });
