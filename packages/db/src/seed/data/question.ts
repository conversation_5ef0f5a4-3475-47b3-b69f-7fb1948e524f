import type { question } from "../../schema";

export const sellerQuestion: (typeof question.$inferSelect)[] = [
  {
    id: "gm2j9kzwd2hhzpnqtlmeg68p",
    content:
      "How do I give a review or rate the Scraplo Process and the Pick-up Partner? ",
    questionFor: "SELLER",
    order: "5",
    parentId: null,
    createdAt: new Date("2025-07-16 15:58:16.853273+05:30"),
    updatedAt: new Date("2025-07-16 16:16:22.686+05:30"),
  },
  {
    id: "itabz58pa5ur4i8a9vpv9na0",
    content: "How do I sell my scrap by connecting with the pick-up partner?",
    questionFor: "SELLER",
    order: "3",
    parentId: null,
    createdAt: new Date("2025-07-16 15:57:47.709958+05:30"),
    updatedAt: new Date("2025-07-16 16:15:30.458+05:30"),
  },
  {
    id: "j8cfhyxkiflbsqfd93irn9tl",
    content: "How do I get paid for the scrap sold?",
    questionFor: "SELLER",
    order: "4",
    parentId: null,
    createdAt: new Date("2025-07-16 15:58:01.071661+05:30"),
    updatedAt: new Date("2025-07-16 16:15:58.326+05:30"),
  },
  {
    id: "lrxm3x9xa6mj7vqxmcriiyr9",
    content: "Why Scraplo? ",
    questionFor: "SELLER",
    order: "0",
    parentId: null,
    createdAt: new Date("2025-07-16 15:57:01.302699+05:30"),
    updatedAt: new Date("2025-07-16 16:03:47.186+05:30"),
  },
  {
    id: "p031y4i2u2fmqrgd7z49d8ls",
    content: "How do I register as a Seller – Consumer / Businesses? ",
    questionFor: "SELLER",
    order: "2",
    parentId: null,
    createdAt: new Date("2025-07-16 15:57:32.007141+05:30"),
    updatedAt: new Date("2025-07-16 16:14:49.593+05:30"),
  },
  {
    id: "p9v6l8abaz5ts1cy8djslphy",
    content: "How does Scraplo work?",
    questionFor: "SELLER",
    order: "1",
    parentId: null,
    createdAt: new Date("2025-07-16 15:57:18.916455+05:30"),
    updatedAt: new Date("2025-07-16 16:04:08.975+05:30"),
  },
  {
    id: "r738e4nn7477x8hhxj4c97qh",
    content:
      "How do I highlight, if the Pickup Partner tries to contact me directly?",
    questionFor: "SELLER",
    order: "6",
    parentId: null,
    createdAt: new Date("2025-07-16 15:58:42.907259+05:30"),
    updatedAt: new Date("2025-07-16 16:16:30.843+05:30"),
  },
];

export const kabadiwalaQuestion: (typeof question.$inferSelect)[] = [
  {
    id: "iwglognea4f7v0jrkj9xcnu2",
    content: "How do I get more orders for scrap pickup on Scraplo?",
    questionFor: "KABADIWALA",
    order: "7",
    parentId: null,
    createdAt: new Date("2025-07-16 16:52:03.293848+05:30"),
    updatedAt: new Date("2025-07-16 16:52:03.286+05:30"),
  },
  {
    id: "pcnia9ojrehehqmqxcs5tyxs",
    content: "How does Scraplo work?",
    questionFor: "KABADIWALA",
    order: "1",
    parentId: null,
    createdAt: new Date("2025-07-16 16:45:23.683015+05:30"),
    updatedAt: new Date("2025-07-16 16:45:23.678+05:30"),
  },
  {
    id: "fznoxm1jtg38mlwx8tt1nfgc",
    content:
      "How do I get the orders for scrap pick up from Seller - consumers / businesses?",
    questionFor: "KABADIWALA",
    order: "3",
    parentId: null,
    createdAt: new Date("2025-07-16 16:45:59.349476+05:30"),
    updatedAt: new Date("2025-07-16 16:45:59.345+05:30"),
  },
  {
    id: "w402e7tcc8sgv1x7598mmzet",
    content: "How do I enroll / register as a Pickup Partner?",
    questionFor: "KABADIWALA",
    order: "2",
    parentId: null,
    createdAt: new Date("2025-07-16 16:45:37.069629+05:30"),
    updatedAt: new Date("2025-07-16 16:46:20.791+05:30"),
  },
  {
    id: "ah99h0s61uhighnidkvpoih7",
    content:
      "Where to make the payment for the scrap to the Seller - consumers / businesses?",
    questionFor: "KABADIWALA",
    order: "4",
    parentId: null,
    createdAt: new Date("2025-07-16 16:47:00.957905+05:30"),
    updatedAt: new Date("2025-07-16 16:47:00.953+05:30"),
  },
  {
    id: "ry7f0n5nfvzubktufrbp5ibi",
    content:
      "How do I get paid for the scrap picked up and deposited at the scrap hub?",
    questionFor: "KABADIWALA",
    order: "6",
    parentId: null,
    createdAt: new Date("2025-07-16 16:47:45.252265+05:30"),
    updatedAt: new Date("2025-07-16 16:47:45.248+05:30"),
  },
  {
    id: "zjmdec8fo1i7mfsqt91gaeem",
    content: "Why Scraplo?",
    questionFor: "KABADIWALA",
    order: "0",
    parentId: null,
    createdAt: new Date("2025-07-16 16:44:44.187719+05:30"),
    updatedAt: new Date("2025-07-16 16:48:39.176+05:30"),
  },
  {
    id: "b4rrvc9wog0w9x582hibpdh1",
    content:
      "Where do I deposit the scrap picked up from the Seller - consumers / businesses?",
    questionFor: "KABADIWALA",
    order: "5",
    parentId: null,
    createdAt: new Date("2025-07-16 16:47:26.433207+05:30"),
    updatedAt: new Date("2025-07-16 16:50:57.4+05:30"),
  },
  {
    id: "ub0zwg3dlue6ck8pal1v9udu",
    content:
      "Can I directly approach the Seller – Consumer / Businesses after I get the order through Scraplo online platform?",
    questionFor: "KABADIWALA",
    order: "8",
    parentId: null,
    createdAt: new Date("2025-07-16 16:48:24.015874+05:30"),
    updatedAt: new Date("2025-07-16 16:51:16.582+05:30"),
  },
];
