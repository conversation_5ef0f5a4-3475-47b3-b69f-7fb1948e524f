import bcrypt from "bcryptjs";

import { db } from "../client";
import { eq } from "../index";
import { admin, adminAccount } from "../schema";

const password = "scraplo@123#_admin";

async function main() {
  try {
    const existingAdmin = await db.query.admin.findFirst({
      where: eq(admin.email, "<EMAIL>"),
    });

    if (existingAdmin) {
      console.log("Default admin already exists, skipping seed.");
      return;
    }

    const [adminRes] = await db
      .insert(admin)
      .values({
        name: "System Generated Admin",
        email: "<EMAIL>",
        emailVerified: true,
        image: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning();

    if (!adminRes) {
      console.error("Failed to create default admin.");
      return;
    }

    const hashedPassword = await bcrypt.hash(password, 10);
    await db.insert(adminAccount).values({
      adminId: adminRes.id,
      accountId: adminRes.id,
      providerId: "credential",
      password: hashedPassword,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    console.log(`Created default admin with id: ${adminRes.id}`);
  } catch (error) {
    console.error("Error seeding default admin:", error);
    throw error;
  }
}

main()
  .then(() => {
    console.log("Seeding completed successfully.");
    process.exit(0);
  })
  .catch((e) => {
    console.error("Error during seeding:", e);
    process.exit(1);
  });
