export { alias } from "drizzle-orm/pg-core";
export * from "drizzle-orm/sql";
export interface GoogleReverseGeocodeAddressComponent {
  long_name: string;
  short_name: string;
  types: string[];
}

export interface GoogleReverseGeocodeLatLng {
  lat: number;
  lng: number;
}

export interface GoogleReverseGeocodeBounds {
  northeast: GoogleReverseGeocodeLatLng;
  southwest: GoogleReverseGeocodeLatLng;
}

export interface GoogleReverseGeocodeGeometry {
  bounds?: GoogleReverseGeocodeBounds;
  location: GoogleReverseGeocodeLatLng;
  location_type: string;
  viewport: GoogleReverseGeocodeBounds;
}

export interface GoogleReverseGeocodeNavigationPoint {
  location: {
    latitude: number;
    longitude: number;
  };
  restricted_travel_modes: string[];
}

export interface GoogleReverseGeocodeResult {
  address_components: GoogleReverseGeocodeAddressComponent[];
  formatted_address: string;
  geometry: GoogleReverseGeocodeGeometry;
  navigation_points?: GoogleReverseGeocodeNavigationPoint[];
  place_id: string;
  types: string[];
}
