import { createId } from "@paralleldrive/cuid2";
import { relations } from "drizzle-orm";
import {
  boolean,
  json,
  pgTable,
  text,
  timestamp,
  varchar,
} from "drizzle-orm/pg-core";

import { kabadiwala, order } from "../schema";

export const scraphub = pgTable("scraphub", {
  id: varchar("id", { length: 128 })
    .$defaultFn(() => createId())
    .primaryKey(),
  name: text("name").notNull(),
  phoneNumber: text("phoneNumber"),
});

export const scraphubRelations = relations(scraphub, ({ one, many }) => ({
  address: one(scraphubAddress, {
    fields: [scraphub.id],
    references: [scraphubAddress.scraphubId],
  }),
  employees: many(scraphubEmployee),
  kabadiwalas: many(kabadiwala),
}));

export const scraphubAddress = pgTable("scraphubAddress", {
  id: varchar("id", { length: 128 })
    .$defaultFn(() => createId())
    .primaryKey(),
  display: text("display").notNull(),
  street: text("street"),
  city: text("city"),
  state: text("state"),
  country: text("country"),
  postalCode: text("postalCode"),
  coordinates: json("coordinates").$type<{
    latitude: number;
    longitude: number;
  }>(),
  localAddress: text("localAddress").notNull(),
  landmark: text("landmark").notNull(),
  scraphubId: varchar("scraphubId", { length: 128 })
    .references(() => scraphub.id, { onDelete: "cascade" })
    .notNull()
    .unique(),
  createdAt: timestamp("createdAt", { mode: "date", withTimezone: true })
    .defaultNow()
    .notNull(),
  updatedAt: timestamp("updatedAt", {
    mode: "date",
    withTimezone: true,
  }).$onUpdate(() => new Date()),
});

export const scraphubAddressRelations = relations(
  scraphubAddress,
  ({ one }) => ({
    scraphub: one(scraphub, {
      fields: [scraphubAddress.scraphubId],
      references: [scraphub.id],
    }),
  }),
);

export const scraphubEmployee = pgTable("scraphubEmployee", {
  id: varchar("id", { length: 128 })
    .$defaultFn(() => createId())
    .primaryKey(),
  scraphubId: varchar("scraphubId", { length: 128 })
    .references(() => scraphub.id, { onDelete: "cascade" })
    .notNull(),
  firstName: text("firstName").notNull(),
  lastName: text("lastName").notNull(),
  email: text("email").notNull(),
  emailVerified: boolean("emailVerified").default(false).notNull(),
  createdAt: timestamp("createdAt", { mode: "date", withTimezone: true })
    .defaultNow()
    .notNull(),
  updatedAt: timestamp("updatedAt", {
    mode: "date",
    withTimezone: true,
  }).$onUpdate(() => new Date()),
});

export const scraphubEmployeeRelations = relations(
  scraphubEmployee,
  ({ one, many }) => ({
    scraphub: one(scraphub, {
      fields: [scraphubEmployee.scraphubId],
      references: [scraphub.id],
    }),
    orders: many(order),
  }),
);

export const scraphubEmployeeSession = pgTable("scraphubEmployeeSession", {
  id: varchar("id", { length: 128 })
    .$defaultFn(() => createId())
    .primaryKey(),
  expiresAt: timestamp("expiresAt").notNull(),
  token: text("token").notNull().unique(),
  createdAt: timestamp("createdAt").notNull(),
  updatedAt: timestamp("updatedAt").notNull(),
  ipAddress: text("ipAddress"),
  userAgent: text("userAgent"),
  scraphubEmployeeId: text("scraphubEmployeeId")
    .notNull()
    .references(() => scraphubEmployee.id, { onDelete: "cascade" }),
});

export const scraphubEmployeeAccount = pgTable("scraphubEmployeeAccount", {
  id: varchar("id", { length: 128 })
    .$defaultFn(() => createId())
    .primaryKey(),
  accountId: text("accountId").notNull(),
  providerId: text("providerId").notNull(),
  scraphubEmployeeId: text("scraphubEmployeeId")
    .notNull()
    .references(() => scraphubEmployee.id, { onDelete: "cascade" }),
  accessToken: text("accessToken"),
  refreshToken: text("refreshToken"),
  idToken: text("idToken"),
  accessTokenExpiresAt: timestamp("accessTokenExpiresAt"),
  refreshTokenExpiresAt: timestamp("refreshTokenExpiresAt"),
  scope: text("scope"),
  password: text("password"),
  createdAt: timestamp("createdAt").notNull(),
  updatedAt: timestamp("updatedAt").notNull(),
});

export const scraphubEmployeeVerification = pgTable(
  "scraphubEmployeeVerification",
  {
    id: varchar("id", { length: 128 })
      .$defaultFn(() => createId())
      .primaryKey(),
    identifier: text("identifier").notNull(),
    value: text("value").notNull(),
    expiresAt: timestamp("expiresAt").notNull(),
    createdAt: timestamp("createdAt"),
    updatedAt: timestamp("updatedAt"),
  },
);
