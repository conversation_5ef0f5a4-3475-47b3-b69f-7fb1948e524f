import { z } from "zod";

import { vehicleTypeEnum } from "@acme/db/schema";

import {
  AddressSchema,
  dobSchema,
  emailSchema,
  fullNameSchema,
  moneySchema,
  otpSchema,
  phoneNumberSchema,
} from "./index";

const vehicleNumberSchema = z
  .string()
  .regex(/^[A-Z]{2}[0-9]{1,2}[A-Z]{1,3}[0-9]{4}$/, {
    message: "Please enter a valid Indian vehicle number (e.g., MH12AB1234)",
  })
  .transform((val) => val.toUpperCase())
  .optional()
  .or(z.literal(""));

const vehicleTypeSchema = z.enum(vehicleTypeEnum.enumValues, {
  message: "Please select a valid option",
});

export const KabadiwalaLoginSchema = z.object({
  phoneNumber: phoneNumberSchema,
});

export const KabadiwalaSignupSchema = z.object({
  phoneNumber: phoneNumberSchema,
  whatsappConsent: z.boolean().default(false),
});

export const KabadiwalaOtpSchema = z.object({
  phoneNumber: phoneNumberSchema,
  otp: otpSchema,
});

const KabadiwalaOnboardingBaseSchema = z.object({
  imageUrl: z.string().url(),
  fullName: fullNameSchema,
  email: emailSchema.optional().or(z.literal("")),
  vehicleType: vehicleTypeSchema,
  vehicleNumber: vehicleNumberSchema,
  dob: dobSchema,
  dlNumber: z.string().nonempty(),
  adharCardNumber: z
    .string()
    .min(12, { message: "Adhar card number must be 12 digits" })
    .max(12, { message: "Adhar card number must be 12 digits" }),
  frontOfAdharCardImageUrl: z.string().url(),
  backOfAdharCardImageUrl: z.string().url(),
  policeVerificationDocumentUrl: z.string().url(),
  address: AddressSchema,
});

export const KabadiwalaOnboardingStepOneSchema =
  KabadiwalaOnboardingBaseSchema.pick({
    imageUrl: true,
    fullName: true,
    email: true,
  }).extend({
    imageFileKey: z.string().nonempty(),
  });

export const KabadiwalaOnboardingStepTwoSchema =
  KabadiwalaOnboardingBaseSchema.pick({
    vehicleType: true,
    vehicleNumber: true,
    // dob: true,
  });

export const KabadiwalaOnboardingStepThreeSchema =
  KabadiwalaOnboardingBaseSchema.pick({
    frontOfAdharCardImageUrl: true,
    backOfAdharCardImageUrl: true,
    adharCardNumber: true,
  }).extend({
    frontOfAdharCardFileKey: z.string().nonempty(),
    backOfAdharCardFileKey: z.string().nonempty(),
  });

export const KabadiwalaOnboardingStepFourSchema =
  KabadiwalaOnboardingBaseSchema.pick({
    dlNumber: true,
    dob: true,
  });

export const KabadiwalaOnboardingStepFiveSchema = z.object({
  policeVerificationDocumentUrl: z.string(),
  policeVerificationDocumentFileKey: z.string().optional(),
});

export const KabadiwalaOnboardingStepSixSchema =
  KabadiwalaOnboardingBaseSchema.pick({
    address: true,
  });

export const EditProfileSchema = z.object({
  imageUrl: z.string().min(1, "Profile photo is required"),
  imageFileKey: z.string().nonempty("Image file key is required"),
  fullName: fullNameSchema,
  phoneNumber: phoneNumberSchema,
  email: emailSchema,
  address: AddressSchema,
});

export const EditDocumentsSchema = z.object({
  frontOfAdharCardImageUrl: z.string().url(),
  frontOfAdharCardFileKey: z.string(),
  backOfAdharCardImageUrl: z.string().url(),
  backOfAdharCardFileKey: z.string(),
  policeVerificationDocumentUrl: z.string().url(),
  policeVerificationDocumentFileKey: z.string(),
});

const timeSlotSchema = z.object({
  startTime: z.string(),
  endTime: z.string(),
});

const dayScheduleSchema = z.object({
  isAvailable: z.boolean().default(false),
  timeSlots: z.array(timeSlotSchema).default([]),
});

export const ScheduleHoursSchema = z.object({
  sunday: dayScheduleSchema,
  monday: dayScheduleSchema,
  tuesday: dayScheduleSchema,
  wednesday: dayScheduleSchema,
  thursday: dayScheduleSchema,
  friday: dayScheduleSchema,
  saturday: dayScheduleSchema,
});

export const VehicleSchema = z.object({
  vehicleNumber: vehicleNumberSchema,
  vehicleType: vehicleTypeSchema,
});

export const AddMoneySchema = z.object({
  amount: moneySchema,
  currency: z.string().optional().default("INR"),
});

export const VerifyPaymentSchema = z.object({
  transactionId: z.string(),
  razorpayOrderId: z.string(),
  razorpayPaymentId: z.string(),
  razorpaySignature: z.string(),
});

export const LocationUpdateSchema = z.object({
  coordinates: z.object({
    latitude: z.number(),
    longitude: z.number(),
  }),
  orderId: z.string().optional(),
});

export const KabadiwalaArrivalVerificationSchema = z.object({
  otp: otpSchema,
});

export const WorkHoursModeSchema = z.object({
  mode: z.enum(["AUTOMATIC", "MANUAL"]),
});

export const UpdateWorkHoursSchema = z.object({
  mode: z.enum(["AUTOMATIC", "MANUAL"]),
  scheduleHours: ScheduleHoursSchema.optional(),
});
