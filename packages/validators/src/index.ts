/**
 * * Why I am doing this ?
 *
 * 1. Reusability - Field validations are defined once and reused across multiple schemas
 * 2. Maintainability - Update validation rules in one place and they propagate everywhere
 * 3. Consistency - Ensures all forms use identical validation for the same fields
 * 4. Composability - Easily build complex schemas from simpler building blocks
 *
 * Example: phoneNumberSchema is defined once but used in multiple forms (login, signup)
 * without duplicating validation logic.
 */

import { z } from "zod";

import { categoryRateTypeEnum, orderStatusEnum } from "@acme/db/schema";

// Individual field validators - reusable building blocks
export const phoneNumberSchema = z.coerce
  .string()
  .min(10, { message: "Phone number must be of at least 10 digits." })
  .max(10, { message: "Phone number must be of at most 10 digits." });

export const otpSchema = z.string().length(6, {
  message: "OTP must be of 6 digits.",
});

export const moneySchema = z.coerce
  .number({ message: "Please enter a valid amount." })
  .min(1000, { message: "Amount must be greater than 1000." })
  .max(100000, { message: "Amount must be less than or equal to 100000." });

export const fullNameSchema = z
  .string()
  .min(2, { message: "Fullname must be at least 2 characters." })
  .max(20, { message: "Fullname must be at most 20 characters." });

export const emailSchema = z
  .string()
  .email({ message: "Invalid email address." });

export const razorpayContactSchema = z.object({
  name: z.string(),
  email: z.string().email(),
  contact: z.string(),
  type: z.enum(["customer", "employee", "vendor", "self"]),
  reference_id: z.string().optional(),
  notes: z.record(z.string()).optional(),
});

export const AddressSchema = z.object({
  name: z.string().nonempty({ message: "Name is required." }),
  display: z.string().nonempty({ message: "Display name is required." }),
  street: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  country: z.string().optional(),
  postalCode: z.string().optional(),
  district: z.string().optional(),
  googleAddressComponent: z
    .array(
      z.object({
        long_name: z.string(),
        short_name: z.string(),
        types: z.array(z.string()),
      }),
    )
    .nullable()
    .optional(),
  googlePlaceId: z.string().optional(),
  coordinates: z.object({
    latitude: z.number(),
    longitude: z.number(),
  }),
  addressType: z.enum(["HOME", "WORK", "OTHER"]),
  localAddress: z.string().nonempty({ message: "Local address is required." }),
  landmark: z.string().nonempty({ message: "Landmark is required." }),
});

export const passwordSchema = z
  .string()
  .min(8, { message: "Password must be at least 8 characters long." })
  .max(20, { message: "Password must be at most 20 characters long." })
  .regex(/[a-z]/, {
    message: "Password must contain at least one lowercase letter.",
  })
  .regex(/[A-Z]/, {
    message: "Password must contain at least one uppercase letter.",
  })
  .regex(/[0-9]/, { message: "Password must contain at least one number." })
  .regex(/[@$!%*?&]/, {
    message: "Password must contain at least one special character.",
  });

export const upiIdSchema = z
  .string()
  .min(1, "UPI ID is required")
  .regex(
    /^[a-zA-Z0-9.-]{2,256}@[a-zA-Z]{2,64}$/,
    "Invalid UPI ID format. Example: yourname@bankname",
  );

export const ifscCodeSchema = z
  .string()
  .min(1, "IFSC Code is required")
  .regex(
    /^[A-Z]{4}0[A-Z0-9]{6}$/,
    "Invalid IFSC Code format. Example: ABCD0123456",
  );

export const dobSchema = z.coerce.date().refine(
  (date) => {
    const age = new Date().getFullYear() - date.getFullYear();
    return age >= 18 && age <= 100;
  },
  { message: "Age must be between 18 and 100 years" },
);

// Combined schemas for different forms
export const SignUpSchema = z.object({
  phoneNumber: phoneNumberSchema,
  whatsAppConsent: z.boolean().default(false),
});

export const LoginSchema = z.object({
  phoneNumber: phoneNumberSchema,
});

export const OtpVerificationSchema = z.object({
  otp: otpSchema,
});

export const OnboardingStepOneSchema = z.object({
  fullName: fullNameSchema,
  email: emailSchema,
});

export const OnboardingStepTwoSchema = AddressSchema;

export const OnboardingStepThreeSchema = z
  .object({
    bankName: z.string().optional(),
    upiId: z
      .string()
      .optional()
      .refine(
        (val) => {
          // Only validate if it's provided
          if (!val || val.trim() === "") return true;
          return upiIdSchema.safeParse(val).success;
        },
        {
          message: "Invalid UPI ID format. Example: yourname@bankname",
        },
      ),
    ifscCode: z
      .string()
      .optional()
      .refine(
        (val) => {
          // Only validate if it's provided
          if (!val || val.trim() === "") return true;
          return ifscCodeSchema.safeParse(val).success;
        },
        {
          message: "Invalid IFSC Code format. Example: ABCD0123456",
        },
      ),
    accountNumber: z
      .string()
      .optional()
      .refine(
        (val) => {
          // Only validate if it's provided
          if (!val || val.trim() === "") return true;
          return /^\d{9,18}$/.test(val);
        },
        {
          message: "Account number must be 9-18 digits",
        },
      ),
  })
  .refine(
    (data) => {
      // If UPI ID is provided, it should be valid and no banking details needed
      if (data.upiId && data.upiId.trim() !== "") {
        return upiIdSchema.safeParse(data.upiId).success;
      }

      // If banking details are provided, all required fields should be valid
      if (
        data.bankName &&
        data.bankName.trim() !== "" &&
        data.ifscCode &&
        data.ifscCode.trim() !== "" &&
        data.accountNumber &&
        data.accountNumber.trim() !== ""
      ) {
        return (
          data.bankName.length >= 2 &&
          data.bankName.length <= 100 &&
          ifscCodeSchema.safeParse(data.ifscCode).success &&
          /^\d{9,18}$/.test(data.accountNumber)
        );
      }

      // Neither UPI nor complete banking details provided
      return false;
    },
    {
      message:
        "Please provide either a valid UPI ID or complete banking details (Bank name, IFSC code, and account number).",
    },
  );

export const PayoutMethodSchema = z
  .object({
    type: z.enum(["UPI", "BANK_ACCOUNT"]),
    bankName: z.string().optional(),
    upiId: z
      .string()
      .optional()
      .refine(
        (val) => {
          // Only validate if it's provided and type is UPI
          if (!val || val.trim() === "") return true;
          return upiIdSchema.safeParse(val).success;
        },
        {
          message: "Invalid UPI ID format. Example: yourname@bankname",
        },
      ),
    ifscCode: z
      .string()
      .optional()
      .refine(
        (val) => {
          // Only validate if it's provided
          if (!val || val.trim() === "") return true;
          return ifscCodeSchema.safeParse(val).success;
        },
        {
          message: "Invalid IFSC Code format. Example: ABCD0123456",
        },
      ),
    accountNumber: z
      .string()
      .optional()
      .refine(
        (val) => {
          // Only validate if it's provided
          if (!val || val.trim() === "") return true;
          return /^\d{9,18}$/.test(val);
        },
        {
          message: "Account number must be 9-18 digits",
        },
      ),
    isDefault: z.boolean().default(false),
  })
  .refine(
    (data) => {
      if (data.type === "UPI") {
        return data.upiId && data.upiId.trim() !== "";
      }

      // For BANK_ACCOUNT type
      return (
        data.bankName &&
        data.bankName.trim() !== "" &&
        data.ifscCode &&
        data.ifscCode.trim() !== "" &&
        data.accountNumber &&
        data.accountNumber.trim() !== "" &&
        data.bankName.length >= 2 &&
        data.bankName.length <= 100
      );
    },
    {
      message:
        "Please fill all required fields for the selected payment method type.",
      path: ["type"], // This will show the error on the type field
    },
  );

export const AdminLoginSchema = z.object({
  email: z.string().email({ message: "Please enter a valid email address." }),
  password: z
    .string()
    .min(8, { message: "Password must be at least 8 characters long." }),
});

export const SubCategorySchema = z.object({
  id: z.string().optional(), // For existing subcategories
  name: z
    .string()
    .min(2, { message: "Subcategory name must be at least 2 characters." }),
  image: z.string().nonempty({ message: "Image is required." }),
  description: z.string().optional(),
  isActive: z.boolean().default(true),
});

export const CategoryBaseSchema = z.object({
  name: z
    .string()
    .min(2, { message: "Category name must be at least 2 characters." })
    .max(50, { message: "Category name must be at most 50 characters." }),
  rate: z.coerce
    .number({ message: "Please enter a valid rate." })
    .min(0, { message: "Rate must be a non-negative number." }),
  rateType: z.enum(categoryRateTypeEnum.enumValues, {
    message: "Invalid rate type.",
  }),
  compensationKabadiwalaRate: z.coerce
    .number({ message: "Please enter a valid Kabadiwala rate." })
    .min(0, { message: "Kabadiwala rate must be a non-negative number." }),
  compensationRecyclerRate: z.coerce
    .number({ message: "Please enter a valid Recycler rate." })
    .min(0, { message: "Recycler rate must be a non-negative number." }),
  image: z.string().nonempty({ message: "Image is required." }),
  description: z.string().optional().nullable(),
  isActive: z.boolean().default(true),
  tag: z
    .string()
    .trim()
    .toLowerCase()
    .max(128)
    .optional()
    .nullable()
    .refine((val) => val == null || /^[a-z0-9]+$/i.test(val), {
      message: "Tag must be alphanumeric (letters and/or numbers only)",
    }),
});

export const CategoryCreateSchema = CategoryBaseSchema.extend({
  parentId: z
    .string()
    .nonempty({ message: "Parent ID is required for sub-category" })
    .nullable() // Allow null for top-level categories
    .optional(), // Make it optional, logic will set to null if not provided for top-level
  tag: z
    .string()
    .trim()
    .toLowerCase()
    .max(128)
    .optional()
    .nullable()
    .refine((val) => val == null || /^[a-z0-9]+$/i.test(val), {
      message: "Tag must be alphanumeric (letters and/or numbers only)",
    }),
});

// Schema for updating a category
export const CategoryUpdateSchema = CategoryBaseSchema.partial().extend({
  id: z.string().nonempty({ message: "Category ID is required for updates." }),
  parentId: z
    .string()
    .nonempty()
    .nullable() // Allow setting parentId to null (making it top-level)
    .optional(), // Allow not providing it if not changing parent
  tag: z
    .string()
    .trim()
    .toLowerCase()
    .max(128)
    .optional()
    .nullable()
    .refine((val) => val == null || /^[a-z0-9]+$/i.test(val), {
      message: "Tag must be alphanumeric (letters and/or numbers only)",
    }),
});

// Schema for operations requiring just an ID
export const CategoryIdInputSchema = z.object({
  id: z.string().nonempty({ message: "Category ID is required." }),
});

// Schema for fetching a category by ID with options
export const CategoryFetchByIdInputSchema = CategoryIdInputSchema.extend({
  withParent: z.boolean().optional().default(false),
  withChildren: z.boolean().optional().default(false),
  includeDeleted: z.boolean().optional().default(false),
});

// Schema for fetching direct children
export const CategoryFetchChildrenInputSchema = z.object({
  parentId: z.string().nonempty({ message: "Parent ID is required." }),
  includeDeleted: z.boolean().optional().default(false),
});

// Schema for fetching direct parent
export const CategoryFetchParentInputSchema = z.object({
  childId: z.string().nonempty({ message: "Child ID is required." }),
  includeDeleted: z.boolean().optional().default(false), // To fetch parent even if child is soft-deleted
});

export const AdminCRUDSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters" }),
  email: emailSchema,
  image: z.string().optional(),
});

export const SellerCRUDSchema = z.object({
  fullName: fullNameSchema,
  image: z.string().optional(),
  phoneNumber: phoneNumberSchema,
  address: AddressSchema,
});

export const KabadiwalaCRUDSchema = z.object({
  name: fullNameSchema,
  image: z.string().optional(),
  phoneNumber: phoneNumberSchema,
});

export const AddCartItemSchema = z.object({
  categoryId: z.string().nonempty({ message: "Category ID is required." }),
  quantity: z.coerce
    .number()
    .min(1, { message: "Quantity should be greater than 0" }),
});

export const UpdateCartItemSchema = z.object({
  orderItemId: z.string(),
});

export const ProfileUpdateSchema = z.object({
  fullName: fullNameSchema,
  email: emailSchema.optional(),
  phoneNumber: phoneNumberSchema,
});

export const OrderFilterTabsSchema = z.object({
  tab: z.enum([...orderStatusEnum.enumValues, "ALL"]).default("ALL"),
});

export const DeleteAccountSchema = z.object({
  reason: z.string().min(10, {
    message: "Please provide a reason for account deletion.",
  }),
  confirmPermanent: z.boolean().refine((val) => val === true, {
    message: "You must confirm that this action is permanent.",
  }),
});

export const RateUsFormSchema = z.object({
  sellingExperience: z.string().min(1, {
    message: "Please rate your experience.",
  }),
  customerSupport: z
    .number()
    .min(1, { message: "Please rate customer support." })
    .max(5),
  suggestions: z.string().nonempty({ message: "Suggestions are required." }),
  quickSuggestions: z.array(z.string()).min(1, {
    message: "Please select at least one quick suggestion.",
  }),
});

export const CreateRazorpayContactSchema = razorpayContactSchema;
