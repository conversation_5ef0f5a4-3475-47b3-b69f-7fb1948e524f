export const ka<PERSON><PERSON><PERSON><PERSON>oSellerNotificationMessages = {
  orderPlaced: ({ orderId }: { orderId: string }) => {
    return {
      title: "Order Placed",
      description: `Your scrap pickup order #${orderId} has been successfully placed. We're finding an agent for you.`,
    };
  },

  orderAcceptedByAgent: ({
    agentName,
    orderId,
  }: {
    agentName: string;
    orderId: string;
  }) => {
    return {
      title: "Order Accepted",
      description: `Great news! Agent ${agentName} has accepted your order #${orderId} and will be on their way soon.`,
    };
  },

  agentEnRouteToCustomer: ({
    agentName,
    orderId,
  }: {
    agentName: string;
    orderId: string;
  }) => {
    return {
      title: "Agent En Route",
      description: `Agent ${agentName} is on the way to your location for order #${orderId}.`,
    };
  },

  agentArrivedAtCustomer: ({
    agentName,
    orderId,
  }: {
    agentName: string;
    orderId: string;
  }) => {
    return {
      title: "Agent Arrived",
      description: `Agent ${agent<PERSON><PERSON>} has arrived at your location for order #${orderId}.`,
    };
  },

  pickupStartedByAgent: ({
    agentName,
    orderId,
  }: {
    agentName: string;
    orderId: string;
  }) => {
    return {
      title: "Pickup Started",
      description: `Agent ${agentName} has started collecting your scrap items for order #${orderId}.`,
    };
  },

  pickupCompletedByAgent: ({
    agentName,
    orderId,
  }: {
    agentName: string;
    orderId: string;
  }) => {
    return {
      title: "Items Collected",
      description: `Agent ${agentName} has collected your items for order #${orderId} and is now proceeding to the warehouse.`,
    };
  },

  orderCompleted: ({ orderId }: { orderId: string }) => {
    return {
      title: "Order Completed",
      description: `Your scrap pickup order #${orderId} has been successfully processed and completed. Thank you for using our service!`,
    };
  },

  otpVerified: ({ orderId }: { orderId: string }) => {
    return {
      title: "OTP Verified",
      description: `The agent has successfully verified the OTP for your order #${orderId}.`,
    };
  },

  selfieVerified: ({ orderId }: { orderId: string }) => {
    return {
      title: "Selfie Verified",
      description: `The agent has uploaded and verified their selfie for order #${orderId}.`,
    };
  },

  customerApprovalRequested: ({ orderId }: { orderId: string }) => {
    return {
      title: "Approval Requested",
      description: `The agent has requested your approval for order #${orderId}. Please review and confirm the order details.`,
    };
  },

  itemAddedToOrder: ({
    orderId,
    categoryName,
  }: {
    orderId: string;
    categoryName: string;
  }) => {
    return {
      title: "Item Added",
      description: `The agent has added '${categoryName}' to your order #${orderId}.`,
    };
  },

  itemUpdatedInOrder: ({
    orderId,
    categoryName,
  }: {
    orderId: string;
    categoryName: string;
  }) => {
    return {
      title: "Item Updated",
      description: `The agent has updated '${categoryName}' in your order #${orderId}.`,
    };
  },

  itemRemovedFromOrder: ({
    orderId,
    categoryName,
  }: {
    orderId: string;
    categoryName: string;
  }) => {
    return {
      title: "Item Removed",
      description: `The agent has removed '${categoryName}' from your order #${orderId}.`,
    };
  },

  paymentProcessed: ({ orderId }: { orderId: string }) => {
    return {
      title: "Payment Processed",
      description: `The agent has completed the payment for order #${orderId}.`,
    };
  },

  orderRejected: ({ orderId }: { orderId: string }) => {
    return {
      title: "Order Rejected",
      description: `The agent has rejected order #${orderId}.`,
    };
  },
};

export const sellerToKabadiwalaNotificationMessages = {
  orderPlaced: ({ orderId }: { orderId: string }) => {
    return {
      title: "Order Placed",
      description: `Your scrap pickup order #${orderId} has been successfully placed. We're finding an agent for you.`,
    };
  },

  updateOrderApproveStatus: ({
    orderId,
    status,
  }: {
    orderId: string;
    status: string;
  }) => {
    return {
      title: "Order Approval Status Change",
      description: `Seller has changed the order approval status to ${status} for order ${orderId}.`,
    };
  },

  quickPickupOrderPlaced: ({ orderId }: { orderId: string }) => {
    return {
      title: "Quick Pickup Order Placed",
      description: `A quick pickup order #${orderId} has been placed by the seller. Please assign an agent as soon as possible.`,
    };
  },

  itemAddedToOrderBySeller: ({
    orderId,
    categoryName,
  }: {
    orderId: string;
    categoryName: string;
  }) => {
    return {
      title: "Item Added by Seller",
      description: `Seller has added '${categoryName}' to order #${orderId}. Please review the updated order details.`,
    };
  },

  itemUpdatedInOrderBySeller: ({
    orderId,
    categoryName,
  }: {
    orderId: string;
    categoryName: string;
  }) => {
    return {
      title: "Item Updated by Seller",
      description: `Seller has updated '${categoryName}' in order #${orderId}. Please review the updated order details.`,
    };
  },

  itemRemovedFromOrderBySeller: ({
    orderId,
    categoryName,
  }: {
    orderId: string;
    categoryName: string;
  }) => {
    return {
      title: "Item Removed by Seller",
      description: `Seller has removed '${categoryName}' from order #${orderId}. Please review the updated order details.`,
    };
  },
};
