{"name": "@acme/validators", "private": true, "version": "0.1.0", "type": "module", "exports": {".": {"default": "./src/index.ts"}, "./kabadiwala": {"default": "./src/kabadiwala.ts"}, "./utils": {"default": "./src/utils.ts"}, "./notification": "./src/notification.ts"}, "license": "MIT", "scripts": {"clean": "git clean -xdf .cache .turbo dist node_modules", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "dependencies": {"@acme/db": "workspace:*", "zod": "catalog:"}, "devDependencies": {"@acme/eslint-config": "workspace:*", "@acme/prettier-config": "workspace:*", "@acme/tsconfig": "workspace:*", "eslint": "catalog:", "prettier": "catalog:", "typescript": "catalog:"}, "prettier": "@acme/prettier-config"}